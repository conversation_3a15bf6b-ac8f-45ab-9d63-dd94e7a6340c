#!/usr/bin/env python3
"""
优化报告生成器
生成超参数优化前后的详细对比报告
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
import os
from datetime import datetime

class OptimizationReportGenerator:
    """优化报告生成器"""
    
    def __init__(self):
        """初始化报告生成器"""
        self.baseline_results = {}
        self.optimized_results = {}
        self.optimization_details = {}
        
    def load_optimization_results(self, traditional_file=None, dl_file=None):
        """加载优化结果"""
        optimization_details = {}
        
        if traditional_file and os.path.exists(traditional_file):
            try:
                with open(traditional_file, 'r', encoding='utf-8') as f:
                    traditional_data = json.load(f)
                    optimization_details['traditional'] = traditional_data
                print(f"✅ 加载传统ML优化结果: {traditional_file}")
            except Exception as e:
                print(f"⚠️  加载传统ML优化结果失败: {str(e)}")
        
        if dl_file and os.path.exists(dl_file):
            try:
                with open(dl_file, 'r', encoding='utf-8') as f:
                    dl_data = json.load(f)
                    optimization_details['deep_learning'] = dl_data
                print(f"✅ 加载深度学习优化结果: {dl_file}")
            except Exception as e:
                print(f"⚠️  加载深度学习优化结果失败: {str(e)}")
        
        self.optimization_details = optimization_details
        return optimization_details
    
    def set_baseline_results(self, results: dict):
        """设置基线结果（优化前）"""
        self.baseline_results = results
    
    def set_optimized_results(self, results: dict):
        """设置优化后结果"""
        self.optimized_results = results
    
    def calculate_improvements(self) -> dict:
        """计算性能提升"""
        improvements = {}
        
        for dataset_name in self.baseline_results.keys():
            if dataset_name in self.optimized_results:
                baseline = self.baseline_results[dataset_name]
                optimized = self.optimized_results[dataset_name]
                
                dataset_improvements = {}
                
                for model_name in baseline.keys():
                    if model_name in optimized and 'error' not in baseline[model_name] and 'error' not in optimized[model_name]:
                        baseline_score = baseline[model_name].get('r2_score', baseline[model_name].get('accuracy', 0))
                        optimized_score = optimized[model_name].get('r2_score', optimized[model_name].get('accuracy', 0))
                        
                        if baseline_score > 0:
                            improvement = ((optimized_score - baseline_score) / baseline_score) * 100
                            dataset_improvements[model_name] = {
                                'baseline_score': baseline_score,
                                'optimized_score': optimized_score,
                                'improvement_percent': improvement,
                                'absolute_improvement': optimized_score - baseline_score
                            }
                
                improvements[dataset_name] = dataset_improvements
        
        return improvements
    
    def generate_performance_comparison_chart(self, improvements: dict, dataset_name: str):
        """生成性能对比图表"""
        try:
            if dataset_name not in improvements:
                return
            
            dataset_improvements = improvements[dataset_name]
            if not dataset_improvements:
                return
            
            models = list(dataset_improvements.keys())
            baseline_scores = [dataset_improvements[model]['baseline_score'] for model in models]
            optimized_scores = [dataset_improvements[model]['optimized_score'] for model in models]
            
            # 创建对比图
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
            
            # 性能对比柱状图
            x = np.arange(len(models))
            width = 0.35
            
            bars1 = ax1.bar(x - width/2, baseline_scores, width, label='优化前', alpha=0.7, color='lightcoral')
            bars2 = ax1.bar(x + width/2, optimized_scores, width, label='优化后', alpha=0.7, color='lightgreen')
            
            ax1.set_xlabel('模型')
            ax1.set_ylabel('性能分数')
            ax1.set_title(f'{dataset_name} - 优化前后性能对比')
            ax1.set_xticks(x)
            ax1.set_xticklabels(models, rotation=45, ha='right')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 添加数值标签
            for bar in bars1:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{height:.3f}', ha='center', va='bottom', fontsize=8)
            
            for bar in bars2:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{height:.3f}', ha='center', va='bottom', fontsize=8)
            
            # 改进百分比图
            improvements_percent = [dataset_improvements[model]['improvement_percent'] for model in models]
            colors = ['green' if imp > 0 else 'red' for imp in improvements_percent]
            
            bars3 = ax2.bar(models, improvements_percent, color=colors, alpha=0.7)
            ax2.set_xlabel('模型')
            ax2.set_ylabel('改进百分比 (%)')
            ax2.set_title(f'{dataset_name} - 性能改进百分比')
            ax2.set_xticklabels(models, rotation=45, ha='right')
            ax2.grid(True, alpha=0.3)
            ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
            
            # 添加数值标签
            for bar, imp in zip(bars3, improvements_percent):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + (1 if height > 0 else -3),
                        f'{imp:.1f}%', ha='center', va='bottom' if height > 0 else 'top', fontsize=8)
            
            plt.tight_layout()
            
            # 保存图表
            os.makedirs('optimization_reports', exist_ok=True)
            plt.savefig(f'optimization_reports/{dataset_name}_performance_comparison.png', 
                       dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✅ 生成性能对比图: optimization_reports/{dataset_name}_performance_comparison.png")
            
        except Exception as e:
            print(f"❌ 生成性能对比图失败: {str(e)}")
    
    def generate_optimization_summary_chart(self):
        """生成优化总结图表"""
        try:
            improvements = self.calculate_improvements()
            
            if not improvements:
                return
            
            # 收集所有改进数据
            all_improvements = []
            model_names = []
            dataset_names = []
            
            for dataset_name, dataset_improvements in improvements.items():
                for model_name, improvement_data in dataset_improvements.items():
                    all_improvements.append(improvement_data['improvement_percent'])
                    model_names.append(model_name)
                    dataset_names.append(dataset_name)
            
            if not all_improvements:
                return
            
            # 创建总结图
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
            
            # 改进分布直方图
            ax1.hist(all_improvements, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            ax1.set_xlabel('改进百分比 (%)')
            ax1.set_ylabel('频次')
            ax1.set_title('优化效果分布')
            ax1.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='无改进线')
            ax1.axvline(x=np.mean(all_improvements), color='green', linestyle='-', alpha=0.7, label=f'平均改进: {np.mean(all_improvements):.1f}%')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 模型改进对比
            unique_models = list(set(model_names))
            model_avg_improvements = []
            
            for model in unique_models:
                model_improvements = [all_improvements[i] for i, name in enumerate(model_names) if name == model]
                model_avg_improvements.append(np.mean(model_improvements))
            
            colors = ['green' if imp > 0 else 'red' for imp in model_avg_improvements]
            bars = ax2.bar(unique_models, model_avg_improvements, color=colors, alpha=0.7)
            ax2.set_xlabel('模型')
            ax2.set_ylabel('平均改进百分比 (%)')
            ax2.set_title('各模型平均优化效果')
            ax2.set_xticklabels(unique_models, rotation=45, ha='right')
            ax2.grid(True, alpha=0.3)
            ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
            
            # 添加数值标签
            for bar, imp in zip(bars, model_avg_improvements):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + (0.5 if height > 0 else -1),
                        f'{imp:.1f}%', ha='center', va='bottom' if height > 0 else 'top', fontsize=8)
            
            plt.tight_layout()
            
            # 保存图表
            plt.savefig('optimization_reports/optimization_summary.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✅ 生成优化总结图: optimization_reports/optimization_summary.png")
            
        except Exception as e:
            print(f"❌ 生成优化总结图失败: {str(e)}")
    
    def generate_detailed_report(self) -> str:
        """生成详细的优化报告"""
        improvements = self.calculate_improvements()
        
        report_lines = []
        report_lines.append("# 🚀 超参数优化详细报告")
        report_lines.append("=" * 80)
        report_lines.append("")
        report_lines.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # 总体统计
        all_improvements = []
        for dataset_improvements in improvements.values():
            for improvement_data in dataset_improvements.values():
                all_improvements.append(improvement_data['improvement_percent'])
        
        if all_improvements:
            report_lines.append("## 📊 总体优化效果")
            report_lines.append("")
            report_lines.append(f"- **总优化模型数**: {len(all_improvements)}")
            report_lines.append(f"- **平均改进**: {np.mean(all_improvements):.2f}%")
            report_lines.append(f"- **最大改进**: {np.max(all_improvements):.2f}%")
            report_lines.append(f"- **最小改进**: {np.min(all_improvements):.2f}%")
            report_lines.append(f"- **改进模型数**: {sum(1 for imp in all_improvements if imp > 0)}")
            report_lines.append(f"- **性能下降模型数**: {sum(1 for imp in all_improvements if imp < 0)}")
            report_lines.append("")
        
        # 各数据集详细结果
        for dataset_name, dataset_improvements in improvements.items():
            report_lines.append(f"## 📈 {dataset_name}")
            report_lines.append("")
            
            if not dataset_improvements:
                report_lines.append("❌ 该数据集没有有效的优化结果")
                report_lines.append("")
                continue
            
            # 最佳改进
            best_model = max(dataset_improvements.keys(), 
                           key=lambda x: dataset_improvements[x]['improvement_percent'])
            best_improvement = dataset_improvements[best_model]
            
            report_lines.append(f"### 🏆 最佳优化")
            report_lines.append(f"- **模型**: {best_model}")
            report_lines.append(f"- **优化前**: {best_improvement['baseline_score']:.4f}")
            report_lines.append(f"- **优化后**: {best_improvement['optimized_score']:.4f}")
            report_lines.append(f"- **改进**: {best_improvement['improvement_percent']:.2f}%")
            report_lines.append("")
            
            # 详细结果表格
            report_lines.append("### 📋 详细优化结果")
            report_lines.append("")
            report_lines.append("| 模型 | 优化前 | 优化后 | 绝对改进 | 相对改进(%) | 状态 |")
            report_lines.append("|------|--------|--------|----------|-------------|------|")
            
            for model_name, improvement_data in dataset_improvements.items():
                baseline = improvement_data['baseline_score']
                optimized = improvement_data['optimized_score']
                abs_imp = improvement_data['absolute_improvement']
                rel_imp = improvement_data['improvement_percent']
                status = "✅ 改进" if rel_imp > 0 else "❌ 下降" if rel_imp < 0 else "➖ 无变化"
                
                report_lines.append(f"| {model_name} | {baseline:.4f} | {optimized:.4f} | {abs_imp:+.4f} | {rel_imp:+.2f}% | {status} |")
            
            report_lines.append("")
        
        # 优化参数详情
        if self.optimization_details:
            report_lines.append("## 🔧 优化参数详情")
            report_lines.append("")
            
            if 'traditional' in self.optimization_details:
                traditional_data = self.optimization_details['traditional']
                if 'best_parameters' in traditional_data:
                    report_lines.append("### 🤖 传统机器学习最优参数")
                    report_lines.append("")
                    
                    for model_name, params in traditional_data['best_parameters'].items():
                        report_lines.append(f"#### {model_name}")
                        for param_name, param_value in params.items():
                            report_lines.append(f"- **{param_name}**: {param_value}")
                        report_lines.append("")
            
            if 'deep_learning' in self.optimization_details:
                dl_data = self.optimization_details['deep_learning']
                if 'best_parameters' in dl_data:
                    report_lines.append("### 🧠 深度学习最优参数")
                    report_lines.append("")
                    
                    for model_name, params in dl_data['best_parameters'].items():
                        report_lines.append(f"#### {model_name}")
                        for param_name, param_value in params.items():
                            report_lines.append(f"- **{param_name}**: {param_value}")
                        report_lines.append("")
        
        # 建议和结论
        report_lines.append("## 💡 优化建议和结论")
        report_lines.append("")
        
        if all_improvements:
            avg_improvement = np.mean(all_improvements)
            if avg_improvement > 5:
                report_lines.append("✅ **优化效果显著**: 平均改进超过5%，建议采用优化后的参数。")
            elif avg_improvement > 0:
                report_lines.append("✅ **优化效果良好**: 有一定改进，建议采用优化后的参数。")
            else:
                report_lines.append("⚠️  **优化效果有限**: 平均改进较小，可考虑进一步调整优化策略。")
            
            report_lines.append("")
            
            # 模型特定建议
            model_improvements = {}
            for dataset_improvements in improvements.values():
                for model_name, improvement_data in dataset_improvements.items():
                    if model_name not in model_improvements:
                        model_improvements[model_name] = []
                    model_improvements[model_name].append(improvement_data['improvement_percent'])
            
            report_lines.append("### 🎯 模型特定建议")
            for model_name, model_imps in model_improvements.items():
                avg_imp = np.mean(model_imps)
                if avg_imp > 10:
                    report_lines.append(f"- **{model_name}**: 优化效果优秀 ({avg_imp:.1f}%)，强烈推荐使用优化参数")
                elif avg_imp > 5:
                    report_lines.append(f"- **{model_name}**: 优化效果良好 ({avg_imp:.1f}%)，推荐使用优化参数")
                elif avg_imp > 0:
                    report_lines.append(f"- **{model_name}**: 优化效果一般 ({avg_imp:.1f}%)，可考虑使用优化参数")
                else:
                    report_lines.append(f"- **{model_name}**: 优化效果不佳 ({avg_imp:.1f}%)，建议保持默认参数或进一步调优")
        
        report_content = '\n'.join(report_lines)
        
        # 保存报告
        os.makedirs('optimization_reports', exist_ok=True)
        with open('optimization_reports/detailed_optimization_report.md', 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print("✅ 详细优化报告已保存: optimization_reports/detailed_optimization_report.md")
        
        return report_content
    
    def generate_complete_report(self, baseline_results: dict, optimized_results: dict, 
                               traditional_file: str = None, dl_file: str = None):
        """生成完整的优化报告"""
        print("📊 生成超参数优化报告...")
        
        # 设置结果
        self.set_baseline_results(baseline_results)
        self.set_optimized_results(optimized_results)
        
        # 加载优化详情
        self.load_optimization_results(traditional_file, dl_file)
        
        # 计算改进
        improvements = self.calculate_improvements()
        
        # 生成图表
        for dataset_name in improvements.keys():
            self.generate_performance_comparison_chart(improvements, dataset_name)
        
        self.generate_optimization_summary_chart()
        
        # 生成详细报告
        report_content = self.generate_detailed_report()
        
        print("✅ 超参数优化报告生成完成!")
        print("📁 报告文件位置: optimization_reports/")
        
        return report_content

def main():
    """测试函数"""
    # 模拟数据进行测试
    baseline_results = {
        'speed_prediction': {
            'Random Forest': {'r2_score': 0.85, 'training_time': 1.2},
            'XGBoost': {'r2_score': 0.82, 'training_time': 2.1},
            'Extra Trees': {'r2_score': 0.88, 'training_time': 0.8}
        }
    }
    
    optimized_results = {
        'speed_prediction': {
            'Random Forest': {'r2_score': 0.91, 'training_time': 1.5},
            'XGBoost': {'r2_score': 0.89, 'training_time': 2.3},
            'Extra Trees': {'r2_score': 0.92, 'training_time': 1.0}
        }
    }
    
    # 生成报告
    generator = OptimizationReportGenerator()
    generator.generate_complete_report(baseline_results, optimized_results)
    
    print("✅ 测试完成!")

if __name__ == "__main__":
    main()
