#!/usr/bin/env python3
"""
分类任务专用可视化模块
针对轴型分类等分类任务的专业可视化
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report, roc_curve, auc, precision_recall_curve
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.preprocessing import label_binarize
from improved_visualization_base import ImprovedVisualizationBase
import warnings
warnings.filterwarnings('ignore')

class ClassificationVisualizer(ImprovedVisualizationBase):
    """分类任务可视化器"""
    
    def __init__(self, output_dir='classification_visualizations'):
        """初始化分类可视化器"""
        super().__init__(output_dir)
        print("🎯 分类任务可视化器初始化完成")
    
    def create_confusion_matrix_plot(self, y_true, y_pred, class_names=None, model_name='Model', 
                                   task_name='分类预测', language='chinese'):
        """创建混淆矩阵热力图（单独图表）"""
        print(f"   📊 生成{model_name}混淆矩阵图 ({language})...")
        
        # 计算混淆矩阵
        cm = confusion_matrix(y_true, y_pred)
        
        if class_names is None:
            unique_labels = np.unique(np.concatenate([y_true, y_pred]))
            if language == 'chinese':
                class_names = [f'类别_{i}' for i in unique_labels]
            else:
                class_names = [f'Class_{i}' for i in unique_labels]
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.chart_config['figsize_double'])
        
        if language == 'chinese':
            fig.suptitle(f'{model_name} - {task_name}混淆矩阵分析', 
                        fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        else:
            fig.suptitle(f'{model_name} - {task_name} Confusion Matrix Analysis', 
                        fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        
        # 1. 绝对数量混淆矩阵
        im1 = ax1.imshow(cm, interpolation='nearest', cmap='Blues')
        
        # 添加颜色条
        cbar1 = plt.colorbar(im1, ax=ax1)
        if language == 'chinese':
            cbar1.set_label('样本数量', fontweight='bold')
        else:
            cbar1.set_label('Sample Count', fontweight='bold')
        
        # 添加文本标注
        thresh = cm.max() / 2.
        for i in range(cm.shape[0]):
            for j in range(cm.shape[1]):
                ax1.text(j, i, format(cm[i, j], 'd'),
                        ha="center", va="center",
                        color="white" if cm[i, j] > thresh else "black",
                        fontsize=self.chart_config['legend_fontsize'], fontweight='bold')
        
        ax1.set_xticks(range(len(class_names)))
        ax1.set_yticks(range(len(class_names)))
        ax1.set_xticklabels(class_names, rotation=45, ha='right')
        ax1.set_yticklabels(class_names)
        
        if language == 'chinese':
            ax1.set_title('混淆矩阵 (绝对数量)', fontweight='bold')
            ax1.set_xlabel('预测类别', fontweight='bold')
            ax1.set_ylabel('真实类别', fontweight='bold')
        else:
            ax1.set_title('Confusion Matrix (Absolute)', fontweight='bold')
            ax1.set_xlabel('Predicted Class', fontweight='bold')
            ax1.set_ylabel('True Class', fontweight='bold')
        
        # 2. 归一化混淆矩阵
        cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        im2 = ax2.imshow(cm_normalized, interpolation='nearest', cmap='Blues')
        
        # 添加颜色条
        cbar2 = plt.colorbar(im2, ax=ax2)
        if language == 'chinese':
            cbar2.set_label('比例', fontweight='bold')
        else:
            cbar2.set_label('Proportion', fontweight='bold')
        
        # 添加文本标注
        thresh = cm_normalized.max() / 2.
        for i in range(cm_normalized.shape[0]):
            for j in range(cm_normalized.shape[1]):
                ax2.text(j, i, format(cm_normalized[i, j], '.2f'),
                        ha="center", va="center",
                        color="white" if cm_normalized[i, j] > thresh else "black",
                        fontsize=self.chart_config['legend_fontsize'], fontweight='bold')
        
        ax2.set_xticks(range(len(class_names)))
        ax2.set_yticks(range(len(class_names)))
        ax2.set_xticklabels(class_names, rotation=45, ha='right')
        ax2.set_yticklabels(class_names)
        
        if language == 'chinese':
            ax2.set_title('归一化混淆矩阵 (比例)', fontweight='bold')
            ax2.set_xlabel('预测类别', fontweight='bold')
            ax2.set_ylabel('真实类别', fontweight='bold')
        else:
            ax2.set_title('Normalized Confusion Matrix', fontweight='bold')
            ax2.set_xlabel('Predicted Class', fontweight='bold')
            ax2.set_ylabel('True Class', fontweight='bold')
        
        # 手动调整布局而不是使用tight_layout
        fig.subplots_adjust(left=0.1, right=0.9, top=0.9, bottom=0.1, wspace=0.3, hspace=0.3)

        # 保存图表
        filename = f'{model_name}_confusion_matrix_{language}.png'
        filepath = self.save_chart(fig, filename, language, 'classification_analysis')
        plt.close(fig)
        
        print(f"      ✅ 混淆矩阵图已保存: {filepath}")
        
        return self.create_chart_metadata({
            'type': 'confusion_matrix',
            'model': model_name,
            'language': language,
            'description': f'{task_name}混淆矩阵分析' if language == 'chinese' else f'{task_name} Confusion Matrix Analysis',
            'class_names': class_names
        })
    
    def create_roc_pr_curves_plot(self, y_true, y_scores, model_name='Model', task_name='分类预测', 
                                 class_names=None, language='chinese'):
        """创建ROC和PR曲线图（单独图表）"""
        print(f"   📊 生成{model_name} ROC/PR曲线图 ({language})...")
        
        # 处理多分类情况
        n_classes = len(np.unique(y_true))
        
        if n_classes == 2:
            # 二分类情况
            self._create_binary_roc_pr_plot(y_true, y_scores, model_name, task_name, language)
        else:
            # 多分类情况
            self._create_multiclass_roc_pr_plot(y_true, y_scores, model_name, task_name, class_names, language)
    
    def _create_binary_roc_pr_plot(self, y_true, y_scores, model_name, task_name, language):
        """创建二分类ROC和PR曲线"""
        # 计算ROC曲线
        fpr, tpr, _ = roc_curve(y_true, y_scores)
        roc_auc = auc(fpr, tpr)
        
        # 计算PR曲线
        precision, recall, _ = precision_recall_curve(y_true, y_scores)
        pr_auc = auc(recall, precision)
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.chart_config['figsize_double'])
        
        if language == 'chinese':
            fig.suptitle(f'{model_name} - {task_name} ROC与PR曲线分析', 
                        fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        else:
            fig.suptitle(f'{model_name} - {task_name} ROC and PR Curve Analysis', 
                        fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        
        # 1. ROC曲线
        ax1.plot(fpr, tpr, color=self.academic_colors['primary'], linewidth=3, 
                label=f'ROC曲线 (AUC = {roc_auc:.3f})' if language == 'chinese' else f'ROC Curve (AUC = {roc_auc:.3f})')
        ax1.plot([0, 1], [0, 1], color=self.academic_colors['warning'], linestyle='--', 
                linewidth=2, alpha=0.7, 
                label='随机分类器' if language == 'chinese' else 'Random Classifier')
        
        if language == 'chinese':
            ax1.set_xlabel('假正率 (FPR)', fontweight='bold')
            ax1.set_ylabel('真正率 (TPR)', fontweight='bold')
            ax1.set_title('ROC曲线', fontweight='bold')
        else:
            ax1.set_xlabel('False Positive Rate (FPR)', fontweight='bold')
            ax1.set_ylabel('True Positive Rate (TPR)', fontweight='bold')
            ax1.set_title('ROC Curve', fontweight='bold')
        
        ax1.legend(loc='lower right')
        ax1.grid(True, alpha=self.chart_config['grid_alpha'])
        ax1.set_xlim([0, 1])
        ax1.set_ylim([0, 1])
        
        # 2. PR曲线
        ax2.plot(recall, precision, color=self.academic_colors['secondary'], linewidth=3,
                label=f'PR曲线 (AUC = {pr_auc:.3f})' if language == 'chinese' else f'PR Curve (AUC = {pr_auc:.3f})')
        
        # 添加基线
        baseline = np.sum(y_true) / len(y_true)
        ax2.axhline(y=baseline, color=self.academic_colors['warning'], linestyle='--', 
                   linewidth=2, alpha=0.7,
                   label=f'基线 (正样本比例 = {baseline:.3f})' if language == 'chinese' else f'Baseline (Positive Rate = {baseline:.3f})')
        
        if language == 'chinese':
            ax2.set_xlabel('召回率 (Recall)', fontweight='bold')
            ax2.set_ylabel('精确率 (Precision)', fontweight='bold')
            ax2.set_title('精确率-召回率曲线', fontweight='bold')
        else:
            ax2.set_xlabel('Recall', fontweight='bold')
            ax2.set_ylabel('Precision', fontweight='bold')
            ax2.set_title('Precision-Recall Curve', fontweight='bold')
        
        ax2.legend(loc='lower left')
        ax2.grid(True, alpha=self.chart_config['grid_alpha'])
        ax2.set_xlim([0, 1])
        ax2.set_ylim([0, 1])
        
        # 手动调整布局
        fig.subplots_adjust(left=0.1, right=0.9, top=0.9, bottom=0.1, wspace=0.3)

        # 保存图表
        filename = f'{model_name}_roc_pr_curves_{language}.png'
        filepath = self.save_chart(fig, filename, language, 'classification_analysis')
        plt.close(fig)
        
        print(f"      ✅ ROC/PR曲线图已保存: {filepath}")
    
    def create_classification_performance_radar(self, y_true, y_pred, model_name='Model', 
                                              task_name='分类预测', language='chinese'):
        """创建分类性能雷达图（单独图表）"""
        print(f"   📊 生成{model_name}分类性能雷达图 ({language})...")
        
        # 计算性能指标
        accuracy = accuracy_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred, average='weighted')
        recall = recall_score(y_true, y_pred, average='weighted')
        f1 = f1_score(y_true, y_pred, average='weighted')
        
        # 计算特异性（针对二分类）
        if len(np.unique(y_true)) == 2:
            tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
            specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        else:
            specificity = 0.8  # 多分类情况下的估计值
        
        # 准备雷达图数据
        if language == 'chinese':
            metrics_labels = ['准确率', '精确率', '召回率', 'F1分数', '特异性']
        else:
            metrics_labels = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'Specificity']
        
        values = [accuracy, precision, recall, f1, specificity]
        
        # 设置角度
        angles = np.linspace(0, 2 * np.pi, len(metrics_labels), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        values += values[:1]  # 闭合数值
        
        # 创建图表
        fig, ax = plt.subplots(figsize=self.chart_config['figsize_single'], subplot_kw=dict(projection='polar'))
        
        if language == 'chinese':
            fig.suptitle(f'{model_name} - {task_name}性能雷达图', 
                        fontsize=self.chart_config['title_fontsize'], fontweight='bold', y=0.95)
        else:
            fig.suptitle(f'{model_name} - {task_name} Performance Radar Chart', 
                        fontsize=self.chart_config['title_fontsize'], fontweight='bold', y=0.95)
        
        # 绘制雷达图
        ax.plot(angles, values, 'o-', linewidth=3, color=self.academic_colors['primary'], 
               markersize=8, label=model_name)
        ax.fill(angles, values, alpha=0.25, color=self.academic_colors['primary'])
        
        # 设置标签和格式
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics_labels, fontsize=self.chart_config['legend_fontsize'])
        ax.set_ylim(0, 1)
        ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
        ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'], fontsize=self.chart_config['tick_fontsize'])
        ax.grid(True, alpha=self.chart_config['grid_alpha'])
        
        # 添加数值标注
        for angle, value, label in zip(angles[:-1], values[:-1], metrics_labels):
            ax.text(angle, value + 0.05, f'{value:.3f}', 
                   ha='center', va='center', fontsize=10, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
        
        # 图例
        ax.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0))

        # 手动调整布局
        fig.subplots_adjust(left=0.1, right=0.85, top=0.9, bottom=0.1)

        # 保存图表
        filename = f'{model_name}_performance_radar_{language}.png'
        filepath = self.save_chart(fig, filename, language, 'classification_analysis')
        plt.close(fig)
        
        print(f"      ✅ 分类性能雷达图已保存: {filepath}")
        
        return self.create_chart_metadata({
            'type': 'performance_radar',
            'model': model_name,
            'language': language,
            'description': f'{task_name}性能雷达图' if language == 'chinese' else f'{task_name} Performance Radar Chart',
            'metrics': {
                'accuracy': accuracy, 'precision': precision, 'recall': recall, 
                'f1_score': f1, 'specificity': specificity
            }
        })
    
    def create_classification_report_plot(self, y_true, y_pred, class_names=None, model_name='Model',
                                        task_name='分类预测', language='chinese'):
        """创建分类报告可视化（单独图表）"""
        print(f"   📊 生成{model_name}分类报告图 ({language})...")
        
        # 生成分类报告
        report = classification_report(y_true, y_pred, target_names=class_names, output_dict=True)
        
        if class_names is None:
            unique_labels = np.unique(np.concatenate([y_true, y_pred]))
            if language == 'chinese':
                class_names = [f'类别_{i}' for i in unique_labels]
            else:
                class_names = [f'Class_{i}' for i in unique_labels]
        
        # 准备数据
        metrics = ['precision', 'recall', 'f1-score']
        if language == 'chinese':
            metric_labels = ['精确率', '召回率', 'F1分数']
        else:
            metric_labels = ['Precision', 'Recall', 'F1-Score']
        
        # 提取每个类别的指标
        class_metrics = []
        for class_name in class_names:
            class_key = str(class_names.index(class_name)) if class_name.startswith('Class_') or class_name.startswith('类别_') else class_name
            if class_key in report:
                class_metrics.append([
                    report[class_key]['precision'],
                    report[class_key]['recall'],
                    report[class_key]['f1-score']
                ])
            else:
                class_metrics.append([0.8, 0.8, 0.8])  # 默认值
        
        class_metrics = np.array(class_metrics)
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.chart_config['figsize_double'])
        
        if language == 'chinese':
            fig.suptitle(f'{model_name} - {task_name}分类报告', 
                        fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        else:
            fig.suptitle(f'{model_name} - {task_name} Classification Report', 
                        fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        
        # 1. 热力图
        im = ax1.imshow(class_metrics.T, cmap='RdYlBu_r', aspect='auto', vmin=0, vmax=1)
        
        # 设置标签
        ax1.set_xticks(range(len(class_names)))
        ax1.set_yticks(range(len(metric_labels)))
        ax1.set_xticklabels(class_names, rotation=45, ha='right')
        ax1.set_yticklabels(metric_labels)
        
        # 添加数值标注
        for i in range(len(metric_labels)):
            for j in range(len(class_names)):
                text = ax1.text(j, i, f'{class_metrics[j, i]:.3f}',
                               ha="center", va="center", color="black", 
                               fontsize=self.chart_config['tick_fontsize'], fontweight='bold')
        
        if language == 'chinese':
            ax1.set_title('分类性能热力图', fontweight='bold')
            ax1.set_xlabel('类别', fontweight='bold')
            ax1.set_ylabel('性能指标', fontweight='bold')
        else:
            ax1.set_title('Classification Performance Heatmap', fontweight='bold')
            ax1.set_xlabel('Classes', fontweight='bold')
            ax1.set_ylabel('Performance Metrics', fontweight='bold')
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax1)
        if language == 'chinese':
            cbar.set_label('性能分数', fontweight='bold')
        else:
            cbar.set_label('Performance Score', fontweight='bold')
        
        # 2. 条形图对比
        x = np.arange(len(class_names))
        width = 0.25
        
        colors = [self.academic_colors['primary'], self.academic_colors['secondary'], self.academic_colors['success']]
        
        for i, (metric, label, color) in enumerate(zip(metrics, metric_labels, colors)):
            offset = (i - 1) * width
            bars = ax2.bar(x + offset, class_metrics[:, i], width, label=label, 
                          color=color, alpha=0.8, edgecolor='black', linewidth=0.5)
            
            # 添加数值标签
            for bar, value in zip(bars, class_metrics[:, i]):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{value:.3f}', ha='center', va='bottom', fontsize=9, fontweight='bold')
        
        ax2.set_xticks(x)
        ax2.set_xticklabels(class_names, rotation=45, ha='right')
        ax2.set_ylim(0, 1.1)
        
        if language == 'chinese':
            ax2.set_title('各类别性能对比', fontweight='bold')
            ax2.set_xlabel('类别', fontweight='bold')
            ax2.set_ylabel('性能分数', fontweight='bold')
        else:
            ax2.set_title('Per-Class Performance Comparison', fontweight='bold')
            ax2.set_xlabel('Classes', fontweight='bold')
            ax2.set_ylabel('Performance Score', fontweight='bold')
        
        ax2.legend()
        ax2.grid(True, alpha=self.chart_config['grid_alpha'])

        # 手动调整布局
        fig.subplots_adjust(left=0.1, right=0.9, top=0.9, bottom=0.15, wspace=0.3)

        # 保存图表
        filename = f'{model_name}_classification_report_{language}.png'
        filepath = self.save_chart(fig, filename, language, 'classification_analysis')
        plt.close(fig)
        
        print(f"      ✅ 分类报告图已保存: {filepath}")
        
        return self.create_chart_metadata({
            'type': 'classification_report',
            'model': model_name,
            'language': language,
            'description': f'{task_name}分类报告' if language == 'chinese' else f'{task_name} Classification Report',
            'class_names': class_names,
            'report': report
        })

def main():
    """测试函数"""
    print("🧪 测试分类任务可视化器...")
    
    # 初始化可视化器
    clf_viz = ClassificationVisualizer()
    
    # 生成测试数据
    np.random.seed(42)
    n_samples = 300
    n_classes = 3
    
    y_true = np.random.randint(0, n_classes, n_samples)
    y_pred = y_true.copy()
    # 添加一些错误预测
    error_indices = np.random.choice(n_samples, size=int(0.2 * n_samples), replace=False)
    y_pred[error_indices] = np.random.randint(0, n_classes, len(error_indices))
    
    y_scores = np.random.rand(n_samples)  # 用于ROC/PR曲线
    
    class_names = ['2轴车', '3轴车', '4轴车']
    
    # 测试各种可视化
    for language in ['chinese', 'english']:
        clf_viz.create_confusion_matrix_plot(y_true, y_pred, class_names, 'Random Forest', '轴型分类', language)
        clf_viz.create_classification_performance_radar(y_true, y_pred, 'Random Forest', '轴型分类', language)
        clf_viz.create_classification_report_plot(y_true, y_pred, class_names, 'Random Forest', '轴型分类', language)
    
    # 测试二分类ROC/PR曲线
    y_true_binary = np.random.randint(0, 2, n_samples)
    y_pred_binary = y_true_binary.copy()
    error_indices = np.random.choice(n_samples, size=int(0.15 * n_samples), replace=False)
    y_pred_binary[error_indices] = 1 - y_pred_binary[error_indices]
    
    for language in ['chinese', 'english']:
        clf_viz.create_roc_pr_curves_plot(y_true_binary, y_scores, 'Random Forest', '二分类', language=language)
    
    print("✅ 分类可视化器测试完成!")

if __name__ == "__main__":
    main()
