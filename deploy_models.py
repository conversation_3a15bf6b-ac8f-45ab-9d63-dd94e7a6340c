#!/usr/bin/env python3
"""
模型部署脚本
用于实际应用中的振动信号预测
"""

import pandas as pd
import numpy as np
import pickle
import joblib
import torch
import os
import sys
import logging
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 添加ml目录到路径
sys.path.append('./ml')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VibrationSignalPredictor:
    """振动信号预测器"""
    
    def __init__(self, models_dir: str = './models'):
        """
        初始化预测器
        
        Args:
            models_dir: 模型保存目录
        """
        self.models_dir = models_dir
        self.models = {}
        self.scalers = {}
        self.feature_extractors = {}
        
        # 创建模型目录
        os.makedirs(models_dir, exist_ok=True)
        
    def save_trained_model(self, model, model_name: str, task_type: str, 
                          scaler=None, feature_extractor=None):
        """保存训练好的模型"""
        logger.info(f"💾 保存模型: {model_name}")
        
        model_info = {
            'model': model,
            'task_type': task_type,
            'saved_time': datetime.now().isoformat(),
            'model_type': type(model).__name__
        }
        
        # 保存模型
        model_file = os.path.join(self.models_dir, f'{model_name}.pkl')
        with open(model_file, 'wb') as f:
            pickle.dump(model_info, f)
        
        # 保存标准化器
        if scaler:
            scaler_file = os.path.join(self.models_dir, f'{model_name}_scaler.pkl')
            with open(scaler_file, 'wb') as f:
                pickle.dump(scaler, f)
        
        # 保存特征提取器
        if feature_extractor:
            extractor_file = os.path.join(self.models_dir, f'{model_name}_extractor.pkl')
            with open(extractor_file, 'wb') as f:
                pickle.dump(feature_extractor, f)
        
        logger.info(f"✅ 模型已保存: {model_file}")
    
    def load_model(self, model_name: str) -> bool:
        """加载模型"""
        logger.info(f"📂 加载模型: {model_name}")
        
        try:
            # 加载模型
            model_file = os.path.join(self.models_dir, f'{model_name}.pkl')
            if os.path.exists(model_file):
                with open(model_file, 'rb') as f:
                    model_info = pickle.load(f)
                self.models[model_name] = model_info
                logger.info(f"✅ 模型已加载: {model_name}")
            else:
                logger.warning(f"❌ 模型文件不存在: {model_file}")
                return False
            
            # 加载标准化器
            scaler_file = os.path.join(self.models_dir, f'{model_name}_scaler.pkl')
            if os.path.exists(scaler_file):
                with open(scaler_file, 'rb') as f:
                    self.scalers[model_name] = pickle.load(f)
                logger.info(f"✅ 标准化器已加载: {model_name}")
            
            # 加载特征提取器
            extractor_file = os.path.join(self.models_dir, f'{model_name}_extractor.pkl')
            if os.path.exists(extractor_file):
                with open(extractor_file, 'rb') as f:
                    self.feature_extractors[model_name] = pickle.load(f)
                logger.info(f"✅ 特征提取器已加载: {model_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 模型加载失败: {model_name} - {str(e)}")
            return False
    
    def load_all_available_models(self):
        """加载所有可用模型"""
        logger.info("📂 加载所有可用模型...")
        
        if not os.path.exists(self.models_dir):
            logger.warning(f"模型目录不存在: {self.models_dir}")
            return
        
        model_files = [f for f in os.listdir(self.models_dir) if f.endswith('.pkl') and not f.endswith('_scaler.pkl') and not f.endswith('_extractor.pkl')]
        
        for model_file in model_files:
            model_name = model_file.replace('.pkl', '')
            self.load_model(model_name)
        
        logger.info(f"✅ 已加载 {len(self.models)} 个模型")
    
    def extract_features_from_signal(self, signal_data: pd.DataFrame) -> np.ndarray:
        """从振动信号提取特征"""
        logger.info("🔍 提取振动信号特征...")
        
        # 这里使用简化的特征提取，实际应用中应该使用与训练时相同的特征提取方法
        features = []
        
        # 获取传感器列
        sensor_columns = [col for col in signal_data.columns if 'sensor' in col.lower()]
        
        for col in sensor_columns:
            if col in signal_data.columns:
                signal_values = signal_data[col].dropna().values
                
                if len(signal_values) > 0:
                    # 基本统计特征
                    features.extend([
                        np.mean(signal_values),
                        np.std(signal_values),
                        np.var(signal_values),
                        np.sqrt(np.mean(signal_values**2)),  # RMS
                        np.max(np.abs(signal_values)),  # Peak
                        np.max(signal_values) - np.min(signal_values),  # Peak-to-peak
                    ])
                    
                    # 频域特征（简化）
                    fft_data = np.fft.fft(signal_values)
                    fft_magnitude = np.abs(fft_data[:len(fft_data)//2])
                    features.extend([
                        np.mean(fft_magnitude),
                        np.std(fft_magnitude),
                        np.max(fft_magnitude)
                    ])
        
        return np.array(features).reshape(1, -1)
    
    def predict_speed(self, signal_data: pd.DataFrame, model_name: str = None) -> Dict[str, Any]:
        """预测速度"""
        logger.info("🚗 预测车辆速度...")
        
        # 选择模型
        if model_name is None:
            # 自动选择最佳速度预测模型
            speed_models = [name for name in self.models.keys() if 'speed' in name.lower()]
            if not speed_models:
                logger.error("❌ 未找到速度预测模型")
                return {'error': '未找到速度预测模型'}
            model_name = speed_models[0]
        
        if model_name not in self.models:
            logger.error(f"❌ 模型不存在: {model_name}")
            return {'error': f'模型不存在: {model_name}'}
        
        try:
            # 提取特征
            features = self.extract_features_from_signal(signal_data)
            
            # 标准化特征
            if model_name in self.scalers:
                features = self.scalers[model_name].transform(features)
            
            # 预测
            model = self.models[model_name]['model']
            prediction = model.predict(features)[0]
            
            # 计算置信度（简化方法）
            confidence = 0.85  # 实际应用中应该基于模型的不确定性估计
            
            result = {
                'predicted_speed_kmh': float(prediction),
                'confidence': confidence,
                'model_used': model_name,
                'prediction_time': datetime.now().isoformat()
            }
            
            logger.info(f"✅ 速度预测完成: {prediction:.2f} km/h")
            return result
            
        except Exception as e:
            logger.error(f"❌ 速度预测失败: {str(e)}")
            return {'error': f'预测失败: {str(e)}'}
    
    def predict_load(self, signal_data: pd.DataFrame, model_name: str = None) -> Dict[str, Any]:
        """预测轴重"""
        logger.info("⚖️ 预测轴重...")
        
        # 选择模型
        if model_name is None:
            load_models = [name for name in self.models.keys() if 'load' in name.lower()]
            if not load_models:
                logger.error("❌ 未找到轴重预测模型")
                return {'error': '未找到轴重预测模型'}
            model_name = load_models[0]
        
        if model_name not in self.models:
            logger.error(f"❌ 模型不存在: {model_name}")
            return {'error': f'模型不存在: {model_name}'}
        
        try:
            # 提取特征
            features = self.extract_features_from_signal(signal_data)
            
            # 标准化特征
            if model_name in self.scalers:
                features = self.scalers[model_name].transform(features)
            
            # 预测
            model = self.models[model_name]['model']
            prediction = model.predict(features)[0]
            
            confidence = 0.80
            
            result = {
                'predicted_load_tons': float(prediction),
                'confidence': confidence,
                'model_used': model_name,
                'prediction_time': datetime.now().isoformat()
            }
            
            logger.info(f"✅ 轴重预测完成: {prediction:.2f} 吨")
            return result
            
        except Exception as e:
            logger.error(f"❌ 轴重预测失败: {str(e)}")
            return {'error': f'预测失败: {str(e)}'}
    
    def predict_vehicle_type(self, signal_data: pd.DataFrame, model_name: str = None) -> Dict[str, Any]:
        """预测车辆类型"""
        logger.info("🚛 预测车辆类型...")
        
        # 选择模型
        if model_name is None:
            type_models = [name for name in self.models.keys() if 'type' in name.lower() or 'class' in name.lower()]
            if not type_models:
                logger.error("❌ 未找到车辆类型预测模型")
                return {'error': '未找到车辆类型预测模型'}
            model_name = type_models[0]
        
        if model_name not in self.models:
            logger.error(f"❌ 模型不存在: {model_name}")
            return {'error': f'模型不存在: {model_name}'}
        
        try:
            # 提取特征
            features = self.extract_features_from_signal(signal_data)
            
            # 标准化特征
            if model_name in self.scalers:
                features = self.scalers[model_name].transform(features)
            
            # 预测
            model = self.models[model_name]['model']
            
            # 获取预测概率（如果支持）
            if hasattr(model, 'predict_proba'):
                probabilities = model.predict_proba(features)[0]
                prediction = model.predict(features)[0]
                confidence = float(np.max(probabilities))
            else:
                prediction = model.predict(features)[0]
                confidence = 0.75
            
            # 车辆类型映射
            vehicle_types = {
                0: 'passenger_car',
                1: 'light_truck', 
                2: 'heavy_truck',
                3: 'bus'
            }
            
            vehicle_type = vehicle_types.get(int(prediction), 'unknown')
            
            result = {
                'predicted_vehicle_type': vehicle_type,
                'confidence': confidence,
                'model_used': model_name,
                'prediction_time': datetime.now().isoformat()
            }
            
            logger.info(f"✅ 车辆类型预测完成: {vehicle_type}")
            return result
            
        except Exception as e:
            logger.error(f"❌ 车辆类型预测失败: {str(e)}")
            return {'error': f'预测失败: {str(e)}'}
    
    def predict_all(self, signal_data: pd.DataFrame) -> Dict[str, Any]:
        """执行所有预测任务"""
        logger.info("🔮 执行完整预测...")
        
        results = {
            'prediction_id': f"pred_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'input_data_shape': signal_data.shape,
            'predictions': {}
        }
        
        # 速度预测
        speed_result = self.predict_speed(signal_data)
        if 'error' not in speed_result:
            results['predictions']['speed'] = speed_result
        
        # 轴重预测
        load_result = self.predict_load(signal_data)
        if 'error' not in load_result:
            results['predictions']['load'] = load_result
        
        # 车辆类型预测
        type_result = self.predict_vehicle_type(signal_data)
        if 'error' not in type_result:
            results['predictions']['vehicle_type'] = type_result
        
        # 综合置信度
        confidences = []
        for pred in results['predictions'].values():
            if 'confidence' in pred:
                confidences.append(pred['confidence'])
        
        if confidences:
            results['overall_confidence'] = np.mean(confidences)
        
        logger.info(f"✅ 完整预测完成，成功预测 {len(results['predictions'])} 项")
        return results
    
    def batch_predict(self, signal_files: List[str], output_file: str = None) -> pd.DataFrame:
        """批量预测"""
        logger.info(f"📊 批量预测 {len(signal_files)} 个文件...")
        
        all_results = []
        
        for i, file_path in enumerate(signal_files, 1):
            logger.info(f"处理文件 {i}/{len(signal_files)}: {os.path.basename(file_path)}")
            
            try:
                # 读取信号数据
                signal_data = pd.read_csv(file_path)
                
                # 执行预测
                prediction_results = self.predict_all(signal_data)
                
                # 整理结果
                result_row = {
                    'file_name': os.path.basename(file_path),
                    'file_path': file_path,
                    'prediction_time': datetime.now().isoformat()
                }
                
                # 添加预测结果
                for task, pred in prediction_results['predictions'].items():
                    if task == 'speed':
                        result_row['predicted_speed_kmh'] = pred.get('predicted_speed_kmh', None)
                        result_row['speed_confidence'] = pred.get('confidence', None)
                    elif task == 'load':
                        result_row['predicted_load_tons'] = pred.get('predicted_load_tons', None)
                        result_row['load_confidence'] = pred.get('confidence', None)
                    elif task == 'vehicle_type':
                        result_row['predicted_vehicle_type'] = pred.get('predicted_vehicle_type', None)
                        result_row['type_confidence'] = pred.get('confidence', None)
                
                result_row['overall_confidence'] = prediction_results.get('overall_confidence', None)
                all_results.append(result_row)
                
            except Exception as e:
                logger.error(f"❌ 处理文件失败: {file_path} - {str(e)}")
                result_row = {
                    'file_name': os.path.basename(file_path),
                    'file_path': file_path,
                    'error': str(e),
                    'prediction_time': datetime.now().isoformat()
                }
                all_results.append(result_row)
        
        # 转换为DataFrame
        results_df = pd.DataFrame(all_results)
        
        # 保存结果
        if output_file is None:
            output_file = f"batch_predictions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        results_df.to_csv(output_file, index=False)
        logger.info(f"✅ 批量预测完成，结果已保存: {output_file}")
        
        return results_df

def create_demo_signal_data() -> pd.DataFrame:
    """创建演示用的振动信号数据"""
    logger.info("🎭 创建演示数据...")
    
    # 生成模拟振动信号
    np.random.seed(42)
    n_samples = 1000
    time_stamps = pd.date_range('2024-12-07 14:30:00', periods=n_samples, freq='10ms')
    
    # 模拟传感器数据
    data = {
        'timestamp': time_stamps,
        'sensor_1_x': np.random.normal(0, 0.1, n_samples) + 0.05 * np.sin(np.linspace(0, 10*np.pi, n_samples)),
        'sensor_1_y': np.random.normal(0, 0.08, n_samples) + 0.03 * np.cos(np.linspace(0, 8*np.pi, n_samples)),
        'sensor_1_z': np.random.normal(0, 0.12, n_samples) + 0.07 * np.sin(np.linspace(0, 6*np.pi, n_samples)),
        'sensor_2_x': np.random.normal(0, 0.09, n_samples) + 0.04 * np.sin(np.linspace(0, 12*np.pi, n_samples)),
        'sensor_2_y': np.random.normal(0, 0.11, n_samples) + 0.06 * np.cos(np.linspace(0, 9*np.pi, n_samples)),
        'sensor_2_z': np.random.normal(0, 0.10, n_samples) + 0.05 * np.sin(np.linspace(0, 7*np.pi, n_samples)),
        'temperature': np.random.normal(23.5, 1.0, n_samples),
        'humidity': np.random.normal(45.0, 5.0, n_samples)
    }
    
    df = pd.DataFrame(data)
    logger.info(f"✅ 演示数据已创建: {df.shape}")
    return df

def main():
    """主函数 - 演示模型部署和预测"""
    print("🚀 振动信号分析系统 - 模型部署演示")
    print("=" * 60)
    
    # 创建预测器
    predictor = VibrationSignalPredictor()
    
    # 加载所有可用模型
    predictor.load_all_available_models()
    
    if not predictor.models:
        print("❌ 未找到已训练的模型")
        print("💡 请先运行训练脚本生成模型")
        return
    
    print(f"✅ 已加载 {len(predictor.models)} 个模型")
    for model_name in predictor.models.keys():
        print(f"   📦 {model_name}")
    
    # 创建演示数据
    print("\n🎭 创建演示振动信号数据...")
    demo_signal = create_demo_signal_data()
    
    # 执行预测演示
    print("\n🔮 执行预测演示...")
    print("-" * 40)
    
    # 完整预测
    results = predictor.predict_all(demo_signal)
    
    print(f"\n📊 预测结果:")
    print(f"预测ID: {results['prediction_id']}")
    print(f"输入数据形状: {results['input_data_shape']}")
    print(f"总体置信度: {results.get('overall_confidence', 'N/A'):.2f}")
    
    for task, pred in results['predictions'].items():
        print(f"\n{task.upper()}预测:")
        for key, value in pred.items():
            if key != 'prediction_time':
                print(f"   {key}: {value}")
    
    # 保存预测结果
    import json
    results_file = f"prediction_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n✅ 预测演示完成!")
    print(f"📁 结果已保存: {results_file}")
    print("\n💡 使用说明:")
    print("   1. 将新的振动信号CSV文件放入指定目录")
    print("   2. 调用相应的预测方法")
    print("   3. 获取预测结果和置信度")

if __name__ == "__main__":
    main()
