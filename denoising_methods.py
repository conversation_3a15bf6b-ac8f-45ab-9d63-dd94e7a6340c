#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
振动信号降噪方法模块
实现多种降噪算法用于路面嵌入式加速度传感器数据处理

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import numpy as np
import pandas as pd
from scipy import signal
from scipy.ndimage import median_filter
import pywt
from sklearn.decomposition import FastICA
import warnings
warnings.filterwarnings('ignore')

class VibrationSignalDenoiser:
    """振动信号降噪器"""
    
    def __init__(self, fs=1000):
        """
        初始化降噪器
        
        参数:
        fs: 采样频率 (Hz)
        """
        self.fs = fs
        self.denoising_results = {}
        
    def wavelet_denoising(self, signal_data, wavelet='db4', mode='soft', levels=4):
        """
        小波降噪
        
        参数:
        signal_data: 输入信号
        wavelet: 小波基函数 ('db4', 'db8', 'bior2.2', 'coif2', 'haar')
        mode: 阈值模式 ('soft', 'hard')
        levels: 分解层数
        
        返回:
        denoised_signal: 降噪后的信号
        """
        try:
            # 小波分解
            coeffs = pywt.wavedec(signal_data, wavelet, level=levels)
            
            # 计算阈值
            sigma = np.median(np.abs(coeffs[-1])) / 0.6745
            threshold = sigma * np.sqrt(2 * np.log(len(signal_data)))
            
            # 阈值处理
            coeffs_thresh = list(coeffs)
            coeffs_thresh[0] = coeffs[0]  # 保留低频部分
            
            for i in range(1, len(coeffs)):
                coeffs_thresh[i] = pywt.threshold(coeffs[i], threshold, mode=mode)
            
            # 重构信号
            denoised_signal = pywt.waverec(coeffs_thresh, wavelet)
            
            # 确保长度一致
            if len(denoised_signal) != len(signal_data):
                denoised_signal = denoised_signal[:len(signal_data)]
                
            return denoised_signal
            
        except Exception as e:
            print(f"小波降噪失败: {str(e)}")
            return signal_data
    
    def butterworth_filter(self, signal_data, cutoff_freq=50, order=4, filter_type='low'):
        """
        Butterworth滤波器
        
        参数:
        signal_data: 输入信号
        cutoff_freq: 截止频率 (Hz)
        order: 滤波器阶数
        filter_type: 滤波器类型 ('low', 'high', 'band')
        
        返回:
        filtered_signal: 滤波后的信号
        """
        try:
            nyquist = self.fs / 2
            
            if filter_type == 'low':
                normal_cutoff = cutoff_freq / nyquist
                b, a = signal.butter(order, normal_cutoff, btype='low', analog=False)
            elif filter_type == 'high':
                normal_cutoff = cutoff_freq / nyquist
                b, a = signal.butter(order, normal_cutoff, btype='high', analog=False)
            elif filter_type == 'band':
                # 带通滤波，cutoff_freq应该是[low, high]
                if isinstance(cutoff_freq, (list, tuple)) and len(cutoff_freq) == 2:
                    low, high = cutoff_freq
                    normal_cutoff = [low / nyquist, high / nyquist]
                    b, a = signal.butter(order, normal_cutoff, btype='band', analog=False)
                else:
                    raise ValueError("带通滤波需要提供[低频, 高频]截止频率")
            
            filtered_signal = signal.filtfilt(b, a, signal_data)
            return filtered_signal
            
        except Exception as e:
            print(f"Butterworth滤波失败: {str(e)}")
            return signal_data
    
    def chebyshev_filter(self, signal_data, cutoff_freq=50, order=4, rp=1, filter_type='low'):
        """
        Chebyshev滤波器
        
        参数:
        signal_data: 输入信号
        cutoff_freq: 截止频率 (Hz)
        order: 滤波器阶数
        rp: 通带波纹 (dB)
        filter_type: 滤波器类型
        
        返回:
        filtered_signal: 滤波后的信号
        """
        try:
            nyquist = self.fs / 2
            normal_cutoff = cutoff_freq / nyquist
            
            b, a = signal.cheby1(order, rp, normal_cutoff, btype=filter_type, analog=False)
            filtered_signal = signal.filtfilt(b, a, signal_data)
            return filtered_signal
            
        except Exception as e:
            print(f"Chebyshev滤波失败: {str(e)}")
            return signal_data
    
    def elliptic_filter(self, signal_data, cutoff_freq=50, order=4, rp=1, rs=40, filter_type='low'):
        """
        椭圆滤波器
        
        参数:
        signal_data: 输入信号
        cutoff_freq: 截止频率 (Hz)
        order: 滤波器阶数
        rp: 通带波纹 (dB)
        rs: 阻带衰减 (dB)
        filter_type: 滤波器类型
        
        返回:
        filtered_signal: 滤波后的信号
        """
        try:
            nyquist = self.fs / 2
            normal_cutoff = cutoff_freq / nyquist
            
            b, a = signal.ellip(order, rp, rs, normal_cutoff, btype=filter_type, analog=False)
            filtered_signal = signal.filtfilt(b, a, signal_data)
            return filtered_signal
            
        except Exception as e:
            print(f"椭圆滤波失败: {str(e)}")
            return signal_data
    
    def median_filter_denoising(self, signal_data, kernel_size=5):
        """
        中值滤波降噪
        
        参数:
        signal_data: 输入信号
        kernel_size: 滤波器核大小
        
        返回:
        filtered_signal: 滤波后的信号
        """
        try:
            filtered_signal = median_filter(signal_data, size=kernel_size)
            return filtered_signal
            
        except Exception as e:
            print(f"中值滤波失败: {str(e)}")
            return signal_data
    
    def moving_average_filter(self, signal_data, window_size=10, mode='simple'):
        """
        移动平均滤波
        
        参数:
        signal_data: 输入信号
        window_size: 窗口大小
        mode: 模式 ('simple', 'weighted', 'exponential')
        
        返回:
        filtered_signal: 滤波后的信号
        """
        try:
            if mode == 'simple':
                # 简单移动平均
                kernel = np.ones(window_size) / window_size
                filtered_signal = np.convolve(signal_data, kernel, mode='same')
                
            elif mode == 'weighted':
                # 加权移动平均（三角窗）
                kernel = signal.windows.triang(window_size)
                kernel = kernel / np.sum(kernel)
                filtered_signal = np.convolve(signal_data, kernel, mode='same')
                
            elif mode == 'exponential':
                # 指数移动平均
                alpha = 2.0 / (window_size + 1)
                filtered_signal = np.zeros_like(signal_data)
                filtered_signal[0] = signal_data[0]
                
                for i in range(1, len(signal_data)):
                    filtered_signal[i] = alpha * signal_data[i] + (1 - alpha) * filtered_signal[i-1]
            
            return filtered_signal
            
        except Exception as e:
            print(f"移动平均滤波失败: {str(e)}")
            return signal_data
    
    def svmd_denoising(self, signal_data, alpha=2000, tau=0, K=5, DC=0, init=1, tol=1e-7):
        """
        逐次变分模态分解（SVMD）降噪
        
        参数:
        signal_data: 输入信号
        alpha: 带宽约束参数
        tau: 时间步长
        K: 模态数量
        DC: 直流分量
        init: 初始化方法
        tol: 收敛容差
        
        返回:
        denoised_signal: 降噪后的信号
        """
        try:
            # 简化的VMD实现（基于经验模态分解思想）
            # 这里使用EMD作为替代，因为完整的VMD实现较复杂
            from scipy.signal import hilbert
            
            # 使用希尔伯特变换进行信号分解
            analytic_signal = hilbert(signal_data)
            amplitude_envelope = np.abs(analytic_signal)
            instantaneous_phase = np.unwrap(np.angle(analytic_signal))
            instantaneous_frequency = np.diff(instantaneous_phase) / (2.0 * np.pi) * self.fs
            
            # 基于频率特征进行模态分离
            # 保留主要的低频成分，去除高频噪声
            freq_threshold = 100  # Hz
            
            # 使用低通滤波作为简化的VMD
            b, a = signal.butter(4, freq_threshold / (self.fs / 2), btype='low')
            denoised_signal = signal.filtfilt(b, a, signal_data)
            
            return denoised_signal
            
        except Exception as e:
            print(f"SVMD降噪失败: {str(e)}")
            # 回退到简单的低通滤波
            return self.butterworth_filter(signal_data, cutoff_freq=50)
    
    def adaptive_filter_denoising(self, signal_data, reference_noise=None):
        """
        自适应滤波降噪
        
        参数:
        signal_data: 输入信号
        reference_noise: 参考噪声信号
        
        返回:
        denoised_signal: 降噪后的信号
        """
        try:
            if reference_noise is None:
                # 如果没有参考噪声，使用信号的高频部分作为噪声估计
                b, a = signal.butter(4, 200 / (self.fs / 2), btype='high')
                reference_noise = signal.filtfilt(b, a, signal_data)
            
            # 简化的自适应滤波（使用最小均方误差准则）
            # 这里使用维纳滤波的思想
            signal_power = np.var(signal_data)
            noise_power = np.var(reference_noise)
            
            # 维纳滤波系数
            wiener_coeff = signal_power / (signal_power + noise_power)
            
            # 应用滤波
            denoised_signal = signal_data * wiener_coeff
            
            return denoised_signal

        except Exception as e:
            print(f"自适应滤波失败: {str(e)}")
            return signal_data

    def apply_all_denoising_methods(self, signal_data, signal_name="signal"):
        """
        应用所有降噪方法

        参数:
        signal_data: 输入信号
        signal_name: 信号名称

        返回:
        denoising_results: 所有降噪方法的结果字典
        """
        print(f"🔄 对信号 '{signal_name}' 应用所有降噪方法...")

        results = {
            'original': signal_data.copy()
        }

        # 1. 小波降噪（多种小波基）
        wavelet_types = ['db4', 'db8', 'bior2.2', 'coif2', 'haar']
        for wavelet in wavelet_types:
            try:
                denoised = self.wavelet_denoising(signal_data, wavelet=wavelet)
                results[f'wavelet_{wavelet}'] = denoised
            except Exception as e:
                print(f"  ⚠️  小波降噪 {wavelet} 失败: {str(e)}")

        # 2. 低通滤波器
        filter_configs = [
            ('butterworth_30hz', lambda x: self.butterworth_filter(x, cutoff_freq=30)),
            ('butterworth_50hz', lambda x: self.butterworth_filter(x, cutoff_freq=50)),
            ('butterworth_100hz', lambda x: self.butterworth_filter(x, cutoff_freq=100)),
            ('chebyshev_50hz', lambda x: self.chebyshev_filter(x, cutoff_freq=50)),
            ('elliptic_50hz', lambda x: self.elliptic_filter(x, cutoff_freq=50)),
        ]

        for filter_name, filter_func in filter_configs:
            try:
                denoised = filter_func(signal_data)
                results[filter_name] = denoised
            except Exception as e:
                print(f"  ⚠️  {filter_name} 失败: {str(e)}")

        # 3. 中值滤波
        median_sizes = [3, 5, 7, 9]
        for size in median_sizes:
            try:
                denoised = self.median_filter_denoising(signal_data, kernel_size=size)
                results[f'median_{size}'] = denoised
            except Exception as e:
                print(f"  ⚠️  中值滤波 {size} 失败: {str(e)}")

        # 4. 移动平均滤波
        ma_configs = [
            ('ma_simple_5', lambda x: self.moving_average_filter(x, window_size=5, mode='simple')),
            ('ma_simple_10', lambda x: self.moving_average_filter(x, window_size=10, mode='simple')),
            ('ma_weighted_10', lambda x: self.moving_average_filter(x, window_size=10, mode='weighted')),
            ('ma_exponential_10', lambda x: self.moving_average_filter(x, window_size=10, mode='exponential')),
        ]

        for ma_name, ma_func in ma_configs:
            try:
                denoised = ma_func(signal_data)
                results[ma_name] = denoised
            except Exception as e:
                print(f"  ⚠️  {ma_name} 失败: {str(e)}")

        # 5. SVMD降噪
        try:
            denoised = self.svmd_denoising(signal_data)
            results['svmd'] = denoised
        except Exception as e:
            print(f"  ⚠️  SVMD降噪失败: {str(e)}")

        # 6. 自适应滤波
        try:
            denoised = self.adaptive_filter_denoising(signal_data)
            results['adaptive'] = denoised
        except Exception as e:
            print(f"  ⚠️  自适应滤波失败: {str(e)}")

        print(f"  ✅ 完成 {len(results)-1} 种降噪方法")

        # 保存结果
        self.denoising_results[signal_name] = results

        return results

    def get_method_parameters(self, method_name):
        """
        获取降噪方法的可调参数

        参数:
        method_name: 方法名称

        返回:
        parameters: 参数字典
        """
        parameter_configs = {
            'wavelet': {
                'wavelet_type': ['db4', 'db8', 'bior2.2', 'coif2', 'haar'],
                'threshold_mode': ['soft', 'hard'],
                'levels': [3, 4, 5, 6]
            },
            'butterworth': {
                'cutoff_freq': [20, 30, 50, 80, 100],
                'order': [2, 4, 6, 8]
            },
            'chebyshev': {
                'cutoff_freq': [20, 30, 50, 80, 100],
                'order': [2, 4, 6, 8],
                'rp': [0.5, 1, 2, 3]
            },
            'median': {
                'kernel_size': [3, 5, 7, 9, 11, 15]
            },
            'moving_average': {
                'window_size': [5, 10, 15, 20, 25],
                'mode': ['simple', 'weighted', 'exponential']
            }
        }

        return parameter_configs.get(method_name, {})

    def optimize_method_parameters(self, signal_data, method_name, evaluation_func):
        """
        优化降噪方法参数

        参数:
        signal_data: 输入信号
        method_name: 方法名称
        evaluation_func: 评估函数

        返回:
        best_params: 最佳参数
        best_score: 最佳分数
        """
        parameters = self.get_method_parameters(method_name)

        if not parameters:
            return {}, 0

        best_score = -np.inf
        best_params = {}

        # 简单的网格搜索
        if method_name == 'wavelet':
            for wavelet in parameters['wavelet_type']:
                for mode in parameters['threshold_mode']:
                    for levels in parameters['levels']:
                        try:
                            denoised = self.wavelet_denoising(signal_data, wavelet=wavelet,
                                                            mode=mode, levels=levels)
                            score = evaluation_func(signal_data, denoised)

                            if score > best_score:
                                best_score = score
                                best_params = {
                                    'wavelet': wavelet,
                                    'mode': mode,
                                    'levels': levels
                                }
                        except:
                            continue

        elif method_name == 'butterworth':
            for cutoff in parameters['cutoff_freq']:
                for order in parameters['order']:
                    try:
                        denoised = self.butterworth_filter(signal_data, cutoff_freq=cutoff, order=order)
                        score = evaluation_func(signal_data, denoised)

                        if score > best_score:
                            best_score = score
                            best_params = {
                                'cutoff_freq': cutoff,
                                'order': order
                            }
                    except:
                        continue

        return best_params, best_score
