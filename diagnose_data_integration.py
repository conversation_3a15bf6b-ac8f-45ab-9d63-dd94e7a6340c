#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据整合问题诊断脚本
用于诊断振动信号分析系统的数据预处理流程问题

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import os
import json
import pandas as pd
from pathlib import Path
from typing import Dict, List
import glob

def diagnose_data_integration():
    """诊断数据整合问题"""
    print("🔍 开始诊断数据整合问题...")
    print("=" * 80)
    
    # 1. 检查数据目录结构
    print("\n📁 步骤1: 检查数据目录结构")
    print("-" * 50)
    
    data_dir = Path("data")
    if not data_dir.exists():
        print("❌ data目录不存在")
        return
    
    # 统计新格式CSV文件
    new_format_files = list(data_dir.glob("**/GW100001_*.csv"))
    print(f"📊 新格式CSV文件数量: {len(new_format_files)}")
    
    if len(new_format_files) > 0:
        print(f"   示例文件: {new_format_files[0].name}")
        
        # 检查文件内容结构
        try:
            sample_df = pd.read_csv(new_format_files[0])
            print(f"   文件列数: {sample_df.shape[1]}")
            print(f"   文件行数: {sample_df.shape[0]}")
            print(f"   列名示例: {list(sample_df.columns[:5])}")
        except Exception as e:
            print(f"   ❌ 读取示例文件失败: {str(e)}")
    
    # 2. 检查预处理状态
    print("\n🔄 步骤2: 检查数据预处理状态")
    print("-" * 50)
    
    # 检查预处理目录
    preprocessed_dirs = [
        "data_preprocessed",
        "data_legacy_preprocessed", 
        "preprocessed_data",
        "legacy_preprocessed_data"
    ]
    
    for dir_name in preprocessed_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"✅ 找到预处理目录: {dir_name}")
            
            # 检查data_info.json
            info_file = dir_path / "data_info.json"
            if info_file.exists():
                try:
                    with open(info_file, 'r', encoding='utf-8') as f:
                        info = json.load(f)
                    print(f"   📄 data_info.json内容:")
                    print(f"      - 数据格式: {info.get('data_format', 'N/A')}")
                    print(f"      - 处理文件数: {info.get('processed_files', 'N/A')}")
                    print(f"      - 总文件数: {info.get('total_files', 'N/A')}")
                    print(f"      - 成功率: {info.get('success_rate', 'N/A')}")
                except Exception as e:
                    print(f"   ❌ 读取data_info.json失败: {str(e)}")
            else:
                print(f"   ⚠️  未找到data_info.json")
                
            # 统计预处理后的文件数量
            csv_files = list(dir_path.glob("**/*.csv"))
            print(f"   📊 预处理后CSV文件数: {len(csv_files)}")
        else:
            print(f"❌ 预处理目录不存在: {dir_name}")
    
    # 3. 检查特征文件状态
    print("\n📊 步骤3: 检查特征文件状态")
    print("-" * 50)
    
    feature_files = [
        "combined_features.csv",
        "./ml/combined_features_clean.csv",
        "./analysis_results/combined_features.csv"
    ]
    
    for file_path in feature_files:
        if os.path.exists(file_path):
            try:
                df = pd.read_csv(file_path)
                print(f"✅ 特征文件: {file_path}")
                print(f"   数据形状: {df.shape}")
                print(f"   列名示例: {list(df.columns[:10])}")
                
                # 检查目标变量
                target_cols = ['speed_kmh', 'load_tons', 'axle_type']
                available_targets = [col for col in target_cols if col in df.columns]
                print(f"   可用目标变量: {available_targets}")
                
                # 检查数据分布
                if 'speed_kmh' in df.columns:
                    speed_stats = df['speed_kmh'].describe()
                    print(f"   速度分布: {speed_stats['min']:.1f} - {speed_stats['max']:.1f} km/h")
                
                if 'load_tons' in df.columns:
                    load_stats = df['load_tons'].describe()
                    print(f"   载重分布: {load_stats['min']:.1f} - {load_stats['max']:.1f} 吨")
                    
            except Exception as e:
                print(f"❌ 读取特征文件失败 {file_path}: {str(e)}")
        else:
            print(f"❌ 特征文件不存在: {file_path}")
    
    # 4. 检查数据格式适配器状态
    print("\n🔧 步骤4: 检查数据格式适配器")
    print("-" * 50)
    
    try:
        from data_format_adapter import DataFormatAdapter
        
        adapter = DataFormatAdapter()
        format_type = adapter.detect_data_format("data")
        print(f"📊 检测到的数据格式: {format_type}")
        
        # 测试兼容性检查
        compatible_dir = adapter.get_compatible_data_dir("data")
        if compatible_dir:
            print(f"✅ 兼容数据目录: {compatible_dir}")
        else:
            print(f"❌ 无法获取兼容数据目录")
            
    except ImportError as e:
        print(f"❌ 数据格式适配器导入失败: {str(e)}")
    except Exception as e:
        print(f"❌ 数据格式适配器测试失败: {str(e)}")
    
    # 5. 生成诊断报告
    print("\n📋 步骤5: 生成诊断报告")
    print("-" * 50)
    
    report = {
        "new_format_files_count": len(new_format_files),
        "current_features_count": 0,
        "preprocessing_status": {},
        "recommendations": []
    }
    
    # 统计当前特征数量
    if os.path.exists("combined_features.csv"):
        try:
            df = pd.read_csv("combined_features.csv")
            report["current_features_count"] = len(df)
        except:
            pass
    
    # 生成建议
    if len(new_format_files) > 0 and report["current_features_count"] < len(new_format_files) * 0.8:
        report["recommendations"].append("需要重新处理新格式数据以增加样本数量")
    
    if not any(os.path.exists(d) for d in preprocessed_dirs):
        report["recommendations"].append("需要运行数据预处理流程")
    
    # 保存诊断报告
    with open("data_integration_diagnosis.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 诊断完成，报告已保存到: data_integration_diagnosis.json")
    print(f"\n📊 诊断摘要:")
    print(f"   新格式文件数: {report['new_format_files_count']}")
    print(f"   当前特征数: {report['current_features_count']}")
    print(f"   建议操作: {len(report['recommendations'])} 项")
    
    for i, rec in enumerate(report['recommendations'], 1):
        print(f"   {i}. {rec}")
    
    return report

if __name__ == "__main__":
    diagnose_data_integration()
