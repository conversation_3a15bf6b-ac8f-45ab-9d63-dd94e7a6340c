#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习结果验证程序
检查已完成的机器学习训练结果

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import os
import json
import glob
import pandas as pd
from pathlib import Path

def check_ml_results():
    """检查机器学习结果"""
    print("🔍 检查机器学习训练结果...")
    print("=" * 60)
    
    results = {
        'speed_prediction': {'found': False, 'performance': {}},
        'weight_prediction': {'found': False, 'performance': {}},
        'axle_classification': {'found': False, 'performance': {}},
        'files_found': []
    }
    
    # 1. 检查结果文件
    result_patterns = [
        '*results*.json',
        '*performance*.json',
        '*optimization*.json',
        '*ml_results*.json',
        '*speed*.json',
        '*weight*.json',
        '*axle*.json'
    ]
    
    found_files = []
    for pattern in result_patterns:
        files = glob.glob(pattern)
        found_files.extend(files)
    
    results['files_found'] = found_files
    
    print(f"📁 找到结果文件: {len(found_files)} 个")
    for file in found_files:
        print(f"   - {file}")
    
    # 2. 分析结果文件内容
    for file_path in found_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"\n📊 分析文件: {file_path}")
            
            # 检查速度预测结果
            if any(keyword in file_path.lower() for keyword in ['speed', 'velocity']) or \
               any(keyword in str(data).lower() for keyword in ['speed', 'velocity']):
                results['speed_prediction']['found'] = True
                if isinstance(data, dict):
                    for key, value in data.items():
                        if isinstance(value, dict) and 'r2' in str(value).lower():
                            results['speed_prediction']['performance'][key] = value
                print("   ✅ 发现速度预测结果")
            
            # 检查轴重预测结果
            if any(keyword in file_path.lower() for keyword in ['weight', 'load', 'axle_weight']) or \
               any(keyword in str(data).lower() for keyword in ['weight', 'load']):
                results['weight_prediction']['found'] = True
                if isinstance(data, dict):
                    for key, value in data.items():
                        if isinstance(value, dict) and 'r2' in str(value).lower():
                            results['weight_prediction']['performance'][key] = value
                print("   ✅ 发现轴重预测结果")
            
            # 检查轴型分类结果
            if any(keyword in file_path.lower() for keyword in ['axle', 'classification', 'class']) or \
               any(keyword in str(data).lower() for keyword in ['accuracy', 'classification']):
                results['axle_classification']['found'] = True
                if isinstance(data, dict):
                    for key, value in data.items():
                        if isinstance(value, dict) and 'accuracy' in str(value).lower():
                            results['axle_classification']['performance'][key] = value
                print("   ✅ 发现轴型分类结果")
            
        except Exception as e:
            print(f"   ❌ 文件读取失败: {str(e)}")
    
    # 3. 检查预测输出文件
    print(f"\n📤 检查预测输出文件...")
    prediction_patterns = [
        '*predictions*.csv',
        '*speed_pred*.csv',
        '*weight_pred*.csv',
        '*axle_pred*.csv'
    ]
    
    prediction_files = []
    for pattern in prediction_patterns:
        files = glob.glob(pattern)
        prediction_files.extend(files)
    
    print(f"📁 找到预测文件: {len(prediction_files)} 个")
    for file in prediction_files:
        print(f"   - {file}")
    
    # 4. 检查可视化文件
    print(f"\n📊 检查可视化文件...")
    viz_patterns = [
        '*scatter*.png',
        '*performance*.png',
        '*prediction*.png',
        '*comparison*.png'
    ]
    
    viz_files = []
    for pattern in viz_patterns:
        files = glob.glob(pattern)
        viz_files.extend(files)
    
    print(f"📁 找到可视化文件: {len(viz_files)} 个")
    for file in viz_files:
        print(f"   - {file}")
    
    # 5. 检查特征数据
    print(f"\n📊 检查特征数据...")
    if os.path.exists('combined_features.csv'):
        try:
            df = pd.read_csv('combined_features.csv')
            print(f"   ✅ 特征数据: {df.shape[0]} 个样本, {df.shape[1]} 个特征")
            
            # 检查目标变量
            speed_cols = [col for col in df.columns if 'speed' in col.lower()]
            weight_cols = [col for col in df.columns if any(w in col.lower() for w in ['weight', 'load', 'tons'])]
            axle_cols = [col for col in df.columns if 'axle' in col.lower()]
            
            print(f"   📊 速度相关列: {len(speed_cols)} 个")
            print(f"   📊 重量相关列: {len(weight_cols)} 个")
            print(f"   📊 轴型相关列: {len(axle_cols)} 个")
            
        except Exception as e:
            print(f"   ❌ 特征数据读取失败: {str(e)}")
    else:
        print("   ❌ 未找到特征数据文件")
    
    # 6. 生成验证报告
    print(f"\n📋 验证结果总结:")
    print("=" * 60)
    
    print(f"🚄 速度预测: {'✅ 已实现' if results['speed_prediction']['found'] else '❌ 未找到'}")
    if results['speed_prediction']['performance']:
        for model, perf in results['speed_prediction']['performance'].items():
            print(f"   - {model}: {perf}")
    
    print(f"⚖️  轴重预测: {'✅ 已实现' if results['weight_prediction']['found'] else '❌ 未找到'}")
    if results['weight_prediction']['performance']:
        for model, perf in results['weight_prediction']['performance'].items():
            print(f"   - {model}: {perf}")
    
    print(f"🚗 轴型分类: {'✅ 已实现' if results['axle_classification']['found'] else '❌ 未找到'}")
    if results['axle_classification']['performance']:
        for model, perf in results['axle_classification']['performance'].items():
            print(f"   - {model}: {perf}")
    
    print(f"\n📁 文件统计:")
    print(f"   结果文件: {len(found_files)} 个")
    print(f"   预测文件: {len(prediction_files)} 个")
    print(f"   可视化文件: {len(viz_files)} 个")
    
    # 保存验证结果
    with open('ml_verification_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n✅ 验证结果已保存: ml_verification_results.json")
    
    return results

def check_hyperparameter_optimization():
    """检查超参数优化结果"""
    print("\n🔧 检查超参数优化结果...")
    print("=" * 60)
    
    # 查找优化结果文件
    optimization_files = glob.glob('*optimization*.json') + glob.glob('*hyperparameter*.json')
    
    if optimization_files:
        print(f"📁 找到优化文件: {len(optimization_files)} 个")
        
        for file_path in optimization_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"\n📊 优化文件: {file_path}")
                
                if isinstance(data, dict):
                    for task, results in data.items():
                        if isinstance(results, dict):
                            print(f"   📈 任务: {task}")
                            
                            if 'best_models' in results:
                                best_models = results['best_models']
                                for model_name, model_info in best_models.items():
                                    if isinstance(model_info, dict) and 'score' in model_info:
                                        score = model_info['score']
                                        print(f"      🏆 {model_name}: {score:.4f}")
                            
                            elif 'models' in results:
                                models = results['models']
                                for model_name, model_info in models.items():
                                    if isinstance(model_info, dict) and 'best_score' in model_info:
                                        score = model_info['best_score']
                                        print(f"      🏆 {model_name}: {score:.4f}")
                
            except Exception as e:
                print(f"   ❌ 文件读取失败: {str(e)}")
    else:
        print("❌ 未找到超参数优化结果文件")

if __name__ == "__main__":
    print("🚀 开始机器学习结果验证")
    print("=" * 80)
    
    # 检查ML结果
    ml_results = check_ml_results()
    
    # 检查超参数优化
    check_hyperparameter_optimization()
    
    print("\n" + "=" * 80)
    print("✅ 验证完成")
