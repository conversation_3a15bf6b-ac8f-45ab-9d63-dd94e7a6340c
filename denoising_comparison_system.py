#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
振动信号降噪对比分析系统
集成多种降噪方法的对比分析功能

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import os
import numpy as np
import pandas as pd
import time
from denoising_methods import VibrationSignalDenoiser
from denoising_evaluator import DenoisingEvaluator
from denoising_visualizer import DenoisingVisualizer
import warnings
warnings.filterwarnings('ignore')

class DenoisingComparisonSystem:
    """降噪对比分析系统"""
    
    def __init__(self, fs=1000, output_dir='denoising_analysis_results'):
        """
        初始化降噪对比分析系统
        
        参数:
        fs: 采样频率 (Hz)
        output_dir: 输出目录
        """
        self.fs = fs
        self.output_dir = output_dir
        
        # 初始化各个组件
        self.denoiser = VibrationSignalDenoiser(fs=fs)
        self.evaluator = DenoisingEvaluator(fs=fs)
        self.visualizer = DenoisingVisualizer(output_dir=os.path.join(output_dir, 'visualizations'))
        
        # 结果存储
        self.analysis_results = {}
        self.best_methods = {}
        self.recommendations = {}
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        print("🔧 振动信号降噪对比分析系统已初始化")
        print(f"   采样频率: {fs} Hz")
        print(f"   输出目录: {output_dir}")
    
    def analyze_single_signal(self, signal_data, signal_name="signal", 
                            generate_visualizations=True, save_results=True):
        """
        分析单个信号的降噪效果
        
        参数:
        signal_data: 输入信号数据
        signal_name: 信号名称
        generate_visualizations: 是否生成可视化
        save_results: 是否保存结果
        
        返回:
        analysis_result: 分析结果
        """
        print(f"\n🔍 开始分析信号: {signal_name}")
        print("=" * 60)
        
        start_time = time.time()
        
        # 1. 应用所有降噪方法
        print("📊 步骤1: 应用所有降噪方法")
        denoising_results = self.denoiser.apply_all_denoising_methods(signal_data, signal_name)
        
        # 2. 评估降噪效果
        print("\n📈 步骤2: 评估降噪效果")
        evaluation_summary = self.evaluator.evaluate_all_methods(denoising_results)
        
        # 3. 选择最佳方法
        print("\n🏆 步骤3: 选择最佳降噪方法")
        best_method = self.evaluator.get_best_method(evaluation_summary)
        
        if best_method:
            print(f"   最佳方法: {best_method['method_name']}")
            print(f"   推荐理由: {best_method['recommendation_reason']}")
        
        # 4. 生成可视化
        if generate_visualizations:
            print("\n🎨 步骤4: 生成可视化图表")
            # 只选择最好的几种方法进行可视化
            top_methods = dict(sorted(evaluation_summary.items(), 
                                    key=lambda x: x[1]['composite_score'], reverse=True)[:5])
            top_denoising_results = {k: denoising_results[k] for k in top_methods.keys() 
                                   if k in denoising_results}
            
            self.visualizer.generate_comprehensive_visualization_report(
                signal_data, top_denoising_results, evaluation_summary, 
                self.fs, signal_name
            )
        
        # 5. 生成分析报告
        print("\n📝 步骤5: 生成分析报告")
        report = self.evaluator.generate_evaluation_report(
            evaluation_summary, 
            save_path=os.path.join(self.output_dir, f'{signal_name}_denoising_report.md')
        )
        
        # 6. 保存结果
        if save_results:
            self._save_analysis_results(signal_name, denoising_results, evaluation_summary, best_method)
        
        analysis_time = time.time() - start_time
        
        analysis_result = {
            'signal_name': signal_name,
            'denoising_results': denoising_results,
            'evaluation_summary': evaluation_summary,
            'best_method': best_method,
            'analysis_time': analysis_time,
            'report': report
        }
        
        # 存储结果
        self.analysis_results[signal_name] = analysis_result
        if best_method:
            self.best_methods[signal_name] = best_method
        
        print(f"\n✅ 信号 {signal_name} 分析完成 (耗时: {analysis_time:.2f}秒)")
        print("=" * 60)
        
        return analysis_result
    
    def analyze_multiple_signals(self, signals_dict, generate_visualizations=True):
        """
        分析多个信号的降噪效果
        
        参数:
        signals_dict: 信号字典 {signal_name: signal_data}
        generate_visualizations: 是否生成可视化
        
        返回:
        batch_results: 批量分析结果
        """
        print(f"\n🚀 开始批量分析 {len(signals_dict)} 个信号")
        print("=" * 80)
        
        batch_results = {}
        
        for i, (signal_name, signal_data) in enumerate(signals_dict.items(), 1):
            print(f"\n📊 处理信号 {i}/{len(signals_dict)}: {signal_name}")
            
            try:
                result = self.analyze_single_signal(
                    signal_data, signal_name, 
                    generate_visualizations=generate_visualizations
                )
                batch_results[signal_name] = result
                
            except Exception as e:
                print(f"❌ 分析信号 {signal_name} 失败: {str(e)}")
                continue
        
        # 生成批量分析摘要
        self._generate_batch_summary(batch_results)
        
        print(f"\n✅ 批量分析完成，成功处理 {len(batch_results)} 个信号")
        
        return batch_results
    
    def get_automatic_recommendation(self, signal_data, signal_characteristics=None):
        """
        基于信号特征自动推荐最佳降噪方法
        
        参数:
        signal_data: 输入信号
        signal_characteristics: 信号特征描述
        
        返回:
        recommendation: 推荐结果
        """
        print("🤖 自动分析信号特征并推荐最佳降噪方法...")
        
        # 分析信号特征
        signal_features = self._analyze_signal_characteristics(signal_data)
        
        # 基于特征推荐方法
        if signal_features['noise_level'] > 0.5:
            # 高噪声信号
            recommended_methods = ['wavelet_db4', 'butterworth_30hz', 'median_5']
            reason = "信号噪声水平较高，推荐使用强降噪方法"
        elif signal_features['has_strong_peaks']:
            # 有强峰值的信号（车辆通过事件）
            recommended_methods = ['wavelet_db4', 'butterworth_50hz', 'svmd']
            reason = "检测到强峰值特征，推荐保持峰值特征的降噪方法"
        elif signal_features['frequency_content'] == 'low_frequency':
            # 低频信号
            recommended_methods = ['butterworth_100hz', 'ma_simple_10', 'chebyshev_50hz']
            reason = "信号主要包含低频成分，推荐低通滤波方法"
        else:
            # 一般信号
            recommended_methods = ['wavelet_db4', 'butterworth_50hz', 'ma_weighted_10']
            reason = "一般信号特征，推荐平衡性能的降噪方法"
        
        recommendation = {
            'signal_features': signal_features,
            'recommended_methods': recommended_methods,
            'reason': reason,
            'confidence': 0.8  # 推荐置信度
        }
        
        print(f"   推荐方法: {recommended_methods}")
        print(f"   推荐理由: {reason}")
        
        return recommendation
    
    def _analyze_signal_characteristics(self, signal_data):
        """
        分析信号特征
        
        参数:
        signal_data: 输入信号
        
        返回:
        features: 信号特征字典
        """
        features = {}
        
        # 噪声水平估计
        from scipy import signal as sp_signal
        b, a = sp_signal.butter(4, 200 / (self.fs / 2), btype='high')
        high_freq_component = sp_signal.filtfilt(b, a, signal_data)
        features['noise_level'] = np.std(high_freq_component) / np.std(signal_data)
        
        # 峰值检测
        peaks, _ = sp_signal.find_peaks(np.abs(signal_data), height=np.std(signal_data) * 3)
        features['has_strong_peaks'] = len(peaks) > 0
        features['peak_count'] = len(peaks)
        
        # 频率内容分析
        freqs, psd = sp_signal.welch(signal_data, fs=self.fs)
        low_freq_power = np.sum(psd[freqs <= 50])
        high_freq_power = np.sum(psd[freqs > 50])
        
        if low_freq_power > 2 * high_freq_power:
            features['frequency_content'] = 'low_frequency'
        elif high_freq_power > 2 * low_freq_power:
            features['frequency_content'] = 'high_frequency'
        else:
            features['frequency_content'] = 'broadband'
        
        # 信号平稳性
        # 简单的平稳性检测：比较前半段和后半段的统计特性
        mid_point = len(signal_data) // 2
        first_half_std = np.std(signal_data[:mid_point])
        second_half_std = np.std(signal_data[mid_point:])
        features['is_stationary'] = abs(first_half_std - second_half_std) / np.std(signal_data) < 0.2
        
        return features
    
    def _save_analysis_results(self, signal_name, denoising_results, evaluation_summary, best_method):
        """
        保存分析结果
        
        参数:
        signal_name: 信号名称
        denoising_results: 降噪结果
        evaluation_summary: 评估摘要
        best_method: 最佳方法
        """
        try:
            # 保存评估结果为CSV
            if evaluation_summary:
                eval_df = pd.DataFrame(evaluation_summary).T
                eval_df.to_csv(os.path.join(self.output_dir, f'{signal_name}_evaluation_results.csv'))
            
            # 保存最佳方法信息
            if best_method:
                import json
                with open(os.path.join(self.output_dir, f'{signal_name}_best_method.json'), 'w', encoding='utf-8') as f:
                    json.dump(best_method, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ 分析结果已保存到: {self.output_dir}")
            
        except Exception as e:
            print(f"   ⚠️  保存结果失败: {str(e)}")
    
    def _generate_batch_summary(self, batch_results):
        """
        生成批量分析摘要
        
        参数:
        batch_results: 批量分析结果
        """
        try:
            summary_lines = [
                "# 批量降噪分析摘要报告",
                "",
                f"## 分析概况",
                f"- 分析信号数量: {len(batch_results)}",
                f"- 分析时间: {sum(r['analysis_time'] for r in batch_results.values()):.2f} 秒",
                "",
                "## 最佳方法统计",
                ""
            ]
            
            # 统计最佳方法
            method_counts = {}
            for result in batch_results.values():
                if result['best_method']:
                    method_name = result['best_method']['method_name']
                    method_counts[method_name] = method_counts.get(method_name, 0) + 1
            
            for method, count in sorted(method_counts.items(), key=lambda x: x[1], reverse=True):
                summary_lines.append(f"- {method}: {count} 次")
            
            summary_lines.extend([
                "",
                "## 详细结果",
                ""
            ])
            
            for signal_name, result in batch_results.items():
                best_method = result['best_method']
                if best_method:
                    score = best_method['evaluation_results']['composite_score']
                    summary_lines.append(f"- **{signal_name}**: {best_method['method_name']} (评分: {score:.3f})")
                else:
                    summary_lines.append(f"- **{signal_name}**: 分析失败")
            
            # 保存摘要
            summary_content = "\n".join(summary_lines)
            with open(os.path.join(self.output_dir, 'batch_analysis_summary.md'), 'w', encoding='utf-8') as f:
                f.write(summary_content)
            
            print(f"   ✅ 批量分析摘要已保存")
            
        except Exception as e:
            print(f"   ⚠️  生成批量摘要失败: {str(e)}")
