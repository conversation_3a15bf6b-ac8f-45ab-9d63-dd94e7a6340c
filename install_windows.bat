@echo off
chcp 65001 > nul
echo.
echo ========================================================================
echo 深度学习增强振动信号分析系统 - Windows自动安装脚本
echo ========================================================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ 检测到Python
python --version

echo.
echo 📦 开始安装依赖包...
echo.

:: 升级pip
echo 📦 升级pip...
python -m pip install --upgrade pip

:: 安装基础包
echo.
echo 📦 安装基础数据处理包...
python -m pip install numpy>=1.21.0 pandas>=1.3.0 scipy>=1.7.0

:: 安装机器学习包
echo.
echo 🤖 安装机器学习包...
python -m pip install scikit-learn>=1.0.0 joblib>=1.1.0

:: 安装梯度提升包
echo.
echo 🚀 安装梯度提升包...
python -m pip install xgboost>=1.6.0 lightgbm>=3.3.0 optuna>=3.0.0

:: 安装可视化包
echo.
echo 📊 安装可视化包...
python -m pip install matplotlib>=3.5.0 seaborn>=0.11.0 plotly>=5.0.0

:: 安装工具包
echo.
echo 🔧 安装工具包...
python -m pip install chardet>=4.0.0 tqdm>=4.62.0 psutil>=5.8.0 openpyxl>=3.0.0

:: 安装信号处理包
echo.
echo 📡 安装信号处理包...
python -m pip install PyWavelets>=1.1.0 statsmodels>=0.13.0

:: 询问是否安装GPU版本PyTorch
echo.
set /p gpu_choice="是否安装GPU版本的PyTorch? (y/n, 默认n): "

if /i "%gpu_choice%"=="y" (
    echo.
    echo 🚀 安装PyTorch GPU版本 (CUDA 12.1)...
    python -m pip uninstall torch torchvision torchaudio -y
    python -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
) else (
    echo.
    echo 🧠 安装PyTorch CPU版本...
    python -m pip install torch>=2.0.0 torchvision>=0.15.0 torchaudio>=2.0.0
)

echo.
echo 🔍 验证安装...
python verify_installation.py

echo.
echo ========================================================================
echo 🎉 安装完成!
echo ========================================================================
echo.
echo 下一步操作:
echo   1. 检查验证报告: verification_report.txt
echo   2. 运行真实数据训练: python setup_real_data_training.py
echo.
pause
