#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一图表目录配置
验证所有可视化图表都保存到unified_charts目录中

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import sys
from pathlib import Path

def test_enhanced_academic_visualizations():
    """测试增强版学术可视化（统一目录）"""
    print("🧪 测试增强版学术可视化（统一目录）...")
    
    try:
        from visualization_generator_enhanced import EnhancedVisualizationGenerator
        
        # 初始化生成器（统一目录）
        generator = EnhancedVisualizationGenerator(
            output_dir='test_unified_charts',
            file_prefix='academic_'
        )
        
        # 生成所有图表
        generator.generate_all_visualizations()
        
        # 检查输出文件
        output_dir = Path('test_unified_charts')
        expected_files = [
            'academic_data_expansion_comparison.png',
            'academic_model_performance_comparison.png',
            'academic_optimization_results.png',
            'academic_data_distribution_analysis.png',
            'academic_feature_importance_analysis.png',
            'academic_confusion_matrix_analysis.png',
            'academic_roc_curves_multiclass.png',
            'academic_precision_recall_curves.png',
            'academic_precision_recall_summary.png'
        ]
        
        success_count = 0
        for file_name in expected_files:
            file_path = output_dir / file_name
            if file_path.exists():
                print(f"   ✅ {file_name} - 生成成功")
                success_count += 1
            else:
                print(f"   ❌ {file_name} - 生成失败")
        
        print(f"   📊 学术图表成功率: {success_count}/{len(expected_files)} ({success_count/len(expected_files)*100:.1f}%)")
        return success_count == len(expected_files)
        
    except Exception as e:
        print(f"   ❌ 增强版学术可视化测试失败: {str(e)}")
        return False

def test_technical_workflow_visualizations():
    """测试技术工作流可视化（统一目录）"""
    print("\n🧪 测试技术工作流可视化（统一目录）...")
    
    try:
        from technical_workflow_visualizer import TechnicalWorkflowVisualizer
        
        # 初始化生成器（统一目录）
        generator = TechnicalWorkflowVisualizer(
            output_base_dir='test_unified_charts',
            file_prefix='technical_'
        )
        
        # 生成所有图表
        generator.generate_all_technical_visualizations()
        
        # 检查输出文件
        output_dir = Path('test_unified_charts')
        expected_files = [
            'technical_system_overview_diagram.png',
            'technical_data_processing_pipeline.png',
            'technical_comprehensive_workflow.png',
            'technical_sample_vibration_signals.png',
            'technical_multi_sensor_comparison.png',
            'technical_time_domain_features.png',
            'technical_frequency_domain_features.png',
            'technical_time_frequency_features.png',
            'technical_signal_preprocessing_demo.png',
            'technical_filtering_comparison.png'
        ]
        
        success_count = 0
        for file_name in expected_files:
            file_path = output_dir / file_name
            if file_path.exists():
                print(f"   ✅ {file_name} - 生成成功")
                success_count += 1
            else:
                print(f"   ❌ {file_name} - 生成失败")
        
        print(f"   📊 技术图表成功率: {success_count}/{len(expected_files)} ({success_count/len(expected_files)*100:.1f}%)")
        return success_count == len(expected_files)
        
    except Exception as e:
        print(f"   ❌ 技术工作流可视化测试失败: {str(e)}")
        return False

def test_unified_directory_structure():
    """测试统一目录结构"""
    print("\n🧪 测试统一目录结构...")
    
    try:
        output_dir = Path('test_unified_charts')
        
        if not output_dir.exists():
            print(f"   ❌ 统一目录不存在: {output_dir}")
            return False
        
        # 检查所有文件
        all_files = list(output_dir.glob('*.png'))
        academic_files = [f for f in all_files if f.name.startswith('academic_')]
        technical_files = [f for f in all_files if f.name.startswith('technical_')]
        
        print(f"   📊 统一目录文件统计:")
        print(f"      - 总文件数: {len(all_files)}")
        print(f"      - 学术图表: {len(academic_files)} 个")
        print(f"      - 技术图表: {len(technical_files)} 个")
        
        # 检查文件名冲突
        file_names = [f.name for f in all_files]
        unique_names = set(file_names)
        
        if len(file_names) == len(unique_names):
            print(f"   ✅ 无文件名冲突")
        else:
            print(f"   ❌ 发现文件名冲突: {len(file_names) - len(unique_names)} 个")
            return False
        
        # 检查前缀分类
        prefix_stats = {}
        for file_name in file_names:
            if '_' in file_name:
                prefix = file_name.split('_')[0]
                prefix_stats[prefix] = prefix_stats.get(prefix, 0) + 1
        
        print(f"   📊 前缀分类统计:")
        for prefix, count in prefix_stats.items():
            print(f"      - {prefix}_: {count} 个文件")
        
        expected_total = 19  # 9 academic + 10 technical
        if len(all_files) >= expected_total:
            print(f"   ✅ 文件数量达标: {len(all_files)} >= {expected_total}")
            return True
        else:
            print(f"   ❌ 文件数量不足: {len(all_files)} < {expected_total}")
            return False
        
    except Exception as e:
        print(f"   ❌ 统一目录结构测试失败: {str(e)}")
        return False

def test_file_quality():
    """测试文件质量"""
    print("\n🧪 测试文件质量...")
    
    try:
        output_dir = Path('test_unified_charts')
        png_files = list(output_dir.glob('*.png'))
        
        if not png_files:
            print(f"   ❌ 未找到PNG文件")
            return False
        
        quality_checks = {
            'file_size_ok': 0,
            'file_readable': 0,
            'total_files': len(png_files)
        }
        
        for png_file in png_files:
            # 检查文件大小（应该大于10KB，小于10MB）
            file_size = png_file.stat().st_size
            if 10 * 1024 < file_size < 10 * 1024 * 1024:  # 10KB - 10MB
                quality_checks['file_size_ok'] += 1
            
            # 检查文件可读性
            try:
                with open(png_file, 'rb') as f:
                    header = f.read(8)
                    if header.startswith(b'\x89PNG\r\n\x1a\n'):  # PNG文件头
                        quality_checks['file_readable'] += 1
            except:
                pass
        
        print(f"   📊 文件质量统计:")
        print(f"      - 总文件数: {quality_checks['total_files']}")
        print(f"      - 文件大小正常: {quality_checks['file_size_ok']}/{quality_checks['total_files']}")
        print(f"      - 文件格式正确: {quality_checks['file_readable']}/{quality_checks['total_files']}")
        
        success_rate = (quality_checks['file_size_ok'] + quality_checks['file_readable']) / (2 * quality_checks['total_files'])
        
        if success_rate >= 0.9:  # 90%通过率
            print(f"   ✅ 文件质量良好: {success_rate*100:.1f}%")
            return True
        else:
            print(f"   ❌ 文件质量不佳: {success_rate*100:.1f}%")
            return False
        
    except Exception as e:
        print(f"   ❌ 文件质量测试失败: {str(e)}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    try:
        import shutil
        test_dir = Path('test_unified_charts')
        
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"   ✅ 已清理测试目录: {test_dir}")
        else:
            print(f"   ℹ️  测试目录不存在: {test_dir}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 清理测试文件失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始统一图表目录配置测试")
    print("=" * 80)
    
    # 运行所有测试
    tests = [
        ("增强版学术可视化", test_enhanced_academic_visualizations),
        ("技术工作流可视化", test_technical_workflow_visualizations),
        ("统一目录结构", test_unified_directory_structure),
        ("文件质量", test_file_quality)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结测试结果
    print("\n" + "=" * 80)
    print("🎯 统一目录配置测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！统一图表目录配置工作正常。")
        print("📁 所有图表现在都保存在unified_charts目录中，使用前缀区分类型。")
    elif passed >= total * 0.75:
        print("⚠️  大部分测试通过，系统基本可用，建议检查失败项目。")
    else:
        print("❌ 多个测试失败，需要修复问题后重新测试。")
    
    # 清理测试文件
    cleanup_test_files()
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
