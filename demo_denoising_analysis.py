#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
降噪分析演示脚本
展示多种降噪方法的对比分析功能

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import numpy as np
import matplotlib.pyplot as plt
import os

def create_demo_signals():
    """创建演示用的振动信号"""
    print("🔧 创建演示用的振动信号...")
    
    fs = 1000  # 采样频率
    t = np.arange(0, 5, 1/fs)  # 5秒信号
    
    signals = {}
    
    # 信号1: 车辆通过信号（低频主信号 + 中等噪声）
    vehicle_signal = 3 * np.sin(2 * np.pi * 8 * t) * np.exp(-((t-2.5)**2)/0.8)  # 车辆通过事件
    vehicle_signal += 0.5 * np.sin(2 * np.pi * 25 * t)  # 路面振动
    vehicle_noise = 0.6 * np.random.randn(len(t))  # 环境噪声
    signals['车辆通过_中等噪声'] = vehicle_signal + vehicle_noise
    
    # 信号2: 高频振动信号（强噪声）
    vibration_signal = 2 * np.sin(2 * np.pi * 60 * t) + 1.2 * np.sin(2 * np.pi * 120 * t)
    vibration_signal += 0.8 * np.sin(2 * np.pi * 15 * t)  # 低频成分
    vibration_noise = 1.2 * np.random.randn(len(t))  # 强噪声
    signals['高频振动_强噪声'] = vibration_signal + vibration_noise
    
    # 信号3: 多频率混合信号（脉冲噪声）
    mixed_signal = (1.5 * np.sin(2 * np.pi * 5 * t) + 
                   1.0 * np.sin(2 * np.pi * 30 * t) + 
                   0.8 * np.sin(2 * np.pi * 85 * t))
    
    # 添加脉冲噪声
    impulse_noise = np.zeros_like(t)
    impulse_indices = np.random.choice(len(t), size=int(0.02 * len(t)), replace=False)
    impulse_noise[impulse_indices] = 5 * np.random.randn(len(impulse_indices))
    
    gaussian_noise = 0.4 * np.random.randn(len(t))
    signals['多频混合_脉冲噪声'] = mixed_signal + impulse_noise + gaussian_noise
    
    print(f"   ✅ 创建了 {len(signals)} 个演示信号")
    return signals, fs

def run_denoising_demo():
    """运行降噪演示"""
    print("🚀 开始降噪方法对比分析演示")
    print("=" * 80)
    
    try:
        # 创建演示信号
        signals, fs = create_demo_signals()
        
        # 导入降噪对比系统
        from denoising_comparison_system import DenoisingComparisonSystem
        
        # 初始化系统
        system = DenoisingComparisonSystem(fs=fs, output_dir='demo_denoising_results')
        
        print(f"\n📊 对 {len(signals)} 个信号进行降噪分析...")
        
        # 批量分析
        batch_results = system.analyze_multiple_signals(signals, generate_visualizations=True)
        
        # 显示结果摘要
        print(f"\n📈 分析结果摘要:")
        print("-" * 60)
        
        for signal_name, result in batch_results.items():
            if result.get('best_method'):
                best_method = result['best_method']
                method_name = best_method['method_name']
                score = best_method['evaluation_results']['composite_score']
                snr = best_method['evaluation_results']['snr_improvement']
                fidelity = best_method['evaluation_results']['signal_fidelity']
                
                print(f"🔊 {signal_name}:")
                print(f"   最佳方法: {method_name}")
                print(f"   综合评分: {score:.3f}")
                print(f"   SNR改善: {snr:.2f} dB")
                print(f"   信号保真度: {fidelity:.3f}")
                print(f"   推荐理由: {best_method['recommendation_reason']}")
                print()
        
        # 生成总体推荐
        if hasattr(system, 'best_methods') and system.best_methods:
            print(f"🏆 总体推荐:")
            method_counts = {}
            for signal_name, best_method in system.best_methods.items():
                method_name = best_method['method_name']
                method_counts[method_name] = method_counts.get(method_name, 0) + 1
            
            sorted_methods = sorted(method_counts.items(), key=lambda x: x[1], reverse=True)
            for i, (method, count) in enumerate(sorted_methods[:3]):
                print(f"   {i+1}. {method}: {count}/{len(signals)} 个信号的最佳选择")
        
        print(f"\n📁 结果文件:")
        print(f"   📊 可视化图表: demo_denoising_results/visualizations/")
        print(f"   📋 分析报告: demo_denoising_results/*_denoising_report.md")
        print(f"   📈 批量摘要: demo_denoising_results/batch_analysis_summary.md")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入降噪模块失败: {str(e)}")
        print("💡 请确保降噪相关模块文件存在:")
        print("   - denoising_methods.py")
        print("   - denoising_evaluator.py") 
        print("   - denoising_visualizer.py")
        print("   - denoising_comparison_system.py")
        return False
        
    except Exception as e:
        print(f"❌ 降噪演示失败: {str(e)}")
        return False

def run_quick_demo():
    """运行快速演示（不生成可视化）"""
    print("⚡ 快速降噪演示（仅显示结果）")
    print("=" * 50)
    
    try:
        from denoising_methods import VibrationSignalDenoiser
        from denoising_evaluator import DenoisingEvaluator
        
        # 创建简单测试信号
        fs = 1000
        t = np.arange(0, 2, 1/fs)
        clean_signal = 2 * np.sin(2 * np.pi * 10 * t) * np.exp(-((t-1)**2)/0.3)
        noisy_signal = clean_signal + 0.5 * np.random.randn(len(t))
        
        # 初始化组件
        denoiser = VibrationSignalDenoiser(fs=fs)
        evaluator = DenoisingEvaluator(fs=fs)
        
        print("🔊 测试信号: 车辆通过事件 + 高斯噪声")
        print(f"   信号长度: {len(noisy_signal)} 点 ({len(noisy_signal)/fs:.1f} 秒)")
        print(f"   采样频率: {fs} Hz")
        
        # 应用几种主要降噪方法
        methods_to_test = [
            ('小波降噪_db4', lambda x: denoiser.wavelet_denoising(x, wavelet='db4')),
            ('Butterworth_50Hz', lambda x: denoiser.butterworth_filter(x, cutoff_freq=50)),
            ('中值滤波_5点', lambda x: denoiser.median_filter_denoising(x, kernel_size=5)),
            ('移动平均_10点', lambda x: denoiser.moving_average_filter(x, window_size=10)),
        ]
        
        print(f"\n📊 测试 {len(methods_to_test)} 种降噪方法:")
        print("-" * 50)
        
        results = []
        for method_name, method_func in methods_to_test:
            try:
                # 应用降噪
                denoised = method_func(noisy_signal)
                
                # 评估效果
                evaluation = evaluator.comprehensive_evaluation(
                    noisy_signal, denoised, method_name, include_efficiency=False
                )
                
                results.append((method_name, evaluation))
                
                print(f"✅ {method_name}:")
                print(f"   综合评分: {evaluation['composite_score']:.3f}")
                print(f"   SNR改善: {evaluation['snr_improvement']:.2f} dB")
                print(f"   信号保真度: {evaluation['signal_fidelity']:.3f}")
                
            except Exception as e:
                print(f"❌ {method_name}: 失败 ({str(e)})")
        
        # 找出最佳方法
        if results:
            best_method = max(results, key=lambda x: x[1]['composite_score'])
            print(f"\n🏆 最佳方法: {best_method[0]}")
            print(f"   综合评分: {best_method[1]['composite_score']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 快速演示失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎯 振动信号降噪方法对比分析演示")
    print("=" * 80)
    
    # 检查是否有完整的降噪模块
    try:
        from denoising_comparison_system import DenoisingComparisonSystem
        has_full_system = True
    except ImportError:
        has_full_system = False
    
    if has_full_system:
        print("🔍 检测到完整的降噪分析系统")
        choice = input("选择演示模式 (1: 完整演示, 2: 快速演示): ").strip()
        
        if choice == "1":
            success = run_denoising_demo()
        else:
            success = run_quick_demo()
    else:
        print("⚠️  降噪分析系统模块不完整，运行快速演示")
        success = run_quick_demo()
    
    if success:
        print("\n✅ 演示完成！")
        print("\n💡 使用建议:")
        print("   1. 对于实时处理：推荐使用简单滤波方法")
        print("   2. 对于离线分析：推荐使用小波降噪")
        print("   3. 对于脉冲噪声：推荐使用中值滤波")
        print("   4. 对于多传感器融合：建议结合多种方法")
        
        print(f"\n🔗 集成到主系统:")
        print(f"   python unified_vibration_analysis.py")
        print(f"   系统将自动执行降噪分析并选择最佳方法")
    else:
        print("\n❌ 演示失败")
    
    return success

if __name__ == "__main__":
    main()
