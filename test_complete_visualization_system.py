#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整可视化系统
验证所有新增图表类型的生成和质量

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import sys
from pathlib import Path
import time

def test_original_charts():
    """测试原有图表生成"""
    print("🧪 测试原有图表生成...")
    
    try:
        from visualization_generator_enhanced import EnhancedVisualizationGenerator
        
        generator = EnhancedVisualizationGenerator(
            output_dir='unified_charts',
            file_prefix='academic_'
        )
        
        start_time = time.time()
        generator.generate_all_visualizations()
        end_time = time.time()
        
        # 检查生成的文件
        output_dir = Path('unified_charts')
        original_files = [
            'academic_data_expansion_comparison.png',
            'academic_model_performance_comparison.png',
            'academic_optimization_results.png',
            'academic_data_distribution_analysis.png',
            'academic_feature_importance_analysis.png',
            'academic_confusion_matrix_analysis.png',
            'academic_roc_curves_multiclass.png',
            'academic_precision_recall_curves.png',
            'academic_precision_recall_summary.png'
        ]
        
        success_count = 0
        for file_name in original_files:
            file_path = output_dir / file_name
            if file_path.exists():
                print(f"   ✅ {file_name}")
                success_count += 1
            else:
                print(f"   ❌ {file_name}")
        
        print(f"   📊 原有图表: {success_count}/{len(original_files)} 成功")
        print(f"   ⏱️ 生成时间: {end_time - start_time:.1f}秒")
        
        return success_count == len(original_files)
        
    except Exception as e:
        print(f"   ❌ 原有图表测试失败: {str(e)}")
        return False

def test_supplementary_charts():
    """测试补充图表生成"""
    print("\n🧪 测试补充图表生成...")
    
    try:
        from visualization_generator_supplement import SupplementaryVisualizationGenerator
        
        generator = SupplementaryVisualizationGenerator(
            output_dir='unified_charts',
            file_prefix='academic_'
        )
        
        start_time = time.time()
        generator.generate_all_supplementary_charts()
        end_time = time.time()
        
        # 检查生成的文件
        output_dir = Path('unified_charts')
        supplementary_files = [
            'academic_hyperparameter_optimization.png',
            'academic_training_loss_curves.png',
            'academic_prediction_vs_actual_scatter.png',
            'academic_model_performance_radar.png',
            'academic_cross_validation_results.png'
        ]
        
        success_count = 0
        for file_name in supplementary_files:
            file_path = output_dir / file_name
            if file_path.exists():
                print(f"   ✅ {file_name}")
                success_count += 1
            else:
                print(f"   ❌ {file_name}")
        
        print(f"   📊 补充图表: {success_count}/{len(supplementary_files)} 成功")
        print(f"   ⏱️ 生成时间: {end_time - start_time:.1f}秒")
        
        return success_count >= len(supplementary_files) * 0.8  # 80%通过率
        
    except Exception as e:
        print(f"   ❌ 补充图表测试失败: {str(e)}")
        return False

def test_technical_charts():
    """测试技术工作流图表"""
    print("\n🧪 测试技术工作流图表...")
    
    try:
        from technical_workflow_visualizer import TechnicalWorkflowVisualizer
        
        generator = TechnicalWorkflowVisualizer(
            output_base_dir='unified_charts',
            file_prefix='technical_'
        )
        
        start_time = time.time()
        generator.generate_all_technical_visualizations()
        end_time = time.time()
        
        # 检查生成的文件
        output_dir = Path('unified_charts')
        technical_files = [f for f in output_dir.glob('technical_*.png')]
        
        print(f"   📊 技术图表: {len(technical_files)} 个文件生成")
        print(f"   ⏱️ 生成时间: {end_time - start_time:.1f}秒")
        
        # 显示前几个文件名
        for i, file_path in enumerate(technical_files[:5]):
            print(f"   ✅ {file_path.name}")
        if len(technical_files) > 5:
            print(f"   ... 还有 {len(technical_files) - 5} 个文件")
        
        return len(technical_files) >= 8  # 至少8个技术图表
        
    except Exception as e:
        print(f"   ❌ 技术图表测试失败: {str(e)}")
        return False

def analyze_complete_chart_collection():
    """分析完整图表集合"""
    print("\n🧪 分析完整图表集合...")
    
    try:
        output_dir = Path('unified_charts')
        
        if not output_dir.exists():
            print(f"   ❌ 输出目录不存在: {output_dir}")
            return False
        
        # 统计所有图表
        all_files = list(output_dir.glob('*.png'))
        academic_files = [f for f in all_files if f.name.startswith('academic_')]
        technical_files = [f for f in all_files if f.name.startswith('technical_')]
        
        print(f"   📊 完整图表统计:")
        print(f"      - 总图表数: {len(all_files)}")
        print(f"      - 学术图表: {len(academic_files)} 个")
        print(f"      - 技术图表: {len(technical_files)} 个")
        
        # 分析图表类型覆盖
        chart_categories = {
            'data_processing': 0,
            'model_training': 0,
            'model_evaluation': 0,
            'optimization': 0,
            'algorithm_specific': 0,
            'workflow': 0
        }
        
        for file_path in academic_files:
            name = file_path.name.lower()
            if any(keyword in name for keyword in ['data', 'preprocessing', 'cleaning', 'feature']):
                chart_categories['data_processing'] += 1
            elif any(keyword in name for keyword in ['training', 'loss', 'convergence']):
                chart_categories['model_training'] += 1
            elif any(keyword in name for keyword in ['prediction', 'performance', 'confusion', 'roc', 'precision']):
                chart_categories['model_evaluation'] += 1
            elif any(keyword in name for keyword in ['optimization', 'hyperparameter', 'cross_validation']):
                chart_categories['optimization'] += 1
            elif any(keyword in name for keyword in ['bp', 'cnn', 'lstm', 'neural']):
                chart_categories['algorithm_specific'] += 1
        
        for file_path in technical_files:
            chart_categories['workflow'] += 1
        
        print(f"   📊 图表类型覆盖:")
        for category, count in chart_categories.items():
            print(f"      - {category.replace('_', ' ').title()}: {count} 个")
        
        # 检查文件质量
        quality_issues = 0
        for file_path in all_files:
            file_size = file_path.stat().st_size
            if file_size < 10 * 1024:  # 小于10KB可能有问题
                quality_issues += 1
        
        print(f"   📊 质量检查:")
        print(f"      - 正常文件: {len(all_files) - quality_issues}")
        print(f"      - 可能问题: {quality_issues}")
        
        # 检查命名规范
        naming_issues = 0
        for file_path in all_files:
            if not (file_path.name.startswith('academic_') or 
                   file_path.name.startswith('technical_') or
                   file_path.name.startswith('legacy_')):
                naming_issues += 1
        
        print(f"   📊 命名规范:")
        print(f"      - 规范命名: {len(all_files) - naming_issues}")
        print(f"      - 命名问题: {naming_issues}")
        
        # 总体评估
        total_expected = 25  # 预期的最少图表数量
        coverage_score = len(all_files) / total_expected * 100
        quality_score = (len(all_files) - quality_issues) / len(all_files) * 100 if all_files else 0
        naming_score = (len(all_files) - naming_issues) / len(all_files) * 100 if all_files else 0
        
        print(f"   📊 总体评估:")
        print(f"      - 覆盖度: {coverage_score:.1f}% ({len(all_files)}/{total_expected})")
        print(f"      - 质量分: {quality_score:.1f}%")
        print(f"      - 规范分: {naming_score:.1f}%")
        
        overall_score = (coverage_score + quality_score + naming_score) / 3
        print(f"      - 综合分: {overall_score:.1f}%")
        
        return overall_score >= 80  # 80分以上算通过
        
    except Exception as e:
        print(f"   ❌ 图表集合分析失败: {str(e)}")
        return False

def generate_completion_report():
    """生成完成报告"""
    print("\n📋 生成完成报告...")
    
    try:
        output_dir = Path('unified_charts')
        all_files = list(output_dir.glob('*.png'))
        
        report_content = []
        report_content.append("# 振动信号分析系统完整可视化图表报告")
        report_content.append("")
        report_content.append(f"## 📊 图表统计")
        report_content.append(f"- **总图表数**: {len(all_files)} 个")
        report_content.append(f"- **学术图表**: {len([f for f in all_files if f.name.startswith('academic_')])} 个")
        report_content.append(f"- **技术图表**: {len([f for f in all_files if f.name.startswith('technical_')])} 个")
        report_content.append("")
        
        report_content.append("## 📋 完整图表清单")
        report_content.append("")
        
        # 按类型分组
        academic_files = sorted([f for f in all_files if f.name.startswith('academic_')])
        technical_files = sorted([f for f in all_files if f.name.startswith('technical_')])
        
        report_content.append("### 学术发表级图表")
        for i, file_path in enumerate(academic_files, 1):
            report_content.append(f"{i}. `{file_path.name}`")
        
        report_content.append("")
        report_content.append("### 技术工作流图表")
        for i, file_path in enumerate(technical_files, 1):
            report_content.append(f"{i}. `{file_path.name}`")
        
        report_content.append("")
        report_content.append("## ✅ 完成状态")
        report_content.append("- 所有图表已生成到统一目录 `unified_charts/`")
        report_content.append("- 图表质量: 330 DPI, Times New Roman字体, 英文标签")
        report_content.append("- 命名规范: 使用前缀区分图表类型")
        report_content.append("- 覆盖范围: 机器学习全流程可视化")
        
        # 保存报告
        report_path = output_dir / 'complete_visualization_report.md'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_content))
        
        print(f"   ✅ 完成报告已保存: {report_path}")
        return True
        
    except Exception as e:
        print(f"   ❌ 生成完成报告失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始完整可视化系统测试")
    print("=" * 80)
    
    # 运行所有测试
    tests = [
        ("原有图表生成", test_original_charts),
        ("补充图表生成", test_supplementary_charts),
        ("技术工作流图表", test_technical_charts),
        ("完整图表集合分析", analyze_complete_chart_collection),
        ("生成完成报告", generate_completion_report)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结测试结果
    print("\n" + "=" * 80)
    print("🎯 完整可视化系统测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！完整可视化系统工作正常。")
        print("📁 所有图表已保存到unified_charts目录中。")
    elif passed >= total * 0.8:
        print("⚠️  大部分测试通过，系统基本可用，建议检查失败项目。")
    else:
        print("❌ 多个测试失败，需要修复问题后重新测试。")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
