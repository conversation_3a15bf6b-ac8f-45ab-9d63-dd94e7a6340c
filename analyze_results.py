#!/usr/bin/env python3
"""
训练结果分析脚本
分析和解释模型训练结果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
import os
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ResultsAnalyzer:
    """结果分析器"""
    
    def __init__(self, results_dir: str = './results'):
        """
        初始化结果分析器
        
        Args:
            results_dir: 结果目录路径
        """
        self.results_dir = results_dir
        self.reports_dir = os.path.join(results_dir, 'reports')
        self.plots_dir = os.path.join(results_dir, 'plots')
        
        # 创建目录
        os.makedirs(self.plots_dir, exist_ok=True)
        
    def load_training_results(self) -> Dict[str, Any]:
        """加载训练结果"""
        logger.info("📊 加载训练结果...")
        
        results = {}
        
        # 加载完整训练结果
        complete_results_file = os.path.join(self.results_dir, 'complete_training_results.json')
        if os.path.exists(complete_results_file):
            with open(complete_results_file, 'r', encoding='utf-8') as f:
                results['complete_training'] = json.load(f)
            logger.info("✅ 完整训练结果已加载")
        
        # 加载深度学习结果
        dl_results_file = 'deep_learning_optimization_results.json'
        if os.path.exists(dl_results_file):
            with open(dl_results_file, 'r', encoding='utf-8') as f:
                results['deep_learning'] = json.load(f)
            logger.info("✅ 深度学习结果已加载")
        
        return results
    
    def analyze_model_performance(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """分析模型性能"""
        logger.info("📈 分析模型性能...")
        
        analysis = {
            'best_models': {},
            'performance_comparison': {},
            'target_achievement': {}
        }
        
        all_scores = {}
        
        # 收集所有模型分数
        if 'complete_training' in results:
            # 传统机器学习结果
            if 'traditional_ml' in results['complete_training']:
                traditional_scores = results['complete_training']['traditional_ml'].get('best_scores', {})
                for model, score in traditional_scores.items():
                    all_scores[f"{model} (传统ML)"] = score
        
        # 深度学习结果
        if 'deep_learning' in results:
            for dataset_name, dataset_results in results['deep_learning'].items():
                bp_score = dataset_results.get('bp_score', 0.0)
                tcn_score = dataset_results.get('tcn_score', 0.0)
                
                if bp_score > 0:
                    all_scores[f"BP神经网络 ({dataset_name})"] = bp_score
                if tcn_score > 0:
                    all_scores[f"TCN ({dataset_name})"] = tcn_score
        
        # 分析最佳模型
        if all_scores:
            sorted_scores = sorted(all_scores.items(), key=lambda x: x[1], reverse=True)
            analysis['best_models'] = dict(sorted_scores[:5])  # 前5个最佳模型
            
            # 性能比较
            analysis['performance_comparison'] = {
                'total_models': len(all_scores),
                'best_score': sorted_scores[0][1] if sorted_scores else 0.0,
                'worst_score': sorted_scores[-1][1] if sorted_scores else 0.0,
                'average_score': np.mean(list(all_scores.values())),
                'std_score': np.std(list(all_scores.values()))
            }
            
            # 目标达成分析
            target_r2 = 0.75
            models_above_target = sum(1 for score in all_scores.values() if score > target_r2)
            analysis['target_achievement'] = {
                'target_r2': target_r2,
                'models_above_target': models_above_target,
                'achievement_rate': models_above_target / len(all_scores),
                'best_achievement': sorted_scores[0][1] > target_r2 if sorted_scores else False
            }
        
        return analysis
    
    def create_performance_plots(self, results: Dict[str, Any], analysis: Dict[str, Any]):
        """创建性能图表"""
        logger.info("📊 创建性能图表...")
        
        # 收集所有分数
        all_scores = {}
        model_types = {}
        
        if 'complete_training' in results:
            traditional_scores = results['complete_training'].get('traditional_ml', {}).get('best_scores', {})
            for model, score in traditional_scores.items():
                model_name = f"{model}"
                all_scores[model_name] = score
                model_types[model_name] = '传统ML'
        
        if 'deep_learning' in results:
            for dataset_name, dataset_results in results['deep_learning'].items():
                bp_score = dataset_results.get('bp_score', 0.0)
                tcn_score = dataset_results.get('tcn_score', 0.0)
                
                if bp_score > 0:
                    model_name = f"BP ({dataset_name})"
                    all_scores[model_name] = bp_score
                    model_types[model_name] = '深度学习'
                
                if tcn_score > 0:
                    model_name = f"TCN ({dataset_name})"
                    all_scores[model_name] = tcn_score
                    model_types[model_name] = '深度学习'
        
        if not all_scores:
            logger.warning("没有找到模型分数数据")
            return
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('模型性能分析报告', fontsize=16, fontweight='bold')
        
        # 1. 模型性能对比条形图
        ax1 = axes[0, 0]
        models = list(all_scores.keys())
        scores = list(all_scores.values())
        colors = ['#1f77b4' if model_types[model] == '传统ML' else '#ff7f0e' for model in models]
        
        bars = ax1.bar(range(len(models)), scores, color=colors)
        ax1.axhline(y=0.75, color='red', linestyle='--', alpha=0.7, label='目标线 (R²=0.75)')
        ax1.set_xlabel('模型')
        ax1.set_ylabel('R² 分数')
        ax1.set_title('模型性能对比')
        ax1.set_xticks(range(len(models)))
        ax1.set_xticklabels(models, rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, score in zip(bars, scores):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{score:.3f}', ha='center', va='bottom', fontsize=8)
        
        # 2. 模型类型分布饼图
        ax2 = axes[0, 1]
        type_counts = {}
        for model_type in model_types.values():
            type_counts[model_type] = type_counts.get(model_type, 0) + 1
        
        ax2.pie(type_counts.values(), labels=type_counts.keys(), autopct='%1.1f%%',
                colors=['#1f77b4', '#ff7f0e'])
        ax2.set_title('模型类型分布')
        
        # 3. 性能分布直方图
        ax3 = axes[1, 0]
        ax3.hist(scores, bins=10, alpha=0.7, color='skyblue', edgecolor='black')
        ax3.axvline(x=0.75, color='red', linestyle='--', alpha=0.7, label='目标线')
        ax3.axvline(x=np.mean(scores), color='green', linestyle='-', alpha=0.7, label='平均值')
        ax3.set_xlabel('R² 分数')
        ax3.set_ylabel('模型数量')
        ax3.set_title('性能分布')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 目标达成情况
        ax4 = axes[1, 1]
        above_target = sum(1 for score in scores if score > 0.75)
        below_target = len(scores) - above_target
        
        ax4.bar(['达到目标', '未达目标'], [above_target, below_target], 
                color=['green', 'orange'], alpha=0.7)
        ax4.set_ylabel('模型数量')
        ax4.set_title('目标达成情况')
        ax4.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, v in enumerate([above_target, below_target]):
            ax4.text(i, v + 0.1, str(v), ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图表
        plot_file = os.path.join(self.plots_dir, 'model_performance_analysis.png')
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"✅ 性能图表已保存: {plot_file}")
    
    def generate_detailed_report(self, results: Dict[str, Any], analysis: Dict[str, Any]):
        """生成详细分析报告"""
        logger.info("📝 生成详细分析报告...")
        
        report_file = os.path.join(self.reports_dir, 'detailed_analysis_report.md')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 深度学习增强振动信号分析系统 - 详细分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 执行摘要
            f.write("## 📊 执行摘要\n\n")
            
            if analysis['performance_comparison']:
                perf = analysis['performance_comparison']
                f.write(f"- **总模型数量**: {perf['total_models']}\n")
                f.write(f"- **最佳性能**: R² = {perf['best_score']:.4f}\n")
                f.write(f"- **平均性能**: R² = {perf['average_score']:.4f} ± {perf['std_score']:.4f}\n")
                f.write(f"- **性能范围**: {perf['worst_score']:.4f} - {perf['best_score']:.4f}\n\n")
            
            if analysis['target_achievement']:
                target = analysis['target_achievement']
                f.write(f"- **目标达成率**: {target['achievement_rate']:.1%} ({target['models_above_target']}/{analysis['performance_comparison']['total_models']})\n")
                f.write(f"- **最佳模型达标**: {'✅ 是' if target['best_achievement'] else '❌ 否'}\n\n")
            
            # 最佳模型排名
            f.write("## 🏆 最佳模型排名\n\n")
            f.write("| 排名 | 模型名称 | R² 分数 | 达标状态 |\n")
            f.write("|------|----------|---------|----------|\n")
            
            for i, (model_name, score) in enumerate(analysis['best_models'].items(), 1):
                status = "✅ 达标" if score > 0.75 else "📈 待优化"
                f.write(f"| {i} | {model_name} | {score:.4f} | {status} |\n")
            
            f.write("\n")
            
            # 模型类型分析
            f.write("## 🔍 模型类型分析\n\n")
            
            # 传统机器学习分析
            if 'complete_training' in results:
                traditional_scores = results['complete_training'].get('traditional_ml', {}).get('best_scores', {})
                if traditional_scores:
                    f.write("### 传统机器学习模型\n\n")
                    traditional_best = max(traditional_scores.values())
                    traditional_avg = np.mean(list(traditional_scores.values()))
                    
                    f.write(f"- **最佳性能**: {traditional_best:.4f}\n")
                    f.write(f"- **平均性能**: {traditional_avg:.4f}\n")
                    f.write(f"- **模型数量**: {len(traditional_scores)}\n\n")
                    
                    f.write("**详细结果**:\n")
                    for model, score in sorted(traditional_scores.items(), key=lambda x: x[1], reverse=True):
                        status = "✅" if score > 0.75 else "📈"
                        f.write(f"- {status} {model}: {score:.4f}\n")
                    f.write("\n")
            
            # 深度学习分析
            if 'deep_learning' in results:
                f.write("### 深度学习模型\n\n")
                
                dl_scores = []
                for dataset_name, dataset_results in results['deep_learning'].items():
                    bp_score = dataset_results.get('bp_score', 0.0)
                    tcn_score = dataset_results.get('tcn_score', 0.0)
                    if bp_score > 0:
                        dl_scores.append(bp_score)
                    if tcn_score > 0:
                        dl_scores.append(tcn_score)
                
                if dl_scores:
                    dl_best = max(dl_scores)
                    dl_avg = np.mean(dl_scores)
                    
                    f.write(f"- **最佳性能**: {dl_best:.4f}\n")
                    f.write(f"- **平均性能**: {dl_avg:.4f}\n")
                    f.write(f"- **模型数量**: {len(dl_scores)}\n\n")
                    
                    f.write("**详细结果**:\n")
                    for dataset_name, dataset_results in results['deep_learning'].items():
                        f.write(f"\n**{dataset_name}**:\n")
                        
                        bp_score = dataset_results.get('bp_score', 0.0)
                        tcn_score = dataset_results.get('tcn_score', 0.0)
                        
                        if bp_score > 0:
                            status = "✅" if bp_score > 0.75 else "📈"
                            f.write(f"- {status} BP神经网络: {bp_score:.4f}\n")
                        
                        if tcn_score > 0:
                            status = "✅" if tcn_score > 0.75 else "📈"
                            f.write(f"- {status} TCN: {tcn_score:.4f}\n")
                    f.write("\n")
            
            # 改进建议
            f.write("## 💡 改进建议\n\n")
            
            if analysis['target_achievement']['best_achievement']:
                f.write("### ✅ 目标已达成\n\n")
                f.write("恭喜！您的最佳模型已经达到了目标性能 (R² > 0.75)。\n\n")
                f.write("**下一步建议**:\n")
                f.write("1. 🚀 **模型部署**: 将最佳模型部署到生产环境\n")
                f.write("2. 🔄 **模型集成**: 考虑集成多个高性能模型以提升稳定性\n")
                f.write("3. 📊 **持续监控**: 建立模型性能监控系统\n")
                f.write("4. 🔧 **模型优化**: 进一步优化模型以达到更高性能\n\n")
            else:
                f.write("### 📈 性能优化建议\n\n")
                f.write("您的模型尚未达到目标性能，以下是改进建议:\n\n")
                
                best_score = analysis['performance_comparison']['best_score']
                gap = 0.75 - best_score
                
                f.write(f"**当前最佳性能**: {best_score:.4f}\n")
                f.write(f"**距离目标差距**: {gap:.4f}\n\n")
                
                f.write("**优化策略**:\n")
                f.write("1. 📊 **数据质量**: 检查和改善训练数据质量\n")
                f.write("2. 🔧 **特征工程**: 添加更多相关特征或改进特征提取\n")
                f.write("3. ⚙️ **超参数调优**: 增加贝叶斯优化试验次数\n")
                f.write("4. 🧠 **模型架构**: 尝试更复杂的深度学习架构\n")
                f.write("5. 📈 **数据增强**: 增加训练数据量或使用数据增强技术\n")
                f.write("6. 🔄 **集成学习**: 使用模型集成方法提升性能\n\n")
            
            # 技术细节
            f.write("## 🔧 技术实现细节\n\n")
            f.write("### GPU加速状态\n")
            f.write("- ✅ PyTorch GPU: CUDA 12.1 支持\n")
            f.write("- ✅ XGBoost GPU: device='cuda' 已启用\n")
            f.write("- ✅ 自动GPU/CPU切换: 智能降级机制\n\n")
            
            f.write("### 深度学习架构\n")
            f.write("- **BP神经网络**: 多层感知机 + Dropout + 贝叶斯优化\n")
            f.write("- **TCN**: 因果卷积 + 残差连接 + 层归一化\n")
            f.write("- **优化器**: Adam + 学习率调度 + 早停机制\n\n")
            
            f.write("### 评估方法\n")
            f.write("- **交叉验证**: 5折分层交叉验证\n")
            f.write("- **评估指标**: R²分数 (回归), 准确率 (分类)\n")
            f.write("- **超参数优化**: TPE采样器 + 25-50次试验\n\n")
        
        logger.info(f"✅ 详细分析报告已保存: {report_file}")
    
    def run_complete_analysis(self):
        """运行完整分析"""
        logger.info("🚀 开始结果分析...")
        
        # 加载结果
        results = self.load_training_results()
        
        if not results:
            logger.error("❌ 未找到训练结果文件")
            return
        
        # 分析性能
        analysis = self.analyze_model_performance(results)
        
        # 创建图表
        self.create_performance_plots(results, analysis)
        
        # 生成报告
        self.generate_detailed_report(results, analysis)
        
        # 显示摘要
        print("\n📊 结果分析完成!")
        print("=" * 50)
        
        if analysis['performance_comparison']:
            perf = analysis['performance_comparison']
            print(f"📈 总模型数量: {perf['total_models']}")
            print(f"🏆 最佳性能: R² = {perf['best_score']:.4f}")
            print(f"📊 平均性能: R² = {perf['average_score']:.4f}")
            
            if analysis['target_achievement']['best_achievement']:
                print("🎉 目标已达成!")
            else:
                gap = 0.75 - perf['best_score']
                print(f"📈 距离目标: {gap:.4f}")
        
        print(f"\n📁 分析结果已保存到: {self.reports_dir}")
        print(f"📊 图表已保存到: {self.plots_dir}")

def main():
    """主函数"""
    print("📊 训练结果分析工具")
    print("=" * 50)
    
    analyzer = ResultsAnalyzer()
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
