#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
降噪系统测试脚本
测试多种降噪方法的对比分析功能

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys

def test_denoising_methods():
    """测试降噪方法"""
    print("🧪 测试降噪方法模块...")
    
    try:
        from denoising_methods import VibrationSignalDenoiser
        
        # 创建测试信号
        fs = 1000
        t = np.arange(0, 2, 1/fs)
        
        # 车辆通过信号 + 噪声
        clean_signal = 2 * np.sin(2 * np.pi * 10 * t) * np.exp(-((t-1)**2)/0.2)
        noise = 0.5 * np.random.randn(len(t))
        noisy_signal = clean_signal + noise
        
        # 初始化降噪器
        denoiser = VibrationSignalDenoiser(fs=fs)
        
        # 测试各种降噪方法
        print("   测试小波降噪...")
        denoised_wavelet = denoiser.wavelet_denoising(noisy_signal, wavelet='db4')
        
        print("   测试Butterworth滤波...")
        denoised_butter = denoiser.butterworth_filter(noisy_signal, cutoff_freq=50)
        
        print("   测试中值滤波...")
        denoised_median = denoiser.median_filter_denoising(noisy_signal, kernel_size=5)
        
        print("   测试移动平均滤波...")
        denoised_ma = denoiser.moving_average_filter(noisy_signal, window_size=10)
        
        print("   ✅ 降噪方法测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 降噪方法测试失败: {str(e)}")
        return False

def test_denoising_evaluator():
    """测试降噪评估器"""
    print("🧪 测试降噪评估器...")
    
    try:
        from denoising_evaluator import DenoisingEvaluator
        
        # 创建测试信号
        fs = 1000
        t = np.arange(0, 2, 1/fs)
        original_signal = 2 * np.sin(2 * np.pi * 10 * t) * np.exp(-((t-1)**2)/0.2)
        denoised_signal = original_signal + 0.1 * np.random.randn(len(t))
        
        # 初始化评估器
        evaluator = DenoisingEvaluator(fs=fs)
        
        # 测试评估指标
        print("   测试SNR计算...")
        snr = evaluator.calculate_snr(original_signal, denoised_signal)
        
        print("   测试RMSE计算...")
        rmse = evaluator.calculate_rmse(original_signal, denoised_signal)
        
        print("   测试信号保真度...")
        fidelity = evaluator.calculate_signal_fidelity(original_signal, denoised_signal)
        
        print("   测试频域保持度...")
        freq_pres = evaluator.calculate_frequency_preservation(original_signal, denoised_signal)
        
        print("   测试综合评估...")
        evaluation = evaluator.comprehensive_evaluation(original_signal, denoised_signal, "test_method")
        
        print(f"   评估结果: SNR={snr:.2f}dB, RMSE={rmse:.4f}, 保真度={fidelity:.3f}")
        print("   ✅ 降噪评估器测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 降噪评估器测试失败: {str(e)}")
        return False

def test_denoising_visualizer():
    """测试降噪可视化器"""
    print("🧪 测试降噪可视化器...")
    
    try:
        from denoising_visualizer import DenoisingVisualizer
        
        # 创建测试信号
        fs = 1000
        t = np.arange(0, 2, 1/fs)
        original_signal = 2 * np.sin(2 * np.pi * 10 * t) * np.exp(-((t-1)**2)/0.2)
        
        # 模拟降噪结果
        denoised_signals = {
            'wavelet_db4': original_signal + 0.1 * np.random.randn(len(t)),
            'butterworth_50hz': original_signal + 0.15 * np.random.randn(len(t)),
            'median_5': original_signal + 0.12 * np.random.randn(len(t))
        }
        
        # 初始化可视化器
        visualizer = DenoisingVisualizer(output_dir='test_denoising_visualizations')
        
        print("   测试信号对比图...")
        visualizer.plot_signal_comparison(original_signal, denoised_signals, fs, "test_signal")
        
        print("   测试频域分析图...")
        visualizer.plot_frequency_analysis(original_signal, denoised_signals, fs, "test_signal")
        
        print("   ✅ 降噪可视化器测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 降噪可视化器测试失败: {str(e)}")
        return False

def test_denoising_comparison_system():
    """测试降噪对比分析系统"""
    print("🧪 测试降噪对比分析系统...")
    
    try:
        from denoising_comparison_system import DenoisingComparisonSystem
        
        # 创建测试信号
        fs = 1000
        t = np.arange(0, 3, 1/fs)
        
        # 多个测试信号
        signals = {
            'vehicle_signal': 2 * np.sin(2 * np.pi * 10 * t) * np.exp(-((t-1.5)**2)/0.3) + 0.3 * np.random.randn(len(t)),
            'vibration_signal': 1.5 * np.sin(2 * np.pi * 50 * t) + 0.5 * np.random.randn(len(t)),
            'mixed_signal': (np.sin(2 * np.pi * 5 * t) + 0.5 * np.sin(2 * np.pi * 25 * t) + 
                           0.3 * np.sin(2 * np.pi * 80 * t) + 0.4 * np.random.randn(len(t)))
        }
        
        # 初始化对比系统
        system = DenoisingComparisonSystem(fs=fs, output_dir='test_denoising_analysis')
        
        print("   测试单信号分析...")
        result = system.analyze_single_signal(signals['vehicle_signal'], 'test_vehicle_signal', 
                                            generate_visualizations=False)
        
        if result and result.get('best_method'):
            print(f"   最佳方法: {result['best_method']['method_name']}")
        
        print("   测试自动推荐...")
        recommendation = system.get_automatic_recommendation(signals['vehicle_signal'])
        
        if recommendation:
            print(f"   推荐方法: {recommendation['recommended_methods']}")
        
        print("   ✅ 降噪对比分析系统测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 降噪对比分析系统测试失败: {str(e)}")
        return False

def test_integration_with_main_system():
    """测试与主系统的集成"""
    print("🧪 测试与主系统的集成...")
    
    try:
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 创建系统实例
        system = UnifiedVibrationAnalysisSystem()
        
        # 测试降噪分析方法是否存在
        if hasattr(system, 'run_denoising_analysis'):
            print("   ✅ 降噪分析方法已集成到主系统")
        else:
            print("   ❌ 降噪分析方法未集成到主系统")
            return False
        
        # 测试降噪相关属性
        if hasattr(system, 'denoising_enabled') and hasattr(system, 'denoising_results'):
            print("   ✅ 降噪相关属性已添加")
        else:
            print("   ❌ 降噪相关属性缺失")
            return False
        
        print("   ✅ 主系统集成测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 主系统集成测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试振动信号降噪对比分析系统")
    print("=" * 60)
    
    test_results = []
    
    # 测试各个模块
    test_results.append(("降噪方法模块", test_denoising_methods()))
    test_results.append(("降噪评估器", test_denoising_evaluator()))
    test_results.append(("降噪可视化器", test_denoising_visualizer()))
    test_results.append(("降噪对比系统", test_denoising_comparison_system()))
    test_results.append(("主系统集成", test_integration_with_main_system()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 测试统计: {passed}/{total} 个测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！降噪系统已成功集成到振动信号分析系统中。")
        print("\n💡 使用方法:")
        print("   python unified_vibration_analysis.py")
        print("   系统将自动执行降噪方法对比分析")
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
