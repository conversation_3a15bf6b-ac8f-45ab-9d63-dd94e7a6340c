# 振动信号分析系统

## 项目概述

这是一个完整的振动信号分析系统，用于处理车辆通过时的路面振动信号数据，实现车辆参数（轴重、速度、轴型）的智能识别和预测。

## 主要功能

- 🔍 **振动信号预处理**：支持acce01-acce20格式传感器数据，自动处理缺失数据
- 🚗 **车辆通过检测**：使用global_max方法自动检测车辆通过事件
- 📊 **特征提取**：提取时域、频域、时频域振动信号特征
- 🤖 **机器学习预测**：训练轴重回归、速度回归、轴型分类模型
- 🛠️ **数据质量保证**：自动处理数据类型错误、异常值、格式不一致等问题

## 技术特点

- ✅ 支持多种传感器命名格式（acce01-acce20, sensor_01-sensor_20）
- ✅ 鲁棒的缺失数据处理和异常恢复机制
- ✅ 完整的机器学习流水线（数据清理→特征提取→模型训练→结果评估）
- ✅ 详细的处理日志和错误诊断
- ✅ 自动化的批量数据处理

## 快速开始

### 1. 环境配置
```bash
pip install pandas numpy matplotlib seaborn scikit-learn scipy pywt
```

### 2. 数据准备
将您的CSV数据文件按以下结构组织：
```
your_data/
├── 轴重1/
│   ├── 轴型1/
│   │   ├── 速度1/
│   │   │   ├── data_001.csv
│   │   │   ├── data_002.csv
│   │   │   └── ...
│   │   └── 速度2/
│   └── 轴型2/
└── 轴重2/
```

### 3. 运行分析
```bash
cd ml/
python complete_ml_pipeline.py
```

## 详细使用说明

请参考 `docs/` 目录中的详细文档：
- 📖 [完整使用指南](docs/完整使用指南.md)
- 🔧 [环境配置指南](docs/环境配置指南.md)
- 📊 [数据格式说明](docs/数据格式说明.md)
- ❓ [常见问题解答](docs/常见问题解答.md)

## 项目结构

```
vibration_signal_analysis_system/
├── core/                           # 核心处理模块
│   ├── experimental_data_processor.py
│   └── vibration_signal_analysis_framework.py
├── ml/                             # 机器学习模块
│   ├── fix_ml_dataset.py
│   ├── train_ml_models.py
│   └── complete_ml_pipeline.py
├── tools/                          # 工具脚本
│   ├── diagnose_ml_dataset.py
│   └── diagnose_feature_extraction.py
├── docs/                           # 文档
├── examples/                       # 示例数据
├── results/                        # 结果输出
└── archive/                        # 归档文件
```

## 输出结果

- `combined_features_clean.csv` - 清理后的完整特征数据
- `training_datasets_clean/` - 机器学习训练数据集
- `*_regression_results.png` - 回归模型结果图表
- `*_classification_results.png` - 分类模型结果图表

## 技术支持

如遇问题，请查看：
1. 📋 处理日志中的详细错误信息
2. 📖 docs/常见问题解答.md
3. 🔧 tools/目录中的诊断脚本

## 更新日志

- v1.0.0 - 初始版本，支持完整的振动信号分析流水线
- 已解决传感器格式支持、缺失数据处理、特征提取失败、数据类型转换等问题

---
© 2024 振动信号分析系统
