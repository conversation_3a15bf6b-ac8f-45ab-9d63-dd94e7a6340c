#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习模型功能完整性验证程序
验证轴型分类、速度预测、轴重预测等交通信息解析功能

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import chinese_font_config  # 中文字体配置
import pandas as pd
import numpy as np
import os
import sys
import json
import glob
from pathlib import Path
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, classification_report, r2_score, mean_absolute_error, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

class MLModelFunctionalityVerifier:
    """机器学习模型功能验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.results = {
            'axle_classification': {},
            'speed_prediction': {},
            'weight_prediction': {},
            'model_integration': {},
            'performance_visualization': {},
            'overall_assessment': {}
        }
        
        # 性能目标
        self.performance_targets = {
            'axle_classification': {'accuracy': 0.90},
            'speed_prediction': {'r2': 0.90, 'mae': 5.0},
            'weight_prediction': {'r2': 0.85, 'mae': 2.0}
        }
        
        print("🔍 机器学习模型功能验证器已初始化")
        print(f"   性能目标: 轴型分类>90%, 速度预测R²>0.90, 轴重预测R²>0.85")
    
    def verify_axle_classification_functionality(self):
        """验证轴型分类功能"""
        print("\n🚗 步骤1: 验证轴型分类功能")
        print("=" * 60)
        
        verification_result = {
            'implementation_status': 'unknown',
            'model_found': False,
            'training_data_analysis': {},
            'performance_metrics': {},
            'class_coverage': {},
            'output_verification': {},
            'issues': []
        }
        
        try:
            # 1. 检查轴型分类实现
            implementation_status = self._check_axle_classification_implementation()
            verification_result['implementation_status'] = implementation_status
            
            # 2. 分析训练数据
            if implementation_status != 'not_implemented':
                data_analysis = self._analyze_axle_classification_data()
                verification_result['training_data_analysis'] = data_analysis
                
                # 3. 检查模型性能
                performance = self._check_axle_classification_performance()
                verification_result['performance_metrics'] = performance
                
                # 4. 验证类别覆盖
                class_coverage = self._verify_axle_class_coverage()
                verification_result['class_coverage'] = class_coverage
                
                # 5. 检查输出
                output_check = self._verify_axle_classification_output()
                verification_result['output_verification'] = output_check
            
            self.results['axle_classification'] = verification_result
            
            # 显示验证结果
            self._display_axle_classification_results(verification_result)
            
            return verification_result
            
        except Exception as e:
            print(f"❌ 轴型分类功能验证失败: {str(e)}")
            verification_result['issues'].append(f"验证过程异常: {str(e)}")
            self.results['axle_classification'] = verification_result
            return verification_result
    
    def _check_axle_classification_implementation(self):
        """检查轴型分类实现"""
        print("🔍 检查轴型分类实现...")
        
        # 检查主程序中的轴型分类相关代码
        main_files = [
            'unified_vibration_analysis.py',
            'ml_analysis.py',
            'classification_models.py'
        ]
        
        implementation_indicators = [
            'axle_type',
            'axle_classification',
            'classify_axle',
            'ClassificationModel',
            'axle_class'
        ]
        
        found_implementations = []
        
        for file_path in main_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    for indicator in implementation_indicators:
                        if indicator in content:
                            found_implementations.append({
                                'file': file_path,
                                'indicator': indicator,
                                'context': self._extract_context(content, indicator)
                            })
                            
                except Exception as e:
                    print(f"   ⚠️  文件读取失败 {file_path}: {str(e)}")
                    continue
        
        if found_implementations:
            print(f"   ✅ 发现轴型分类实现: {len(found_implementations)} 个指标")
            for impl in found_implementations[:3]:
                print(f"      - {impl['file']}: {impl['indicator']}")
            return 'implemented'
        else:
            print("   ❌ 未发现轴型分类实现")
            return 'not_implemented'
    
    def _extract_context(self, content, keyword, context_lines=2):
        """提取关键词上下文"""
        lines = content.split('\n')
        contexts = []
        
        for i, line in enumerate(lines):
            if keyword in line:
                start = max(0, i - context_lines)
                end = min(len(lines), i + context_lines + 1)
                context = '\n'.join(lines[start:end])
                contexts.append(context)
                
        return contexts[:2]  # 返回前两个上下文
    
    def _analyze_axle_classification_data(self):
        """分析轴型分类训练数据"""
        print("📊 分析轴型分类训练数据...")
        
        data_analysis = {
            'feature_file_found': False,
            'axle_type_column': None,
            'class_distribution': {},
            'data_quality': {},
            'sample_count': 0
        }
        
        # 查找特征文件
        feature_files = [
            'combined_features.csv',
            './ml/combined_features_clean.csv',
            './analysis_results/combined_features.csv'
        ]
        
        features_df = None
        used_file = None
        
        for file_path in feature_files:
            if os.path.exists(file_path):
                try:
                    features_df = pd.read_csv(file_path)
                    used_file = file_path
                    data_analysis['feature_file_found'] = True
                    break
                except Exception as e:
                    print(f"   ⚠️  文件读取失败 {file_path}: {str(e)}")
                    continue
        
        if features_df is not None:
            print(f"   ✅ 找到特征文件: {used_file}")
            print(f"   📊 数据形状: {features_df.shape}")
            
            data_analysis['sample_count'] = len(features_df)
            
            # 查找轴型相关列
            axle_columns = [col for col in features_df.columns 
                          if any(keyword in col.lower() for keyword in ['axle', '轴', 'axis'])]
            
            if axle_columns:
                print(f"   🔍 发现轴型相关列: {axle_columns}")
                
                # 分析主要轴型列
                main_axle_col = axle_columns[0]
                data_analysis['axle_type_column'] = main_axle_col
                
                # 分析类别分布
                if main_axle_col in features_df.columns:
                    class_dist = features_df[main_axle_col].value_counts()
                    data_analysis['class_distribution'] = class_dist.to_dict()
                    
                    print(f"   📈 轴型分布:")
                    for axle_type, count in class_dist.items():
                        print(f"      {axle_type}: {count} 个样本")
                    
                    # 数据质量分析
                    missing_count = features_df[main_axle_col].isnull().sum()
                    unique_count = features_df[main_axle_col].nunique()
                    
                    data_analysis['data_quality'] = {
                        'missing_values': int(missing_count),
                        'unique_classes': int(unique_count),
                        'missing_ratio': float(missing_count / len(features_df))
                    }
                    
                    print(f"   📋 数据质量: {unique_count} 个类别, {missing_count} 个缺失值")
            else:
                print("   ❌ 未发现轴型相关列")
                data_analysis['axle_type_column'] = None
        else:
            print("   ❌ 未找到特征文件")
        
        return data_analysis
    
    def _check_axle_classification_performance(self):
        """检查轴型分类性能"""
        print("📈 检查轴型分类性能...")
        
        performance = {
            'model_results_found': False,
            'accuracy': None,
            'classification_report': None,
            'confusion_matrix': None,
            'target_achieved': False
        }
        
        # 查找模型结果文件
        result_files = [
            'axle_classification_results.json',
            'classification_results.json',
            'ml_results.json',
            './analysis_results/axle_classification_results.json'
        ]
        
        for file_path in result_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        results = json.load(f)
                    
                    # 查找轴型分类相关结果
                    if 'axle_classification' in results:
                        axle_results = results['axle_classification']
                        performance['model_results_found'] = True
                        
                        if 'accuracy' in axle_results:
                            performance['accuracy'] = axle_results['accuracy']
                            
                        if 'classification_report' in axle_results:
                            performance['classification_report'] = axle_results['classification_report']
                            
                        print(f"   ✅ 找到轴型分类结果: {file_path}")
                        break
                        
                except Exception as e:
                    print(f"   ⚠️  结果文件读取失败 {file_path}: {str(e)}")
                    continue
        
        if not performance['model_results_found']:
            print("   ❌ 未找到轴型分类性能结果")
        else:
            accuracy = performance.get('accuracy')
            if accuracy is not None:
                target_accuracy = self.performance_targets['axle_classification']['accuracy']
                performance['target_achieved'] = accuracy >= target_accuracy
                print(f"   📊 分类准确率: {accuracy:.4f}")
                print(f"   🎯 目标达成: {'✅' if performance['target_achieved'] else '❌'} (目标: {target_accuracy})")
        
        return performance
    
    def _verify_axle_class_coverage(self):
        """验证轴型类别覆盖"""
        print("🔍 验证轴型类别覆盖...")
        
        coverage = {
            'expected_classes': ['单轴', '双轴', '三轴', '四轴', '多轴'],
            'found_classes': [],
            'missing_classes': [],
            'coverage_ratio': 0.0
        }
        
        # 从数据分析结果获取类别信息
        data_analysis = self.results.get('axle_classification', {}).get('training_data_analysis', {})
        class_distribution = data_analysis.get('class_distribution', {})
        
        if class_distribution:
            found_classes = list(class_distribution.keys())
            coverage['found_classes'] = found_classes
            
            # 检查类别覆盖
            expected_patterns = ['单', '双', '三', '四', '1', '2', '3', '4']
            covered_patterns = []
            
            for found_class in found_classes:
                for pattern in expected_patterns:
                    if pattern in str(found_class):
                        covered_patterns.append(pattern)
            
            coverage['coverage_ratio'] = len(set(covered_patterns)) / len(expected_patterns)
            
            print(f"   📋 发现类别: {found_classes}")
            print(f"   📊 覆盖率: {coverage['coverage_ratio']*100:.1f}%")
        else:
            print("   ❌ 无法获取类别分布信息")
        
        return coverage
    
    def _verify_axle_classification_output(self):
        """验证轴型分类输出"""
        print("📤 验证轴型分类输出...")
        
        output_verification = {
            'prediction_files_found': False,
            'output_format_correct': False,
            'prediction_range_valid': False,
            'confidence_scores_present': False
        }
        
        # 查找预测输出文件
        output_files = [
            'axle_predictions.csv',
            'classification_predictions.csv',
            './analysis_results/axle_classification_predictions.csv'
        ]
        
        for file_path in output_files:
            if os.path.exists(file_path):
                try:
                    predictions_df = pd.read_csv(file_path)
                    output_verification['prediction_files_found'] = True
                    
                    # 检查输出格式
                    required_columns = ['prediction', 'actual']
                    if all(col in predictions_df.columns for col in required_columns):
                        output_verification['output_format_correct'] = True
                    
                    # 检查置信度分数
                    confidence_columns = [col for col in predictions_df.columns 
                                        if 'confidence' in col.lower() or 'probability' in col.lower()]
                    if confidence_columns:
                        output_verification['confidence_scores_present'] = True
                    
                    print(f"   ✅ 找到预测输出: {file_path}")
                    print(f"   📊 预测数量: {len(predictions_df)}")
                    break
                    
                except Exception as e:
                    print(f"   ⚠️  输出文件读取失败 {file_path}: {str(e)}")
                    continue
        
        if not output_verification['prediction_files_found']:
            print("   ❌ 未找到轴型分类预测输出")
        
        return output_verification
    
    def _display_axle_classification_results(self, verification_result):
        """显示轴型分类验证结果"""
        print("\n📋 轴型分类功能验证结果:")
        
        implementation = verification_result['implementation_status']
        print(f"   实现状态: {'✅ 已实现' if implementation == 'implemented' else '❌ 未实现'}")
        
        if implementation == 'implemented':
            data_analysis = verification_result['training_data_analysis']
            if data_analysis.get('feature_file_found'):
                sample_count = data_analysis.get('sample_count', 0)
                class_count = data_analysis.get('data_quality', {}).get('unique_classes', 0)
                print(f"   训练数据: {sample_count} 个样本, {class_count} 个类别")
            
            performance = verification_result['performance_metrics']
            if performance.get('model_results_found'):
                accuracy = performance.get('accuracy')
                target_achieved = performance.get('target_achieved', False)
                if accuracy is not None:
                    print(f"   性能指标: 准确率 {accuracy:.4f} {'✅' if target_achieved else '❌'}")
            else:
                print("   性能指标: ❌ 未找到性能结果")
            
            coverage = verification_result['class_coverage']
            coverage_ratio = coverage.get('coverage_ratio', 0) * 100
            print(f"   类别覆盖: {coverage_ratio:.1f}%")
            
            output_check = verification_result['output_verification']
            output_found = output_check.get('prediction_files_found', False)
            print(f"   输出验证: {'✅ 找到预测输出' if output_found else '❌ 未找到输出'}")
        
        print("")

    def verify_speed_prediction_functionality(self):
        """验证速度预测功能"""
        print("\n🚄 步骤2: 验证速度预测功能")
        print("=" * 60)

        verification_result = {
            'implementation_status': 'unknown',
            'model_found': False,
            'training_data_analysis': {},
            'performance_metrics': {},
            'prediction_range': {},
            'output_verification': {},
            'issues': []
        }

        try:
            # 1. 检查速度预测实现
            implementation_status = self._check_speed_prediction_implementation()
            verification_result['implementation_status'] = implementation_status

            # 2. 分析训练数据
            if implementation_status != 'not_implemented':
                data_analysis = self._analyze_speed_prediction_data()
                verification_result['training_data_analysis'] = data_analysis

                # 3. 检查模型性能
                performance = self._check_speed_prediction_performance()
                verification_result['performance_metrics'] = performance

                # 4. 验证预测范围
                prediction_range = self._verify_speed_prediction_range()
                verification_result['prediction_range'] = prediction_range

                # 5. 检查输出
                output_check = self._verify_speed_prediction_output()
                verification_result['output_verification'] = output_check

            self.results['speed_prediction'] = verification_result

            # 显示验证结果
            self._display_speed_prediction_results(verification_result)

            return verification_result

        except Exception as e:
            print(f"❌ 速度预测功能验证失败: {str(e)}")
            verification_result['issues'].append(f"验证过程异常: {str(e)}")
            self.results['speed_prediction'] = verification_result
            return verification_result

    def _check_speed_prediction_implementation(self):
        """检查速度预测实现"""
        print("🔍 检查速度预测实现...")

        implementation_indicators = [
            'speed_prediction',
            'predict_speed',
            'speed_kmh',
            'velocity_prediction',
            'RegressionModel'
        ]

        found_implementations = []
        main_files = [
            'unified_vibration_analysis.py',
            'ml_analysis.py',
            'regression_models.py'
        ]

        for file_path in main_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    for indicator in implementation_indicators:
                        if indicator in content:
                            found_implementations.append({
                                'file': file_path,
                                'indicator': indicator
                            })

                except Exception as e:
                    continue

        if found_implementations:
            print(f"   ✅ 发现速度预测实现: {len(found_implementations)} 个指标")
            return 'implemented'
        else:
            print("   ❌ 未发现速度预测实现")
            return 'not_implemented'

    def _analyze_speed_prediction_data(self):
        """分析速度预测训练数据"""
        print("📊 分析速度预测训练数据...")

        data_analysis = {
            'feature_file_found': False,
            'speed_column': None,
            'speed_distribution': {},
            'data_quality': {},
            'sample_count': 0
        }

        # 查找特征文件
        feature_files = [
            'combined_features.csv',
            './ml/combined_features_clean.csv'
        ]

        features_df = None

        for file_path in feature_files:
            if os.path.exists(file_path):
                try:
                    features_df = pd.read_csv(file_path)
                    data_analysis['feature_file_found'] = True
                    break
                except:
                    continue

        if features_df is not None:
            data_analysis['sample_count'] = len(features_df)

            # 查找速度相关列
            speed_columns = [col for col in features_df.columns
                           if any(keyword in col.lower() for keyword in ['speed', '速度', 'kmh', 'km_h', 'velocity'])]

            if speed_columns:
                main_speed_col = speed_columns[0]
                data_analysis['speed_column'] = main_speed_col

                # 分析速度分布
                speed_data = features_df[main_speed_col]
                data_analysis['speed_distribution'] = {
                    'min': float(speed_data.min()),
                    'max': float(speed_data.max()),
                    'mean': float(speed_data.mean()),
                    'std': float(speed_data.std())
                }

                # 数据质量分析
                missing_count = speed_data.isnull().sum()
                data_analysis['data_quality'] = {
                    'missing_values': int(missing_count),
                    'missing_ratio': float(missing_count / len(features_df))
                }

                print(f"   ✅ 发现速度列: {main_speed_col}")
                print(f"   📊 速度范围: {data_analysis['speed_distribution']['min']:.1f} - {data_analysis['speed_distribution']['max']:.1f} km/h")
                print(f"   📈 平均速度: {data_analysis['speed_distribution']['mean']:.1f} ± {data_analysis['speed_distribution']['std']:.1f} km/h")
            else:
                print("   ❌ 未发现速度相关列")

        return data_analysis

    def _check_speed_prediction_performance(self):
        """检查速度预测性能"""
        print("📈 检查速度预测性能...")

        performance = {
            'model_results_found': False,
            'r2_score': None,
            'mae': None,
            'rmse': None,
            'target_achieved': False
        }

        # 查找模型结果文件
        result_files = [
            'speed_prediction_results.json',
            'regression_results.json',
            'ml_results.json'
        ]

        for file_path in result_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        results = json.load(f)

                    # 查找速度预测相关结果
                    speed_keys = ['speed_prediction', 'speed', 'velocity']
                    for key in speed_keys:
                        if key in results:
                            speed_results = results[key]
                            performance['model_results_found'] = True

                            if 'r2_score' in speed_results or 'r2' in speed_results:
                                performance['r2_score'] = speed_results.get('r2_score') or speed_results.get('r2')

                            if 'mae' in speed_results:
                                performance['mae'] = speed_results['mae']

                            if 'rmse' in speed_results:
                                performance['rmse'] = speed_results['rmse']

                            break

                    if performance['model_results_found']:
                        break

                except Exception as e:
                    continue

        if performance['model_results_found']:
            r2 = performance.get('r2_score')
            mae = performance.get('mae')

            if r2 is not None:
                target_r2 = self.performance_targets['speed_prediction']['r2']
                target_mae = self.performance_targets['speed_prediction']['mae']

                r2_achieved = r2 >= target_r2
                mae_achieved = mae <= target_mae if mae is not None else True
                performance['target_achieved'] = r2_achieved and mae_achieved

                print(f"   📊 R²分数: {r2:.4f}")
                if mae is not None:
                    print(f"   📊 MAE: {mae:.4f}")
                print(f"   🎯 目标达成: {'✅' if performance['target_achieved'] else '❌'}")
        else:
            print("   ❌ 未找到速度预测性能结果")

        return performance

    def _verify_speed_prediction_range(self):
        """验证速度预测范围"""
        print("🔍 验证速度预测范围...")

        range_verification = {
            'expected_range': [40, 100],
            'actual_range': None,
            'coverage_adequate': False
        }

        # 从数据分析获取速度范围
        data_analysis = self.results.get('speed_prediction', {}).get('training_data_analysis', {})
        speed_dist = data_analysis.get('speed_distribution', {})

        if speed_dist:
            actual_min = speed_dist.get('min', 0)
            actual_max = speed_dist.get('max', 0)
            range_verification['actual_range'] = [actual_min, actual_max]

            # 检查是否覆盖预期范围
            expected_min, expected_max = range_verification['expected_range']
            coverage_adequate = (actual_min <= expected_min + 10) and (actual_max >= expected_max - 10)
            range_verification['coverage_adequate'] = coverage_adequate

            print(f"   📊 实际范围: {actual_min:.1f} - {actual_max:.1f} km/h")
            print(f"   🎯 预期范围: {expected_min} - {expected_max} km/h")
            print(f"   ✅ 覆盖充分: {'是' if coverage_adequate else '否'}")
        else:
            print("   ❌ 无法获取速度范围信息")

        return range_verification

    def _verify_speed_prediction_output(self):
        """验证速度预测输出"""
        print("📤 验证速度预测输出...")

        output_verification = {
            'prediction_files_found': False,
            'output_format_correct': False,
            'error_analysis_present': False,
            'visualization_found': False
        }

        # 查找预测输出文件
        output_files = [
            'speed_predictions.csv',
            'regression_predictions.csv',
            './analysis_results/speed_prediction_results.csv'
        ]

        for file_path in output_files:
            if os.path.exists(file_path):
                try:
                    predictions_df = pd.read_csv(file_path)
                    output_verification['prediction_files_found'] = True

                    # 检查输出格式
                    required_columns = ['predicted', 'actual']
                    if any(col in predictions_df.columns for col in required_columns):
                        output_verification['output_format_correct'] = True

                    print(f"   ✅ 找到预测输出: {file_path}")
                    break

                except Exception as e:
                    continue

        # 查找可视化文件
        viz_files = glob.glob('*speed*prediction*.png') + glob.glob('*speed*scatter*.png')
        if viz_files:
            output_verification['visualization_found'] = True
            print(f"   ✅ 找到可视化文件: {len(viz_files)} 个")

        return output_verification

    def _display_speed_prediction_results(self, verification_result):
        """显示速度预测验证结果"""
        print("\n📋 速度预测功能验证结果:")

        implementation = verification_result['implementation_status']
        print(f"   实现状态: {'✅ 已实现' if implementation == 'implemented' else '❌ 未实现'}")

        if implementation == 'implemented':
            data_analysis = verification_result['training_data_analysis']
            if data_analysis.get('feature_file_found'):
                sample_count = data_analysis.get('sample_count', 0)
                speed_range = data_analysis.get('speed_distribution', {})
                if speed_range:
                    print(f"   训练数据: {sample_count} 个样本, 速度范围 {speed_range.get('min', 0):.1f}-{speed_range.get('max', 0):.1f} km/h")

            performance = verification_result['performance_metrics']
            if performance.get('model_results_found'):
                r2 = performance.get('r2_score')
                target_achieved = performance.get('target_achieved', False)
                if r2 is not None:
                    print(f"   性能指标: R² {r2:.4f} {'✅' if target_achieved else '❌'}")
            else:
                print("   性能指标: ❌ 未找到性能结果")

            range_check = verification_result['prediction_range']
            coverage_adequate = range_check.get('coverage_adequate', False)
            print(f"   预测范围: {'✅ 覆盖充分' if coverage_adequate else '❌ 覆盖不足'}")

            output_check = verification_result['output_verification']
            output_found = output_check.get('prediction_files_found', False)
            print(f"   输出验证: {'✅ 找到预测输出' if output_found else '❌ 未找到输出'}")

        print("")

    def verify_weight_prediction_functionality(self):
        """验证轴重预测功能"""
        print("\n⚖️  步骤3: 验证轴重预测功能")
        print("=" * 60)

        verification_result = {
            'implementation_status': 'unknown',
            'model_found': False,
            'training_data_analysis': {},
            'performance_metrics': {},
            'weight_range': {},
            'output_verification': {},
            'issues': []
        }

        try:
            # 1. 检查轴重预测实现
            implementation_status = self._check_weight_prediction_implementation()
            verification_result['implementation_status'] = implementation_status

            # 2. 分析训练数据
            if implementation_status != 'not_implemented':
                data_analysis = self._analyze_weight_prediction_data()
                verification_result['training_data_analysis'] = data_analysis

                # 3. 检查模型性能
                performance = self._check_weight_prediction_performance()
                verification_result['performance_metrics'] = performance

                # 4. 验证重量范围
                weight_range = self._verify_weight_prediction_range()
                verification_result['weight_range'] = weight_range

                # 5. 检查输出
                output_check = self._verify_weight_prediction_output()
                verification_result['output_verification'] = output_check

            self.results['weight_prediction'] = verification_result

            # 显示验证结果
            self._display_weight_prediction_results(verification_result)

            return verification_result

        except Exception as e:
            print(f"❌ 轴重预测功能验证失败: {str(e)}")
            verification_result['issues'].append(f"验证过程异常: {str(e)}")
            self.results['weight_prediction'] = verification_result
            return verification_result

    def _check_weight_prediction_implementation(self):
        """检查轴重预测实现"""
        print("🔍 检查轴重预测实现...")

        implementation_indicators = [
            'weight_prediction',
            'predict_weight',
            'axle_weight',
            'load_tons',
            'weight_tons'
        ]

        found_implementations = []
        main_files = [
            'unified_vibration_analysis.py',
            'ml_analysis.py',
            'regression_models.py'
        ]

        for file_path in main_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    for indicator in implementation_indicators:
                        if indicator in content:
                            found_implementations.append({
                                'file': file_path,
                                'indicator': indicator
                            })

                except Exception as e:
                    continue

        if found_implementations:
            print(f"   ✅ 发现轴重预测实现: {len(found_implementations)} 个指标")
            return 'implemented'
        else:
            print("   ❌ 未发现轴重预测实现")
            return 'not_implemented'

    def _analyze_weight_prediction_data(self):
        """分析轴重预测训练数据"""
        print("📊 分析轴重预测训练数据...")

        data_analysis = {
            'feature_file_found': False,
            'weight_column': None,
            'weight_distribution': {},
            'weight_categories': {},
            'data_quality': {},
            'sample_count': 0
        }

        # 查找特征文件
        feature_files = [
            'combined_features.csv',
            './ml/combined_features_clean.csv'
        ]

        features_df = None

        for file_path in feature_files:
            if os.path.exists(file_path):
                try:
                    features_df = pd.read_csv(file_path)
                    data_analysis['feature_file_found'] = True
                    break
                except:
                    continue

        if features_df is not None:
            data_analysis['sample_count'] = len(features_df)

            # 查找重量相关列
            weight_columns = [col for col in features_df.columns
                            if any(keyword in col.lower() for keyword in ['weight', '重量', 'tons', '吨', 'load'])]

            if weight_columns:
                main_weight_col = weight_columns[0]
                data_analysis['weight_column'] = main_weight_col

                # 分析重量分布
                weight_data = features_df[main_weight_col]
                data_analysis['weight_distribution'] = {
                    'min': float(weight_data.min()),
                    'max': float(weight_data.max()),
                    'mean': float(weight_data.mean()),
                    'std': float(weight_data.std())
                }

                # 分析重量类别
                weight_categories = {}
                for weight in weight_data.unique():
                    count = len(weight_data[weight_data == weight])
                    weight_categories[str(weight)] = count

                data_analysis['weight_categories'] = weight_categories

                # 数据质量分析
                missing_count = weight_data.isnull().sum()
                data_analysis['data_quality'] = {
                    'missing_values': int(missing_count),
                    'unique_weights': int(weight_data.nunique()),
                    'missing_ratio': float(missing_count / len(features_df))
                }

                print(f"   ✅ 发现重量列: {main_weight_col}")
                print(f"   📊 重量范围: {data_analysis['weight_distribution']['min']:.1f} - {data_analysis['weight_distribution']['max']:.1f} 吨")
                print(f"   📈 平均重量: {data_analysis['weight_distribution']['mean']:.1f} ± {data_analysis['weight_distribution']['std']:.1f} 吨")
                print(f"   🔢 重量类别: {data_analysis['data_quality']['unique_weights']} 个")
            else:
                print("   ❌ 未发现重量相关列")

        return data_analysis

    def _check_weight_prediction_performance(self):
        """检查轴重预测性能"""
        print("📈 检查轴重预测性能...")

        performance = {
            'model_results_found': False,
            'r2_score': None,
            'mae': None,
            'rmse': None,
            'target_achieved': False
        }

        # 查找模型结果文件
        result_files = [
            'weight_prediction_results.json',
            'axle_weight_results.json',
            'regression_results.json',
            'ml_results.json'
        ]

        for file_path in result_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        results = json.load(f)

                    # 查找轴重预测相关结果
                    weight_keys = ['weight_prediction', 'axle_weight', 'weight', 'load']
                    for key in weight_keys:
                        if key in results:
                            weight_results = results[key]
                            performance['model_results_found'] = True

                            if 'r2_score' in weight_results or 'r2' in weight_results:
                                performance['r2_score'] = weight_results.get('r2_score') or weight_results.get('r2')

                            if 'mae' in weight_results:
                                performance['mae'] = weight_results['mae']

                            if 'rmse' in weight_results:
                                performance['rmse'] = weight_results['rmse']

                            break

                    if performance['model_results_found']:
                        break

                except Exception as e:
                    continue

        if performance['model_results_found']:
            r2 = performance.get('r2_score')
            mae = performance.get('mae')

            if r2 is not None:
                target_r2 = self.performance_targets['weight_prediction']['r2']
                target_mae = self.performance_targets['weight_prediction']['mae']

                r2_achieved = r2 >= target_r2
                mae_achieved = mae <= target_mae if mae is not None else True
                performance['target_achieved'] = r2_achieved and mae_achieved

                print(f"   📊 R²分数: {r2:.4f}")
                if mae is not None:
                    print(f"   📊 MAE: {mae:.4f}")
                print(f"   🎯 目标达成: {'✅' if performance['target_achieved'] else '❌'}")
        else:
            print("   ❌ 未找到轴重预测性能结果")

        return performance

    def _verify_weight_prediction_range(self):
        """验证轴重预测范围"""
        print("🔍 验证轴重预测范围...")

        range_verification = {
            'expected_categories': ['轻型车辆(<5吨)', '中型车辆(5-20吨)', '重型车辆(20-50吨)', '超重车辆(>50吨)'],
            'actual_range': None,
            'category_coverage': {}
        }

        # 从数据分析获取重量范围
        data_analysis = self.results.get('weight_prediction', {}).get('training_data_analysis', {})
        weight_dist = data_analysis.get('weight_distribution', {})
        weight_categories = data_analysis.get('weight_categories', {})

        if weight_dist:
            actual_min = weight_dist.get('min', 0)
            actual_max = weight_dist.get('max', 0)
            range_verification['actual_range'] = [actual_min, actual_max]

            # 分析类别覆盖
            light_count = sum(1 for w, c in weight_categories.items() if float(w) < 5)
            medium_count = sum(1 for w, c in weight_categories.items() if 5 <= float(w) < 20)
            heavy_count = sum(1 for w, c in weight_categories.items() if 20 <= float(w) < 50)
            super_heavy_count = sum(1 for w, c in weight_categories.items() if float(w) >= 50)

            range_verification['category_coverage'] = {
                'light': light_count,
                'medium': medium_count,
                'heavy': heavy_count,
                'super_heavy': super_heavy_count
            }

            print(f"   📊 实际范围: {actual_min:.1f} - {actual_max:.1f} 吨")
            print(f"   📋 类别覆盖: 轻型{light_count}个, 中型{medium_count}个, 重型{heavy_count}个, 超重{super_heavy_count}个")
        else:
            print("   ❌ 无法获取重量范围信息")

        return range_verification

    def _verify_weight_prediction_output(self):
        """验证轴重预测输出"""
        print("📤 验证轴重预测输出...")

        output_verification = {
            'prediction_files_found': False,
            'output_format_correct': False,
            'unit_verification': False,
            'precision_check': False
        }

        # 查找预测输出文件
        output_files = [
            'weight_predictions.csv',
            'axle_weight_predictions.csv',
            './analysis_results/weight_prediction_results.csv'
        ]

        for file_path in output_files:
            if os.path.exists(file_path):
                try:
                    predictions_df = pd.read_csv(file_path)
                    output_verification['prediction_files_found'] = True

                    # 检查输出格式
                    required_columns = ['predicted', 'actual']
                    if any(col in predictions_df.columns for col in required_columns):
                        output_verification['output_format_correct'] = True

                    # 检查单位和精度
                    if 'predicted' in predictions_df.columns:
                        pred_values = predictions_df['predicted']
                        # 检查是否为合理的重量值（吨）
                        if pred_values.min() >= 0 and pred_values.max() <= 200:
                            output_verification['unit_verification'] = True

                        # 检查精度（小数位数）
                        decimal_places = pred_values.apply(lambda x: len(str(x).split('.')[-1]) if '.' in str(x) else 0)
                        if decimal_places.max() <= 3:  # 最多3位小数
                            output_verification['precision_check'] = True

                    print(f"   ✅ 找到预测输出: {file_path}")
                    break

                except Exception as e:
                    continue

        return output_verification

    def _display_weight_prediction_results(self, verification_result):
        """显示轴重预测验证结果"""
        print("\n📋 轴重预测功能验证结果:")

        implementation = verification_result['implementation_status']
        print(f"   实现状态: {'✅ 已实现' if implementation == 'implemented' else '❌ 未实现'}")

        if implementation == 'implemented':
            data_analysis = verification_result['training_data_analysis']
            if data_analysis.get('feature_file_found'):
                sample_count = data_analysis.get('sample_count', 0)
                weight_range = data_analysis.get('weight_distribution', {})
                unique_weights = data_analysis.get('data_quality', {}).get('unique_weights', 0)
                if weight_range:
                    print(f"   训练数据: {sample_count} 个样本, {unique_weights} 个重量类别")
                    print(f"   重量范围: {weight_range.get('min', 0):.1f}-{weight_range.get('max', 0):.1f} 吨")

            performance = verification_result['performance_metrics']
            if performance.get('model_results_found'):
                r2 = performance.get('r2_score')
                target_achieved = performance.get('target_achieved', False)
                if r2 is not None:
                    print(f"   性能指标: R² {r2:.4f} {'✅' if target_achieved else '❌'}")
            else:
                print("   性能指标: ❌ 未找到性能结果")

            range_check = verification_result['weight_range']
            category_coverage = range_check.get('category_coverage', {})
            total_categories = sum(category_coverage.values())
            print(f"   重量覆盖: {total_categories} 个重量类别")

            output_check = verification_result['output_verification']
            output_found = output_check.get('prediction_files_found', False)
            unit_correct = output_check.get('unit_verification', False)
            print(f"   输出验证: {'✅ 找到预测输出' if output_found else '❌ 未找到输出'}")
            if output_found:
                print(f"   单位精度: {'✅ 正确' if unit_correct else '❌ 异常'}")

        print("")

    def verify_model_integration_and_output(self):
        """验证模型集成和输出"""
        print("\n🔗 步骤4: 验证模型集成和输出")
        print("=" * 60)

        integration_result = {
            'unified_workflow': False,
            'data_optimization_integration': False,
            'performance_visualization': False,
            'comprehensive_output': False,
            'error_analysis': False,
            'confidence_intervals': False
        }

        try:
            # 1. 检查统一工作流
            unified_workflow = self._check_unified_workflow()
            integration_result['unified_workflow'] = unified_workflow

            # 2. 检查数据集优化集成
            data_optimization = self._check_data_optimization_integration()
            integration_result['data_optimization_integration'] = data_optimization

            # 3. 检查性能可视化
            visualization = self._check_performance_visualization()
            integration_result['performance_visualization'] = visualization

            # 4. 检查综合输出
            comprehensive_output = self._check_comprehensive_output()
            integration_result['comprehensive_output'] = comprehensive_output

            # 5. 检查误差分析
            error_analysis = self._check_error_analysis()
            integration_result['error_analysis'] = error_analysis

            self.results['model_integration'] = integration_result

            # 显示验证结果
            self._display_integration_results(integration_result)

            return integration_result

        except Exception as e:
            print(f"❌ 模型集成验证失败: {str(e)}")
            return integration_result

    def _check_unified_workflow(self):
        """检查统一工作流"""
        print("🔍 检查统一工作流...")

        # 检查主程序是否包含所有三个预测任务
        main_file = 'unified_vibration_analysis.py'

        if os.path.exists(main_file):
            try:
                with open(main_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查是否包含三个预测任务
                axle_found = any(keyword in content for keyword in ['axle_classification', 'axle_type', '轴型'])
                speed_found = any(keyword in content for keyword in ['speed_prediction', 'speed_kmh', '速度'])
                weight_found = any(keyword in content for keyword in ['weight_prediction', 'axle_weight', '轴重'])

                if axle_found and speed_found and weight_found:
                    print("   ✅ 统一工作流包含所有三个预测任务")
                    return True
                else:
                    missing = []
                    if not axle_found: missing.append('轴型分类')
                    if not speed_found: missing.append('速度预测')
                    if not weight_found: missing.append('轴重预测')
                    print(f"   ❌ 统一工作流缺少: {', '.join(missing)}")
                    return False

            except Exception as e:
                print(f"   ❌ 主程序检查失败: {str(e)}")
                return False
        else:
            print("   ❌ 未找到主程序文件")
            return False

    def _check_data_optimization_integration(self):
        """检查数据集优化集成"""
        print("🔍 检查数据集优化集成...")

        # 检查是否使用了优化后的数据
        optimization_files = [
            'dataset_optimization.py',
            'optimization_manager.py'
        ]

        integration_found = False

        for file_path in optimization_files:
            if os.path.exists(file_path):
                integration_found = True
                break

        if integration_found:
            # 检查主程序是否调用了优化功能
            main_file = 'unified_vibration_analysis.py'
            if os.path.exists(main_file):
                try:
                    with open(main_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    optimization_keywords = [
                        'dataset_optimization',
                        'optimization_manager',
                        'OptimizationManager',
                        'DatasetOptimizer'
                    ]

                    if any(keyword in content for keyword in optimization_keywords):
                        print("   ✅ 数据集优化已集成到主工作流")
                        return True
                    else:
                        print("   ⚠️  数据集优化模块存在但未集成")
                        return False

                except Exception as e:
                    print(f"   ❌ 集成检查失败: {str(e)}")
                    return False
            else:
                print("   ❌ 未找到主程序文件")
                return False
        else:
            print("   ❌ 未找到数据集优化模块")
            return False

    def _check_performance_visualization(self):
        """检查性能可视化"""
        print("🔍 检查性能可视化...")

        # 查找可视化文件
        viz_patterns = [
            '*prediction*scatter*.png',
            '*performance*comparison*.png',
            '*model*evaluation*.png',
            '*classification*report*.png'
        ]

        found_visualizations = []
        for pattern in viz_patterns:
            files = glob.glob(pattern)
            found_visualizations.extend(files)

        if found_visualizations:
            print(f"   ✅ 找到性能可视化文件: {len(found_visualizations)} 个")

            # 检查是否包含学术质量图表
            academic_quality_indicators = []
            for viz_file in found_visualizations:
                if any(keyword in viz_file.lower() for keyword in ['300dpi', 'high_quality', 'academic']):
                    academic_quality_indicators.append(viz_file)

            if academic_quality_indicators:
                print("   ✅ 包含学术质量图表")
            else:
                print("   ⚠️  未明确标识学术质量")

            return True
        else:
            print("   ❌ 未找到性能可视化文件")
            return False

    def _check_comprehensive_output(self):
        """检查综合输出"""
        print("🔍 检查综合输出...")

        # 查找输出文件
        output_patterns = [
            '*results*.json',
            '*predictions*.csv',
            '*analysis*.csv',
            '*report*.md'
        ]

        found_outputs = []
        for pattern in output_patterns:
            files = glob.glob(pattern)
            found_outputs.extend(files)

        if found_outputs:
            print(f"   ✅ 找到输出文件: {len(found_outputs)} 个")

            # 检查是否包含所有三个预测任务的输出
            axle_output = any('axle' in f.lower() or 'classification' in f.lower() for f in found_outputs)
            speed_output = any('speed' in f.lower() or 'velocity' in f.lower() for f in found_outputs)
            weight_output = any('weight' in f.lower() or 'load' in f.lower() for f in found_outputs)

            coverage_count = sum([axle_output, speed_output, weight_output])
            print(f"   📊 预测任务输出覆盖: {coverage_count}/3")

            return coverage_count >= 2  # 至少覆盖2个任务
        else:
            print("   ❌ 未找到输出文件")
            return False

    def _check_error_analysis(self):
        """检查误差分析"""
        print("🔍 检查误差分析...")

        # 查找误差分析相关文件
        error_analysis_files = glob.glob('*error*.csv') + glob.glob('*residual*.csv') + glob.glob('*analysis*.json')

        if error_analysis_files:
            print(f"   ✅ 找到误差分析文件: {len(error_analysis_files)} 个")
            return True
        else:
            # 检查结果文件中是否包含误差分析
            result_files = glob.glob('*results*.json')

            for file_path in result_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        results = json.load(f)

                    # 查找误差分析指标
                    error_indicators = ['mae', 'rmse', 'mse', 'residuals', 'error_analysis']

                    def check_nested_dict(d):
                        if isinstance(d, dict):
                            for key, value in d.items():
                                if any(indicator in key.lower() for indicator in error_indicators):
                                    return True
                                if check_nested_dict(value):
                                    return True
                        return False

                    if check_nested_dict(results):
                        print("   ✅ 结果文件中包含误差分析")
                        return True

                except Exception as e:
                    continue

            print("   ❌ 未找到误差分析")
            return False

    def _display_integration_results(self, integration_result):
        """显示集成验证结果"""
        print("\n📋 模型集成和输出验证结果:")

        unified_workflow = integration_result.get('unified_workflow', False)
        print(f"   统一工作流: {'✅ 已实现' if unified_workflow else '❌ 未实现'}")

        data_optimization = integration_result.get('data_optimization_integration', False)
        print(f"   数据优化集成: {'✅ 已集成' if data_optimization else '❌ 未集成'}")

        visualization = integration_result.get('performance_visualization', False)
        print(f"   性能可视化: {'✅ 已实现' if visualization else '❌ 未实现'}")

        comprehensive_output = integration_result.get('comprehensive_output', False)
        print(f"   综合输出: {'✅ 已实现' if comprehensive_output else '❌ 未实现'}")

        error_analysis = integration_result.get('error_analysis', False)
        print(f"   误差分析: {'✅ 已实现' if error_analysis else '❌ 未实现'}")

        print("")

    def generate_comprehensive_functionality_report(self):
        """生成综合功能报告"""
        print("\n📝 生成综合功能报告...")

        report_lines = []
        report_lines.append("# 机器学习模型功能完整性验证报告")
        report_lines.append(f"生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")

        # 执行摘要
        report_lines.append("## 执行摘要")

        # 统计各功能实现状态
        axle_implemented = self.results.get('axle_classification', {}).get('implementation_status') == 'implemented'
        speed_implemented = self.results.get('speed_prediction', {}).get('implementation_status') == 'implemented'
        weight_implemented = self.results.get('weight_prediction', {}).get('implementation_status') == 'implemented'

        implemented_count = sum([axle_implemented, speed_implemented, weight_implemented])

        report_lines.append(f"- **功能实现**: {implemented_count}/3 个交通信息解析功能已实现")
        report_lines.append(f"  - 轴型分类: {'✅ 已实现' if axle_implemented else '❌ 未实现'}")
        report_lines.append(f"  - 速度预测: {'✅ 已实现' if speed_implemented else '❌ 未实现'}")
        report_lines.append(f"  - 轴重预测: {'✅ 已实现' if weight_implemented else '❌ 未实现'}")

        # 性能目标达成情况
        axle_target = self.results.get('axle_classification', {}).get('performance_metrics', {}).get('target_achieved', False)
        speed_target = self.results.get('speed_prediction', {}).get('performance_metrics', {}).get('target_achieved', False)
        weight_target = self.results.get('weight_prediction', {}).get('performance_metrics', {}).get('target_achieved', False)

        target_achieved_count = sum([axle_target, speed_target, weight_target])

        report_lines.append(f"- **性能目标**: {target_achieved_count}/3 个功能达到性能目标")

        # 模型集成状态
        integration = self.results.get('model_integration', {})
        unified_workflow = integration.get('unified_workflow', False)
        data_optimization = integration.get('data_optimization_integration', False)

        report_lines.append(f"- **系统集成**: {'✅ 统一工作流已实现' if unified_workflow else '❌ 统一工作流未实现'}")
        report_lines.append(f"- **数据优化**: {'✅ 已集成数据优化' if data_optimization else '❌ 未集成数据优化'}")
        report_lines.append("")

        # 详细功能分析
        self._add_detailed_analysis_to_report(report_lines)

        # 问题和建议
        self._add_recommendations_to_report(report_lines)

        # 保存报告
        report_content = "\n".join(report_lines)
        report_path = "ml_functionality_verification_report.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"   ✅ 综合功能报告已保存: {report_path}")

        return report_path

    def _add_detailed_analysis_to_report(self, report_lines):
        """添加详细分析到报告"""
        report_lines.append("## 详细功能分析")

        # 轴型分类分析
        report_lines.append("### 轴型分类功能")
        axle_result = self.results.get('axle_classification', {})

        if axle_result.get('implementation_status') == 'implemented':
            data_analysis = axle_result.get('training_data_analysis', {})
            performance = axle_result.get('performance_metrics', {})

            sample_count = data_analysis.get('sample_count', 0)
            class_count = data_analysis.get('data_quality', {}).get('unique_classes', 0)

            report_lines.append(f"- **实现状态**: ✅ 已实现")
            report_lines.append(f"- **训练数据**: {sample_count} 个样本，{class_count} 个轴型类别")

            if performance.get('model_results_found'):
                accuracy = performance.get('accuracy')
                if accuracy is not None:
                    report_lines.append(f"- **性能指标**: 准确率 {accuracy:.4f}")
                    target_achieved = performance.get('target_achieved', False)
                    report_lines.append(f"- **目标达成**: {'✅ 是' if target_achieved else '❌ 否'} (目标: >0.90)")
            else:
                report_lines.append(f"- **性能指标**: ❌ 未找到性能结果")
        else:
            report_lines.append(f"- **实现状态**: ❌ 未实现")

        report_lines.append("")

        # 速度预测分析
        report_lines.append("### 速度预测功能")
        speed_result = self.results.get('speed_prediction', {})

        if speed_result.get('implementation_status') == 'implemented':
            data_analysis = speed_result.get('training_data_analysis', {})
            performance = speed_result.get('performance_metrics', {})

            sample_count = data_analysis.get('sample_count', 0)
            speed_dist = data_analysis.get('speed_distribution', {})

            report_lines.append(f"- **实现状态**: ✅ 已实现")
            report_lines.append(f"- **训练数据**: {sample_count} 个样本")

            if speed_dist:
                speed_min = speed_dist.get('min', 0)
                speed_max = speed_dist.get('max', 0)
                report_lines.append(f"- **速度范围**: {speed_min:.1f} - {speed_max:.1f} km/h")

            if performance.get('model_results_found'):
                r2 = performance.get('r2_score')
                mae = performance.get('mae')
                if r2 is not None:
                    report_lines.append(f"- **性能指标**: R² {r2:.4f}")
                    if mae is not None:
                        report_lines.append(f"- **预测误差**: MAE {mae:.4f}")
                    target_achieved = performance.get('target_achieved', False)
                    report_lines.append(f"- **目标达成**: {'✅ 是' if target_achieved else '❌ 否'} (目标: R²>0.90)")
            else:
                report_lines.append(f"- **性能指标**: ❌ 未找到性能结果")
        else:
            report_lines.append(f"- **实现状态**: ❌ 未实现")

        report_lines.append("")

        # 轴重预测分析
        report_lines.append("### 轴重预测功能")
        weight_result = self.results.get('weight_prediction', {})

        if weight_result.get('implementation_status') == 'implemented':
            data_analysis = weight_result.get('training_data_analysis', {})
            performance = weight_result.get('performance_metrics', {})

            sample_count = data_analysis.get('sample_count', 0)
            weight_dist = data_analysis.get('weight_distribution', {})
            unique_weights = data_analysis.get('data_quality', {}).get('unique_weights', 0)

            report_lines.append(f"- **实现状态**: ✅ 已实现")
            report_lines.append(f"- **训练数据**: {sample_count} 个样本，{unique_weights} 个重量类别")

            if weight_dist:
                weight_min = weight_dist.get('min', 0)
                weight_max = weight_dist.get('max', 0)
                report_lines.append(f"- **重量范围**: {weight_min:.1f} - {weight_max:.1f} 吨")

            if performance.get('model_results_found'):
                r2 = performance.get('r2_score')
                mae = performance.get('mae')
                if r2 is not None:
                    report_lines.append(f"- **性能指标**: R² {r2:.4f}")
                    if mae is not None:
                        report_lines.append(f"- **预测误差**: MAE {mae:.4f}")
                    target_achieved = performance.get('target_achieved', False)
                    report_lines.append(f"- **目标达成**: {'✅ 是' if target_achieved else '❌ 否'} (目标: R²>0.85)")
            else:
                report_lines.append(f"- **性能指标**: ❌ 未找到性能结果")
        else:
            report_lines.append(f"- **实现状态**: ❌ 未实现")

        report_lines.append("")

    def _add_recommendations_to_report(self, report_lines):
        """添加建议到报告"""
        report_lines.append("## 问题和改进建议")

        recommendations = []

        # 基于验证结果生成建议
        axle_result = self.results.get('axle_classification', {})
        speed_result = self.results.get('speed_prediction', {})
        weight_result = self.results.get('weight_prediction', {})
        integration_result = self.results.get('model_integration', {})

        # 检查未实现的功能
        if axle_result.get('implementation_status') != 'implemented':
            recommendations.append("**高优先级**: 实现轴型分类功能，包括模型训练、预测和评估")

        if speed_result.get('implementation_status') != 'implemented':
            recommendations.append("**高优先级**: 实现速度预测功能，目标R²>0.90")

        if weight_result.get('implementation_status') != 'implemented':
            recommendations.append("**高优先级**: 实现轴重预测功能，目标R²>0.85")

        # 检查性能目标
        if axle_result.get('performance_metrics', {}).get('target_achieved') == False:
            recommendations.append("**中优先级**: 优化轴型分类模型性能，提升准确率至90%以上")

        if speed_result.get('performance_metrics', {}).get('target_achieved') == False:
            recommendations.append("**中优先级**: 优化速度预测模型性能，提升R²至0.90以上")

        if weight_result.get('performance_metrics', {}).get('target_achieved') == False:
            recommendations.append("**中优先级**: 优化轴重预测模型性能，提升R²至0.85以上")

        # 检查集成问题
        if not integration_result.get('unified_workflow', False):
            recommendations.append("**中优先级**: 建立统一的分析工作流，集成所有三个预测任务")

        if not integration_result.get('data_optimization_integration', False):
            recommendations.append("**中优先级**: 集成数据集优化功能到机器学习工作流")

        if not integration_result.get('performance_visualization', False):
            recommendations.append("**低优先级**: 完善性能可视化，生成学术质量图表")

        if recommendations:
            for i, rec in enumerate(recommendations):
                report_lines.append(f"{i+1}. {rec}")
        else:
            report_lines.append("✅ 所有功能已正确实现，无需改进")

        report_lines.append("")

    def run_complete_verification(self):
        """运行完整验证流程"""
        print("🚀 开始机器学习模型功能完整性验证")
        print("=" * 80)

        try:
            # 步骤1: 验证轴型分类功能
            self.verify_axle_classification_functionality()

            # 步骤2: 验证速度预测功能
            self.verify_speed_prediction_functionality()

            # 步骤3: 验证轴重预测功能
            self.verify_weight_prediction_functionality()

            # 步骤4: 验证模型集成和输出
            self.verify_model_integration_and_output()

            # 步骤5: 生成综合报告
            report_path = self.generate_comprehensive_functionality_report()

            # 保存验证结果
            results_path = "ml_functionality_verification_results.json"
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2, default=str)

            print("\n" + "=" * 80)
            print("✅ 机器学习模型功能验证完成！")
            print(f"📄 详细报告: {report_path}")
            print(f"📊 验证结果: {results_path}")

            return True

        except Exception as e:
            print(f"❌ 验证过程失败: {str(e)}")
            return False

def main():
    """主函数"""
    verifier = MLModelFunctionalityVerifier()
    success = verifier.run_complete_verification()

    if success:
        print("\n🎯 验证摘要:")

        # 显示关键发现
        axle_implemented = verifier.results.get('axle_classification', {}).get('implementation_status') == 'implemented'
        speed_implemented = verifier.results.get('speed_prediction', {}).get('implementation_status') == 'implemented'
        weight_implemented = verifier.results.get('weight_prediction', {}).get('implementation_status') == 'implemented'

        implemented_count = sum([axle_implemented, speed_implemented, weight_implemented])

        print(f"   🔧 功能实现: {implemented_count}/3 个交通信息解析功能")
        print(f"      轴型分类: {'✅' if axle_implemented else '❌'}")
        print(f"      速度预测: {'✅' if speed_implemented else '❌'}")
        print(f"      轴重预测: {'✅' if weight_implemented else '❌'}")

        # 性能目标达成
        axle_target = verifier.results.get('axle_classification', {}).get('performance_metrics', {}).get('target_achieved', False)
        speed_target = verifier.results.get('speed_prediction', {}).get('performance_metrics', {}).get('target_achieved', False)
        weight_target = verifier.results.get('weight_prediction', {}).get('performance_metrics', {}).get('target_achieved', False)

        target_achieved_count = sum([axle_target, speed_target, weight_target])

        print(f"   🎯 性能目标: {target_achieved_count}/3 个功能达到目标")

        # 系统集成
        integration = verifier.results.get('model_integration', {})
        unified_workflow = integration.get('unified_workflow', False)

        print(f"   🔗 系统集成: {'✅ 统一工作流' if unified_workflow else '❌ 需要集成'}")

        if implemented_count == 3 and target_achieved_count >= 2:
            print("   🎉 系统功能基本完整，性能良好")
        elif implemented_count >= 2:
            print("   ⚠️  系统功能基本实现，需要性能优化")
        else:
            print("   ❌ 系统功能不完整，需要重大改进")

        return True
    else:
        print("❌ 验证失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
