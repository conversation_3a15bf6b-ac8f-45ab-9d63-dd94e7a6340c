#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断unified_vibration_analysis.py数据处理问题
分析为什么系统只处理了传统格式数据而没有处理新格式数据

作者: AI Assistant
日期: 2024-12-22
"""

import os
import pandas as pd
import glob
import json
from pathlib import Path

def diagnose_current_state():
    """诊断当前系统状态"""
    print("🔍 振动信号分析系统数据处理问题诊断")
    print("="*80)
    
    diagnosis_results = {}
    
    # 1. 检查当前特征文件状态
    print("\n📋 1. 当前特征文件状态分析")
    print("-"*50)
    
    if os.path.exists('combined_features.csv'):
        df = pd.read_csv('combined_features.csv')
        print(f"✅ combined_features.csv 存在")
        print(f"   数据形状: {df.shape}")
        print(f"   样本数量: {len(df)}")
        print(f"   特征数量: {df.shape[1]}")
        
        # 检查数据源列
        if 'data_source' in df.columns:
            source_counts = df['data_source'].value_counts()
            print(f"   📊 数据源分布:")
            for source, count in source_counts.items():
                print(f"      {source}: {count} 样本")
            diagnosis_results['has_data_source_column'] = True
            diagnosis_results['data_sources'] = source_counts.to_dict()
        else:
            print(f"   ❌ 缺少 data_source 列")
            diagnosis_results['has_data_source_column'] = False
        
        # 检查关键列
        key_columns = ['speed_kmh', 'load_tons', 'axle_type']
        missing_columns = []
        for col in key_columns:
            if col not in df.columns:
                missing_columns.append(col)
        
        if missing_columns:
            print(f"   ⚠️  缺少关键列: {missing_columns}")
            # 检查替代列名
            alt_columns = {
                'load_tons': ['axle_load_tons', 'weight_tons'],
                'axle_type': ['axle_count', 'axles']
            }
            for missing_col in missing_columns:
                if missing_col in alt_columns:
                    alternatives = [col for col in alt_columns[missing_col] if col in df.columns]
                    if alternatives:
                        print(f"      找到替代列 {missing_col}: {alternatives}")
        
        diagnosis_results['current_samples'] = len(df)
        diagnosis_results['current_features'] = df.shape[1]
    else:
        print(f"❌ combined_features.csv 不存在")
        diagnosis_results['current_samples'] = 0
        diagnosis_results['current_features'] = 0
    
    # 2. 检查原始数据文件分布
    print("\n📁 2. 原始数据文件分布分析")
    print("-"*50)
    
    data_dir = './data'
    if os.path.exists(data_dir):
        # 统计所有CSV文件
        csv_files = glob.glob(os.path.join(data_dir, '**/*.csv'), recursive=True)
        
        # 分类文件
        new_format_files = []
        legacy_format_files = []
        other_files = []
        
        for file_path in csv_files:
            filename = os.path.basename(file_path)
            
            # 检测新格式文件
            if 'GW100001' in filename and 'AcceData' in filename:
                new_format_files.append(file_path)
            # 检测传统格式文件
            elif any(keyword in filename for keyword in ['acce_', 'sensor', '吨', '轴']):
                legacy_format_files.append(file_path)
            else:
                other_files.append(file_path)
        
        print(f"   📊 文件统计:")
        print(f"      总CSV文件: {len(csv_files)}")
        print(f"      新格式文件: {len(new_format_files)}")
        print(f"      传统格式文件: {len(legacy_format_files)}")
        print(f"      其他文件: {len(other_files)}")
        
        diagnosis_results['total_csv_files'] = len(csv_files)
        diagnosis_results['new_format_files'] = len(new_format_files)
        diagnosis_results['legacy_format_files'] = len(legacy_format_files)
        diagnosis_results['other_files'] = len(other_files)
        
        # 检查新格式文件样本
        if new_format_files:
            print(f"\n   📄 新格式文件样本:")
            for i, file_path in enumerate(new_format_files[:5]):
                print(f"      {i+1}. {os.path.basename(file_path)}")
        
        # 检查传统格式文件样本
        if legacy_format_files:
            print(f"\n   📄 传统格式文件样本:")
            for i, file_path in enumerate(legacy_format_files[:5]):
                print(f"      {i+1}. {os.path.basename(file_path)}")
    else:
        print(f"❌ 数据目录 {data_dir} 不存在")
        diagnosis_results['data_dir_exists'] = False
    
    # 3. 检查处理历史记录
    print("\n📝 3. 处理历史记录分析")
    print("-"*50)
    
    history_files = ['processed_files.json', 'processing_history.json']
    for history_file in history_files:
        if os.path.exists(history_file):
            try:
                with open(history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                
                print(f"✅ {history_file} 存在")
                print(f"   已处理文件数: {len(history)}")
                
                # 分析处理的文件类型
                new_format_processed = 0
                legacy_format_processed = 0
                
                for file_path in history.keys():
                    filename = os.path.basename(file_path)
                    if 'GW100001' in filename and 'AcceData' in filename:
                        new_format_processed += 1
                    elif any(keyword in filename for keyword in ['acce_', 'sensor', '吨', '轴']):
                        legacy_format_processed += 1
                
                print(f"   新格式已处理: {new_format_processed}")
                print(f"   传统格式已处理: {legacy_format_processed}")
                
                diagnosis_results[f'{history_file}_processed'] = {
                    'total': len(history),
                    'new_format': new_format_processed,
                    'legacy_format': legacy_format_processed
                }
                
            except Exception as e:
                print(f"❌ {history_file} 读取失败: {str(e)}")
        else:
            print(f"⚠️  {history_file} 不存在")
    
    # 4. 检查enhanced_data_expansion_processor状态
    print("\n🔧 4. enhanced_data_expansion_processor 状态检查")
    print("-"*50)
    
    try:
        from enhanced_data_expansion_processor import EnhancedDataExpansionProcessor
        print("✅ enhanced_data_expansion_processor 可导入")
        
        # 检查输出文件
        output_files = ['combined_features_expanded.csv', 'enhanced_data_expansion_report.json']
        for output_file in output_files:
            if os.path.exists(output_file):
                print(f"✅ {output_file} 存在")
                if output_file.endswith('.csv'):
                    df_expanded = pd.read_csv(output_file)
                    print(f"   数据形状: {df_expanded.shape}")
                    if 'data_source' in df_expanded.columns:
                        source_counts = df_expanded['data_source'].value_counts()
                        print(f"   数据源分布: {dict(source_counts)}")
            else:
                print(f"⚠️  {output_file} 不存在")
        
        diagnosis_results['expansion_processor_available'] = True
        
    except ImportError as e:
        print(f"❌ enhanced_data_expansion_processor 导入失败: {str(e)}")
        diagnosis_results['expansion_processor_available'] = False
    except Exception as e:
        print(f"❌ enhanced_data_expansion_processor 检查失败: {str(e)}")
        diagnosis_results['expansion_processor_available'] = False
    
    return diagnosis_results

def identify_root_causes(diagnosis_results):
    """识别根本原因"""
    print("\n🎯 5. 根本原因分析")
    print("-"*50)
    
    issues = []
    recommendations = []
    
    # 检查数据源问题
    if not diagnosis_results.get('has_data_source_column', False):
        issues.append("缺少data_source列标识数据来源")
        recommendations.append("修复数据合并逻辑，确保添加data_source列")
    
    # 检查样本数量问题
    current_samples = diagnosis_results.get('current_samples', 0)
    expected_samples = diagnosis_results.get('new_format_files', 0) + diagnosis_results.get('legacy_format_files', 0)
    
    if current_samples < expected_samples * 0.8:  # 如果处理的样本数少于预期的80%
        issues.append(f"样本数量不足: 当前{current_samples}个，预期{expected_samples}个")
        recommendations.append("检查文件过滤逻辑和数据处理流程")
    
    # 检查新格式数据处理
    data_sources = diagnosis_results.get('data_sources', {})
    if 'new_format' not in data_sources:
        issues.append("新格式数据未被处理")
        recommendations.append("检查enhanced_data_expansion_processor的调用和返回值处理")
    
    # 检查处理器可用性
    if not diagnosis_results.get('expansion_processor_available', False):
        issues.append("enhanced_data_expansion_processor不可用")
        recommendations.append("修复enhanced_data_expansion_processor导入问题")
    
    print("🔍 发现的问题:")
    for i, issue in enumerate(issues, 1):
        print(f"   {i}. {issue}")
    
    print("\n💡 修复建议:")
    for i, rec in enumerate(recommendations, 1):
        print(f"   {i}. {rec}")
    
    return issues, recommendations

def generate_fix_plan(issues, recommendations):
    """生成修复计划"""
    print("\n🛠️  6. 修复计划")
    print("-"*50)
    
    fix_plan = {
        "priority_1": [],
        "priority_2": [],
        "priority_3": []
    }
    
    # 高优先级：数据处理核心问题
    if "新格式数据未被处理" in str(issues):
        fix_plan["priority_1"].append({
            "task": "修复enhanced_data_expansion_processor调用逻辑",
            "description": "确保新格式数据被正确处理并返回",
            "files": ["unified_vibration_analysis.py"],
            "action": "修复extract_features_enhanced方法中的数据读取和合并逻辑"
        })
    
    if "缺少data_source列标识数据来源" in str(issues):
        fix_plan["priority_1"].append({
            "task": "添加数据源标识",
            "description": "确保所有处理的数据都有data_source列",
            "files": ["unified_vibration_analysis.py"],
            "action": "修复数据合并时的列标识逻辑"
        })
    
    # 中优先级：文件过滤和处理逻辑
    if "样本数量不足" in str(issues):
        fix_plan["priority_2"].append({
            "task": "优化文件过滤逻辑",
            "description": "确保所有符合条件的文件都被处理",
            "files": ["unified_vibration_analysis.py", "enhanced_data_expansion_processor.py"],
            "action": "检查并修复文件识别和过滤条件"
        })
    
    # 低优先级：环境和依赖问题
    if "enhanced_data_expansion_processor不可用" in str(issues):
        fix_plan["priority_3"].append({
            "task": "修复处理器导入问题",
            "description": "确保enhanced_data_expansion_processor可正常导入和使用",
            "files": ["enhanced_data_expansion_processor.py"],
            "action": "检查模块依赖和导入路径"
        })
    
    # 打印修复计划
    for priority, tasks in fix_plan.items():
        if tasks:
            print(f"\n{priority.upper()}:")
            for i, task in enumerate(tasks, 1):
                print(f"   {i}. {task['task']}")
                print(f"      描述: {task['description']}")
                print(f"      文件: {', '.join(task['files'])}")
                print(f"      操作: {task['action']}")
    
    return fix_plan

def main():
    """主函数"""
    # 执行诊断
    diagnosis_results = diagnose_current_state()
    
    # 识别根本原因
    issues, recommendations = identify_root_causes(diagnosis_results)
    
    # 生成修复计划
    fix_plan = generate_fix_plan(issues, recommendations)
    
    # 保存诊断报告
    report = {
        'diagnosis_results': diagnosis_results,
        'issues': issues,
        'recommendations': recommendations,
        'fix_plan': fix_plan,
        'timestamp': pd.Timestamp.now().isoformat()
    }
    
    with open('data_processing_diagnosis_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n💾 诊断报告已保存到: data_processing_diagnosis_report.json")
    
    # 总结
    print(f"\n📋 诊断总结")
    print("="*50)
    print(f"发现问题数: {len(issues)}")
    print(f"修复建议数: {len(recommendations)}")
    print(f"修复任务数: {sum(len(tasks) for tasks in fix_plan.values())}")
    
    if issues:
        print(f"\n⚠️  需要立即修复的关键问题:")
        for issue in issues[:3]:  # 显示前3个最重要的问题
            print(f"   • {issue}")
    else:
        print(f"\n✅ 未发现严重问题")
    
    return report

if __name__ == "__main__":
    main()
