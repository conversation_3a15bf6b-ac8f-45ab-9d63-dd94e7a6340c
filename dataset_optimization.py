#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集优化和模型性能提升模块
实现数据集划分优化、异常值检测和处理、模型性能提升

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import chinese_font_config  # 中文字体配置
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, StratifiedShuffleSplit
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import IsolationForest
from sklearn.svm import OneClassSVM
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
from sklearn.cluster import DBSCAN
import warnings
warnings.filterwarnings('ignore')

import os
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatasetOptimizer:
    """数据集优化器"""
    
    def __init__(self, output_dir: str = "optimization_results"):
        """
        初始化数据集优化器
        
        参数:
        output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 优化配置
        self.train_ratio = 0.70
        self.val_ratio = 0.15
        self.test_ratio = 0.15
        
        # 异常值检测阈值
        self.outlier_thresholds = {
            'statistical_sigma': 3.0,
            'iqr_factor': 1.5,
            'isolation_contamination': 0.1,
            'prediction_error_threshold': 2.0
        }
        
        # 性能目标
        self.performance_targets = {
            'speed_prediction': {'r2': 0.90, 'mae': 5.0},
            'axle_weight_prediction': {'r2': 0.85, 'mae': 2.0}
        }
        
        self.optimization_results = {}
        
        print(f"🔧 数据集优化器已初始化")
        print(f"   输出目录: {self.output_dir}")
        print(f"   数据划分: 训练集{self.train_ratio*100:.0f}% | 验证集{self.val_ratio*100:.0f}% | 测试集{self.test_ratio*100:.0f}%")
    
    def load_dataset(self, file_path: str) -> Optional[pd.DataFrame]:
        """
        加载数据集
        
        参数:
        file_path: 数据集文件路径
        
        返回:
        df: 数据框或None
        """
        try:
            if not os.path.exists(file_path):
                print(f"❌ 数据集文件不存在: {file_path}")
                return None
            
            df = pd.read_csv(file_path)
            print(f"✅ 数据集加载成功: {file_path}")
            print(f"   数据形状: {df.shape}")
            print(f"   列数: {len(df.columns)}")
            
            return df
            
        except Exception as e:
            print(f"❌ 数据集加载失败: {str(e)}")
            return None
    
    def analyze_data_distribution(self, df: pd.DataFrame, target_col: str) -> Dict[str, Any]:
        """
        分析数据分布
        
        参数:
        df: 数据框
        target_col: 目标列名
        
        返回:
        distribution_info: 分布信息
        """
        try:
            print(f"📊 分析数据分布...")
            
            distribution_info = {
                'total_samples': len(df),
                'feature_count': len(df.columns) - 1,
                'target_column': target_col,
                'missing_values': df.isnull().sum().sum(),
                'duplicate_rows': df.duplicated().sum()
            }
            
            # 目标变量分布分析
            if target_col in df.columns:
                target_data = df[target_col]
                
                if target_data.dtype in ['object', 'category']:
                    # 分类变量
                    distribution_info['target_type'] = 'categorical'
                    distribution_info['class_distribution'] = target_data.value_counts().to_dict()
                    distribution_info['class_balance'] = target_data.value_counts(normalize=True).to_dict()
                else:
                    # 数值变量
                    distribution_info['target_type'] = 'numerical'
                    distribution_info['target_stats'] = {
                        'mean': float(target_data.mean()),
                        'std': float(target_data.std()),
                        'min': float(target_data.min()),
                        'max': float(target_data.max()),
                        'median': float(target_data.median()),
                        'q25': float(target_data.quantile(0.25)),
                        'q75': float(target_data.quantile(0.75))
                    }
            
            # 特征列分析
            feature_cols = [col for col in df.columns if col != target_col]
            distribution_info['feature_stats'] = {}
            
            for col in feature_cols[:10]:  # 只分析前10个特征
                if df[col].dtype in ['int64', 'float64']:
                    distribution_info['feature_stats'][col] = {
                        'mean': float(df[col].mean()),
                        'std': float(df[col].std()),
                        'missing_ratio': float(df[col].isnull().mean())
                    }
            
            print(f"   总样本数: {distribution_info['total_samples']}")
            print(f"   特征数量: {distribution_info['feature_count']}")
            print(f"   缺失值: {distribution_info['missing_values']}")
            print(f"   重复行: {distribution_info['duplicate_rows']}")
            
            return distribution_info
            
        except Exception as e:
            print(f"❌ 数据分布分析失败: {str(e)}")
            return {}
    
    def stratified_split_dataset(self, df: pd.DataFrame, target_col: str) -> Dict[str, pd.DataFrame]:
        """
        分层抽样划分数据集
        
        参数:
        df: 数据框
        target_col: 目标列名
        
        返回:
        datasets: 划分后的数据集字典
        """
        try:
            print(f"🔄 执行分层抽样数据集划分...")
            
            X = df.drop(columns=[target_col])
            y = df[target_col]
            
            # 处理数值型目标变量的分层抽样
            if y.dtype in ['int64', 'float64']:
                # 将连续变量离散化用于分层
                n_bins = min(5, max(2, len(y.unique()) // 10))  # 确保至少2个bins
                try:
                    y_binned = pd.cut(y, bins=n_bins, labels=False, duplicates='drop')
                    # 检查每个bin是否有足够的样本
                    bin_counts = pd.Series(y_binned).value_counts()
                    if bin_counts.min() < 2:
                        # 如果某些bin样本太少，使用简单随机划分
                        print(f"   ⚠️  分层样本不足，使用随机划分")
                        stratify_var = None
                    else:
                        stratify_var = y_binned
                except Exception as e:
                    print(f"   ⚠️  分层划分失败，使用随机划分: {str(e)}")
                    stratify_var = None
            else:
                # 分类变量直接使用
                class_counts = y.value_counts()
                if class_counts.min() < 2:
                    print(f"   ⚠️  类别样本不足，使用随机划分")
                    stratify_var = None
                else:
                    stratify_var = y
            
            # 第一次划分：训练集 vs (验证集+测试集)
            if stratify_var is not None:
                X_train, X_temp, y_train, y_temp = train_test_split(
                    X, y,
                    test_size=(self.val_ratio + self.test_ratio),
                    random_state=42,
                    stratify=stratify_var
                )

                # 为第二次划分准备分层变量
                if len(stratify_var) == len(y):
                    temp_indices = y_temp.index
                    strat_temp = pd.Series(stratify_var).loc[temp_indices]
                else:
                    strat_temp = None

                # 第二次划分：验证集 vs 测试集
                val_test_ratio = self.val_ratio / (self.val_ratio + self.test_ratio)
                if strat_temp is not None and strat_temp.value_counts().min() >= 2:
                    X_val, X_test, y_val, y_test = train_test_split(
                        X_temp, y_temp,
                        test_size=(1 - val_test_ratio),
                        random_state=42,
                        stratify=strat_temp
                    )
                else:
                    X_val, X_test, y_val, y_test = train_test_split(
                        X_temp, y_temp,
                        test_size=(1 - val_test_ratio),
                        random_state=42
                    )
            else:
                # 随机划分
                X_train, X_temp, y_train, y_temp = train_test_split(
                    X, y,
                    test_size=(self.val_ratio + self.test_ratio),
                    random_state=42
                )

                # 第二次划分：验证集 vs 测试集
                val_test_ratio = self.val_ratio / (self.val_ratio + self.test_ratio)
                X_val, X_test, y_val, y_test = train_test_split(
                    X_temp, y_temp,
                    test_size=(1 - val_test_ratio),
                    random_state=42
                )
            
            # 重新组合数据集
            train_df = pd.concat([X_train, y_train], axis=1)
            val_df = pd.concat([X_val, y_val], axis=1)
            test_df = pd.concat([X_test, y_test], axis=1)
            
            datasets = {
                'train': train_df,
                'validation': val_df,
                'test': test_df
            }
            
            print(f"   训练集: {len(train_df)} 样本 ({len(train_df)/len(df)*100:.1f}%)")
            print(f"   验证集: {len(val_df)} 样本 ({len(val_df)/len(df)*100:.1f}%)")
            print(f"   测试集: {len(test_df)} 样本 ({len(test_df)/len(df)*100:.1f}%)")
            
            # 验证分布均衡性
            self.validate_split_balance(datasets, target_col)
            
            return datasets
            
        except Exception as e:
            print(f"❌ 数据集划分失败: {str(e)}")
            return {}
    
    def validate_split_balance(self, datasets: Dict[str, pd.DataFrame], target_col: str):
        """
        验证数据集划分的均衡性
        
        参数:
        datasets: 划分后的数据集
        target_col: 目标列名
        """
        try:
            print(f"🔍 验证数据集划分均衡性...")
            
            for split_name, df in datasets.items():
                target_data = df[target_col]
                
                if target_data.dtype in ['object', 'category']:
                    # 分类变量：检查类别分布
                    class_dist = target_data.value_counts(normalize=True)
                    print(f"   {split_name}集类别分布: {dict(class_dist)}")
                else:
                    # 数值变量：检查统计分布
                    stats = {
                        'mean': target_data.mean(),
                        'std': target_data.std(),
                        'median': target_data.median()
                    }
                    print(f"   {split_name}集统计: 均值={stats['mean']:.2f}, 标准差={stats['std']:.2f}")
            
        except Exception as e:
            print(f"⚠️  均衡性验证失败: {str(e)}")
    
    def detect_outliers_statistical(self, df: pd.DataFrame, target_col: str) -> Dict[str, Any]:
        """
        基于统计方法检测异常值
        
        参数:
        df: 数据框
        target_col: 目标列名
        
        返回:
        outlier_info: 异常值信息
        """
        try:
            print(f"🔍 执行统计方法异常值检测...")
            
            outlier_info = {
                'method': 'statistical',
                'outlier_indices': set(),
                'feature_outliers': {},
                'target_outliers': set()
            }
            
            # 数值型特征列
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            feature_cols = [col for col in numeric_cols if col != target_col]
            
            # 3σ准则检测特征异常值
            for col in feature_cols:
                data = df[col]
                mean_val = data.mean()
                std_val = data.std()
                
                if std_val > 0:
                    z_scores = np.abs((data - mean_val) / std_val)
                    outliers = df.index[z_scores > self.outlier_thresholds['statistical_sigma']]
                    
                    outlier_info['feature_outliers'][col] = list(outliers)
                    outlier_info['outlier_indices'].update(outliers)
            
            # IQR方法检测目标变量异常值
            if target_col in numeric_cols:
                target_data = df[target_col]
                Q1 = target_data.quantile(0.25)
                Q3 = target_data.quantile(0.75)
                IQR = Q3 - Q1
                
                lower_bound = Q1 - self.outlier_thresholds['iqr_factor'] * IQR
                upper_bound = Q3 + self.outlier_thresholds['iqr_factor'] * IQR
                
                target_outliers = df.index[
                    (target_data < lower_bound) | (target_data > upper_bound)
                ]
                
                outlier_info['target_outliers'] = set(target_outliers)
                outlier_info['outlier_indices'].update(target_outliers)
            
            outlier_count = len(outlier_info['outlier_indices'])
            outlier_ratio = outlier_count / len(df) * 100
            
            print(f"   检测到异常值: {outlier_count} 个 ({outlier_ratio:.2f}%)")
            
            return outlier_info
            
        except Exception as e:
            print(f"❌ 统计异常值检测失败: {str(e)}")
            return {}
    
    def detect_outliers_model_based(self, df: pd.DataFrame, target_col: str) -> Dict[str, Any]:
        """
        基于模型的异常值检测
        
        参数:
        df: 数据框
        target_col: 目标列名
        
        返回:
        outlier_info: 异常值信息
        """
        try:
            print(f"🔍 执行基于模型的异常值检测...")
            
            # 准备特征数据
            feature_cols = [col for col in df.columns if col != target_col]
            X = df[feature_cols].select_dtypes(include=[np.number])
            
            if X.empty:
                print(f"   ⚠️  没有数值型特征，跳过模型异常值检测")
                return {}
            
            # 标准化特征
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            outlier_info = {
                'method': 'model_based',
                'isolation_forest_outliers': set(),
                'one_class_svm_outliers': set(),
                'combined_outliers': set()
            }
            
            # Isolation Forest
            iso_forest = IsolationForest(
                contamination=self.outlier_thresholds['isolation_contamination'],
                random_state=42
            )
            iso_predictions = iso_forest.fit_predict(X_scaled)
            iso_outliers = df.index[iso_predictions == -1]
            outlier_info['isolation_forest_outliers'] = set(iso_outliers)
            
            # One-Class SVM
            oc_svm = OneClassSVM(nu=self.outlier_thresholds['isolation_contamination'])
            svm_predictions = oc_svm.fit_predict(X_scaled)
            svm_outliers = df.index[svm_predictions == -1]
            outlier_info['one_class_svm_outliers'] = set(svm_outliers)
            
            # 合并结果
            outlier_info['combined_outliers'] = (
                outlier_info['isolation_forest_outliers'] | 
                outlier_info['one_class_svm_outliers']
            )
            
            print(f"   Isolation Forest检测: {len(outlier_info['isolation_forest_outliers'])} 个异常值")
            print(f"   One-Class SVM检测: {len(outlier_info['one_class_svm_outliers'])} 个异常值")
            print(f"   合并结果: {len(outlier_info['combined_outliers'])} 个异常值")
            
            return outlier_info
            
        except Exception as e:
            print(f"❌ 基于模型的异常值检测失败: {str(e)}")
            return {}

    def detect_prediction_outliers(self, df: pd.DataFrame, target_col: str, model_predictions: np.ndarray) -> Dict[str, Any]:
        """
        基于预测误差检测异常值

        参数:
        df: 数据框
        target_col: 目标列名
        model_predictions: 模型预测值

        返回:
        outlier_info: 异常值信息
        """
        try:
            print(f"🔍 执行基于预测误差的异常值检测...")

            if target_col not in df.columns:
                print(f"   ⚠️  目标列 {target_col} 不存在")
                return {}

            y_true = df[target_col].values

            if len(y_true) != len(model_predictions):
                print(f"   ⚠️  真实值和预测值长度不匹配")
                return {}

            # 计算预测误差
            errors = np.abs(y_true - model_predictions)
            error_mean = np.mean(errors)
            error_std = np.std(errors)

            # 基于误差阈值检测异常值
            error_threshold = error_mean + self.outlier_thresholds['prediction_error_threshold'] * error_std
            outlier_indices = df.index[errors > error_threshold]

            outlier_info = {
                'method': 'prediction_error',
                'error_threshold': error_threshold,
                'error_mean': error_mean,
                'error_std': error_std,
                'outlier_indices': set(outlier_indices),
                'outlier_errors': errors[errors > error_threshold]
            }

            print(f"   误差阈值: {error_threshold:.4f}")
            print(f"   检测到异常值: {len(outlier_indices)} 个")

            return outlier_info

        except Exception as e:
            print(f"❌ 基于预测误差的异常值检测失败: {str(e)}")
            return {}

    def comprehensive_outlier_detection(self, df: pd.DataFrame, target_col: str) -> Dict[str, Any]:
        """
        综合异常值检测

        参数:
        df: 数据框
        target_col: 目标列名

        返回:
        comprehensive_outliers: 综合异常值信息
        """
        try:
            print(f"🔍 执行综合异常值检测...")

            # 统计方法检测
            statistical_outliers = self.detect_outliers_statistical(df, target_col)

            # 模型方法检测
            model_outliers = self.detect_outliers_model_based(df, target_col)

            # 合并所有异常值
            all_outliers = set()

            if statistical_outliers:
                all_outliers.update(statistical_outliers.get('outlier_indices', set()))

            if model_outliers:
                all_outliers.update(model_outliers.get('combined_outliers', set()))

            comprehensive_outliers = {
                'statistical_outliers': statistical_outliers,
                'model_outliers': model_outliers,
                'all_outliers': all_outliers,
                'outlier_count': len(all_outliers),
                'outlier_ratio': len(all_outliers) / len(df) * 100 if len(df) > 0 else 0
            }

            print(f"   综合检测结果: {len(all_outliers)} 个异常值 ({comprehensive_outliers['outlier_ratio']:.2f}%)")

            return comprehensive_outliers

        except Exception as e:
            print(f"❌ 综合异常值检测失败: {str(e)}")
            return {}

    def visualize_outliers(self, df: pd.DataFrame, target_col: str, outlier_info: Dict[str, Any], save_path: str = None):
        """
        可视化异常值分析

        参数:
        df: 数据框
        target_col: 目标列名
        outlier_info: 异常值信息
        save_path: 保存路径
        """
        try:
            print(f"📊 生成异常值可视化分析...")

            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

            # 获取异常值索引
            outlier_indices = outlier_info.get('all_outliers', set())
            normal_indices = set(df.index) - outlier_indices

            # 子图1：目标变量分布
            if target_col in df.columns:
                normal_values = df.loc[list(normal_indices), target_col] if normal_indices else []
                outlier_values = df.loc[list(outlier_indices), target_col] if outlier_indices else []

                if len(normal_values) > 0:
                    ax1.hist(normal_values, bins=30, alpha=0.7, label='正常值', color='skyblue')
                if len(outlier_values) > 0:
                    ax1.hist(outlier_values, bins=20, alpha=0.7, label='异常值', color='red')

                ax1.set_title(f'{target_col} 分布对比', fontsize=14, fontweight='bold')
                ax1.set_xlabel(target_col, fontsize=12)
                ax1.set_ylabel('频次', fontsize=12)
                ax1.legend()
                ax1.grid(True, alpha=0.3)

            # 子图2：异常值比例饼图
            outlier_count = len(outlier_indices)
            normal_count = len(normal_indices)

            sizes = [normal_count, outlier_count]
            labels = ['正常值', '异常值']
            colors = ['lightblue', 'lightcoral']

            ax2.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax2.set_title('异常值比例分布', fontsize=14, fontweight='bold')

            # 子图3：特征散点图（选择前两个数值特征）
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            feature_cols = [col for col in numeric_cols if col != target_col]

            if len(feature_cols) >= 2:
                x_col, y_col = feature_cols[0], feature_cols[1]

                if normal_indices:
                    ax3.scatter(df.loc[list(normal_indices), x_col],
                              df.loc[list(normal_indices), y_col],
                              alpha=0.6, label='正常值', color='blue', s=20)

                if outlier_indices:
                    ax3.scatter(df.loc[list(outlier_indices), x_col],
                              df.loc[list(outlier_indices), y_col],
                              alpha=0.8, label='异常值', color='red', s=30, marker='x')

                ax3.set_title(f'特征散点图: {x_col} vs {y_col}', fontsize=14, fontweight='bold')
                ax3.set_xlabel(x_col, fontsize=12)
                ax3.set_ylabel(y_col, fontsize=12)
                ax3.legend()
                ax3.grid(True, alpha=0.3)

            # 子图4：异常值检测方法对比
            methods = []
            counts = []

            if 'statistical_outliers' in outlier_info:
                stat_count = len(outlier_info['statistical_outliers'].get('outlier_indices', set()))
                methods.append('统计方法')
                counts.append(stat_count)

            if 'model_outliers' in outlier_info:
                model_count = len(outlier_info['model_outliers'].get('combined_outliers', set()))
                methods.append('模型方法')
                counts.append(model_count)

            if methods and counts:
                bars = ax4.bar(methods, counts, color=['lightgreen', 'orange'], alpha=0.7)
                ax4.set_title('不同检测方法结果对比', fontsize=14, fontweight='bold')
                ax4.set_ylabel('异常值数量', fontsize=12)

                # 添加数值标签
                for bar, count in zip(bars, counts):
                    height = bar.get_height()
                    ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                            f'{count}', ha='center', va='bottom', fontsize=12)

                ax4.grid(True, alpha=0.3)

            plt.suptitle('异常值检测分析报告', fontsize=16, fontweight='bold')
            plt.tight_layout()

            # 保存图表
            if save_path is None:
                save_path = self.output_dir / 'outlier_analysis.png'

            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"   ✅ 异常值可视化已保存: {save_path}")

        except Exception as e:
            print(f"❌ 异常值可视化失败: {str(e)}")

    def clean_dataset(self, df: pd.DataFrame, outlier_info: Dict[str, Any], strategy: str = 'remove') -> pd.DataFrame:
        """
        清理数据集

        参数:
        df: 数据框
        outlier_info: 异常值信息
        strategy: 清理策略 ('remove', 'cap', 'transform')

        返回:
        cleaned_df: 清理后的数据框
        """
        try:
            print(f"🧹 执行数据清理 (策略: {strategy})...")

            outlier_indices = outlier_info.get('all_outliers', set())

            if strategy == 'remove':
                # 删除异常值
                cleaned_df = df.drop(index=list(outlier_indices))
                print(f"   删除了 {len(outlier_indices)} 个异常值")

            elif strategy == 'cap':
                # 限制异常值（使用分位数）
                cleaned_df = df.copy()
                numeric_cols = df.select_dtypes(include=[np.number]).columns

                for col in numeric_cols:
                    Q1 = df[col].quantile(0.05)
                    Q3 = df[col].quantile(0.95)
                    cleaned_df[col] = np.clip(df[col], Q1, Q3)

                print(f"   限制了异常值到5%-95%分位数范围")

            elif strategy == 'transform':
                # 对数变换（仅适用于正值）
                cleaned_df = df.copy()
                numeric_cols = df.select_dtypes(include=[np.number]).columns

                for col in numeric_cols:
                    if (df[col] > 0).all():
                        cleaned_df[col] = np.log1p(df[col])

                print(f"   应用了对数变换")

            else:
                print(f"   ⚠️  未知清理策略: {strategy}，返回原数据")
                cleaned_df = df.copy()

            print(f"   清理前: {len(df)} 样本")
            print(f"   清理后: {len(cleaned_df)} 样本")

            return cleaned_df

        except Exception as e:
            print(f"❌ 数据清理失败: {str(e)}")
            return df

    def generate_optimization_report(self, dataset_name: str, original_df: pd.DataFrame,
                                   optimized_datasets: Dict[str, pd.DataFrame],
                                   outlier_info: Dict[str, Any]) -> str:
        """
        生成优化报告

        参数:
        dataset_name: 数据集名称
        original_df: 原始数据框
        optimized_datasets: 优化后的数据集
        outlier_info: 异常值信息

        返回:
        report_path: 报告文件路径
        """
        try:
            print(f"📝 生成数据集优化报告...")

            report_lines = []
            report_lines.append(f"# {dataset_name} 数据集优化报告")
            report_lines.append(f"生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_lines.append("")

            # 原始数据集信息
            report_lines.append("## 原始数据集信息")
            report_lines.append(f"- 总样本数: {len(original_df)}")
            report_lines.append(f"- 特征数量: {len(original_df.columns) - 1}")
            report_lines.append(f"- 缺失值: {original_df.isnull().sum().sum()}")
            report_lines.append(f"- 重复行: {original_df.duplicated().sum()}")
            report_lines.append("")

            # 异常值检测结果
            if outlier_info:
                report_lines.append("## 异常值检测结果")
                report_lines.append(f"- 检测到异常值: {outlier_info.get('outlier_count', 0)} 个")
                report_lines.append(f"- 异常值比例: {outlier_info.get('outlier_ratio', 0):.2f}%")

                if 'statistical_outliers' in outlier_info:
                    stat_count = len(outlier_info['statistical_outliers'].get('outlier_indices', set()))
                    report_lines.append(f"- 统计方法检测: {stat_count} 个")

                if 'model_outliers' in outlier_info:
                    model_count = len(outlier_info['model_outliers'].get('combined_outliers', set()))
                    report_lines.append(f"- 模型方法检测: {model_count} 个")

                report_lines.append("")

            # 数据集划分结果
            if optimized_datasets:
                report_lines.append("## 数据集划分结果")
                total_samples = sum(len(df) for df in optimized_datasets.values())

                for split_name, df in optimized_datasets.items():
                    ratio = len(df) / total_samples * 100
                    report_lines.append(f"- {split_name}集: {len(df)} 样本 ({ratio:.1f}%)")

                report_lines.append("")

            # 优化建议
            report_lines.append("## 优化建议")

            outlier_ratio = outlier_info.get('outlier_ratio', 0)
            if outlier_ratio > 10:
                report_lines.append("- ⚠️  异常值比例较高，建议进一步检查数据质量")
            elif outlier_ratio > 5:
                report_lines.append("- 💡 异常值比例适中，可考虑删除或修正")
            else:
                report_lines.append("- ✅ 异常值比例较低，数据质量良好")

            if len(original_df) < 1000:
                report_lines.append("- ⚠️  样本数量较少，建议收集更多数据")
            elif len(original_df) < 5000:
                report_lines.append("- 💡 样本数量适中，可进行模型训练")
            else:
                report_lines.append("- ✅ 样本数量充足，有利于模型训练")

            # 保存报告
            report_content = "\n".join(report_lines)
            report_path = self.output_dir / f"{dataset_name}_optimization_report.md"

            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)

            print(f"   ✅ 优化报告已保存: {report_path}")

            return str(report_path)

        except Exception as e:
            print(f"❌ 生成优化报告失败: {str(e)}")
            return ""
