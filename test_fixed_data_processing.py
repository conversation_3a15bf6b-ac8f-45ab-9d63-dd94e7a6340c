#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的unified_vibration_analysis.py数据处理功能
验证是否能够正确处理两种格式的振动数据

作者: AI Assistant
日期: 2024-12-22
"""

import os
import pandas as pd
import sys

def test_fixed_data_processing():
    """测试修复后的数据处理功能"""
    print("🧪 测试修复后的振动信号分析系统")
    print("="*80)
    print("🎯 目标：验证双格式数据处理修复效果")
    print("   - 确保系统能正确调用双格式处理逻辑")
    print("   - 验证新格式数据（22列CSV）被正确集成")
    print("   - 确认数据源标识正确添加")
    print("-"*80)
    
    try:
        # 导入修复后的系统
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        print("\n📦 步骤1：初始化分析器...")
        analyzer = UnifiedVibrationAnalysisSystem(data_dir='./data')
        
        # 配置测试参数
        analyzer.force_feature_extraction = False  # 使用现有扩展数据
        print("✅ 分析器初始化成功")
        
        print("\n📊 步骤2：运行特征提取...")
        features_df = analyzer.extract_features_from_data()
        
        if features_df is not None and not features_df.empty:
            print(f"✅ 特征提取成功")
            print(f"   数据形状: {features_df.shape}")
            
            # 验证数据源
            if 'data_source' in features_df.columns:
                source_counts = features_df['data_source'].value_counts()
                print(f"   📊 数据源分布:")
                for source, count in source_counts.items():
                    print(f"      {source}: {count} 样本")
                
                # 检查是否包含两种格式的数据
                has_new_format = 'new_format' in source_counts
                has_legacy_format = 'legacy_format' in source_counts
                
                print(f"\n🔍 格式支持验证:")
                print(f"   新格式数据: {'✅' if has_new_format else '❌'}")
                print(f"   传统格式数据: {'✅' if has_legacy_format else '❌'}")
                
                # 验证样本数量
                total_samples = len(features_df)
                print(f"   总样本数: {total_samples}")
                
                if total_samples >= 3000:
                    print("✅ 样本数量达到目标（≥3000）")
                    sample_check = True
                else:
                    print(f"⚠️  样本数量未达到目标（{total_samples} < 3000）")
                    sample_check = False
                
                # 验证特征数量
                feature_count = features_df.shape[1]
                print(f"   总特征数: {feature_count}")
                
                if feature_count >= 200:
                    print("✅ 特征数量丰富（≥200）")
                    feature_check = True
                else:
                    print(f"⚠️  特征数量较少（{feature_count} < 200）")
                    feature_check = False
                
                # 综合评估
                if has_new_format and has_legacy_format and sample_check and feature_check:
                    print("\n🎉 修复完全成功！系统已支持双格式数据处理")
                    return True
                elif has_new_format and sample_check:
                    print("\n✅ 修复基本成功，新格式数据已集成")
                    return True
                else:
                    print("\n⚠️  修复部分成功，仍需进一步优化")
                    return False
            else:
                print("❌ 特征数据中缺少data_source列")
                return False
        else:
            print("❌ 特征提取失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def verify_file_status():
    """验证文件状态"""
    print("\n📋 步骤3：验证文件状态...")
    
    files_to_check = {
        'combined_features.csv': '主特征文件',
        'combined_features_expanded.csv': '扩展特征文件',
        'enhanced_data_expansion_report.json': '扩展报告'
    }
    
    for filename, description in files_to_check.items():
        if os.path.exists(filename):
            try:
                if filename.endswith('.csv'):
                    df = pd.read_csv(filename)
                    print(f"✅ {description}: {df.shape}")
                    
                    if 'data_source' in df.columns:
                        source_counts = df['data_source'].value_counts()
                        print(f"   数据源: {dict(source_counts)}")
                    else:
                        print(f"   ⚠️  缺少data_source列")
                else:
                    file_size = os.path.getsize(filename) / 1024
                    print(f"✅ {description}: {file_size:.1f} KB")
            except Exception as e:
                print(f"❌ {description}: 读取失败 - {str(e)}")
        else:
            print(f"❌ {description}: 文件不存在")

def check_data_consistency():
    """检查数据一致性"""
    print("\n🔍 步骤4：检查数据一致性...")
    
    try:
        # 比较主文件和扩展文件
        main_file = 'combined_features.csv'
        expanded_file = 'combined_features_expanded.csv'
        
        if os.path.exists(main_file) and os.path.exists(expanded_file):
            main_df = pd.read_csv(main_file)
            expanded_df = pd.read_csv(expanded_file)
            
            print(f"   主文件样本数: {len(main_df)}")
            print(f"   扩展文件样本数: {len(expanded_df)}")
            
            # 检查是否一致
            if len(main_df) == len(expanded_df):
                print("✅ 主文件和扩展文件样本数一致")
                
                # 检查数据源分布
                if 'data_source' in main_df.columns and 'data_source' in expanded_df.columns:
                    main_sources = main_df['data_source'].value_counts()
                    expanded_sources = expanded_df['data_source'].value_counts()
                    
                    print(f"   主文件数据源: {dict(main_sources)}")
                    print(f"   扩展文件数据源: {dict(expanded_sources)}")
                    
                    if main_sources.equals(expanded_sources):
                        print("✅ 数据源分布一致")
                        return True
                    else:
                        print("⚠️  数据源分布不一致")
                        return False
                else:
                    print("⚠️  缺少数据源信息")
                    return False
            else:
                print("⚠️  主文件和扩展文件样本数不一致")
                return False
        else:
            print("❌ 缺少必要的文件")
            return False
            
    except Exception as e:
        print(f"❌ 数据一致性检查失败: {str(e)}")
        return False

def generate_test_report(test_results):
    """生成测试报告"""
    print("\n" + "="*80)
    print("📋 修复效果测试报告")
    print("="*80)
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    print(f"\n📊 测试结果总览:")
    print(f"   总测试项: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   失败测试: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 总体评估
    if passed_tests == total_tests:
        print(f"\n🎉 修复完全成功！系统已完美支持双格式数据处理")
        recommendation = "系统已准备就绪，可以进行完整的振动信号分析"
    elif passed_tests >= total_tests * 0.75:
        print(f"\n✅ 修复基本成功，系统大部分功能正常")
        recommendation = "系统基本可用，建议运行完整分析验证性能提升"
    elif passed_tests >= total_tests * 0.5:
        print(f"\n⚠️  修复部分成功，仍需进一步优化")
        recommendation = "建议检查失败的测试项并进行针对性修复"
    else:
        print(f"\n❌ 修复效果不佳，需要重新检查修复方案")
        recommendation = "建议重新分析问题并制定新的修复策略"
    
    print(f"\n💡 建议: {recommendation}")
    
    # 保存测试报告
    report = {
        'test_summary': {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'pass_rate': passed_tests/total_tests*100
        },
        'test_results': test_results,
        'recommendation': recommendation
    }
    
    import json
    with open('data_processing_fix_test_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试报告已保存到: data_processing_fix_test_report.json")
    
    return passed_tests == total_tests

def main():
    """主函数"""
    print("🚀 启动修复效果验证测试")
    
    # 执行测试
    test_results = {}
    
    # 测试1：双格式数据处理
    test_results['双格式数据处理'] = test_fixed_data_processing()
    
    # 测试2：文件状态验证
    verify_file_status()
    
    # 测试3：数据一致性检查
    test_results['数据一致性检查'] = check_data_consistency()
    
    # 生成测试报告
    success = generate_test_report(test_results)
    
    if success:
        print(f"\n🎯 下一步建议:")
        print(f"   1. 运行 python unified_vibration_analysis.py 进行完整分析")
        print(f"   2. 检查模型性能是否有显著提升")
        print(f"   3. 验证预测准确率是否达到目标（速度R²>0.90, 轴重R²>0.85）")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
