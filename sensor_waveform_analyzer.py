#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sensor Waveform Analyzer for Vibration Signal Analysis System
Generates time-series waveform plots and 30 time-frequency feature visualizations

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import sys
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from scipy import signal
from scipy.fft import fft, fftfreq
import pywt
import warnings
warnings.filterwarnings('ignore')

class SensorWaveformAnalyzer:
    """Sensor waveform and feature visualization analyzer"""
    
    def __init__(self, output_dir: str = "unified_charts", file_prefix: str = "waveform_analysis_"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.file_prefix = file_prefix
        
        # Setup academic style
        self.setup_academic_style()
        
        # Analysis parameters
        self.sampling_rate = 1000  # Hz
        self.sensor_data = None
        self.sensor_id = None
        self.time_vector = None
        self.features = None
        
    def setup_academic_style(self):
        """Setup academic publication style with Times New Roman font"""
        plt.rcParams['font.family'] = 'serif'
        plt.rcParams['font.serif'] = ['Times New Roman', 'Times', 'DejaVu Serif']
        plt.rcParams['mathtext.fontset'] = 'stix'
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['figure.dpi'] = 330
        plt.rcParams['savefig.dpi'] = 330
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.titlesize'] = 16
        plt.rcParams['axes.labelsize'] = 14
        plt.rcParams['xtick.labelsize'] = 12
        plt.rcParams['ytick.labelsize'] = 12
        plt.rcParams['legend.fontsize'] = 12
        plt.rcParams['figure.titlesize'] = 18
        
        # IEEE/Elsevier color scheme
        self.colors = {
            'primary': '#1f77b4', 'secondary': '#ff7f0e', 'success': '#2ca02c',
            'danger': '#d62728', 'warning': '#ff7f0e', 'info': '#17a2b8'
        }
    
    def load_sensor_waveform_data(self, file_path: str, sensor_id: str):
        """Load sensor waveform data and apply consistent vehicle passage detection"""
        try:
            print(f"📂 Loading waveform data from: {file_path}")

            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Data file not found: {file_path}")

            # Read CSV file
            df = pd.read_csv(file_path)
            print(f"   📊 Original data shape: {df.shape}")
            print(f"   📋 Columns: {list(df.columns)}")

            # Check if sensor column exists
            if sensor_id not in df.columns:
                available_sensors = [col for col in df.columns if 'sensor' in col.lower() or 'Sensor' in col]
                raise ValueError(f"Sensor {sensor_id} not found. Available sensors: {available_sensors}")

            # Extract all sensor data for vehicle passage detection
            sensor_columns = [col for col in df.columns if 'Sensor_' in col]
            print(f"   🔍 Found {len(sensor_columns)} sensor columns")

            # Apply consistent vehicle passage detection (same as main program)
            extracted_data = self._detect_and_extract_vehicle_passage(df, sensor_columns, sensor_id)

            if extracted_data is None:
                print("   ⚠️ Vehicle passage detection failed, using raw data")
                sensor_data = df[sensor_id].values
                time_vector = np.arange(len(sensor_data)) / self.sampling_rate
            else:
                sensor_data, time_vector = extracted_data
                print(f"   ✅ Vehicle passage detected and extracted")

            # Store data
            self.sensor_data = sensor_data
            self.sensor_id = sensor_id
            self.time_vector = time_vector

            print(f"   ✅ Successfully loaded {len(sensor_data)} data points for {sensor_id}")
            print(f"   📈 Data range: {np.min(sensor_data):.4f} to {np.max(sensor_data):.4f} mg")
            print(f"   ⏱️ Time duration: {time_vector[-1]:.2f} seconds")

            return True

        except Exception as e:
            print(f"   ❌ Error loading sensor waveform data: {str(e)}")
            return False

    def _detect_and_extract_vehicle_passage(self, df, sensor_columns, target_sensor):
        """
        使用与主程序一致的车辆通过检测方法
        基于unified_vibration_analysis.py中的检测算法
        """
        try:
            print("   🚗 Applying vehicle passage detection (consistent with main program)...")

            # 计算多传感器融合信号（与主程序一致）
            valid_sensors = []
            for col in sensor_columns:
                if col in df.columns:
                    sensor_data = df[col].dropna().values
                    if len(sensor_data) > 100 and np.std(sensor_data) > 1e-6:
                        valid_sensors.append(col)

            if len(valid_sensors) < 2:
                print("   ⚠️ Insufficient valid sensors for fusion")
                return None

            # 使用有效传感器的加权平均作为融合信号
            fusion_data = []
            weights = []
            for col in valid_sensors:
                sensor_data = df[col].dropna().values
                fusion_data.append(sensor_data)
                weights.append(1.0 / (np.std(sensor_data) + 1e-8))

            # 归一化权重
            weights = np.array(weights)
            weights = weights / np.sum(weights)

            # 计算融合信号
            min_length = min(len(data) for data in fusion_data)
            fusion_signal = np.zeros(min_length)
            for i, data in enumerate(fusion_data):
                fusion_signal += weights[i] * data[:min_length]

            # 信号预处理（与主程序一致）
            # 1. 去除直流分量
            fusion_signal = fusion_signal - np.mean(fusion_signal)

            # 2. 带通滤波 (1-200 Hz)
            try:
                from scipy.signal import butter, filtfilt
                nyquist = self.sampling_rate / 2
                low_freq = 1.0 / nyquist
                high_freq = min(200.0, nyquist - 1) / nyquist
                b, a = butter(4, [low_freq, high_freq], btype='band')
                fusion_signal = filtfilt(b, a, fusion_signal)
                print("   🔍 Applied bandpass filter (1-200 Hz)")
            except:
                print("   ⚠️ Bandpass filtering failed, using raw signal")

            # 3. 计算信号包络
            from scipy.signal import hilbert
            envelope = np.abs(hilbert(fusion_signal))

            # 4. 平滑包络
            window_size = int(0.1 * self.sampling_rate)  # 100ms窗口
            if window_size > 1:
                envelope = np.convolve(envelope, np.ones(window_size)/window_size, mode='same')

            # 事件检测参数（与主程序一致）
            min_event_duration = int(0.5 * self.sampling_rate)  # 0.5秒
            max_event_duration = int(3.0 * self.sampling_rate)   # 3秒
            event_segment_length = int(1.0 * self.sampling_rate) # 1秒

            # 动态阈值计算
            baseline_level = np.median(envelope)
            noise_level = np.median(np.abs(envelope - baseline_level))  # MAD
            threshold = baseline_level + 3 * noise_level

            # 如果阈值太低，使用信号标准差的倍数
            if threshold < 2 * np.std(fusion_signal):
                threshold = 2 * np.std(fusion_signal)

            print(f"   📊 Detection threshold: {threshold:.4f}")

            # 检测超过阈值的区域
            above_threshold = envelope > threshold

            # 查找连续的超阈值区域
            diff_above = np.diff(np.concatenate(([0], above_threshold.astype(int), [0])))
            event_starts = np.where(diff_above == 1)[0]
            event_ends = np.where(diff_above == -1)[0] - 1

            # 过滤和选择最佳事件
            best_event = None
            max_peak = 0

            for i in range(len(event_starts)):
                event_duration = event_ends[i] - event_starts[i] + 1

                # 检查事件持续时间
                if min_event_duration <= event_duration <= max_event_duration:
                    # 检查事件强度
                    event_signal = envelope[event_starts[i]:event_ends[i]]
                    event_peak = np.max(event_signal)

                    if event_peak > threshold * 1.5 and event_peak > max_peak:
                        max_peak = event_peak
                        best_event = i

            if best_event is None:
                print("   ⚠️ No valid vehicle passage event detected")
                return None

            # 提取最佳事件段
            event_start = event_starts[best_event]
            event_end = event_ends[best_event]

            # 找到事件中的最大值位置
            event_envelope = envelope[event_start:event_end]
            max_idx = np.argmax(event_envelope)
            peak_position = event_start + max_idx

            # 以峰值为中心提取1秒数据（与主程序一致）
            segment_start = peak_position - event_segment_length // 2
            segment_end = peak_position + event_segment_length // 2

            # 确保段在有效范围内
            if segment_start < 0:
                segment_start = 0
                segment_end = min(event_segment_length, len(fusion_signal))
            elif segment_end > len(fusion_signal):
                segment_end = len(fusion_signal)
                segment_start = max(0, len(fusion_signal) - event_segment_length)

            # 提取目标传感器的数据段
            target_data = df[target_sensor].values[segment_start:segment_end]
            time_vector = np.arange(len(target_data)) / self.sampling_rate

            print(f"   ✅ Extracted vehicle passage: {len(target_data)} points ({len(target_data)/self.sampling_rate:.2f}s)")
            print(f"   📍 Peak position: {peak_position/self.sampling_rate:.2f}s")

            return target_data, time_vector

        except Exception as e:
            print(f"   ❌ Vehicle passage detection failed: {str(e)}")
            return None
    
    def extract_30_features(self):
        """Extract 30 time-frequency domain features from sensor data"""
        if self.sensor_data is None:
            print("❌ No sensor data available for feature extraction")
            return False
        
        print(f"🔧 Extracting 30 time-frequency features from {self.sensor_id}...")
        
        try:
            features = {}
            
            # Time domain features (10 features)
            features['mean'] = np.mean(self.sensor_data)
            features['std'] = np.std(self.sensor_data)
            features['var'] = np.var(self.sensor_data)
            features['rms'] = np.sqrt(np.mean(self.sensor_data**2))
            features['peak'] = np.max(np.abs(self.sensor_data))
            features['peak_to_peak'] = np.max(self.sensor_data) - np.min(self.sensor_data)
            features['crest_factor'] = features['peak'] / features['rms'] if features['rms'] > 0 else 0
            features['skewness'] = float(pd.Series(self.sensor_data).skew())
            features['kurtosis'] = float(pd.Series(self.sensor_data).kurtosis())
            features['energy'] = np.sum(self.sensor_data**2)
            
            # Frequency domain features (10 features)
            # FFT analysis
            fft_values = fft(self.sensor_data)
            fft_freqs = fftfreq(len(self.sensor_data), 1/self.sampling_rate)
            positive_freq_idx = fft_freqs > 0
            freqs = fft_freqs[positive_freq_idx]
            magnitude = np.abs(fft_values[positive_freq_idx])
            
            # Zero crossing rate
            zero_crossings = np.where(np.diff(np.signbit(self.sensor_data)))[0]
            features['zero_crossing_rate'] = len(zero_crossings) / len(self.sensor_data)
            
            # Dominant frequency
            dominant_freq_idx = np.argmax(magnitude)
            features['dominant_frequency'] = freqs[dominant_freq_idx]
            
            # Mean frequency
            features['mean_frequency'] = np.sum(freqs * magnitude) / np.sum(magnitude)
            
            # Frequency standard deviation
            features['frequency_std'] = np.sqrt(np.sum(((freqs - features['mean_frequency'])**2) * magnitude) / np.sum(magnitude))
            
            # Spectral centroid
            features['spectral_centroid'] = features['mean_frequency']
            
            # Spectral rolloff (85% of energy)
            cumulative_magnitude = np.cumsum(magnitude)
            rolloff_threshold = 0.85 * cumulative_magnitude[-1]
            rolloff_idx = np.where(cumulative_magnitude >= rolloff_threshold)[0]
            features['spectral_rolloff'] = freqs[rolloff_idx[0]] if len(rolloff_idx) > 0 else freqs[-1]
            
            # Spectral flux
            if len(magnitude) > 1:
                spectral_flux = np.sum(np.diff(magnitude)**2)
                features['spectral_flux'] = spectral_flux
            else:
                features['spectral_flux'] = 0
            
            # Power in different frequency bands
            total_power = np.sum(magnitude**2)
            low_freq_mask = (freqs >= 1) & (freqs <= 50)
            mid_freq_mask = (freqs >= 50) & (freqs <= 150)
            high_freq_mask = (freqs >= 150) & (freqs <= 400)
            
            features['total_power'] = total_power
            features['low_freq_power'] = np.sum(magnitude[low_freq_mask]**2) / total_power if total_power > 0 else 0
            features['mid_freq_power'] = np.sum(magnitude[mid_freq_mask]**2) / total_power if total_power > 0 else 0
            features['high_freq_power'] = np.sum(magnitude[high_freq_mask]**2) / total_power if total_power > 0 else 0
            
            # Time-frequency domain features (10 features)
            # Spectrogram analysis
            f_spec, t_spec, Sxx = signal.spectrogram(self.sensor_data, fs=self.sampling_rate, 
                                                    nperseg=min(256, len(self.sensor_data)//4))
            
            features['spectrogram_mean'] = np.mean(Sxx)
            features['spectrogram_std'] = np.std(Sxx)
            features['spectrogram_max'] = np.max(Sxx)
            
            # Time-bandwidth product
            signal_duration = len(self.sensor_data) / self.sampling_rate
            bandwidth = features['spectral_rolloff'] - 1  # Approximate bandwidth
            features['time_bandwidth_product'] = signal_duration * bandwidth
            
            # Wavelet transform features
            wavelet = 'db4'
            coeffs = pywt.wavedec(self.sensor_data, wavelet, level=4)
            
            # Wavelet energy in different levels
            features['wavelet_energy_d1'] = np.sum(coeffs[1]**2) / features['energy'] if features['energy'] > 0 else 0
            features['wavelet_energy_d2'] = np.sum(coeffs[2]**2) / features['energy'] if features['energy'] > 0 else 0
            features['wavelet_energy_d3'] = np.sum(coeffs[3]**2) / features['energy'] if features['energy'] > 0 else 0
            features['wavelet_energy_d4'] = np.sum(coeffs[4]**2) / features['energy'] if features['energy'] > 0 else 0
            features['wavelet_energy_a4'] = np.sum(coeffs[0]**2) / features['energy'] if features['energy'] > 0 else 0
            
            # Remove the extra feature to keep exactly 30 features
            # features['instantaneous_frequency_mean'] = features['mean_frequency']  # Removed to maintain 30 features
            
            self.features = features
            
            print(f"   ✅ Successfully extracted 30 features")
            print(f"   📊 Feature categories: 10 time + 10 frequency + 10 time-frequency")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Error extracting features: {str(e)}")
            return False
    
    def generate_waveform_plot(self):
        """Generate time-series waveform plot with key moment identification"""
        if self.sensor_data is None or self.time_vector is None:
            print("❌ No sensor data available for waveform plotting")
            return False
        
        print(f"📈 Generating waveform plot for {self.sensor_id}...")
        
        try:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
            fig.suptitle(f'Vehicle Passage Vibration Waveform Analysis - {self.sensor_id}', 
                        fontsize=18, fontweight='bold', y=0.95)
            
            # Main waveform plot
            ax1.plot(self.time_vector, self.sensor_data, color=self.colors['primary'], 
                    linewidth=1.2, alpha=0.8, label='Vibration Signal')
            
            # Identify and mark key moments with improved peak detection
            # Find maximum positive value and minimum negative value
            max_positive_idx = np.argmax(self.sensor_data)
            min_negative_idx = np.argmin(self.sensor_data)

            max_positive_val = self.sensor_data[max_positive_idx]
            min_negative_val = self.sensor_data[min_negative_idx]

            # Mark maximum positive peak
            ax1.scatter(self.time_vector[max_positive_idx], max_positive_val,
                       color=self.colors['danger'], s=120, zorder=5, marker='^',
                       label=f'Max Positive: {max_positive_val:.3f} mg')

            # Mark minimum negative peak
            ax1.scatter(self.time_vector[min_negative_idx], min_negative_val,
                       color=self.colors['info'], s=120, zorder=5, marker='v',
                       label=f'Min Negative: {min_negative_val:.3f} mg')

            # Calculate and display peak-to-peak value
            peak_to_peak = max_positive_val - min_negative_val

            # Annotate extreme values
            ax1.annotate(f'Max: {max_positive_val:.3f} mg',
                       xy=(self.time_vector[max_positive_idx], max_positive_val),
                       xytext=(10, 15), textcoords='offset points',
                       fontsize=10, ha='left', fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

            ax1.annotate(f'Min: {min_negative_val:.3f} mg',
                       xy=(self.time_vector[min_negative_idx], min_negative_val),
                       xytext=(10, -25), textcoords='offset points',
                       fontsize=10, ha='left', fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='blue', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

            # Find additional peaks for vehicle axles detection
            peaks, properties = signal.find_peaks(np.abs(self.sensor_data),
                                                 height=np.std(self.sensor_data)*1.5,
                                                 distance=int(0.05*self.sampling_rate))  # Min 0.05s between peaks

            # Mark additional peaks (potential axles)
            if len(peaks) > 2:  # More than just the two extreme points
                # Filter out the extreme points we already marked
                filtered_peaks = [p for p in peaks if p != max_positive_idx and p != min_negative_idx]
                if filtered_peaks:
                    ax1.scatter(self.time_vector[filtered_peaks], self.sensor_data[filtered_peaks],
                               color=self.colors['warning'], s=60, zorder=4, alpha=0.7,
                               label=f'Additional Peaks ({len(filtered_peaks)} detected)')

                    # Annotate some additional peaks
                    for i, peak_idx in enumerate(filtered_peaks[:3]):  # Limit to first 3
                        ax1.annotate(f'P{i+1}',
                                   xy=(self.time_vector[peak_idx], self.sensor_data[peak_idx]),
                                   xytext=(5, 5), textcoords='offset points',
                                   fontsize=8, ha='center',
                                   bbox=dict(boxstyle='round,pad=0.2', facecolor='orange', alpha=0.6))
            
            # Mark vehicle passage start and end (based on signal energy)
            signal_energy = np.convolve(self.sensor_data**2, np.ones(50)/50, mode='same')
            energy_threshold = np.mean(signal_energy) + 2*np.std(signal_energy)
            passage_indices = np.where(signal_energy > energy_threshold)[0]
            
            if len(passage_indices) > 0:
                passage_start = self.time_vector[passage_indices[0]]
                passage_end = self.time_vector[passage_indices[-1]]
                
                ax1.axvline(x=passage_start, color=self.colors['success'], linestyle='--', 
                           alpha=0.7, label='Vehicle Entry')
                ax1.axvline(x=passage_end, color=self.colors['warning'], linestyle='--', 
                           alpha=0.7, label='Vehicle Exit')
                
                # Shade vehicle passage period
                ax1.axvspan(passage_start, passage_end, alpha=0.1, color=self.colors['info'], 
                           label='Vehicle Passage Period')
            
            ax1.set_title('Vehicle Passage Vibration Waveform', fontsize=16, pad=20)
            ax1.set_xlabel('Time (s)', fontsize=14)
            ax1.set_ylabel('Amplitude (mg)', fontsize=14)
            ax1.grid(True, alpha=0.3)
            ax1.legend(loc='upper right')
            
            # Add enhanced statistics box
            stats_text = (f'Duration: {self.time_vector[-1]:.2f}s\n'
                         f'Max Positive: {max_positive_val:.3f} mg\n'
                         f'Min Negative: {min_negative_val:.3f} mg\n'
                         f'Peak-to-Peak: {peak_to_peak:.3f} mg\n'
                         f'RMS: {np.sqrt(np.mean(self.sensor_data**2)):.3f} mg\n'
                         f'Data Points: {len(self.sensor_data)}\n'
                         f'Sampling Rate: {self.sampling_rate} Hz')
            ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, fontsize=10,
                    verticalalignment='top', bbox=dict(boxstyle="round,pad=0.4",
                    facecolor='white', alpha=0.9, edgecolor='gray'))
            
            # Detailed view of main vehicle passage
            if len(passage_indices) > 0:
                # Focus on vehicle passage period with some margin
                margin = int(0.1 * self.sampling_rate)  # 0.1s margin
                start_idx = max(0, passage_indices[0] - margin)
                end_idx = min(len(self.sensor_data), passage_indices[-1] + margin)
                
                detail_time = self.time_vector[start_idx:end_idx]
                detail_data = self.sensor_data[start_idx:end_idx]
                
                ax2.plot(detail_time, detail_data, color=self.colors['primary'], 
                        linewidth=1.5, alpha=0.9)
                
                # Mark peaks in detailed view
                detail_peaks = peaks[(peaks >= start_idx) & (peaks < end_idx)]
                if len(detail_peaks) > 0:
                    ax2.scatter(self.time_vector[detail_peaks], self.sensor_data[detail_peaks], 
                               color=self.colors['danger'], s=100, zorder=5)
                
                ax2.set_title('Detailed View - Vehicle Passage Period', fontsize=16, pad=20)
                ax2.set_xlabel('Time (s)', fontsize=14)
                ax2.set_ylabel('Amplitude (mg)', fontsize=14)
                ax2.grid(True, alpha=0.3)
                
                # Add envelope
                from scipy.signal import hilbert
                analytic_signal = hilbert(detail_data)
                envelope = np.abs(analytic_signal)
                ax2.plot(detail_time, envelope, color=self.colors['warning'], 
                        linewidth=2, alpha=0.7, linestyle='--', label='Signal Envelope')
                ax2.plot(detail_time, -envelope, color=self.colors['warning'], 
                        linewidth=2, alpha=0.7, linestyle='--')
                ax2.legend()
            else:
                # If no clear passage detected, show full signal
                ax2.plot(self.time_vector, self.sensor_data, color=self.colors['secondary'], 
                        linewidth=1.0, alpha=0.8)
                ax2.set_title('Full Signal View', fontsize=16, pad=20)
                ax2.set_xlabel('Time (s)', fontsize=14)
                ax2.set_ylabel('Amplitude (mg)', fontsize=14)
                ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.subplots_adjust(top=0.92)
            
            # Save plot
            plot_path = self.output_dir / f'{self.file_prefix}{self.sensor_id}_waveform.png'
            plt.savefig(plot_path, dpi=330, bbox_inches='tight', facecolor='white')
            plt.close()
            
            print(f"   ✅ Waveform plot saved: {plot_path}")
            return True
            
        except Exception as e:
            print(f"   ❌ Error generating waveform plot: {str(e)}")
            return False

    def generate_features_visualization(self):
        """Generate 30 time-frequency features visualization"""
        if self.features is None:
            print("❌ No features available for visualization")
            return False

        print(f"📊 Generating 30 features visualization for {self.sensor_id}...")

        try:
            fig = plt.figure(figsize=(20, 16))
            fig.suptitle(f'30 Time-Frequency Domain Features Analysis - {self.sensor_id}',
                        fontsize=20, fontweight='bold', y=0.95)

            # Organize features by category
            time_features = {
                'Mean': self.features['mean'],
                'Std Dev': self.features['std'],
                'Variance': self.features['var'],
                'RMS': self.features['rms'],
                'Peak': self.features['peak'],
                'Peak-to-Peak': self.features['peak_to_peak'],
                'Crest Factor': self.features['crest_factor'],
                'Skewness': self.features['skewness'],
                'Kurtosis': self.features['kurtosis'],
                'Energy': self.features['energy']
            }

            freq_features = {
                'Zero Cross Rate': self.features['zero_crossing_rate'],
                'Dominant Freq': self.features['dominant_frequency'],
                'Mean Freq': self.features['mean_frequency'],
                'Freq Std': self.features['frequency_std'],
                'Spectral Centroid': self.features['spectral_centroid'],
                'Spectral Rolloff': self.features['spectral_rolloff'],
                'Spectral Flux': self.features['spectral_flux'],
                'Total Power': self.features['total_power'],
                'Low Freq Power': self.features['low_freq_power'],
                'Mid Freq Power': self.features['mid_freq_power']
            }

            time_freq_features = {
                'High Freq Power': self.features['high_freq_power'],
                'Spectrogram Mean': self.features['spectrogram_mean'],
                'Spectrogram Std': self.features['spectrogram_std'],
                'Spectrogram Max': self.features['spectrogram_max'],
                'Time-BW Product': self.features['time_bandwidth_product'],
                'Wavelet Energy D1': self.features['wavelet_energy_d1'],
                'Wavelet Energy D2': self.features['wavelet_energy_d2'],
                'Wavelet Energy D3': self.features['wavelet_energy_d3'],
                'Wavelet Energy D4': self.features['wavelet_energy_d4'],
                'Wavelet Energy A4': self.features['wavelet_energy_a4']
            }

            # Create subplots
            gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)

            # 1. Time domain features bar chart
            ax1 = fig.add_subplot(gs[0, :2])
            time_names = list(time_features.keys())
            time_values = list(time_features.values())

            # Normalize values for better visualization
            time_values_norm = np.array(time_values)
            time_values_norm = (time_values_norm - np.min(time_values_norm)) / (np.max(time_values_norm) - np.min(time_values_norm) + 1e-8)

            bars1 = ax1.bar(range(len(time_names)), time_values_norm,
                           color=plt.cm.Blues(np.linspace(0.4, 0.8, len(time_names))), alpha=0.8)
            ax1.set_title('Time Domain Features (Normalized)', fontsize=14, pad=15)
            ax1.set_xlabel('Feature Type', fontsize=12)
            ax1.set_ylabel('Normalized Value', fontsize=12)
            ax1.set_xticks(range(len(time_names)))
            ax1.set_xticklabels(time_names, rotation=45, ha='right', fontsize=10)
            ax1.grid(True, alpha=0.3)

            # Add actual values as text
            for i, (bar, val) in enumerate(zip(bars1, time_values)):
                ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.02,
                        f'{val:.2e}' if abs(val) > 1000 or abs(val) < 0.01 else f'{val:.3f}',
                        ha='center', va='bottom', fontsize=8, rotation=90)

            # 2. Frequency domain features bar chart
            ax2 = fig.add_subplot(gs[0, 2:])
            freq_names = list(freq_features.keys())
            freq_values = list(freq_features.values())

            # Normalize values for better visualization
            freq_values_norm = np.array(freq_values)
            freq_values_norm = (freq_values_norm - np.min(freq_values_norm)) / (np.max(freq_values_norm) - np.min(freq_values_norm) + 1e-8)

            bars2 = ax2.bar(range(len(freq_names)), freq_values_norm,
                           color=plt.cm.Oranges(np.linspace(0.4, 0.8, len(freq_names))), alpha=0.8)
            ax2.set_title('Frequency Domain Features (Normalized)', fontsize=14, pad=15)
            ax2.set_xlabel('Feature Type', fontsize=12)
            ax2.set_ylabel('Normalized Value', fontsize=12)
            ax2.set_xticks(range(len(freq_names)))
            ax2.set_xticklabels(freq_names, rotation=45, ha='right', fontsize=10)
            ax2.grid(True, alpha=0.3)

            # Add actual values as text
            for i, (bar, val) in enumerate(zip(bars2, freq_values)):
                ax2.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.02,
                        f'{val:.2e}' if abs(val) > 1000 or abs(val) < 0.01 else f'{val:.3f}',
                        ha='center', va='bottom', fontsize=8, rotation=90)

            # 3. Time-frequency domain features bar chart
            ax3 = fig.add_subplot(gs[1, :2])
            tf_names = list(time_freq_features.keys())
            tf_values = list(time_freq_features.values())

            # Normalize values for better visualization
            tf_values_norm = np.array(tf_values)
            tf_values_norm = (tf_values_norm - np.min(tf_values_norm)) / (np.max(tf_values_norm) - np.min(tf_values_norm) + 1e-8)

            bars3 = ax3.bar(range(len(tf_names)), tf_values_norm,
                           color=plt.cm.Greens(np.linspace(0.4, 0.8, len(tf_names))), alpha=0.8)
            ax3.set_title('Time-Frequency Domain Features (Normalized)', fontsize=14, pad=15)
            ax3.set_xlabel('Feature Type', fontsize=12)
            ax3.set_ylabel('Normalized Value', fontsize=12)
            ax3.set_xticks(range(len(tf_names)))
            ax3.set_xticklabels(tf_names, rotation=45, ha='right', fontsize=10)
            ax3.grid(True, alpha=0.3)

            # Add actual values as text
            for i, (bar, val) in enumerate(zip(bars3, tf_values)):
                ax3.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.02,
                        f'{val:.2e}' if abs(val) > 1000 or abs(val) < 0.01 else f'{val:.3f}',
                        ha='center', va='bottom', fontsize=8, rotation=90)

            # 4. Radar chart for key features
            ax4 = fig.add_subplot(gs[1, 2:], projection='polar')

            # Select key features for radar chart
            key_features = {
                'RMS': self.features['rms'],
                'Peak': self.features['peak'],
                'Crest Factor': self.features['crest_factor'],
                'Dominant Freq': self.features['dominant_frequency'],
                'Spectral Centroid': self.features['spectral_centroid'],
                'Total Power': self.features['total_power'],
                'Wavelet Energy D1': self.features['wavelet_energy_d1'],
                'Wavelet Energy D2': self.features['wavelet_energy_d2']
            }

            # Normalize for radar chart
            key_values = np.array(list(key_features.values()))
            key_values_norm = (key_values - np.min(key_values)) / (np.max(key_values) - np.min(key_values) + 1e-8)

            # Create radar chart
            angles = np.linspace(0, 2 * np.pi, len(key_features), endpoint=False).tolist()
            key_values_norm = key_values_norm.tolist()
            angles += angles[:1]  # Complete the circle
            key_values_norm += key_values_norm[:1]

            ax4.plot(angles, key_values_norm, 'o-', linewidth=2, color=self.colors['primary'], alpha=0.8)
            ax4.fill(angles, key_values_norm, alpha=0.25, color=self.colors['primary'])
            ax4.set_xticks(angles[:-1])
            ax4.set_xticklabels(list(key_features.keys()), fontsize=10)
            ax4.set_ylim(0, 1)
            ax4.set_title('Key Features Radar Chart', fontsize=14, pad=20)
            ax4.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.subplots_adjust(top=0.92)

            # Save plot
            plot_path = self.output_dir / f'{self.file_prefix}{self.sensor_id}_features.png'
            plt.savefig(plot_path, dpi=330, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"   ✅ Features visualization saved: {plot_path}")
            return True

        except Exception as e:
            print(f"   ❌ Error generating features visualization: {str(e)}")
            return False

    def generate_detailed_features_analysis(self):
        """Generate detailed analysis for each of the 30 time-frequency features"""
        if self.features is None or self.sensor_data is None:
            print("❌ No features or sensor data available for detailed analysis")
            return False

        print(f"🔬 Generating detailed features analysis for {self.sensor_id}...")

        try:
            # Generate individual feature charts first
            individual_success = self._generate_individual_feature_charts()

            # Create a comprehensive overview figure with optimized layout
            fig = plt.figure(figsize=(30, 45))  # Increased size for better spacing
            fig.suptitle(f'Detailed 30 Time-Frequency Features Analysis - {self.sensor_id}',
                        fontsize=18, fontweight='bold', y=0.985)  # Adjusted position

            # Create grid layout with optimized spacing
            gs = fig.add_gridspec(10, 3, hspace=0.8, wspace=0.5,
                                 top=0.97, bottom=0.03, left=0.06, right=0.94)

            # Time domain features detailed analysis (10 features)
            time_features_info = [
                ('mean', 'Signal Mean', 'Average amplitude over time', 'mg'),
                ('std', 'Standard Deviation', 'Signal variability measure', 'mg'),
                ('var', 'Variance', 'Square of standard deviation', 'mg²'),
                ('rms', 'RMS Value', 'Root mean square amplitude', 'mg'),
                ('peak', 'Peak Value', 'Maximum absolute amplitude', 'mg'),
                ('peak_to_peak', 'Peak-to-Peak', 'Range between max and min', 'mg'),
                ('crest_factor', 'Crest Factor', 'Peak to RMS ratio', 'dimensionless'),
                ('skewness', 'Skewness', 'Asymmetry of distribution', 'dimensionless'),
                ('kurtosis', 'Kurtosis', 'Tail heaviness of distribution', 'dimensionless'),
                ('energy', 'Signal Energy', 'Total energy content', 'mg²·s')
            ]

            # Generate detailed plots for time domain features
            for i, (feature_key, title, description, unit) in enumerate(time_features_info):
                ax = fig.add_subplot(gs[i, 0])
                self._plot_feature_detail_optimized(ax, feature_key, title, description, unit, 'time')

            # Frequency domain features detailed analysis (10 features)
            freq_features_info = [
                ('zero_crossing_rate', 'Zero Crossing Rate', 'Frequency of sign changes', 'Hz'),
                ('dominant_frequency', 'Dominant Frequency', 'Peak frequency component', 'Hz'),
                ('mean_frequency', 'Mean Frequency', 'Spectral centroid frequency', 'Hz'),
                ('frequency_std', 'Frequency Std', 'Spectral spread measure', 'Hz'),
                ('spectral_centroid', 'Spectral Centroid', 'Center of mass of spectrum', 'Hz'),
                ('spectral_rolloff', 'Spectral Rolloff', '85% energy frequency', 'Hz'),
                ('spectral_flux', 'Spectral Flux', 'Rate of spectral change', 'dimensionless'),
                ('total_power', 'Total Power', 'Total spectral energy', 'mg²'),
                ('low_freq_power', 'Low Freq Power', 'Power in 1-50 Hz band', 'ratio'),
                ('mid_freq_power', 'Mid Freq Power', 'Power in 50-150 Hz band', 'ratio')
            ]

            # Generate detailed plots for frequency domain features
            for i, (feature_key, title, description, unit) in enumerate(freq_features_info):
                ax = fig.add_subplot(gs[i, 1])
                self._plot_feature_detail_optimized(ax, feature_key, title, description, unit, 'frequency')

            # Time-frequency domain features detailed analysis (10 features)
            tf_features_info = [
                ('high_freq_power', 'High Freq Power', 'Power in 150-400 Hz band', 'ratio'),
                ('spectrogram_mean', 'Spectrogram Mean', 'Average spectrogram value', 'mg²/Hz'),
                ('spectrogram_std', 'Spectrogram Std', 'Spectrogram variability', 'mg²/Hz'),
                ('spectrogram_max', 'Spectrogram Max', 'Peak spectrogram value', 'mg²/Hz'),
                ('time_bandwidth_product', 'Time-BW Product', 'Time-frequency resolution', 'Hz·s'),
                ('wavelet_energy_d1', 'Wavelet Energy D1', 'Detail level 1 energy', 'ratio'),
                ('wavelet_energy_d2', 'Wavelet Energy D2', 'Detail level 2 energy', 'ratio'),
                ('wavelet_energy_d3', 'Wavelet Energy D3', 'Detail level 3 energy', 'ratio'),
                ('wavelet_energy_d4', 'Wavelet Energy D4', 'Detail level 4 energy', 'ratio'),
                ('wavelet_energy_a4', 'Wavelet Energy A4', 'Approximation level 4 energy', 'ratio')
            ]

            # Generate detailed plots for time-frequency domain features
            for i, (feature_key, title, description, unit) in enumerate(tf_features_info):
                ax = fig.add_subplot(gs[i, 2])
                self._plot_feature_detail_optimized(ax, feature_key, title, description, unit, 'time_frequency')

            plt.tight_layout()
            plt.subplots_adjust(top=0.97, bottom=0.03, left=0.06, right=0.94, hspace=0.8, wspace=0.5)

            # Save detailed features plot
            plot_path = self.output_dir / f'{self.file_prefix}{self.sensor_id}_detailed_features.png'
            plt.savefig(plot_path, dpi=330, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"   ✅ Detailed features analysis saved: {plot_path}")

            if individual_success:
                print(f"   ✅ Individual feature charts generated successfully")
            else:
                print(f"   ⚠️ Some individual feature charts may have failed")

            return True

        except Exception as e:
            print(f"   ❌ Error generating detailed features analysis: {str(e)}")
            return False

    def _generate_individual_feature_charts(self):
        """Generate individual chart for each of the 30 features"""
        print(f"   📊 Generating individual feature charts...")

        try:
            # All 30 features with their information
            all_features_info = [
                # Time domain features (10)
                ('mean', 'Signal Mean', 'Average amplitude over time', 'mg', 'time'),
                ('std', 'Standard Deviation', 'Signal variability measure', 'mg', 'time'),
                ('var', 'Variance', 'Square of standard deviation', 'mg²', 'time'),
                ('rms', 'RMS Value', 'Root mean square amplitude', 'mg', 'time'),
                ('peak', 'Peak Value', 'Maximum absolute amplitude', 'mg', 'time'),
                ('peak_to_peak', 'Peak-to-Peak', 'Range between max and min', 'mg', 'time'),
                ('crest_factor', 'Crest Factor', 'Peak to RMS ratio', 'dimensionless', 'time'),
                ('skewness', 'Skewness', 'Asymmetry of distribution', 'dimensionless', 'time'),
                ('kurtosis', 'Kurtosis', 'Tail heaviness of distribution', 'dimensionless', 'time'),
                ('energy', 'Signal Energy', 'Total energy content', 'mg²·s', 'time'),

                # Frequency domain features (10)
                ('zero_crossing_rate', 'Zero Crossing Rate', 'Frequency of sign changes', 'Hz', 'frequency'),
                ('dominant_frequency', 'Dominant Frequency', 'Peak frequency component', 'Hz', 'frequency'),
                ('mean_frequency', 'Mean Frequency', 'Spectral centroid frequency', 'Hz', 'frequency'),
                ('frequency_std', 'Frequency Std', 'Spectral spread measure', 'Hz', 'frequency'),
                ('spectral_centroid', 'Spectral Centroid', 'Center of mass of spectrum', 'Hz', 'frequency'),
                ('spectral_rolloff', 'Spectral Rolloff', '85% energy frequency', 'Hz', 'frequency'),
                ('spectral_flux', 'Spectral Flux', 'Rate of spectral change', 'dimensionless', 'frequency'),
                ('total_power', 'Total Power', 'Total spectral energy', 'mg²', 'frequency'),
                ('low_freq_power', 'Low Freq Power', 'Power in 1-50 Hz band', 'ratio', 'frequency'),
                ('mid_freq_power', 'Mid Freq Power', 'Power in 50-150 Hz band', 'ratio', 'frequency'),

                # Time-frequency domain features (10)
                ('high_freq_power', 'High Freq Power', 'Power in 150-400 Hz band', 'ratio', 'time_frequency'),
                ('spectrogram_mean', 'Spectrogram Mean', 'Average spectrogram value', 'mg²/Hz', 'time_frequency'),
                ('spectrogram_std', 'Spectrogram Std', 'Spectrogram variability', 'mg²/Hz', 'time_frequency'),
                ('spectrogram_max', 'Spectrogram Max', 'Peak spectrogram value', 'mg²/Hz', 'time_frequency'),
                ('time_bandwidth_product', 'Time-BW Product', 'Time-frequency resolution', 'Hz·s', 'time_frequency'),
                ('wavelet_energy_d1', 'Wavelet Energy D1', 'Detail level 1 energy', 'ratio', 'time_frequency'),
                ('wavelet_energy_d2', 'Wavelet Energy D2', 'Detail level 2 energy', 'ratio', 'time_frequency'),
                ('wavelet_energy_d3', 'Wavelet Energy D3', 'Detail level 3 energy', 'ratio', 'time_frequency'),
                ('wavelet_energy_d4', 'Wavelet Energy D4', 'Detail level 4 energy', 'ratio', 'time_frequency'),
                ('wavelet_energy_a4', 'Wavelet Energy A4', 'Approximation level 4 energy', 'ratio', 'time_frequency')
            ]

            success_count = 0

            for feature_key, title, description, unit, domain_type in all_features_info:
                try:
                    # Create individual figure for this feature
                    fig, ax = plt.subplots(1, 1, figsize=(12, 8))

                    # Generate the feature-specific plot
                    self._plot_individual_feature(ax, feature_key, title, description, unit, domain_type)

                    # Set main title
                    fig.suptitle(f'{title} Analysis - {self.sensor_id}',
                                fontsize=16, fontweight='bold', y=0.95)

                    # Adjust layout
                    plt.tight_layout()
                    plt.subplots_adjust(top=0.90)

                    # Save individual feature chart
                    chart_path = self.output_dir / f'{self.file_prefix}{self.sensor_id}_feature_{feature_key}.png'
                    plt.savefig(chart_path, dpi=330, bbox_inches='tight', facecolor='white')
                    plt.close()

                    success_count += 1

                except Exception as e:
                    print(f"      ⚠️ Failed to generate chart for {feature_key}: {str(e)}")

            print(f"   ✅ Generated {success_count}/30 individual feature charts")
            return success_count == 30

        except Exception as e:
            print(f"   ❌ Error generating individual feature charts: {str(e)}")
            return False

    def _plot_individual_feature(self, ax, feature_key, title, description, unit, domain_type):
        """Plot individual feature with enhanced layout and clear demonstration"""
        try:
            feature_value = self.features.get(feature_key, 0)

            # Create feature-specific visualization based on domain type
            if domain_type == 'time':
                self._plot_time_domain_feature_enhanced(ax, feature_key, feature_value, title, description, unit)
            elif domain_type == 'frequency':
                self._plot_frequency_domain_feature_enhanced(ax, feature_key, feature_value, title, description, unit)
            else:  # time_frequency
                self._plot_time_frequency_feature_enhanced(ax, feature_key, feature_value, title, description, unit)

            # Enhanced formatting with better spacing
            ax.set_title(f'{title}\n{description}', fontsize=14, pad=20, fontweight='bold')
            ax.grid(True, alpha=0.3)

            # Add feature value annotation with better positioning
            ax.text(0.02, 0.98, f'Value: {feature_value:.4f} {unit}',
                   transform=ax.transAxes, fontsize=12, fontweight='bold',
                   verticalalignment='top',
                   bbox=dict(boxstyle="round,pad=0.4", facecolor='yellow', alpha=0.9, edgecolor='orange'))

            # Add calculation method explanation
            calc_method = self._get_calculation_method(feature_key)
            ax.text(0.02, 0.02, f'Calculation: {calc_method}',
                   transform=ax.transAxes, fontsize=10,
                   verticalalignment='bottom',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.8))

        except Exception as e:
            # Fallback simple plot with better error handling
            ax.text(0.5, 0.5, f'{title}\nValue: {feature_value:.4f} {unit}\n{description}\n\nError: {str(e)}',
                   transform=ax.transAxes, ha='center', va='center', fontsize=12,
                   bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.8))
            ax.set_title(title, fontsize=14, fontweight='bold')

    def _plot_feature_detail_optimized(self, ax, feature_key, title, description, unit, domain_type):
        """Plot feature detail with optimized layout for overview chart"""
        try:
            feature_value = self.features.get(feature_key, 0)

            # Create feature-specific visualization based on domain type
            if domain_type == 'time':
                self._plot_time_domain_feature_compact(ax, feature_key, feature_value, title, description, unit)
            elif domain_type == 'frequency':
                self._plot_frequency_domain_feature_compact(ax, feature_key, feature_value, title, description, unit)
            else:  # time_frequency
                self._plot_time_frequency_feature_compact(ax, feature_key, feature_value, title, description, unit)

            # Optimized formatting for compact display
            ax.set_title(f'{title}', fontsize=11, pad=8, fontweight='bold')
            ax.grid(True, alpha=0.3)

            # Compact feature value annotation
            ax.text(0.02, 0.98, f'{feature_value:.3f} {unit}',
                   transform=ax.transAxes, fontsize=9, fontweight='bold',
                   verticalalignment='top',
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='yellow', alpha=0.8))

            # Adjust tick label sizes for better readability
            ax.tick_params(axis='both', which='major', labelsize=8)
            ax.tick_params(axis='both', which='minor', labelsize=7)

        except Exception as e:
            # Fallback simple plot
            ax.text(0.5, 0.5, f'{title}\n{feature_value:.3f} {unit}',
                   transform=ax.transAxes, ha='center', va='center', fontsize=10,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.8))
            ax.set_title(title, fontsize=11, fontweight='bold')

    def _get_calculation_method(self, feature_key):
        """Get calculation method description for each feature"""
        methods = {
            'mean': 'np.mean(signal)',
            'std': 'np.std(signal)',
            'var': 'np.var(signal)',
            'rms': 'np.sqrt(np.mean(signal²))',
            'peak': 'np.max(np.abs(signal))',
            'peak_to_peak': 'np.max(signal) - np.min(signal)',
            'crest_factor': 'peak / rms',
            'skewness': 'scipy.stats.skew(signal)',
            'kurtosis': 'scipy.stats.kurtosis(signal)',
            'energy': 'np.sum(signal²)',
            'zero_crossing_rate': 'count(sign_changes) / length',
            'dominant_frequency': 'freq[np.argmax(fft_magnitude)]',
            'mean_frequency': 'weighted_mean(freq, magnitude)',
            'frequency_std': 'weighted_std(freq, magnitude)',
            'spectral_centroid': 'weighted_mean(freq, magnitude)',
            'spectral_rolloff': 'freq[cumsum(magnitude) >= 0.85*total]',
            'spectral_flux': 'np.sum(np.diff(magnitude)²)',
            'total_power': 'np.sum(magnitude²)',
            'low_freq_power': 'power[1-50Hz] / total_power',
            'mid_freq_power': 'power[50-150Hz] / total_power',
            'high_freq_power': 'power[150-400Hz] / total_power',
            'spectrogram_mean': 'np.mean(spectrogram)',
            'spectrogram_std': 'np.std(spectrogram)',
            'spectrogram_max': 'np.max(spectrogram)',
            'time_bandwidth_product': 'duration × bandwidth',
            'wavelet_energy_d1': 'energy(detail_1) / total_energy',
            'wavelet_energy_d2': 'energy(detail_2) / total_energy',
            'wavelet_energy_d3': 'energy(detail_3) / total_energy',
            'wavelet_energy_d4': 'energy(detail_4) / total_energy',
            'wavelet_energy_a4': 'energy(approx_4) / total_energy'
        }
        return methods.get(feature_key, 'Custom calculation')

    def _plot_time_domain_feature_enhanced(self, ax, feature_key, feature_value, title, description, unit):
        """Plot enhanced time domain feature for individual charts"""
        time_data = self.time_vector
        signal_data = self.sensor_data

        if feature_key == 'mean':
            ax.plot(time_data, signal_data, 'b-', alpha=0.7, linewidth=1.5, label='Signal')
            ax.axhline(y=feature_value, color='red', linestyle='--', linewidth=3,
                      label=f'Mean: {feature_value:.3f} mg')
            ax.fill_between(time_data, feature_value-0.1, feature_value+0.1, alpha=0.2, color='red')
            ax.legend(fontsize=12)

        elif feature_key == 'std':
            ax.plot(time_data, signal_data, 'b-', alpha=0.7, linewidth=1.5, label='Signal')
            mean_val = np.mean(signal_data)
            ax.axhline(y=mean_val, color='green', linestyle='-', linewidth=2, alpha=0.8, label='Mean')
            ax.axhline(y=mean_val + feature_value, color='orange', linestyle='--', linewidth=2,
                      label=f'+1σ: {mean_val + feature_value:.3f}')
            ax.axhline(y=mean_val - feature_value, color='orange', linestyle='--', linewidth=2,
                      label=f'-1σ: {mean_val - feature_value:.3f}')
            ax.fill_between(time_data, mean_val - feature_value, mean_val + feature_value,
                           alpha=0.2, color='orange', label='±1σ region')
            ax.legend(fontsize=10)

        elif feature_key == 'peak':
            ax.plot(time_data, signal_data, 'b-', alpha=0.7, linewidth=1.5, label='Signal')
            # Find both positive and negative peaks
            max_pos_idx = np.argmax(signal_data)
            min_neg_idx = np.argmin(signal_data)
            ax.scatter(time_data[max_pos_idx], signal_data[max_pos_idx],
                      color='red', s=150, zorder=5, marker='^', label=f'Max: {signal_data[max_pos_idx]:.3f}')
            ax.scatter(time_data[min_neg_idx], signal_data[min_neg_idx],
                      color='blue', s=150, zorder=5, marker='v', label=f'Min: {signal_data[min_neg_idx]:.3f}')
            ax.axhline(y=feature_value, color='red', linestyle='--', alpha=0.7,
                      label=f'Peak (abs): {feature_value:.3f}')
            ax.axhline(y=-feature_value, color='red', linestyle='--', alpha=0.7)
            ax.legend(fontsize=10)

        elif feature_key == 'rms':
            ax.plot(time_data, signal_data, 'b-', alpha=0.7, linewidth=1.5, label='Signal')
            ax.plot(time_data, np.sqrt(signal_data**2), 'g-', alpha=0.8, linewidth=2, label='|Signal|')
            ax.axhline(y=feature_value, color='red', linestyle='--', linewidth=3,
                      label=f'RMS: {feature_value:.3f} mg')
            ax.fill_between(time_data, 0, feature_value, alpha=0.1, color='red')
            ax.legend(fontsize=10)

        elif feature_key == 'peak_to_peak':
            ax.plot(time_data, signal_data, 'b-', alpha=0.7, linewidth=1.5, label='Signal')
            max_val = np.max(signal_data)
            min_val = np.min(signal_data)
            max_idx = np.argmax(signal_data)
            min_idx = np.argmin(signal_data)

            ax.scatter(time_data[max_idx], max_val, color='red', s=150, zorder=5, marker='^')
            ax.scatter(time_data[min_idx], min_val, color='blue', s=150, zorder=5, marker='v')
            ax.axhline(y=max_val, color='red', linestyle='--', alpha=0.7, label=f'Max: {max_val:.3f}')
            ax.axhline(y=min_val, color='blue', linestyle='--', alpha=0.7, label=f'Min: {min_val:.3f}')

            # Draw peak-to-peak line
            ax.annotate('', xy=(time_data[max_idx], max_val), xytext=(time_data[max_idx], min_val),
                       arrowprops=dict(arrowstyle='<->', color='purple', lw=3))
            ax.text(time_data[max_idx], (max_val + min_val)/2, f'P-P: {feature_value:.3f}',
                   ha='left', va='center', fontsize=12, fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='purple', alpha=0.7))
            ax.legend(fontsize=10)

        else:
            # Generic time domain plot for other features
            ax.plot(time_data, signal_data, 'b-', alpha=0.7, linewidth=1.5, label='Signal')

            if feature_key == 'crest_factor':
                rms_val = np.sqrt(np.mean(signal_data**2))
                peak_val = np.max(np.abs(signal_data))
                ax.axhline(y=rms_val, color='green', linestyle='--', label=f'RMS: {rms_val:.3f}')
                ax.axhline(y=peak_val, color='red', linestyle='--', label=f'Peak: {peak_val:.3f}')
                ax.axhline(y=-peak_val, color='red', linestyle='--')
                ax.text(0.5, 0.8, f'Crest Factor = Peak/RMS = {feature_value:.3f}',
                       transform=ax.transAxes, ha='center', fontsize=12, fontweight='bold',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.8))
                ax.legend(fontsize=10)

        ax.set_xlabel('Time (s)', fontsize=12)
        ax.set_ylabel('Amplitude (mg)', fontsize=12)

    def _plot_time_domain_feature_compact(self, ax, feature_key, feature_value, title, description, unit):
        """Plot compact time domain feature for overview chart"""
        # Use subset of data for compact display
        time_subset = self.time_vector[:min(200, len(self.time_vector))]
        signal_subset = self.sensor_data[:len(time_subset)]

        ax.plot(time_subset, signal_subset, 'b-', alpha=0.7, linewidth=1)

        if feature_key == 'mean':
            ax.axhline(y=feature_value, color='red', linestyle='--', linewidth=2)
        elif feature_key == 'std':
            mean_val = np.mean(signal_subset)
            ax.axhline(y=mean_val, color='red', linestyle='-', alpha=0.7)
            ax.fill_between(time_subset, mean_val - feature_value, mean_val + feature_value,
                           alpha=0.2, color='orange')
        elif feature_key == 'peak':
            peak_idx = np.argmax(np.abs(signal_subset))
            ax.scatter(time_subset[peak_idx], signal_subset[peak_idx], color='red', s=50, zorder=5)
        elif feature_key == 'rms':
            ax.axhline(y=feature_value, color='red', linestyle='--', linewidth=2)

        ax.set_xlabel('Time (s)', fontsize=9)
        ax.set_ylabel('Amplitude (mg)', fontsize=9)

    def _plot_frequency_domain_feature_enhanced(self, ax, feature_key, feature_value, title, description, unit):
        """Plot enhanced frequency domain feature for individual charts"""
        # Compute FFT for demonstration
        fft_values = fft(self.sensor_data)
        fft_freqs = fftfreq(len(self.sensor_data), 1/self.sampling_rate)
        positive_freq_idx = fft_freqs > 0
        freqs = fft_freqs[positive_freq_idx]
        magnitude = np.abs(fft_values[positive_freq_idx])

        # Limit to reasonable frequency range
        freq_limit = min(200, self.sampling_rate/2)
        freq_mask = freqs <= freq_limit
        freqs = freqs[freq_mask]
        magnitude = magnitude[freq_mask]

        if feature_key == 'dominant_frequency':
            ax.plot(freqs, magnitude, 'b-', alpha=0.7, linewidth=1.5, label='Magnitude Spectrum')
            dominant_idx = np.argmax(magnitude)
            ax.scatter(freqs[dominant_idx], magnitude[dominant_idx], color='red', s=200, zorder=5,
                      marker='*', label=f'Dominant: {feature_value:.1f} Hz')
            ax.axvline(x=feature_value, color='red', linestyle='--', linewidth=3, alpha=0.8)
            ax.fill_between([feature_value-2, feature_value+2], 0, np.max(magnitude),
                           alpha=0.2, color='red', label='Dominant region')
            ax.legend(fontsize=12)

        elif feature_key == 'spectral_centroid':
            ax.plot(freqs, magnitude, 'b-', alpha=0.7, linewidth=1.5, label='Magnitude Spectrum')
            ax.axvline(x=feature_value, color='red', linestyle='--', linewidth=3,
                      label=f'Centroid: {feature_value:.1f} Hz')
            # Show center of mass concept
            ax.fill_between(freqs, 0, magnitude, alpha=0.3, color='blue')
            ax.scatter(feature_value, np.interp(feature_value, freqs, magnitude),
                      color='red', s=200, zorder=5, marker='o')
            ax.legend(fontsize=12)

        elif feature_key == 'spectral_rolloff':
            ax.plot(freqs, magnitude, 'b-', alpha=0.7, linewidth=1.5, label='Magnitude Spectrum')
            ax.axvline(x=feature_value, color='orange', linestyle='--', linewidth=3,
                      label=f'85% Rolloff: {feature_value:.1f} Hz')

            # Show cumulative energy
            cumulative_energy = np.cumsum(magnitude**2)
            total_energy = cumulative_energy[-1]
            rolloff_85 = 0.85 * total_energy

            ax2 = ax.twinx()
            ax2.plot(freqs, cumulative_energy/total_energy, 'g--', alpha=0.8, linewidth=2,
                    label='Cumulative Energy')
            ax2.axhline(y=0.85, color='orange', linestyle=':', alpha=0.8)
            ax2.set_ylabel('Cumulative Energy Ratio', fontsize=12, color='green')
            ax2.tick_params(axis='y', labelcolor='green')

            ax.legend(loc='upper left', fontsize=10)
            ax2.legend(loc='upper right', fontsize=10)

        elif 'power' in feature_key:
            ax.plot(freqs, magnitude**2, 'b-', alpha=0.7, linewidth=1.5, label='Power Spectrum')

            if 'low' in feature_key:
                freq_band_mask = (freqs >= 1) & (freqs <= 50)
                ax.fill_between(freqs[freq_band_mask], 0, magnitude[freq_band_mask]**2,
                               alpha=0.5, color='green', label=f'Low Freq (1-50 Hz): {feature_value:.3f}')
                ax.axvspan(1, 50, alpha=0.1, color='green')
            elif 'mid' in feature_key:
                freq_band_mask = (freqs >= 50) & (freqs <= 150)
                ax.fill_between(freqs[freq_band_mask], 0, magnitude[freq_band_mask]**2,
                               alpha=0.5, color='orange', label=f'Mid Freq (50-150 Hz): {feature_value:.3f}')
                ax.axvspan(50, 150, alpha=0.1, color='orange')
            elif 'high' in feature_key:
                freq_band_mask = (freqs >= 150) & (freqs <= 400)
                if np.any(freq_band_mask):
                    ax.fill_between(freqs[freq_band_mask], 0, magnitude[freq_band_mask]**2,
                                   alpha=0.5, color='red', label=f'High Freq (150-400 Hz): {feature_value:.3f}')
                ax.axvspan(150, min(400, freq_limit), alpha=0.1, color='red')

            ax.legend(fontsize=12)

        else:
            # Generic frequency domain plot
            ax.plot(freqs, magnitude, 'b-', alpha=0.7, linewidth=1.5, label='Magnitude Spectrum')

            if feature_key == 'mean_frequency':
                ax.axvline(x=feature_value, color='red', linestyle='--', linewidth=3,
                          label=f'Mean Freq: {feature_value:.1f} Hz')
            elif feature_key == 'total_power':
                ax.fill_between(freqs, 0, magnitude**2, alpha=0.3, color='blue',
                               label=f'Total Power: {feature_value:.2e}')

            ax.legend(fontsize=12)

        ax.set_xlabel('Frequency (Hz)', fontsize=12)
        ax.set_ylabel('Magnitude', fontsize=12)
        ax.set_xlim(0, freq_limit)

    def _plot_frequency_domain_feature_compact(self, ax, feature_key, feature_value, title, description, unit):
        """Plot compact frequency domain feature for overview chart"""
        # Compute FFT for demonstration
        fft_values = fft(self.sensor_data)
        fft_freqs = fftfreq(len(self.sensor_data), 1/self.sampling_rate)
        positive_freq_idx = fft_freqs > 0
        freqs = fft_freqs[positive_freq_idx]
        magnitude = np.abs(fft_values[positive_freq_idx])

        # Limit to reasonable frequency range
        freq_limit = min(100, self.sampling_rate/2)
        freq_mask = freqs <= freq_limit
        freqs = freqs[freq_mask]
        magnitude = magnitude[freq_mask]

        ax.plot(freqs, magnitude, 'b-', alpha=0.7, linewidth=1)

        if feature_key == 'dominant_frequency':
            ax.axvline(x=feature_value, color='red', linestyle='--', linewidth=2)
        elif feature_key == 'spectral_centroid':
            ax.axvline(x=feature_value, color='red', linestyle='--', linewidth=2)
        elif 'power' in feature_key:
            if 'low' in feature_key:
                freq_band_mask = (freqs >= 1) & (freqs <= 50)
                ax.fill_between(freqs[freq_band_mask], 0, magnitude[freq_band_mask], alpha=0.3, color='green')
            elif 'mid' in feature_key:
                freq_band_mask = (freqs >= 50) & (freqs <= 100)
                ax.fill_between(freqs[freq_band_mask], 0, magnitude[freq_band_mask], alpha=0.3, color='orange')

        ax.set_xlabel('Frequency (Hz)', fontsize=9)
        ax.set_ylabel('Magnitude', fontsize=9)
        ax.set_xlim(0, freq_limit)

    def _plot_time_frequency_feature_enhanced(self, ax, feature_key, feature_value, title, description, unit):
        """Plot enhanced time-frequency domain feature for individual charts"""
        if 'wavelet' in feature_key:
            # Show wavelet decomposition
            wavelet = 'db4'
            coeffs = pywt.wavedec(self.sensor_data, wavelet, level=4)

            if 'd1' in feature_key:
                detail_coeffs = coeffs[1]
                ax.plot(detail_coeffs, 'b-', alpha=0.8, linewidth=1.5, label='Detail Level 1')
                ax.set_title(f'{title}\nHigh Frequency Components (Detail Level 1)')
                energy_ratio = np.sum(detail_coeffs**2) / np.sum(self.sensor_data**2)
                ax.text(0.02, 0.85, f'Energy Ratio: {energy_ratio:.4f}', transform=ax.transAxes,
                       fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.8))

            elif 'd2' in feature_key:
                detail_coeffs = coeffs[2]
                ax.plot(detail_coeffs, 'g-', alpha=0.8, linewidth=1.5, label='Detail Level 2')
                ax.set_title(f'{title}\nMid-High Frequency Components (Detail Level 2)')

            elif 'd3' in feature_key:
                detail_coeffs = coeffs[3]
                ax.plot(detail_coeffs, 'r-', alpha=0.8, linewidth=1.5, label='Detail Level 3')
                ax.set_title(f'{title}\nMid Frequency Components (Detail Level 3)')

            elif 'd4' in feature_key:
                detail_coeffs = coeffs[4]
                ax.plot(detail_coeffs, 'm-', alpha=0.8, linewidth=1.5, label='Detail Level 4')
                ax.set_title(f'{title}\nLow-Mid Frequency Components (Detail Level 4)')

            elif 'a4' in feature_key:
                approx_coeffs = coeffs[0]
                ax.plot(approx_coeffs, 'k-', alpha=0.8, linewidth=1.5, label='Approximation Level 4')
                ax.set_title(f'{title}\nLow Frequency Components (Approximation Level 4)')

            ax.set_xlabel('Coefficient Index', fontsize=12)
            ax.set_ylabel('Coefficient Value', fontsize=12)
            ax.legend(fontsize=12)
            ax.grid(True, alpha=0.3)

            # Add energy information
            if len(coeffs) > 1:
                total_energy = sum(np.sum(c**2) for c in coeffs)
                current_energy = np.sum(detail_coeffs**2) if 'd' in feature_key else np.sum(approx_coeffs**2)
                energy_percentage = (current_energy / total_energy) * 100
                ax.text(0.02, 0.02, f'Energy: {energy_percentage:.1f}% of total',
                       transform=ax.transAxes, fontsize=11,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.8))

        elif 'spectrogram' in feature_key:
            # Show spectrogram
            f_spec, t_spec, Sxx = signal.spectrogram(self.sensor_data, fs=self.sampling_rate,
                                                    nperseg=min(256, len(self.sensor_data)//4))

            im = ax.pcolormesh(t_spec, f_spec, 10*np.log10(Sxx + 1e-10), shading='gouraud',
                              cmap='viridis', alpha=0.8)
            ax.set_ylabel('Frequency (Hz)', fontsize=12)
            ax.set_xlabel('Time (s)', fontsize=12)
            ax.set_ylim(0, min(200, self.sampling_rate/2))

            # Add colorbar
            cbar = plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
            cbar.set_label('Power (dB)', fontsize=11)

            # Add feature-specific annotations
            if 'mean' in feature_key:
                ax.text(0.02, 0.95, f'Mean: {feature_value:.2e}', transform=ax.transAxes,
                       fontsize=12, color='white', fontweight='bold',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='black', alpha=0.7))
            elif 'max' in feature_key:
                max_idx = np.unravel_index(np.argmax(Sxx), Sxx.shape)
                ax.scatter(t_spec[max_idx[1]], f_spec[max_idx[0]], color='red', s=100,
                          marker='*', zorder=5, label=f'Max: {feature_value:.2e}')
                ax.legend(fontsize=11)

        else:
            # Generic time-frequency plot
            time_data = self.time_vector
            signal_data = self.sensor_data

            ax.plot(time_data, signal_data, 'b-', alpha=0.7, linewidth=1.5, label='Signal')

            if feature_key == 'time_bandwidth_product':
                duration = time_data[-1] - time_data[0]
                # Estimate bandwidth from frequency analysis
                fft_vals = fft(signal_data)
                freqs = fftfreq(len(signal_data), 1/self.sampling_rate)
                magnitude = np.abs(fft_vals)

                # Find bandwidth (frequency range containing 90% of energy)
                positive_freqs = freqs[freqs > 0]
                positive_mag = magnitude[freqs > 0]
                cumulative_energy = np.cumsum(positive_mag**2)
                total_energy = cumulative_energy[-1]

                f_low_idx = np.where(cumulative_energy >= 0.05 * total_energy)[0]
                f_high_idx = np.where(cumulative_energy >= 0.95 * total_energy)[0]

                if len(f_low_idx) > 0 and len(f_high_idx) > 0:
                    bandwidth = positive_freqs[f_high_idx[0]] - positive_freqs[f_low_idx[0]]
                    calculated_tbp = duration * bandwidth

                    ax.text(0.02, 0.85, f'Duration: {duration:.3f} s\nBandwidth: {bandwidth:.1f} Hz\nTBP: {calculated_tbp:.1f}',
                           transform=ax.transAxes, fontsize=11,
                           bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.8))

            ax.set_xlabel('Time (s)', fontsize=12)
            ax.set_ylabel('Amplitude (mg)', fontsize=12)
            ax.legend(fontsize=12)

    def _plot_time_frequency_feature_compact(self, ax, feature_key, feature_value, title, description, unit):
        """Plot compact time-frequency domain feature for overview chart"""
        if 'wavelet' in feature_key:
            # Show simplified wavelet decomposition
            wavelet = 'db4'
            coeffs = pywt.wavedec(self.sensor_data[:min(256, len(self.sensor_data))], wavelet, level=4)

            if 'd1' in feature_key and len(coeffs) > 1:
                ax.plot(coeffs[1], 'b-', alpha=0.7, linewidth=1)
            elif 'd2' in feature_key and len(coeffs) > 2:
                ax.plot(coeffs[2], 'g-', alpha=0.7, linewidth=1)
            elif 'd3' in feature_key and len(coeffs) > 3:
                ax.plot(coeffs[3], 'r-', alpha=0.7, linewidth=1)
            elif 'd4' in feature_key and len(coeffs) > 4:
                ax.plot(coeffs[4], 'm-', alpha=0.7, linewidth=1)
            elif 'a4' in feature_key and len(coeffs) > 0:
                ax.plot(coeffs[0], 'k-', alpha=0.7, linewidth=1)

        elif 'spectrogram' in feature_key:
            # Show simplified spectrogram
            f_spec, t_spec, Sxx = signal.spectrogram(self.sensor_data, fs=self.sampling_rate,
                                                    nperseg=min(64, len(self.sensor_data)//4))
            im = ax.pcolormesh(t_spec, f_spec, Sxx, shading='gouraud', cmap='viridis')
            ax.set_ylim(0, min(100, self.sampling_rate/2))
        else:
            # Generic compact plot
            time_subset = self.time_vector[:min(100, len(self.time_vector))]
            signal_subset = self.sensor_data[:len(time_subset)]
            ax.plot(time_subset, signal_subset, 'b-', alpha=0.7, linewidth=1)

        ax.set_xlabel('Time/Index', fontsize=9)
        ax.set_ylabel('Amplitude', fontsize=9)

    def analyze_sensor_waveform(self, file_path: str, sensor_id: str):
        """Complete sensor waveform analysis workflow"""
        print(f"🚀 Starting sensor waveform analysis for {sensor_id}")
        print("=" * 80)

        # Load waveform data
        if not self.load_sensor_waveform_data(file_path, sensor_id):
            return False

        # Extract 30 features
        if not self.extract_30_features():
            print("⚠️ Feature extraction failed, continuing with waveform analysis only...")

        # Generate visualizations
        results = {
            'waveform_plot': self.generate_waveform_plot(),
            'features_plot': self.generate_features_visualization() if self.features else False,
            'detailed_features': self.generate_detailed_features_analysis() if self.features else False
        }

        # Summary
        print("\n" + "=" * 80)
        print("📊 Waveform Analysis Summary")
        print("=" * 80)

        success_count = sum(results.values())
        total_analyses = len(results)

        for analysis_type, success in results.items():
            status = "✅ Success" if success else "❌ Failed"
            print(f"   {status} - {analysis_type.replace('_', ' ').title()}")

        print(f"\n📈 Overall Success Rate: {success_count}/{total_analyses} ({success_count/total_analyses*100:.1f}%)")
        print(f"📁 Output Directory: {self.output_dir}")
        print(f"🔧 Sensor Analyzed: {sensor_id}")
        print(f"📊 Data Points: {len(self.sensor_data) if self.sensor_data is not None else 0}")
        print(f"⏱️ Duration: {self.time_vector[-1]:.2f} seconds" if self.time_vector is not None else "")

        if self.features:
            print(f"🔍 Features Extracted: 30 time-frequency domain features")
            print(f"   - Time Domain: 10 features")
            print(f"   - Frequency Domain: 10 features")
            print(f"   - Time-Frequency Domain: 10 features")
            print(f"📊 Generated Charts:")
            print(f"   - Main waveform plot with enhanced peak detection")
            print(f"   - Features overview chart (30 features)")
            print(f"   - Detailed features analysis chart")
            print(f"   - Individual feature charts (30 separate files)")

        return success_count > 0


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description='Sensor Waveform Analyzer for Vibration Signal Analysis',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python sensor_waveform_analyzer.py experiment_data.csv Sensor_01
  python sensor_waveform_analyzer.py 25吨_三轴_40.0kmh_实验1_merged_data.csv Sensor_05
  python sensor_waveform_analyzer.py data.csv Sensor_10 --output-dir my_charts
        """
    )

    parser.add_argument('file_path', help='Path to merged_data.csv file')
    parser.add_argument('sensor_id', help='Sensor ID (e.g., Sensor_01)')
    parser.add_argument('--output-dir', default='unified_charts',
                       help='Output directory for charts (default: unified_charts)')
    parser.add_argument('--prefix', default='waveform_analysis_',
                       help='File prefix for generated charts (default: waveform_analysis_)')

    args = parser.parse_args()

    # Validate inputs
    if not os.path.exists(args.file_path):
        print(f"❌ Error: Data file not found: {args.file_path}")
        sys.exit(1)

    # Create analyzer
    analyzer = SensorWaveformAnalyzer(output_dir=args.output_dir, file_prefix=args.prefix)

    # Run analysis
    success = analyzer.analyze_sensor_waveform(
        file_path=args.file_path,
        sensor_id=args.sensor_id
    )

    if success:
        print("\n🎉 Sensor waveform analysis completed successfully!")
        print(f"📁 Check output directory: {args.output_dir}")
        print(f"📊 Generated charts:")
        print(f"   - {args.prefix}{args.sensor_id}_waveform.png (Enhanced vehicle passage waveform)")
        if analyzer.features:
            print(f"   - {args.prefix}{args.sensor_id}_features.png (30 features overview)")
            print(f"   - {args.prefix}{args.sensor_id}_detailed_features.png (Detailed feature analysis)")
            print(f"   - {args.prefix}{args.sensor_id}_feature_*.png (30 individual feature charts)")
            print(f"📈 Total charts generated: 33 high-quality visualization files")
        sys.exit(0)
    else:
        print("\n❌ Sensor waveform analysis failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
