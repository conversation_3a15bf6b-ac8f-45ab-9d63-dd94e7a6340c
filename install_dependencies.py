#!/usr/bin/env python3
"""
深度学习增强振动信号分析系统 - 依赖包自动安装脚本
适用于Windows环境，支持虚拟环境
"""

import os
import sys
import subprocess
import platform
import importlib
from typing import List, Dict, Tuple
import time

class DependencyInstaller:
    """依赖包安装器"""
    
    def __init__(self):
        """初始化安装器"""
        self.python_version = sys.version_info
        self.platform = platform.system()
        self.is_virtual_env = self.check_virtual_environment()
        self.gpu_available = False
        
        print("🔧 深度学习增强振动信号分析系统 - 依赖包安装器")
        print("=" * 70)
        print(f"Python版本: {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}")
        print(f"操作系统: {self.platform}")
        print(f"虚拟环境: {'是' if self.is_virtual_env else '否'}")
        print("=" * 70)
    
    def check_virtual_environment(self) -> bool:
        """检查是否在虚拟环境中"""
        return (hasattr(sys, 'real_prefix') or 
                (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix))
    
    def check_python_version(self) -> bool:
        """检查Python版本"""
        if self.python_version.major < 3 or (self.python_version.major == 3 and self.python_version.minor < 8):
            print(f"❌ 错误: Python版本过低 ({self.python_version.major}.{self.python_version.minor})")
            print("请使用Python 3.8或更高版本")
            return False
        
        print(f"✅ Python版本检查通过: {self.python_version.major}.{self.python_version.minor}")
        return True
    
    def upgrade_pip(self) -> bool:
        """升级pip到最新版本"""
        print("\n📦 升级pip到最新版本...")
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("✅ pip升级成功")
                return True
            else:
                print(f"⚠️  pip升级失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"⚠️  pip升级异常: {str(e)}")
            return False
    
    def install_basic_packages(self) -> bool:
        """安装基础包"""
        print("\n📦 安装基础数据处理包...")
        
        basic_packages = [
            'numpy>=1.21.0',
            'pandas>=1.3.0',
            'scipy>=1.7.0',
            'scikit-learn>=1.0.0',
            'joblib>=1.1.0',
            'chardet>=4.0.0',
            'tqdm>=4.62.0',
            'psutil>=5.8.0'
        ]
        
        return self.install_packages(basic_packages, "基础包")
    
    def install_visualization_packages(self) -> bool:
        """安装可视化包"""
        print("\n📊 安装数据可视化包...")
        
        viz_packages = [
            'matplotlib>=3.5.0',
            'seaborn>=0.11.0',
            'plotly>=5.0.0'
        ]
        
        return self.install_packages(viz_packages, "可视化包")
    
    def install_ml_packages(self) -> bool:
        """安装机器学习包"""
        print("\n🤖 安装机器学习包...")
        
        ml_packages = [
            'xgboost>=1.6.0',
            'lightgbm>=3.3.0',
            'optuna>=3.0.0',
            'statsmodels>=0.13.0'
        ]
        
        return self.install_packages(ml_packages, "机器学习包")
    
    def install_signal_processing_packages(self) -> bool:
        """安装信号处理包"""
        print("\n📡 安装信号处理包...")
        
        signal_packages = [
            'PyWavelets>=1.1.0',
            'openpyxl>=3.0.0'
        ]
        
        return self.install_packages(signal_packages, "信号处理包")
    
    def check_gpu_availability(self) -> bool:
        """检查GPU可用性"""
        print("\n🔍 检查GPU可用性...")
        
        try:
            # 尝试导入已安装的torch来检查GPU
            import torch
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0)
                print(f"✅ 检测到GPU: {gpu_name} (共{gpu_count}个)")
                self.gpu_available = True
                return True
            else:
                print("⚠️  未检测到可用的GPU")
                return False
        except ImportError:
            print("⚠️  PyTorch未安装，无法检查GPU")
            return False
    
    def install_pytorch_cpu(self) -> bool:
        """安装PyTorch CPU版本"""
        print("\n🧠 安装PyTorch (CPU版本)...")
        
        pytorch_packages = [
            'torch>=2.0.0',
            'torchvision>=0.15.0',
            'torchaudio>=2.0.0'
        ]
        
        return self.install_packages(pytorch_packages, "PyTorch CPU")
    
    def install_pytorch_gpu(self) -> bool:
        """安装PyTorch GPU版本"""
        print("\n🚀 安装PyTorch (GPU版本)...")
        print("正在安装CUDA 12.1版本的PyTorch...")
        
        try:
            # 卸载现有的PyTorch
            print("卸载现有PyTorch...")
            subprocess.run([
                sys.executable, '-m', 'pip', 'uninstall', 
                'torch', 'torchvision', 'torchaudio', '-y'
            ], capture_output=True)
            
            # 安装GPU版本
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', 
                'torch', 'torchvision', 'torchaudio',
                '--index-url', 'https://download.pytorch.org/whl/cu121'
            ], capture_output=True, text=True, timeout=1800)
            
            if result.returncode == 0:
                print("✅ PyTorch GPU版本安装成功")
                return True
            else:
                print(f"❌ PyTorch GPU版本安装失败: {result.stderr}")
                print("将回退到CPU版本...")
                return self.install_pytorch_cpu()
        except Exception as e:
            print(f"❌ PyTorch GPU安装异常: {str(e)}")
            print("将回退到CPU版本...")
            return self.install_pytorch_cpu()
    
    def install_packages(self, packages: List[str], category: str) -> bool:
        """安装包列表"""
        success_count = 0
        total_count = len(packages)
        
        for package in packages:
            try:
                print(f"  📦 安装 {package}...")
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', package
                ], capture_output=True, text=True, timeout=600)
                
                if result.returncode == 0:
                    print(f"  ✅ {package} 安装成功")
                    success_count += 1
                else:
                    print(f"  ❌ {package} 安装失败: {result.stderr}")
            except subprocess.TimeoutExpired:
                print(f"  ⏰ {package} 安装超时")
            except Exception as e:
                print(f"  ❌ {package} 安装异常: {str(e)}")
        
        success_rate = success_count / total_count
        if success_rate >= 0.8:
            print(f"✅ {category}安装完成 ({success_count}/{total_count})")
            return True
        else:
            print(f"⚠️  {category}安装部分失败 ({success_count}/{total_count})")
            return False
    
    def verify_installation(self) -> Dict[str, bool]:
        """验证安装结果"""
        print("\n🔍 验证安装结果...")
        
        # 关键包验证
        critical_packages = {
            'numpy': 'numpy',
            'pandas': 'pandas', 
            'scikit-learn': 'sklearn',
            'scipy': 'scipy',
            'matplotlib': 'matplotlib',
            'torch': 'torch',
            'xgboost': 'xgboost',
            'optuna': 'optuna',
            'chardet': 'chardet'
        }
        
        results = {}
        
        for display_name, import_name in critical_packages.items():
            try:
                importlib.import_module(import_name)
                print(f"  ✅ {display_name}")
                results[display_name] = True
            except ImportError:
                print(f"  ❌ {display_name}")
                results[display_name] = False
        
        # GPU功能验证
        try:
            import torch
            if torch.cuda.is_available():
                print(f"  🚀 GPU支持: ✅ ({torch.cuda.get_device_name(0)})")
                results['gpu_support'] = True
            else:
                print(f"  🚀 GPU支持: ❌ (使用CPU)")
                results['gpu_support'] = False
        except:
            print(f"  🚀 GPU支持: ❌ (PyTorch未安装)")
            results['gpu_support'] = False
        
        return results
    
    def generate_installation_report(self, results: Dict[str, bool]):
        """生成安装报告"""
        print("\n📊 生成安装报告...")
        
        report_file = "installation_report.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("深度学习增强振动信号分析系统 - 依赖包安装报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"安装时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Python版本: {self.python_version.major}.{self.python_version.minor}.{self.python_version.micro}\n")
            f.write(f"操作系统: {self.platform}\n")
            f.write(f"虚拟环境: {'是' if self.is_virtual_env else '否'}\n\n")
            
            f.write("安装结果:\n")
            f.write("-" * 30 + "\n")
            
            success_count = 0
            total_count = len(results)
            
            for package, success in results.items():
                status = "✅ 成功" if success else "❌ 失败"
                f.write(f"{package}: {status}\n")
                if success:
                    success_count += 1
            
            f.write(f"\n总体成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)\n")
            
            if success_count >= total_count * 0.8:
                f.write("\n🎉 安装基本成功，系统可以正常使用\n")
            else:
                f.write("\n⚠️  安装存在问题，请检查失败的包\n")
        
        print(f"✅ 安装报告已保存: {report_file}")
    
    def run_installation(self):
        """运行完整安装流程"""
        print("🚀 开始安装深度学习增强振动信号分析系统依赖包...")
        
        # 1. 检查Python版本
        if not self.check_python_version():
            return False
        
        # 2. 升级pip
        self.upgrade_pip()
        
        # 3. 安装基础包
        if not self.install_basic_packages():
            print("❌ 基础包安装失败，无法继续")
            return False
        
        # 4. 安装可视化包
        self.install_visualization_packages()
        
        # 5. 安装机器学习包
        self.install_ml_packages()
        
        # 6. 安装信号处理包
        self.install_signal_processing_packages()
        
        # 7. 安装PyTorch
        print("\n🧠 安装深度学习框架...")
        user_choice = input("是否安装GPU版本的PyTorch? (y/n, 默认n): ").lower().strip()
        
        if user_choice in ['y', 'yes']:
            self.install_pytorch_gpu()
        else:
            self.install_pytorch_cpu()
        
        # 8. 验证安装
        results = self.verify_installation()
        
        # 9. 生成报告
        self.generate_installation_report(results)
        
        # 10. 显示总结
        success_count = sum(results.values())
        total_count = len(results)
        
        print(f"\n🎉 安装完成!")
        print(f"成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count >= total_count * 0.8:
            print("✅ 系统已准备就绪，可以开始使用!")
            print("\n下一步:")
            print("  python setup_real_data_training.py")
        else:
            print("⚠️  部分包安装失败，请检查错误信息")
        
        return success_count >= total_count * 0.8

def main():
    """主函数"""
    try:
        installer = DependencyInstaller()
        installer.run_installation()
    except KeyboardInterrupt:
        print("\n⚠️  用户中断安装")
    except Exception as e:
        print(f"\n❌ 安装过程异常: {str(e)}")

if __name__ == "__main__":
    main()
