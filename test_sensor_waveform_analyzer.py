#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script for Sensor Waveform Analyzer
Tests the functionality of the sensor waveform analysis and visualization program

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import sys
import numpy as np
import pandas as pd
from pathlib import Path
import time

def create_test_waveform_data():
    """Create synthetic test waveform data for testing"""
    print("🔧 Creating synthetic waveform test data...")
    
    try:
        # Parameters for realistic vehicle passage simulation
        duration = 1.0  # seconds
        sampling_rate = 1000  # Hz
        n_samples = int(duration * sampling_rate)
        time_vector = np.linspace(0, duration, n_samples)
        
        # Create synthetic vehicle passage data
        np.random.seed(42)
        
        # Vehicle parameters
        vehicle_speed = 60  # km/h
        axle_count = 3
        axle_spacing = 0.15  # seconds between axles
        
        # Create base signal
        data = {'count': range(n_samples)}
        
        for sensor_id in range(1, 21):
            # Generate realistic vibration signal for vehicle passage
            signal = np.zeros(n_samples)
            
            # Add vehicle axle responses
            for axle in range(axle_count):
                # Axle passage time
                axle_time = 0.3 + axle * axle_spacing
                axle_idx = int(axle_time * sampling_rate)
                
                # Axle response (damped oscillation)
                if axle_idx < n_samples:
                    # Create axle response window
                    response_duration = 0.1  # seconds
                    response_samples = int(response_duration * sampling_rate)
                    
                    start_idx = max(0, axle_idx - response_samples//2)
                    end_idx = min(n_samples, axle_idx + response_samples//2)
                    
                    # Generate damped oscillation
                    t_response = np.linspace(0, response_duration, end_idx - start_idx)
                    frequency = 50 + sensor_id * 2  # Sensor-specific frequency
                    amplitude = 10 + axle * 5  # Increasing amplitude for heavier axles
                    
                    # Damped sinusoidal response
                    damping = 5.0
                    axle_response = amplitude * np.exp(-damping * t_response) * np.sin(2 * np.pi * frequency * t_response)
                    
                    signal[start_idx:end_idx] += axle_response
            
            # Add sensor-specific characteristics
            if sensor_id <= 5:  # Group 1: Lower frequency emphasis
                signal += 0.5 * np.sin(2 * np.pi * 25 * time_vector)
            elif sensor_id <= 10:  # Group 2: Higher frequency emphasis
                signal += 0.3 * np.sin(2 * np.pi * 80 * time_vector)
            elif sensor_id <= 15:  # Group 3: Mid frequency emphasis
                signal += 0.4 * np.sin(2 * np.pi * 45 * time_vector)
            else:  # Group 4: Mixed frequencies
                signal += 0.2 * np.sin(2 * np.pi * 35 * time_vector)
                signal += 0.2 * np.sin(2 * np.pi * 65 * time_vector)
            
            # Add background noise
            noise = np.random.normal(0, 0.5, n_samples)
            signal += noise
            
            # Add some outliers for realism
            outlier_indices = np.random.choice(n_samples, 5, replace=False)
            signal[outlier_indices] += np.random.uniform(-3, 3, 5)
            
            # Store in dataframe
            data[f'Sensor_{sensor_id:02d}'] = signal
        
        # Add timestamp column
        timestamps = pd.date_range('2024-01-01 10:00:00', periods=n_samples, freq='1ms')
        data['timestamp'] = timestamps
        
        # Create DataFrame and save
        df = pd.DataFrame(data)
        test_file_path = 'test_vehicle_passage_data.csv'
        df.to_csv(test_file_path, index=False)
        
        print(f"   ✅ Test waveform data created: {test_file_path}")
        print(f"   📊 Data shape: {df.shape}")
        print(f"   ⏱️ Duration: {duration} seconds")
        print(f"   📈 Sampling rate: {sampling_rate} Hz")
        print(f"   🔢 Sensors: {len([col for col in df.columns if 'Sensor' in col])}")
        print(f"   🚗 Simulated vehicle: {axle_count} axles, {vehicle_speed} km/h")
        
        return test_file_path, df
        
    except Exception as e:
        print(f"   ❌ Error creating test waveform data: {str(e)}")
        return None, None

def test_waveform_analyzer_basic():
    """Test basic functionality of waveform analyzer"""
    print("\n🧪 Testing basic waveform analyzer functionality...")
    
    try:
        from sensor_waveform_analyzer import SensorWaveformAnalyzer
        
        # Create test data
        test_file, test_df = create_test_waveform_data()
        if test_file is None:
            return False
        
        # Initialize analyzer
        analyzer = SensorWaveformAnalyzer(
            output_dir='test_waveform_charts',
            file_prefix='test_waveform_'
        )
        
        # Test data loading
        print("   📂 Testing waveform data loading...")
        success = analyzer.load_sensor_waveform_data(test_file, 'Sensor_01')
        if not success:
            print("   ❌ Waveform data loading failed")
            return False
        print("   ✅ Waveform data loading successful")
        
        # Test feature extraction
        print("   🔧 Testing 30 features extraction...")
        success = analyzer.extract_30_features()
        if not success:
            print("   ❌ Feature extraction failed")
            return False
        print("   ✅ Feature extraction successful")
        
        # Verify features
        if len(analyzer.features) != 30:
            print(f"   ❌ Expected 30 features, got {len(analyzer.features)}")
            return False
        print(f"   ✅ All 30 features extracted correctly")
        
        # Test waveform plot generation
        print("   📈 Testing waveform plot generation...")
        success = analyzer.generate_waveform_plot()
        if not success:
            print("   ❌ Waveform plot generation failed")
            return False
        print("   ✅ Waveform plot generation successful")
        
        # Test features visualization
        print("   📊 Testing features visualization...")
        success = analyzer.generate_features_visualization()
        if not success:
            print("   ❌ Features visualization failed")
            return False
        print("   ✅ Features visualization successful")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Basic functionality test failed: {str(e)}")
        return False

def test_complete_workflow():
    """Test complete waveform analysis workflow"""
    print("\n🧪 Testing complete waveform analysis workflow...")
    
    try:
        from sensor_waveform_analyzer import SensorWaveformAnalyzer
        
        # Create test data
        test_file, test_df = create_test_waveform_data()
        if test_file is None:
            return False
        
        # Initialize analyzer
        analyzer = SensorWaveformAnalyzer(
            output_dir='test_waveform_charts',
            file_prefix='workflow_test_'
        )
        
        # Test complete workflow
        print("   🚀 Running complete waveform analysis workflow...")
        start_time = time.time()
        
        success = analyzer.analyze_sensor_waveform(
            file_path=test_file,
            sensor_id='Sensor_05'
        )
        
        end_time = time.time()
        
        if success:
            print(f"   ✅ Complete workflow successful ({end_time - start_time:.1f}s)")
            return True
        else:
            print("   ❌ Complete workflow failed")
            return False
        
    except Exception as e:
        print(f"   ❌ Complete workflow test failed: {str(e)}")
        return False

def test_multiple_sensors():
    """Test analysis of multiple sensors"""
    print("\n🧪 Testing multiple sensor analysis...")
    
    try:
        from sensor_waveform_analyzer import SensorWaveformAnalyzer
        
        # Create test data
        test_file, test_df = create_test_waveform_data()
        if test_file is None:
            return False
        
        # Test multiple sensors
        test_sensors = ['Sensor_01', 'Sensor_10', 'Sensor_20']
        success_count = 0
        
        for sensor_id in test_sensors:
            print(f"   🔍 Testing {sensor_id}...")
            
            analyzer = SensorWaveformAnalyzer(
                output_dir='test_waveform_charts',
                file_prefix=f'multi_{sensor_id.lower()}_'
            )
            
            success = analyzer.analyze_sensor_waveform(
                file_path=test_file,
                sensor_id=sensor_id
            )
            
            if success:
                print(f"      ✅ {sensor_id} analysis successful")
                success_count += 1
            else:
                print(f"      ❌ {sensor_id} analysis failed")
        
        print(f"   📊 Multiple sensor test: {success_count}/{len(test_sensors)} successful")
        return success_count == len(test_sensors)
        
    except Exception as e:
        print(f"   ❌ Multiple sensor test failed: {str(e)}")
        return False

def test_output_quality():
    """Test output chart quality and naming"""
    print("\n🧪 Testing output chart quality...")
    
    try:
        output_dir = Path('test_waveform_charts')
        
        if not output_dir.exists():
            print("   ❌ Output directory not found")
            return False
        
        # Check generated files
        chart_files = list(output_dir.glob('*.png'))
        
        if not chart_files:
            print("   ❌ No chart files found")
            return False
        
        print(f"   📊 Found {len(chart_files)} chart files")
        
        # Check naming convention and quality
        naming_issues = 0
        quality_issues = 0
        waveform_charts = 0
        feature_charts = 0
        
        for chart_file in chart_files:
            # Check naming convention
            if not (chart_file.name.startswith('test_waveform_') or 
                   chart_file.name.startswith('workflow_test_') or
                   chart_file.name.startswith('multi_')):
                naming_issues += 1
                print(f"      ⚠️ Naming issue: {chart_file.name}")
            
            # Check file size (should be reasonable for 330 DPI)
            file_size = chart_file.stat().st_size
            if file_size < 100 * 1024:  # Less than 100KB might be too small for high-res charts
                quality_issues += 1
                print(f"      ⚠️ Small file size: {chart_file.name} ({file_size/1024:.1f}KB)")
            
            # Count chart types
            if '_waveform.png' in chart_file.name:
                waveform_charts += 1
            elif '_features.png' in chart_file.name:
                feature_charts += 1
        
        print(f"   📋 Naming convention: {len(chart_files) - naming_issues}/{len(chart_files)} correct")
        print(f"   📏 File quality: {len(chart_files) - quality_issues}/{len(chart_files)} good size")
        print(f"   📈 Waveform charts: {waveform_charts}")
        print(f"   📊 Feature charts: {feature_charts}")
        
        # List example files
        print("   📁 Example generated files:")
        for i, chart_file in enumerate(chart_files[:6]):
            print(f"      {i+1}. {chart_file.name}")
        if len(chart_files) > 6:
            print(f"      ... and {len(chart_files) - 6} more files")
        
        # Check if we have both types of charts
        has_both_types = waveform_charts > 0 and feature_charts > 0
        success_rate = (len(chart_files) - naming_issues - quality_issues) / len(chart_files)
        
        return success_rate >= 0.8 and has_both_types
        
    except Exception as e:
        print(f"   ❌ Output quality test failed: {str(e)}")
        return False

def cleanup_test_files():
    """Clean up test files"""
    print("\n🧹 Cleaning up test files...")
    
    try:
        # Remove test data file
        test_file = 'test_vehicle_passage_data.csv'
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"   ✅ Removed: {test_file}")
        
        # Remove test chart directory
        import shutil
        test_dir = Path('test_waveform_charts')
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"   ✅ Removed: {test_dir}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Cleanup failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Sensor Waveform Analyzer Tests")
    print("=" * 80)
    
    # Run all tests
    tests = [
        ("Basic Functionality", test_waveform_analyzer_basic),
        ("Complete Workflow", test_complete_workflow),
        ("Multiple Sensors", test_multiple_sensors),
        ("Output Quality", test_output_quality)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 Sensor Waveform Analyzer Test Results")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Sensor Waveform Analyzer is working correctly.")
        print("📊 Generated charts include:")
        print("   - Time-series waveform plots with vehicle passage analysis")
        print("   - 30 time-frequency domain features visualization")
    elif passed >= total * 0.75:
        print("⚠️  Most tests passed. System is functional with minor issues.")
    else:
        print("❌ Multiple test failures. Please check the implementation.")
    
    # Cleanup
    cleanup_test_files()
    
    return passed >= total * 0.75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
