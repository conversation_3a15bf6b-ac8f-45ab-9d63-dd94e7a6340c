#!/usr/bin/env python3
"""
快速验证select_best_model方法修复效果
只测试核心功能，不运行耗时的深度学习模型
"""

import numpy as np
from optimized_ml_models import OptimizedMLModels

def quick_test_select_best_model():
    """快速测试select_best_model方法"""
    print("🔧 快速验证select_best_model方法修复效果")
    print("=" * 60)
    
    # 初始化训练器
    trainer = OptimizedMLModels()
    
    # 测试1: 检查方法是否存在
    print("\n1️⃣ 检查select_best_model方法是否存在...")
    if hasattr(trainer, 'select_best_model'):
        print("   ✅ select_best_model方法存在")
    else:
        print("   ❌ select_best_model方法不存在")
        return False
    
    # 测试2: 检查方法是否可调用
    print("\n2️⃣ 检查select_best_model方法是否可调用...")
    if callable(getattr(trainer, 'select_best_model', None)):
        print("   ✅ select_best_model方法可调用")
    else:
        print("   ❌ select_best_model方法不可调用")
        return False
    
    # 测试3: 使用模拟数据测试方法
    print("\n3️⃣ 使用模拟数据测试方法...")
    
    # 创建模拟的训练结果
    mock_results = {
        'Random Forest': {
            'r2_score': 0.85,
            'mae': 12.5,
            'training_time': 1.2
        },
        'XGBoost': {
            'r2_score': 0.88,
            'mae': 10.8,
            'training_time': 2.1
        },
        'SVM': {
            'r2_score': 0.75,
            'mae': 15.2,
            'training_time': 0.8
        },
        'Failed Model': {
            'error': 'Training failed'
        }
    }
    
    try:
        # 测试回归任务
        result = trainer.select_best_model(mock_results, 'regression')
        
        print("   ✅ 回归任务测试成功!")
        print(f"      最佳模型: {result['best_model']}")
        print(f"      最佳分数: {result['best_score']:.4f}")
        print(f"      总模型数: {result['total_models']}")
        print(f"      成功模型数: {result['successful_models']}")
        
        # 验证结果正确性
        if result['best_model'] == 'XGBoost' and result['best_score'] == 0.88:
            print("   ✅ 结果验证正确")
        else:
            print("   ❌ 结果验证失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 回归任务测试失败: {str(e)}")
        return False
    
    # 测试4: 分类任务
    print("\n4️⃣ 测试分类任务...")
    
    mock_classification_results = {
        'Random Forest': {
            'accuracy': 0.92,
            'precision': 0.91,
            'training_time': 1.5
        },
        'SVM': {
            'accuracy': 0.89,
            'precision': 0.88,
            'training_time': 0.5
        }
    }
    
    try:
        result = trainer.select_best_model(mock_classification_results, 'classification')
        
        print("   ✅ 分类任务测试成功!")
        print(f"      最佳模型: {result['best_model']}")
        print(f"      最佳分数: {result['best_score']:.4f}")
        
        # 验证结果正确性
        if result['best_model'] == 'Random Forest' and result['best_score'] == 0.92:
            print("   ✅ 结果验证正确")
        else:
            print("   ❌ 结果验证失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 分类任务测试失败: {str(e)}")
        return False
    
    # 测试5: 边界情况
    print("\n5️⃣ 测试边界情况...")
    
    # 空结果
    try:
        result = trainer.select_best_model({}, 'regression')
        if result['best_model'] == 'None':
            print("   ✅ 空结果处理正确")
        else:
            print("   ❌ 空结果处理错误")
            return False
    except Exception as e:
        print(f"   ❌ 空结果测试失败: {str(e)}")
        return False
    
    # 全部失败的结果
    try:
        failed_results = {
            'Model1': {'error': 'Failed'},
            'Model2': {'error': 'Failed'}
        }
        result = trainer.select_best_model(failed_results, 'regression')
        if result['best_model'] == 'None':
            print("   ✅ 全部失败结果处理正确")
        else:
            print("   ❌ 全部失败结果处理错误")
            return False
    except Exception as e:
        print(f"   ❌ 全部失败结果测试失败: {str(e)}")
        return False
    
    # 测试6: 检查generate_performance_statistics方法
    print("\n6️⃣ 检查generate_performance_statistics方法...")
    
    if hasattr(trainer, 'generate_performance_statistics'):
        print("   ✅ generate_performance_statistics方法存在")
        
        try:
            stats = trainer.generate_performance_statistics(mock_results, 'regression')
            if 'performance_stats' in stats and 'model_ranking' in stats:
                print("   ✅ generate_performance_statistics方法工作正常")
            else:
                print("   ❌ generate_performance_statistics方法返回格式错误")
                return False
        except Exception as e:
            print(f"   ❌ generate_performance_statistics方法测试失败: {str(e)}")
            return False
    else:
        print("   ❌ generate_performance_statistics方法不存在")
        return False
    
    return True

def test_unified_vibration_analysis_integration():
    """测试与主程序的集成"""
    print("\n🔗 测试与unified_vibration_analysis.py的集成...")
    
    try:
        # 检查主程序是否能正确导入和使用OptimizedMLModels
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 初始化系统
        system = UnifiedVibrationAnalysisSystem()
        
        # 检查是否使用了OptimizedMLModels
        if hasattr(system, 'trainer'):
            print("   ✅ 主程序成功导入OptimizedMLModels")
            
            # 检查trainer是否有select_best_model方法
            if hasattr(system.trainer, 'select_best_model'):
                print("   ✅ 主程序中的trainer具有select_best_model方法")
                return True
            else:
                print("   ❌ 主程序中的trainer缺少select_best_model方法")
                return False
        else:
            print("   ❌ 主程序未正确初始化trainer")
            return False
            
    except Exception as e:
        print(f"   ❌ 主程序集成测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 OptimizedMLModels类select_best_model方法修复验证")
    print("🎯 目标: 验证'OptimizedMLModels' object has no attribute 'select_best_model'错误已修复")
    print("=" * 80)
    
    # 执行快速测试
    core_test_passed = quick_test_select_best_model()
    
    # 执行集成测试
    integration_test_passed = test_unified_vibration_analysis_integration()
    
    # 总结结果
    print("\n" + "=" * 80)
    print("🎉 修复验证结果总结")
    print("=" * 80)
    
    if core_test_passed and integration_test_passed:
        print("✅ 所有测试通过!")
        print("🎯 select_best_model方法修复成功!")
        print("🔧 OptimizedMLModels类现在完全可用")
        print("💡 原错误 'OptimizedMLModels' object has no attribute 'select_best_model' 已解决")
        print("\n🚀 建议下一步:")
        print("   1. 运行 unified_vibration_analysis.py 进行完整测试")
        print("   2. 验证整个振动信号分析系统是否正常工作")
        return True
    else:
        print("❌ 部分测试失败")
        if not core_test_passed:
            print("   - 核心功能测试失败")
        if not integration_test_passed:
            print("   - 主程序集成测试失败")
        print("🔧 需要进一步检查和修复")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
