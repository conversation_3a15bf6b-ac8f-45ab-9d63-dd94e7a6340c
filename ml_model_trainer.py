#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习模型训练器
第二阶段：机器学习模型训练 (基于扩展后的高质量数据集)

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import os
import json
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# 机器学习库
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold, KFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier, GradientBoostingRegressor, GradientBoostingClassifier
from sklearn.metrics import mean_squared_error, r2_score, accuracy_score, classification_report, confusion_matrix
import xgboost as xgb
import joblib
from datetime import datetime

class MLModelTrainer:
    """机器学习模型训练器"""
    
    def __init__(self, data_file: str = "combined_features.csv", output_dir: str = "ml_models"):
        self.data_file = data_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.training_results = {}
        
    def train_all_models(self) -> Dict:
        """训练所有模型的主流程"""
        print("🤖 开始机器学习模型训练...")
        print("=" * 80)
        
        # 步骤1: 加载和预处理数据
        X, y_speed, y_load, y_axle = self.load_and_preprocess_data()
        
        # 步骤2: 训练速度预测模型
        speed_results = self.train_speed_prediction_models(X, y_speed)
        
        # 步骤3: 训练载重预测模型
        load_results = self.train_load_prediction_models(X, y_load)
        
        # 步骤4: 训练轴型分类模型
        axle_results = self.train_axle_classification_models(X, y_axle)
        
        # 步骤5: 生成综合报告
        comprehensive_report = self.generate_comprehensive_report(speed_results, load_results, axle_results)
        
        return comprehensive_report
    
    def load_and_preprocess_data(self) -> Tuple[pd.DataFrame, pd.Series, pd.Series, pd.Series]:
        """加载和预处理数据"""
        print("\n📁 步骤1: 加载和预处理数据")
        print("-" * 50)
        
        # 加载数据
        df = pd.read_csv(self.data_file)
        print(f"   📊 原始数据: {df.shape[0]} 个样本, {df.shape[1]} 个特征")
        
        # 分离特征和目标变量
        target_columns = ['speed_kmh', 'load_tons', 'axle_type']
        feature_columns = [col for col in df.columns if col not in target_columns + ['file_path', 'file_name', 'data_source']]

        X = df[feature_columns].copy()

        # 确保特征数据都是数值型
        X = X.select_dtypes(include=[np.number])

        # 处理目标变量
        y_speed = pd.to_numeric(df['speed_kmh'], errors='coerce')
        y_load = pd.to_numeric(df['load_tons'], errors='coerce')
        y_axle = pd.to_numeric(df['axle_type'], errors='coerce')

        # 数据清洗
        print(f"   🧹 数据清洗前: 速度{y_speed.notna().sum()}, 载重{y_load.notna().sum()}, 轴型{y_axle.notna().sum()}")

        # 移除无效的速度数据（0 km/h可能是异常值）
        valid_speed_mask = (y_speed > 0) & (y_speed <= 150)

        # 移除无效的载重数据
        valid_load_mask = (y_load > 0) & (y_load <= 100)

        # 移除无效的轴型数据
        valid_axle_mask = y_axle.isin([2, 3, 4, 5, 6])

        print(f"   ✅ 有效数据: 速度{valid_speed_mask.sum()}, 载重{valid_load_mask.sum()}, 轴型{valid_axle_mask.sum()}")

        # 处理特征数据的缺失值
        X = X.fillna(X.mean())
        
        # 移除常数特征
        constant_features = X.columns[X.std() == 0]
        if len(constant_features) > 0:
            X = X.drop(columns=constant_features)
            print(f"   🗑️  移除常数特征: {len(constant_features)} 个")
        
        print(f"   📊 预处理后特征: {X.shape[1]} 个")
        
        return X, y_speed, y_load, y_axle
    
    def train_speed_prediction_models(self, X: pd.DataFrame, y_speed: pd.Series) -> Dict:
        """训练速度预测模型"""
        print("\n🚗 步骤2: 训练速度预测模型 (目标R²>0.90)")
        print("-" * 50)
        
        # 过滤有效数据
        valid_mask = (y_speed > 0) & (y_speed <= 150) & y_speed.notna()
        X_speed = X[valid_mask].copy()
        y_speed_clean = y_speed[valid_mask].copy()
        
        print(f"   📊 速度预测数据: {len(X_speed)} 个样本")
        print(f"   📈 速度范围: {y_speed_clean.min():.1f} - {y_speed_clean.max():.1f} km/h")
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X_speed, y_speed_clean, test_size=0.2, random_state=42
        )
        
        # 特征标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        self.scalers['speed'] = scaler
        
        # 定义模型
        models = {
            'XGBoost': xgb.XGBRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                random_state=42,
                n_jobs=-1
            ),
            'RandomForest': RandomForestRegressor(
                n_estimators=200,
                max_depth=15,
                random_state=42,
                n_jobs=-1
            ),
            'GradientBoosting': GradientBoostingRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                random_state=42
            )
        }
        
        results = {}
        
        for name, model in models.items():
            print(f"   🔧 训练 {name} 模型...")
            
            # 训练模型
            if name == 'XGBoost':
                model.fit(X_train, y_train)
            else:
                model.fit(X_train_scaled, y_train)
            
            # 预测
            if name == 'XGBoost':
                y_pred = model.predict(X_test)
                y_train_pred = model.predict(X_train)
            else:
                y_pred = model.predict(X_test_scaled)
                y_train_pred = model.predict(X_train_scaled)
            
            # 评估
            test_r2 = r2_score(y_test, y_pred)
            train_r2 = r2_score(y_train, y_train_pred)
            test_rmse = np.sqrt(mean_squared_error(y_test, y_pred))
            
            # 5折交叉验证
            if name == 'XGBoost':
                cv_scores = cross_val_score(model, X_speed, y_speed_clean, cv=5, scoring='r2')
            else:
                X_speed_scaled = scaler.transform(X_speed)
                cv_scores = cross_val_score(model, X_speed_scaled, y_speed_clean, cv=5, scoring='r2')
            
            results[name] = {
                'model': model,
                'test_r2': test_r2,
                'train_r2': train_r2,
                'test_rmse': test_rmse,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'target_achieved': test_r2 > 0.90
            }
            
            print(f"      测试R²: {test_r2:.4f}, 训练R²: {train_r2:.4f}")
            print(f"      交叉验证R²: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
            print(f"      目标达成: {'✅' if test_r2 > 0.90 else '❌'} (目标: R²>0.90)")
            
            # 保存模型
            model_path = self.output_dir / f"speed_prediction_{name.lower()}.joblib"
            joblib.dump(model, model_path)
        
        # 保存标准化器
        scaler_path = self.output_dir / "speed_scaler.joblib"
        joblib.dump(scaler, scaler_path)
        
        self.models['speed'] = results
        return results
    
    def train_load_prediction_models(self, X: pd.DataFrame, y_load: pd.Series) -> Dict:
        """训练载重预测模型"""
        print("\n⚖️  步骤3: 训练载重预测模型 (目标R²>0.85)")
        print("-" * 50)
        
        # 过滤有效数据
        valid_mask = (y_load > 0) & (y_load <= 100) & y_load.notna()
        X_load = X[valid_mask].copy()
        y_load_clean = y_load[valid_mask].copy()
        
        print(f"   📊 载重预测数据: {len(X_load)} 个样本")
        print(f"   ⚖️  载重范围: {y_load_clean.min():.1f} - {y_load_clean.max():.1f} 吨")
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X_load, y_load_clean, test_size=0.2, random_state=42
        )
        
        # 特征标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        self.scalers['load'] = scaler
        
        # 定义模型
        models = {
            'XGBoost': xgb.XGBRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                random_state=42,
                n_jobs=-1
            ),
            'RandomForest': RandomForestRegressor(
                n_estimators=200,
                max_depth=15,
                random_state=42,
                n_jobs=-1
            ),
            'GradientBoosting': GradientBoostingRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                random_state=42
            )
        }
        
        results = {}
        
        for name, model in models.items():
            print(f"   🔧 训练 {name} 模型...")
            
            # 训练模型
            if name == 'XGBoost':
                model.fit(X_train, y_train)
            else:
                model.fit(X_train_scaled, y_train)
            
            # 预测
            if name == 'XGBoost':
                y_pred = model.predict(X_test)
                y_train_pred = model.predict(X_train)
            else:
                y_pred = model.predict(X_test_scaled)
                y_train_pred = model.predict(X_train_scaled)
            
            # 评估
            test_r2 = r2_score(y_test, y_pred)
            train_r2 = r2_score(y_train, y_train_pred)
            test_rmse = np.sqrt(mean_squared_error(y_test, y_pred))
            
            # 5折交叉验证
            if name == 'XGBoost':
                cv_scores = cross_val_score(model, X_load, y_load_clean, cv=5, scoring='r2')
            else:
                X_load_scaled = scaler.transform(X_load)
                cv_scores = cross_val_score(model, X_load_scaled, y_load_clean, cv=5, scoring='r2')
            
            results[name] = {
                'model': model,
                'test_r2': test_r2,
                'train_r2': train_r2,
                'test_rmse': test_rmse,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'target_achieved': test_r2 > 0.85
            }
            
            print(f"      测试R²: {test_r2:.4f}, 训练R²: {train_r2:.4f}")
            print(f"      交叉验证R²: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
            print(f"      目标达成: {'✅' if test_r2 > 0.85 else '❌'} (目标: R²>0.85)")
            
            # 保存模型
            model_path = self.output_dir / f"load_prediction_{name.lower()}.joblib"
            joblib.dump(model, model_path)
        
        # 保存标准化器
        scaler_path = self.output_dir / "load_scaler.joblib"
        joblib.dump(scaler, scaler_path)
        
        self.models['load'] = results
        return results

    def train_axle_classification_models(self, X: pd.DataFrame, y_axle: pd.Series) -> Dict:
        """训练轴型分类模型"""
        print("\n🚛 步骤4: 训练轴型分类模型 (目标准确率>90%)")
        print("-" * 50)

        # 过滤有效数据
        valid_mask = y_axle.isin([2, 3, 4, 5, 6]) & y_axle.notna()
        X_axle = X[valid_mask].copy()
        y_axle_clean = y_axle[valid_mask].copy()

        print(f"   📊 轴型分类数据: {len(X_axle)} 个样本")
        print(f"   🚛 轴型分布: {dict(y_axle_clean.value_counts().sort_index())}")

        # 标签编码
        label_encoder = LabelEncoder()
        y_axle_encoded = label_encoder.fit_transform(y_axle_clean)
        self.encoders['axle'] = label_encoder

        # 数据分割 (分层抽样)
        X_train, X_test, y_train, y_test = train_test_split(
            X_axle, y_axle_encoded, test_size=0.2, random_state=42, stratify=y_axle_encoded
        )

        # 特征标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)

        self.scalers['axle'] = scaler

        # 定义模型
        models = {
            'XGBoost': xgb.XGBClassifier(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                random_state=42,
                n_jobs=-1
            ),
            'RandomForest': RandomForestClassifier(
                n_estimators=200,
                max_depth=15,
                random_state=42,
                n_jobs=-1
            ),
            'GradientBoosting': GradientBoostingClassifier(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                random_state=42
            )
        }

        results = {}

        for name, model in models.items():
            print(f"   🔧 训练 {name} 模型...")

            # 训练模型
            if name == 'XGBoost':
                model.fit(X_train, y_train)
            else:
                model.fit(X_train_scaled, y_train)

            # 预测
            if name == 'XGBoost':
                y_pred = model.predict(X_test)
                y_train_pred = model.predict(X_train)
            else:
                y_pred = model.predict(X_test_scaled)
                y_train_pred = model.predict(X_train_scaled)

            # 评估
            test_accuracy = accuracy_score(y_test, y_pred)
            train_accuracy = accuracy_score(y_train, y_train_pred)

            # 5折交叉验证 (分层)
            skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
            if name == 'XGBoost':
                cv_scores = cross_val_score(model, X_axle, y_axle_encoded, cv=skf, scoring='accuracy')
            else:
                X_axle_scaled = scaler.transform(X_axle)
                cv_scores = cross_val_score(model, X_axle_scaled, y_axle_encoded, cv=skf, scoring='accuracy')

            results[name] = {
                'model': model,
                'test_accuracy': test_accuracy,
                'train_accuracy': train_accuracy,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'target_achieved': test_accuracy > 0.90
            }

            print(f"      测试准确率: {test_accuracy:.4f}, 训练准确率: {train_accuracy:.4f}")
            print(f"      交叉验证准确率: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
            print(f"      目标达成: {'✅' if test_accuracy > 0.90 else '❌'} (目标: 准确率>90%)")

            # 保存模型
            model_path = self.output_dir / f"axle_classification_{name.lower()}.joblib"
            joblib.dump(model, model_path)

        # 保存标准化器和编码器
        scaler_path = self.output_dir / "axle_scaler.joblib"
        encoder_path = self.output_dir / "axle_encoder.joblib"
        joblib.dump(scaler, scaler_path)
        joblib.dump(label_encoder, encoder_path)

        self.models['axle'] = results
        return results

    def generate_comprehensive_report(self, speed_results: Dict, load_results: Dict, axle_results: Dict) -> Dict:
        """生成综合报告"""
        print("\n📋 步骤5: 生成综合性能报告")
        print("-" * 50)

        report = {
            "training_summary": {
                "timestamp": datetime.now().isoformat(),
                "data_file": self.data_file,
                "total_models_trained": len(speed_results) + len(load_results) + len(axle_results)
            },
            "speed_prediction": {
                "task": "速度预测",
                "target": "R² > 0.90",
                "models": {}
            },
            "load_prediction": {
                "task": "载重预测",
                "target": "R² > 0.85",
                "models": {}
            },
            "axle_classification": {
                "task": "轴型分类",
                "target": "准确率 > 90%",
                "models": {}
            },
            "overall_performance": {}
        }

        # 速度预测结果
        best_speed_model = None
        best_speed_r2 = 0

        for name, result in speed_results.items():
            model_info = {
                "test_r2": float(result['test_r2']),
                "train_r2": float(result['train_r2']),
                "cv_mean": float(result['cv_mean']),
                "cv_std": float(result['cv_std']),
                "test_rmse": float(result['test_rmse']),
                "target_achieved": result['target_achieved']
            }
            report["speed_prediction"]["models"][name] = model_info

            if result['test_r2'] > best_speed_r2:
                best_speed_r2 = result['test_r2']
                best_speed_model = name

        # 载重预测结果
        best_load_model = None
        best_load_r2 = 0

        for name, result in load_results.items():
            model_info = {
                "test_r2": float(result['test_r2']),
                "train_r2": float(result['train_r2']),
                "cv_mean": float(result['cv_mean']),
                "cv_std": float(result['cv_std']),
                "test_rmse": float(result['test_rmse']),
                "target_achieved": result['target_achieved']
            }
            report["load_prediction"]["models"][name] = model_info

            if result['test_r2'] > best_load_r2:
                best_load_r2 = result['test_r2']
                best_load_model = name

        # 轴型分类结果
        best_axle_model = None
        best_axle_accuracy = 0

        for name, result in axle_results.items():
            model_info = {
                "test_accuracy": float(result['test_accuracy']),
                "train_accuracy": float(result['train_accuracy']),
                "cv_mean": float(result['cv_mean']),
                "cv_std": float(result['cv_std']),
                "target_achieved": result['target_achieved']
            }
            report["axle_classification"]["models"][name] = model_info

            if result['test_accuracy'] > best_axle_accuracy:
                best_axle_accuracy = result['test_accuracy']
                best_axle_model = name

        # 整体性能总结
        report["overall_performance"] = {
            "best_speed_model": best_speed_model,
            "best_speed_r2": float(best_speed_r2),
            "speed_target_achieved": best_speed_r2 > 0.90,
            "best_load_model": best_load_model,
            "best_load_r2": float(best_load_r2),
            "load_target_achieved": best_load_r2 > 0.85,
            "best_axle_model": best_axle_model,
            "best_axle_accuracy": float(best_axle_accuracy),
            "axle_target_achieved": best_axle_accuracy > 0.90,
            "all_targets_achieved": (best_speed_r2 > 0.90) and (best_load_r2 > 0.85) and (best_axle_accuracy > 0.90)
        }

        # 保存报告
        report_path = self.output_dir / "ml_training_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        # 打印摘要
        print(f"   🎯 性能摘要:")
        print(f"      速度预测最佳模型: {best_speed_model} (R²={best_speed_r2:.4f}) {'✅' if best_speed_r2 > 0.90 else '❌'}")
        print(f"      载重预测最佳模型: {best_load_model} (R²={best_load_r2:.4f}) {'✅' if best_load_r2 > 0.85 else '❌'}")
        print(f"      轴型分类最佳模型: {best_axle_model} (准确率={best_axle_accuracy:.4f}) {'✅' if best_axle_accuracy > 0.90 else '❌'}")
        print(f"      所有目标达成: {'✅' if report['overall_performance']['all_targets_achieved'] else '❌'}")
        print(f"   📋 详细报告已保存: {report_path}")

        return report

def main():
    """主函数"""
    trainer = MLModelTrainer()
    report = trainer.train_all_models()

    print(f"\n🎉 机器学习模型训练完成！")
    print(f"   训练模型数: {report['training_summary']['total_models_trained']}")
    print(f"   所有目标达成: {'✅' if report['overall_performance']['all_targets_achieved'] else '❌'}")

    return report

if __name__ == "__main__":
    main()
