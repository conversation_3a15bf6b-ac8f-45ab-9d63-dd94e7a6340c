#!/usr/bin/env python3
"""
真实数据训练设置脚本
完整的从数据迁移到训练完成的自动化流程
"""

import os
import sys
import time
import subprocess
import logging
from datetime import datetime
from typing import Dict, Any

# 设置Windows编码
if sys.platform.startswith('win'):
    try:
        os.system('chcp 65001 > nul')
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
    except:
        pass

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealDataTrainingSetup:
    """真实数据训练设置器"""
    
    def __init__(self):
        """初始化设置器"""
        self.start_time = time.time()
        self.results = {}
        
    def check_prerequisites(self) -> bool:
        """检查前置条件"""
        logger.info("🔍 检查前置条件...")
        
        # 检查data目录
        if not os.path.exists('./data'):
            print("❌ 错误: 未找到 './data' 目录")
            print("请确保您的真实振动信号数据位于 './data' 文件夹中")
            return False
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            print(f"❌ 错误: Python版本过低 ({python_version.major}.{python_version.minor})")
            print("请使用Python 3.8或更高版本")
            return False
        
        # 检查必要的包 (包名 -> 导入名映射)
        required_packages = {
            'pandas': 'pandas',
            'numpy': 'numpy',
            'scikit-learn': 'sklearn',  # 注意：包名和导入名不同
            'chardet': 'chardet'
        }
        missing_packages = []

        for package_name, import_name in required_packages.items():
            try:
                __import__(import_name)
                print(f"✅ {package_name}: 已安装")
            except ImportError:
                missing_packages.append(package_name)
                print(f"❌ {package_name}: 未安装")
        
        if missing_packages:
            print(f"❌ 错误: 缺少必要的Python包: {missing_packages}")
            print("请运行: pip install " + " ".join(missing_packages))
            return False
        
        print("✅ 前置条件检查通过")
        return True
    
    def step_1_data_migration(self) -> bool:
        """步骤1: 数据迁移"""
        print("\n" + "=" * 70)
        print("📁 步骤1: 真实数据迁移和格式转换")
        print("=" * 70)
        print("正在将 './data' 目录中的真实振动信号数据迁移到标准格式...")
        
        try:
            # 运行数据迁移脚本
            result = subprocess.run([
                sys.executable, 'migrate_real_data.py'
            ], capture_output=True, text=True, timeout=600, encoding='utf-8', errors='ignore')
            
            if result.returncode == 0:
                print("✅ 数据迁移完成")
                
                # 检查迁移结果
                if os.path.exists('./raw_data/migration_report.txt'):
                    print("📊 迁移报告已生成: ./raw_data/migration_report.txt")
                
                # 检查生成的文件
                vibration_dir = './raw_data/vibration_signals'
                if os.path.exists(vibration_dir):
                    files = [f for f in os.listdir(vibration_dir) if f.endswith('.csv')]
                    print(f"📁 已迁移 {len(files)} 个振动信号文件")
                
                self.results['data_migration'] = {'success': True, 'files_migrated': len(files) if 'files' in locals() else 0}
                return True
            else:
                print(f"❌ 数据迁移失败")
                if result.stderr:
                    print(f"错误信息: {result.stderr}")
                self.results['data_migration'] = {'success': False, 'error': result.stderr}
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ 数据迁移超时")
            self.results['data_migration'] = {'success': False, 'error': 'timeout'}
            return False
        except Exception as e:
            print(f"❌ 数据迁移异常: {str(e)}")
            self.results['data_migration'] = {'success': False, 'error': str(e)}
            return False
    
    def step_2_data_validation(self) -> bool:
        """步骤2: 数据验证"""
        print("\n" + "=" * 70)
        print("🔍 步骤2: 数据验证")
        print("=" * 70)
        print("验证迁移后的数据格式和质量...")
        
        try:
            result = subprocess.run([
                sys.executable, 'data_validation.py'
            ], capture_output=True, text=True, timeout=300, encoding='utf-8', errors='ignore')
            
            if result.returncode == 0:
                print("✅ 数据验证通过")
                
                # 检查验证报告
                if os.path.exists('data_validation_report.txt'):
                    print("📊 验证报告已生成: data_validation_report.txt")
                
                self.results['data_validation'] = {'success': True}
                return True
            else:
                print("⚠️  数据验证有警告，但可以继续")
                self.results['data_validation'] = {'success': True, 'warnings': True}
                return True
                
        except Exception as e:
            print(f"⚠️  数据验证跳过: {str(e)}")
            self.results['data_validation'] = {'success': True, 'skipped': True}
            return True
    
    def step_3_data_preprocessing(self) -> bool:
        """步骤3: 数据预处理"""
        print("\n" + "=" * 70)
        print("🔄 步骤3: 数据预处理和特征提取")
        print("=" * 70)
        print("从振动信号中提取特征并生成训练数据集...")
        
        try:
            result = subprocess.run([
                sys.executable, 'data_preprocessing.py'
            ], capture_output=True, text=True, timeout=1800, encoding='utf-8', errors='ignore')
            
            if result.returncode == 0:
                print("✅ 数据预处理完成")
                
                # 检查生成的数据集
                datasets_found = []
                expected_datasets = ['speed_regression.csv', 'load_regression.csv', 'type_classification.csv']
                
                for dataset in expected_datasets:
                    dataset_path = os.path.join('./training_datasets', dataset)
                    if os.path.exists(dataset_path):
                        import pandas as pd
                        try:
                            df = pd.read_csv(dataset_path, encoding='utf-8')
                            datasets_found.append((dataset, len(df)))
                            print(f"📊 {dataset}: {len(df)} 条记录")
                        except:
                            datasets_found.append((dataset, 0))
                            print(f"📊 {dataset}: 已生成")
                
                if datasets_found:
                    self.results['data_preprocessing'] = {'success': True, 'datasets': datasets_found}
                    return True
                else:
                    print("❌ 未生成任何训练数据集")
                    self.results['data_preprocessing'] = {'success': False, 'error': 'no_datasets'}
                    return False
            else:
                print(f"❌ 数据预处理失败")
                if result.stderr:
                    print(f"错误信息: {result.stderr}")
                self.results['data_preprocessing'] = {'success': False, 'error': result.stderr}
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ 数据预处理超时")
            self.results['data_preprocessing'] = {'success': False, 'error': 'timeout'}
            return False
        except Exception as e:
            print(f"❌ 数据预处理异常: {str(e)}")
            self.results['data_preprocessing'] = {'success': False, 'error': str(e)}
            return False
    
    def step_4_model_training(self) -> bool:
        """步骤4: 模型训练"""
        print("\n" + "=" * 70)
        print("🚀 步骤4: 深度学习增强模型训练")
        print("=" * 70)
        print("开始训练传统机器学习和深度学习模型...")
        print("注意: 这个过程可能需要30分钟到2小时，请耐心等待...")
        
        try:
            # 首先尝试快速训练（传统机器学习）
            print("\n🤖 开始传统机器学习训练...")
            result = subprocess.run([
                sys.executable, 'ml/gpu_optimized_training.py'
            ], capture_output=True, text=True, timeout=3600, encoding='utf-8', errors='ignore')
            
            traditional_success = result.returncode == 0
            if traditional_success:
                print("✅ 传统机器学习训练完成")
                
                # 解析输出中的最佳分数
                output_lines = result.stdout.split('\n') if result.stdout else []
                best_scores = {}
                
                for line in output_lines:
                    if 'R²' in line and '=' in line:
                        try:
                            parts = line.split(':')
                            if len(parts) >= 2:
                                model_name = parts[0].strip()
                                score_part = parts[1].strip()
                                if 'R²' in score_part and '=' in score_part:
                                    score = float(score_part.split('=')[1].strip())
                                    best_scores[model_name] = score
                        except:
                            pass
                
                if best_scores:
                    print("\n📊 传统机器学习最佳结果:")
                    for model, score in best_scores.items():
                        status = "🎉" if score > 0.75 else "📈"
                        print(f"   {status} {model}: R² = {score:.4f}")
            else:
                print("⚠️  传统机器学习训练失败，继续深度学习训练")
            
            # 深度学习训练
            print("\n🧠 开始深度学习训练...")
            result = subprocess.run([
                sys.executable, 'run_deep_learning_optimization.py'
            ], capture_output=True, text=True, timeout=7200, encoding='utf-8', errors='ignore')
            
            dl_success = result.returncode == 0
            if dl_success:
                print("✅ 深度学习训练完成")
                
                # 检查结果文件
                results_file = 'deep_learning_optimization_results.json'
                if os.path.exists(results_file):
                    try:
                        import json
                        with open(results_file, 'r', encoding='utf-8') as f:
                            dl_results = json.load(f)
                        
                        print("\n📊 深度学习最佳结果:")
                        for dataset_name, dataset_results in dl_results.items():
                            print(f"\n   📊 {dataset_name}:")
                            bp_score = dataset_results.get('bp_score', 0.0)
                            tcn_score = dataset_results.get('tcn_score', 0.0)
                            
                            bp_status = "🎉" if bp_score > 0.75 else "📈"
                            tcn_status = "🎉" if tcn_score > 0.75 else "📈"
                            
                            print(f"      {bp_status} BP神经网络: {bp_score:.4f}")
                            print(f"      {tcn_status} TCN: {tcn_score:.4f}")
                    except:
                        print("📊 深度学习结果文件读取失败")
            else:
                print("⚠️  深度学习训练失败")
            
            # 判断整体成功
            overall_success = traditional_success or dl_success
            
            self.results['model_training'] = {
                'success': overall_success,
                'traditional_ml': traditional_success,
                'deep_learning': dl_success
            }
            
            return overall_success
            
        except subprocess.TimeoutExpired:
            print("❌ 模型训练超时")
            self.results['model_training'] = {'success': False, 'error': 'timeout'}
            return False
        except Exception as e:
            print(f"❌ 模型训练异常: {str(e)}")
            self.results['model_training'] = {'success': False, 'error': str(e)}
            return False
    
    def step_5_results_analysis(self):
        """步骤5: 结果分析"""
        print("\n" + "=" * 70)
        print("📊 步骤5: 训练结果分析")
        print("=" * 70)
        
        try:
            result = subprocess.run([
                sys.executable, 'analyze_results.py'
            ], capture_output=True, text=True, timeout=300, encoding='utf-8', errors='ignore')
            
            if result.returncode == 0:
                print("✅ 结果分析完成")
                print("📁 分析报告已生成: ./results/reports/")
                print("📊 性能图表已生成: ./results/plots/")
            else:
                print("⚠️  结果分析跳过")
        except:
            print("⚠️  结果分析跳过")
    
    def generate_final_summary(self):
        """生成最终总结"""
        end_time = time.time()
        total_time = end_time - self.start_time
        
        print("\n" + "=" * 70)
        print("🎉 真实数据训练完成总结")
        print("=" * 70)
        
        print(f"⏱️  总耗时: {total_time/3600:.2f} 小时")
        
        # 显示各步骤结果
        steps = [
            ('data_migration', '数据迁移'),
            ('data_validation', '数据验证'),
            ('data_preprocessing', '数据预处理'),
            ('model_training', '模型训练')
        ]
        
        print(f"\n📋 执行步骤结果:")
        for step_key, step_name in steps:
            if step_key in self.results:
                result = self.results[step_key]
                status = "✅ 成功" if result.get('success', False) else "❌ 失败"
                print(f"   {step_name}: {status}")
            else:
                print(f"   {step_name}: ⏭️ 跳过")
        
        # 显示生成的文件
        print(f"\n📁 生成的重要文件:")
        important_files = [
            ('./raw_data/', '迁移后的原始数据'),
            ('./training_datasets/', '训练数据集'),
            ('./results/', '训练结果和报告'),
            ('./models/', '训练好的模型'),
            ('data_validation_report.txt', '数据验证报告'),
            ('deep_learning_optimization_results.json', '深度学习结果')
        ]
        
        for file_path, description in important_files:
            if os.path.exists(file_path):
                print(f"   ✅ {description}: {file_path}")
            else:
                print(f"   ❌ {description}: {file_path} (未生成)")
        
        # 下一步建议
        print(f"\n💡 下一步建议:")
        if self.results.get('model_training', {}).get('success', False):
            print("   1. 🔍 查看训练结果: ./results/reports/")
            print("   2. 📊 查看性能图表: ./results/plots/")
            print("   3. 🚀 部署模型: python deploy_models.py")
            print("   4. 📖 查看完整指南: COMPLETE_USER_GUIDE.md")
        else:
            print("   1. 📋 检查错误日志")
            print("   2. 🔧 调整数据或参数")
            print("   3. 🔄 重新运行训练")
        
        print(f"\n🎊 真实数据训练流程完成!")
    
    def run_complete_setup(self):
        """运行完整设置流程"""
        print("🚀 真实数据深度学习训练设置")
        print("=" * 70)
        print("自动化处理从真实数据到模型训练的完整流程")
        print("=" * 70)
        
        # 检查前置条件
        if not self.check_prerequisites():
            return
        
        # 执行各个步骤
        steps = [
            ("数据迁移", self.step_1_data_migration),
            ("数据验证", self.step_2_data_validation),
            ("数据预处理", self.step_3_data_preprocessing),
            ("模型训练", self.step_4_model_training),
            ("结果分析", self.step_5_results_analysis)
        ]
        
        for step_name, step_func in steps:
            try:
                if step_name == "结果分析":
                    step_func()  # 结果分析不返回布尔值
                else:
                    success = step_func()
                    if not success and step_name in ["数据迁移", "数据预处理"]:
                        print(f"\n❌ 关键步骤 '{step_name}' 失败，停止执行")
                        break
            except KeyboardInterrupt:
                print(f"\n⚠️  用户中断在步骤: {step_name}")
                break
            except Exception as e:
                print(f"\n❌ 步骤 '{step_name}' 异常: {str(e)}")
                if step_name in ["数据迁移", "数据预处理"]:
                    break
        
        # 生成最终总结
        self.generate_final_summary()

def main():
    """主函数"""
    try:
        setup = RealDataTrainingSetup()
        setup.run_complete_setup()
    except KeyboardInterrupt:
        print("\n⚠️  用户中断设置流程")
    except Exception as e:
        print(f"\n❌ 设置流程异常: {str(e)}")

if __name__ == "__main__":
    main()
