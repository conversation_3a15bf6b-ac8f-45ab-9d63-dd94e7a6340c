#!/usr/bin/env python3
"""
模型分析专用可视化模块
包含学习曲线、特征重要性、超参数优化历史等高级分析可视化
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from improved_visualization_base import ImprovedVisualizationBase
import warnings
warnings.filterwarnings('ignore')

class ModelAnalysisVisualizer(ImprovedVisualizationBase):
    """模型分析可视化器"""
    
    def __init__(self, output_dir='model_analysis_visualizations'):
        """初始化模型分析可视化器"""
        super().__init__(output_dir)
        print("🎯 模型分析可视化器初始化完成")
    
    def create_learning_curves_plot(self, train_sizes, train_scores, val_scores, model_name='Model',
                                  task_name='模型训练', language='chinese'):
        """创建学习曲线图（单独图表）"""
        print(f"   📊 生成{model_name}学习曲线图 ({language})...")
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.chart_config['figsize_double'])
        
        if language == 'chinese':
            fig.suptitle(f'{model_name} - {task_name}学习曲线分析', 
                        fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        else:
            fig.suptitle(f'{model_name} - {task_name} Learning Curve Analysis', 
                        fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        
        # 计算均值和标准差
        train_mean = np.mean(train_scores, axis=1)
        train_std = np.std(train_scores, axis=1)
        val_mean = np.mean(val_scores, axis=1)
        val_std = np.std(val_scores, axis=1)
        
        # 1. 学习曲线
        ax1.plot(train_sizes, train_mean, 'o-', color=self.academic_colors['primary'], 
                linewidth=3, markersize=8, 
                label='训练集' if language == 'chinese' else 'Training Set')
        ax1.fill_between(train_sizes, train_mean - train_std, train_mean + train_std, 
                        alpha=0.2, color=self.academic_colors['primary'])
        
        ax1.plot(train_sizes, val_mean, 'o-', color=self.academic_colors['warning'], 
                linewidth=3, markersize=8, 
                label='验证集' if language == 'chinese' else 'Validation Set')
        ax1.fill_between(train_sizes, val_mean - val_std, val_mean + val_std, 
                        alpha=0.2, color=self.academic_colors['warning'])
        
        if language == 'chinese':
            ax1.set_xlabel('训练样本数量', fontweight='bold')
            ax1.set_ylabel('模型性能 (R²)', fontweight='bold')
            ax1.set_title('学习曲线', fontweight='bold')
        else:
            ax1.set_xlabel('Training Sample Size', fontweight='bold')
            ax1.set_ylabel('Model Performance (R²)', fontweight='bold')
            ax1.set_title('Learning Curve', fontweight='bold')
        
        ax1.legend(loc='lower right')
        ax1.grid(True, alpha=self.chart_config['grid_alpha'])
        
        # 添加性能标注
        final_train_score = train_mean[-1]
        final_val_score = val_mean[-1]
        ax1.annotate(f'训练集: {final_train_score:.3f}' if language == 'chinese' else f'Training: {final_train_score:.3f}',
                    xy=(train_sizes[-1], final_train_score), xytext=(10, 10),
                    textcoords='offset points', fontsize=10, fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.8))
        
        ax1.annotate(f'验证集: {final_val_score:.3f}' if language == 'chinese' else f'Validation: {final_val_score:.3f}',
                    xy=(train_sizes[-1], final_val_score), xytext=(10, -20),
                    textcoords='offset points', fontsize=10, fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='lightyellow', alpha=0.8))
        
        # 2. 过拟合分析
        performance_gap = train_mean - val_mean
        ax2.plot(train_sizes, performance_gap, 'o-', color=self.academic_colors['secondary'], 
                linewidth=3, markersize=8)
        ax2.fill_between(train_sizes, 0, performance_gap, alpha=0.3, color=self.academic_colors['secondary'])
        
        if language == 'chinese':
            ax2.set_xlabel('训练样本数量', fontweight='bold')
            ax2.set_ylabel('性能差异 (训练集 - 验证集)', fontweight='bold')
            ax2.set_title('过拟合分析', fontweight='bold')
        else:
            ax2.set_xlabel('Training Sample Size', fontweight='bold')
            ax2.set_ylabel('Performance Gap (Train - Validation)', fontweight='bold')
            ax2.set_title('Overfitting Analysis', fontweight='bold')
        
        ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7, linewidth=2)
        ax2.grid(True, alpha=self.chart_config['grid_alpha'])
        
        # 添加过拟合程度标注
        final_gap = performance_gap[-1]
        if final_gap > 0.1:
            overfitting_level = '严重过拟合' if language == 'chinese' else 'Severe Overfitting'
            color = 'red'
        elif final_gap > 0.05:
            overfitting_level = '轻微过拟合' if language == 'chinese' else 'Mild Overfitting'
            color = 'orange'
        else:
            overfitting_level = '拟合良好' if language == 'chinese' else 'Good Fit'
            color = 'green'
        
        ax2.text(0.7, 0.9, f'状态: {overfitting_level}' if language == 'chinese' else f'Status: {overfitting_level}',
                transform=ax2.transAxes, fontsize=12, fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.3))
        
        plt.tight_layout()
        
        # 保存图表
        filename = f'{model_name}_learning_curves_{language}.png'
        filepath = self.save_chart(fig, filename, language, 'model_comparison')
        plt.close(fig)
        
        print(f"      ✅ 学习曲线图已保存: {filepath}")
        
        return self.create_chart_metadata({
            'type': 'learning_curves',
            'model': model_name,
            'language': language,
            'description': f'{task_name}学习曲线分析' if language == 'chinese' else f'{task_name} Learning Curve Analysis',
            'final_scores': {'train': final_train_score, 'validation': final_val_score, 'gap': final_gap}
        })
    
    def create_feature_importance_plot(self, feature_importance_dict, feature_names=None, 
                                     top_n=15, language='chinese'):
        """创建特征重要性对比图（单独图表）"""
        print(f"   📊 生成特征重要性对比图 ({language})...")
        
        if feature_names is None:
            max_features = max(len(imp) for imp in feature_importance_dict.values())
            if language == 'chinese':
                feature_names = [f'特征_{i+1}' for i in range(max_features)]
            else:
                feature_names = [f'Feature_{i+1}' for i in range(max_features)]
        
        # 限制显示的特征数量
        n_features = min(top_n, len(feature_names))
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.chart_config['figsize_double'])
        
        if language == 'chinese':
            fig.suptitle('特征重要性对比分析', fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        else:
            fig.suptitle('Feature Importance Comparison Analysis', fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        
        # 1. 特征重要性热力图
        models = list(feature_importance_dict.keys())
        importance_matrix = []
        
        for model in models:
            if model in feature_importance_dict:
                importance = np.array(feature_importance_dict[model][:n_features])
                # 归一化到0-1范围
                if importance.max() > 0:
                    importance = importance / importance.max()
            else:
                importance = np.random.rand(n_features)
            importance_matrix.append(importance)
        
        importance_matrix = np.array(importance_matrix)
        
        im = ax1.imshow(importance_matrix, cmap='YlOrRd', aspect='auto', vmin=0, vmax=1)
        ax1.set_xticks(range(n_features))
        ax1.set_xticklabels(feature_names[:n_features], rotation=45, ha='right')
        ax1.set_yticks(range(len(models)))
        ax1.set_yticklabels(models)
        
        if language == 'chinese':
            ax1.set_title('特征重要性热力图', fontweight='bold')
            ax1.set_xlabel('特征名称', fontweight='bold')
            ax1.set_ylabel('模型', fontweight='bold')
        else:
            ax1.set_title('Feature Importance Heatmap', fontweight='bold')
            ax1.set_xlabel('Feature Names', fontweight='bold')
            ax1.set_ylabel('Models', fontweight='bold')
        
        # 添加数值标注（仅对重要特征）
        for i in range(len(models)):
            for j in range(n_features):
                if importance_matrix[i, j] > 0.3:  # 只标注重要特征
                    text = ax1.text(j, i, f'{importance_matrix[i, j]:.2f}',
                                   ha="center", va="center", color="black", 
                                   fontsize=8, fontweight='bold')
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax1)
        if language == 'chinese':
            cbar.set_label('归一化重要性分数', fontweight='bold')
        else:
            cbar.set_label('Normalized Importance Score', fontweight='bold')
        
        # 2. 平均特征重要性排序
        avg_importance = np.mean(importance_matrix, axis=0)
        sorted_indices = np.argsort(avg_importance)[::-1]
        
        # 选择前10个最重要的特征
        top_indices = sorted_indices[:10]
        top_importance = avg_importance[top_indices]
        top_feature_names = [feature_names[i] for i in top_indices]
        
        colors = self.get_color_palette(len(top_importance))
        bars = ax2.barh(range(len(top_importance)), top_importance, 
                       color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)
        
        ax2.set_yticks(range(len(top_importance)))
        ax2.set_yticklabels(top_feature_names)
        ax2.invert_yaxis()  # 最重要的特征在顶部
        
        if language == 'chinese':
            ax2.set_title('Top 10 特征重要性排序', fontweight='bold')
            ax2.set_xlabel('平均重要性分数', fontweight='bold')
            ax2.set_ylabel('特征名称', fontweight='bold')
        else:
            ax2.set_title('Top 10 Feature Importance Ranking', fontweight='bold')
            ax2.set_xlabel('Average Importance Score', fontweight='bold')
            ax2.set_ylabel('Feature Names', fontweight='bold')
        
        # 添加数值标签
        for bar, value in zip(bars, top_importance):
            width = bar.get_width()
            ax2.text(width + 0.01, bar.get_y() + bar.get_height()/2.,
                    f'{value:.3f}', ha='left', va='center', fontsize=10, fontweight='bold')
        
        ax2.grid(True, alpha=self.chart_config['grid_alpha'], axis='x')
        
        plt.tight_layout()
        
        # 保存图表
        filename = f'feature_importance_comparison_{language}.png'
        filepath = self.save_chart(fig, filename, language, 'feature_analysis')
        plt.close(fig)
        
        print(f"      ✅ 特征重要性对比图已保存: {filepath}")
        
        return self.create_chart_metadata({
            'type': 'feature_importance',
            'language': language,
            'description': '特征重要性对比分析' if language == 'chinese' else 'Feature Importance Comparison Analysis',
            'models': models,
            'top_features': top_feature_names[:5]  # 保存前5个最重要特征
        })
    
    def create_hyperparameter_optimization_plot(self, optimization_history, model_name='Model', language='chinese'):
        """创建超参数优化历史图（单独图表）"""
        print(f"   📊 生成{model_name}超参数优化历史图 ({language})...")
        
        if not optimization_history:
            # 生成模拟数据
            n_trials = 50
            optimization_history = {
                'trial_numbers': list(range(1, n_trials + 1)),
                'objective_values': [],
                'best_values': [],
                'parameters': {
                    'n_estimators': np.random.randint(50, 500, n_trials),
                    'max_depth': np.random.randint(3, 20, n_trials),
                    'learning_rate': np.random.uniform(0.01, 0.3, n_trials)
                }
            }
            
            # 生成目标函数值（模拟收敛过程）
            base_score = 0.7
            for i in range(n_trials):
                noise = np.random.normal(0, 0.05)
                improvement = 0.2 * (1 - np.exp(-i/20))  # 指数收敛
                score = base_score + improvement + noise
                optimization_history['objective_values'].append(max(0, min(1, score)))
            
            # 计算最佳值历史
            best_so_far = -np.inf
            for val in optimization_history['objective_values']:
                if val > best_so_far:
                    best_so_far = val
                optimization_history['best_values'].append(best_so_far)
        
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.chart_config['figsize_quad'])
        
        if language == 'chinese':
            fig.suptitle(f'{model_name} - 超参数优化历史分析', 
                        fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        else:
            fig.suptitle(f'{model_name} - Hyperparameter Optimization History', 
                        fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        
        trials = optimization_history['trial_numbers']
        
        # 1. 目标函数值历史
        ax1.plot(trials, optimization_history['objective_values'], 'o', 
                color=self.academic_colors['primary'], alpha=0.6, markersize=4, 
                label='试验值' if language == 'chinese' else 'Trial Values')
        ax1.plot(trials, optimization_history['best_values'], 
                color=self.academic_colors['warning'], linewidth=3, 
                label='最佳值' if language == 'chinese' else 'Best Value')
        
        if language == 'chinese':
            ax1.set_xlabel('试验次数', fontweight='bold')
            ax1.set_ylabel('目标函数值 (R²)', fontweight='bold')
            ax1.set_title('优化收敛过程', fontweight='bold')
        else:
            ax1.set_xlabel('Trial Number', fontweight='bold')
            ax1.set_ylabel('Objective Value (R²)', fontweight='bold')
            ax1.set_title('Optimization Convergence', fontweight='bold')
        
        ax1.legend()
        ax1.grid(True, alpha=self.chart_config['grid_alpha'])
        
        # 标注最终最佳值
        final_best = optimization_history['best_values'][-1]
        ax1.annotate(f'最佳: {final_best:.4f}' if language == 'chinese' else f'Best: {final_best:.4f}',
                    xy=(trials[-1], final_best), xytext=(10, 10),
                    textcoords='offset points', fontsize=10, fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.8))
        
        # 2. 参数分布 - n_estimators
        scatter1 = ax2.scatter(trials, optimization_history['parameters']['n_estimators'], 
                              c=optimization_history['objective_values'], cmap='viridis', 
                              alpha=0.7, s=50, edgecolors='black', linewidth=0.5)
        
        if language == 'chinese':
            ax2.set_xlabel('试验次数', fontweight='bold')
            ax2.set_ylabel('n_estimators', fontweight='bold')
            ax2.set_title('n_estimators 参数探索', fontweight='bold')
        else:
            ax2.set_xlabel('Trial Number', fontweight='bold')
            ax2.set_ylabel('n_estimators', fontweight='bold')
            ax2.set_title('n_estimators Parameter Exploration', fontweight='bold')
        
        ax2.grid(True, alpha=self.chart_config['grid_alpha'])
        
        # 3. 参数分布 - max_depth
        scatter2 = ax3.scatter(trials, optimization_history['parameters']['max_depth'], 
                              c=optimization_history['objective_values'], cmap='viridis', 
                              alpha=0.7, s=50, edgecolors='black', linewidth=0.5)
        
        if language == 'chinese':
            ax3.set_xlabel('试验次数', fontweight='bold')
            ax3.set_ylabel('max_depth', fontweight='bold')
            ax3.set_title('max_depth 参数探索', fontweight='bold')
        else:
            ax3.set_xlabel('Trial Number', fontweight='bold')
            ax3.set_ylabel('max_depth', fontweight='bold')
            ax3.set_title('max_depth Parameter Exploration', fontweight='bold')
        
        ax3.grid(True, alpha=self.chart_config['grid_alpha'])
        
        # 4. 参数分布 - learning_rate
        scatter3 = ax4.scatter(trials, optimization_history['parameters']['learning_rate'], 
                              c=optimization_history['objective_values'], cmap='viridis', 
                              alpha=0.7, s=50, edgecolors='black', linewidth=0.5)
        
        if language == 'chinese':
            ax4.set_xlabel('试验次数', fontweight='bold')
            ax4.set_ylabel('learning_rate', fontweight='bold')
            ax4.set_title('learning_rate 参数探索', fontweight='bold')
        else:
            ax4.set_xlabel('Trial Number', fontweight='bold')
            ax4.set_ylabel('learning_rate', fontweight='bold')
            ax4.set_title('learning_rate Parameter Exploration', fontweight='bold')
        
        ax4.grid(True, alpha=self.chart_config['grid_alpha'])
        
        # 添加统一的颜色条
        cbar = plt.colorbar(scatter3, ax=[ax2, ax3, ax4], location='right', shrink=0.8)
        if language == 'chinese':
            cbar.set_label('目标函数值', fontweight='bold')
        else:
            cbar.set_label('Objective Value', fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图表
        filename = f'{model_name}_hyperparameter_optimization_{language}.png'
        filepath = self.save_chart(fig, filename, language, 'optimization_analysis')
        plt.close(fig)
        
        print(f"      ✅ 超参数优化历史图已保存: {filepath}")
        
        return self.create_chart_metadata({
            'type': 'hyperparameter_optimization',
            'model': model_name,
            'language': language,
            'description': f'{model_name}超参数优化历史' if language == 'chinese' else f'{model_name} Hyperparameter Optimization History',
            'final_best_score': final_best,
            'total_trials': len(trials)
        })

def main():
    """测试函数"""
    print("🧪 测试模型分析可视化器...")
    
    # 初始化可视化器
    model_viz = ModelAnalysisVisualizer()
    
    # 生成测试数据
    np.random.seed(42)
    
    # 1. 测试学习曲线
    train_sizes = np.array([100, 200, 400, 600, 800, 1000])
    train_scores = np.random.rand(len(train_sizes), 5) * 0.3 + 0.7  # 5折交叉验证
    val_scores = train_scores - np.random.rand(len(train_sizes), 5) * 0.1  # 验证集稍低
    
    for language in ['chinese', 'english']:
        model_viz.create_learning_curves_plot(train_sizes, train_scores, val_scores, 
                                            'Random Forest', '速度预测', language)
    
    # 2. 测试特征重要性
    feature_importance_dict = {
        'Random Forest': np.random.rand(20),
        'XGBoost': np.random.rand(20),
        'Gradient Boosting': np.random.rand(20)
    }
    
    feature_names = [f'sensor_{i+1:02d}' for i in range(20)]
    
    for language in ['chinese', 'english']:
        model_viz.create_feature_importance_plot(feature_importance_dict, feature_names, language=language)
    
    # 3. 测试超参数优化历史
    for language in ['chinese', 'english']:
        model_viz.create_hyperparameter_optimization_plot({}, 'XGBoost', language)
    
    print("✅ 模型分析可视化器测试完成!")

if __name__ == "__main__":
    main()
