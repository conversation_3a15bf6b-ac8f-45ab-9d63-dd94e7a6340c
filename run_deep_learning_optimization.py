#!/usr/bin/env python3
"""
运行深度学习优化的主脚本
集成BP神经网络和TCN到振动信号分析系统
"""

import numpy as np
import pandas as pd
import logging
import time
import os
import sys
from typing import Dict, Any, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# 添加ml目录到路径
sys.path.append('./ml')

try:
    from ml.bp_neural_network import BPNeuralNetworkOptimizer
    from ml.temporal_convolutional_network import TCNOptimizer
    import torch
    DEEP_LEARNING_AVAILABLE = True
    print("✅ 深度学习模块加载成功")
except ImportError as e:
    DEEP_LEARNING_AVAILABLE = False
    print(f"❌ 深度学习模块加载失败: {e}")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_and_prepare_data(filepath: str) -> <PERSON><PERSON>[np.ndarray, np.ndarray, str]:
    """加载和准备数据"""
    logger.info(f"加载数据集: {filepath}")
    
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"数据集文件不存在: {filepath}")
    
    df = pd.read_csv(filepath)
    logger.info(f"数据集形状: {df.shape}")
    
    # 确定任务类型
    if 'classification' in filepath.lower() or 'type' in filepath.lower():
        task_type = 'classification'
    else:
        task_type = 'regression'
    
    # 准备特征和目标
    numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
    df_numeric = df[numeric_columns]
    
    # 确定目标列
    if task_type == 'classification':
        target_candidates = ['label', 'target', 'class', 'type', 'category']
        target_col = None
        for col in target_candidates:
            if col in df_numeric.columns:
                target_col = col
                break
        if target_col is None:
            target_col = df_numeric.columns[-1]
    else:
        # 回归任务，寻找目标列
        target_col = None
        # 直接检查常见的目标列名
        if 'speed_kmh' in df_numeric.columns:
            target_col = 'speed_kmh'
        elif 'load' in df_numeric.columns:
            target_col = 'load'
        elif 'weight' in df_numeric.columns:
            target_col = 'weight'
        else:
            # 使用最后一列作为目标
            target_col = df_numeric.columns[-1]
    
    logger.info(f"目标列: {target_col}")
    
    # 分离特征和目标
    X = df_numeric.drop(columns=[target_col]).values
    y = df_numeric[target_col].values
    
    logger.info(f"特征形状: {X.shape}")
    logger.info(f"目标变量形状: {y.shape}")
    
    if task_type == 'regression':
        logger.info(f"目标变量范围: [{y.min():.2f}, {y.max():.2f}]")
    else:
        logger.info(f"类别数量: {len(np.unique(y))}")
    
    return X, y, task_type

def optimize_bp_neural_network(X: np.ndarray, y: np.ndarray, task_type: str, 
                              dataset_name: str) -> Dict[str, Any]:
    """优化BP神经网络"""
    logger.info(f"🧠 开始优化BP神经网络 - {dataset_name}")
    
    if not DEEP_LEARNING_AVAILABLE:
        logger.warning("深度学习模块不可用")
        return {}
    
    try:
        optimizer = BPNeuralNetworkOptimizer(
            task_type=task_type,
            n_trials=25,  # 适中的试验次数
            cv_folds=5
        )
        
        start_time = time.time()
        results = optimizer.optimize(X, y)
        end_time = time.time()
        
        logger.info(f"BP神经网络优化完成 - {dataset_name}")
        logger.info(f"最佳分数: {results['best_score']:.4f}")
        logger.info(f"优化时间: {end_time - start_time:.2f}秒")
        
        # 检查是否达到目标
        target_score = 0.75 if task_type == 'regression' else 0.85
        if results['best_score'] > target_score:
            logger.info(f"🎉 BP神经网络达到目标性能!")
        else:
            logger.info(f"📈 距离目标还差: {target_score - results['best_score']:.4f}")
        
        return {
            'best_params': results['best_params'],
            'best_score': results['best_score'],
            'training_time': end_time - start_time,
            'target_achieved': results['best_score'] > target_score
        }
        
    except Exception as e:
        logger.error(f"BP神经网络优化失败: {str(e)}")
        return {}

def optimize_tcn(X: np.ndarray, y: np.ndarray, task_type: str, 
                dataset_name: str) -> Dict[str, Any]:
    """优化TCN"""
    logger.info(f"🔄 开始优化TCN - {dataset_name}")
    
    if not DEEP_LEARNING_AVAILABLE:
        logger.warning("深度学习模块不可用")
        return {}
    
    try:
        optimizer = TCNOptimizer(
            task_type=task_type,
            n_trials=15,  # TCN训练较慢，减少试验次数
            cv_folds=5
        )
        
        start_time = time.time()
        results = optimizer.optimize(X, y)
        end_time = time.time()
        
        logger.info(f"TCN优化完成 - {dataset_name}")
        logger.info(f"最佳分数: {results['best_score']:.4f}")
        logger.info(f"优化时间: {end_time - start_time:.2f}秒")
        
        # 检查是否达到目标
        target_score = 0.75 if task_type == 'regression' else 0.85
        if results['best_score'] > target_score:
            logger.info(f"🎉 TCN达到目标性能!")
        else:
            logger.info(f"📈 距离目标还差: {target_score - results['best_score']:.4f}")
        
        return {
            'best_params': results['best_params'],
            'best_score': results['best_score'],
            'training_time': end_time - start_time,
            'target_achieved': results['best_score'] > target_score
        }
        
    except Exception as e:
        logger.error(f"TCN优化失败: {str(e)}")
        return {}

def run_complete_optimization():
    """运行完整的深度学习优化"""
    print("🚀 深度学习增强振动信号分析系统")
    print("=" * 60)
    
    # 检查GPU状态
    if DEEP_LEARNING_AVAILABLE:
        if torch.cuda.is_available():
            print(f"✅ GPU加速可用: {torch.cuda.get_device_name(0)}")
            print(f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        else:
            print("⚠️  使用CPU训练深度学习模型")
    else:
        print("❌ 深度学习模块不可用")
        return
    
    # 定义数据集
    datasets = [
        ('./training_datasets/speed_regression.csv', '速度预测'),
        ('./training_datasets/load_regression.csv', '轴重预测')
    ]
    
    # 检查分类数据集
    if os.path.exists('./ml/combined_features_clean.csv'):
        datasets.append(('./ml/combined_features_clean.csv', '轴型分类'))
    
    all_results = {}
    
    for dataset_path, dataset_name in datasets:
        print(f"\n{'='*60}")
        print(f"处理数据集: {dataset_name}")
        print(f"{'='*60}")
        
        try:
            # 加载数据
            X, y, task_type = load_and_prepare_data(dataset_path)
            
            # 优化BP神经网络
            bp_results = optimize_bp_neural_network(X, y, task_type, dataset_name)
            
            # 优化TCN
            tcn_results = optimize_tcn(X, y, task_type, dataset_name)
            
            # 保存结果
            all_results[dataset_name] = {
                'task_type': task_type,
                'data_shape': X.shape,
                'bp_results': bp_results,
                'tcn_results': tcn_results
            }
            
            # 显示最佳结果
            print(f"\n📊 {dataset_name} 深度学习结果总结:")
            print("-" * 50)
            
            if bp_results:
                status = "🎉" if bp_results.get('target_achieved', False) else "📈"
                print(f"  {status} BP神经网络: {bp_results['best_score']:.4f}")
            
            if tcn_results:
                status = "🎉" if tcn_results.get('target_achieved', False) else "📈"
                print(f"  {status} TCN: {tcn_results['best_score']:.4f}")
            
            # 比较结果
            if bp_results and tcn_results:
                if bp_results['best_score'] > tcn_results['best_score']:
                    print(f"🏆 最佳模型: BP神经网络 ({bp_results['best_score']:.4f})")
                else:
                    print(f"🏆 最佳模型: TCN ({tcn_results['best_score']:.4f})")
            
        except Exception as e:
            logger.error(f"处理数据集 {dataset_name} 时出错: {str(e)}")
            continue
    
    # 显示总体结果
    print(f"\n{'='*60}")
    print("🎉 深度学习优化总结")
    print(f"{'='*60}")
    
    for dataset_name, results in all_results.items():
        print(f"\n📊 {dataset_name}:")
        
        bp_score = results['bp_results'].get('best_score', 0.0) if results['bp_results'] else 0.0
        tcn_score = results['tcn_results'].get('best_score', 0.0) if results['tcn_results'] else 0.0
        
        target_score = 0.75 if results['task_type'] == 'regression' else 0.85
        
        print(f"   BP神经网络: {bp_score:.4f} {'✅' if bp_score > target_score else '📈'}")
        print(f"   TCN: {tcn_score:.4f} {'✅' if tcn_score > target_score else '📈'}")
        
        best_score = max(bp_score, tcn_score)
        if best_score > target_score:
            print(f"   🎯 目标达成! 最佳分数: {best_score:.4f}")
        else:
            print(f"   📈 距离目标: {target_score - best_score:.4f}")
    
    # 保存结果
    import json
    with open('deep_learning_optimization_results.json', 'w', encoding='utf-8') as f:
        # 转换为可序列化格式
        serializable_results = {}
        for dataset_name, results in all_results.items():
            serializable_results[dataset_name] = {
                'task_type': results['task_type'],
                'data_shape': list(results['data_shape']),
                'bp_score': results['bp_results'].get('best_score', 0.0) if results['bp_results'] else 0.0,
                'tcn_score': results['tcn_results'].get('best_score', 0.0) if results['tcn_results'] else 0.0,
                'bp_params': results['bp_results'].get('best_params', {}) if results['bp_results'] else {},
                'tcn_params': results['tcn_results'].get('best_params', {}) if results['tcn_results'] else {}
            }
        
        json.dump(serializable_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📁 结果已保存到 deep_learning_optimization_results.json")
    print("🎊 深度学习增强训练完成!")

def main():
    """主函数"""
    start_time = time.time()
    
    try:
        run_complete_optimization()
    except KeyboardInterrupt:
        print("\n⚠️  用户中断训练")
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {str(e)}")
    finally:
        end_time = time.time()
        print(f"\n⏱️  总耗时: {end_time - start_time:.2f}秒")

if __name__ == "__main__":
    main()
