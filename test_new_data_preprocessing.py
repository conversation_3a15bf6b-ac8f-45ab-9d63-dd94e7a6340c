#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新格式数据预处理系统测试脚本
全面测试数据预处理功能的正确性

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import os
import sys
import tempfile
import shutil
import numpy as np
import pandas as pd
from pathlib import Path

def test_new_data_preprocessor():
    """测试新数据预处理器"""
    print("🧪 测试新数据预处理器...")
    
    try:
        from new_data_preprocessor import NewDataPreprocessor
        
        # 创建临时测试数据
        with tempfile.TemporaryDirectory() as temp_dir:
            input_dir = Path(temp_dir) / "input"
            output_dir = Path(temp_dir) / "output"
            input_dir.mkdir()
            
            # 创建测试CSV文件
            test_file = input_dir / "GW100001_20231101174605_AcceData_车道1_2轴-2.5t-100kmh.csv"

            # 生成测试数据（新格式：22列）
            data = {}
            # 第1列：count列（没有表头）
            data['0'] = range(1000)
            # 第2列：无用列
            data['1'] = np.random.randn(1000) * 0.1
            # 第3-22列：20个传感器列
            for i in range(1, 21):
                data[f'acce{i:02d}'] = np.random.randn(1000)

            df = pd.DataFrame(data)
            df.to_csv(test_file, index=False)
            
            # 初始化预处理器
            preprocessor = NewDataPreprocessor(str(input_dir), str(output_dir))
            
            # 测试文件名解析
            parsed_info = preprocessor.parse_filename(test_file.name)
            if not parsed_info:
                print("   ❌ 文件名解析失败")
                return False
            
            # 测试CSV结构验证
            if not preprocessor.validate_csv_structure(test_file):
                print("   ❌ CSV结构验证失败")
                return False
            
            # 测试单文件处理
            if not preprocessor.process_single_file(test_file):
                print("   ❌ 单文件处理失败")
                return False
            
            print("   ✅ 新数据预处理器测试通过")
            return True
            
    except Exception as e:
        print(f"   ❌ 新数据预处理器测试失败: {str(e)}")
        return False

def test_data_format_adapter():
    """测试数据格式适配器"""
    print("🧪 测试数据格式适配器...")
    
    try:
        from data_format_adapter import DataFormatAdapter
        
        # 创建临时测试数据
        with tempfile.TemporaryDirectory() as temp_dir:
            test_dir = Path(temp_dir) / "test_data"
            test_dir.mkdir()
            
            # 创建新格式测试文件
            test_file = test_dir / "GW100001_20231101174605_AcceData_车道1_2轴-2.5t-100kmh.csv"

            # 新格式：22列
            data = {}
            data['0'] = range(500)  # count列
            data['1'] = np.random.randn(500) * 0.1  # 无用列
            for i in range(1, 21):
                data[f'acce{i:02d}'] = np.random.randn(500)

            df = pd.DataFrame(data)
            df.to_csv(test_file, index=False)
            
            # 初始化适配器
            adapter = DataFormatAdapter()
            
            # 测试格式检测
            format_type = adapter.detect_data_format(str(test_dir))
            if format_type != "new_format":
                print(f"   ⚠️  格式检测结果: {format_type} (期望: new_format)")
            
            # 测试获取兼容目录
            compatible_dir = adapter.get_compatible_data_dir(str(test_dir))
            if not compatible_dir:
                print("   ❌ 获取兼容目录失败")
                return False
            
            print("   ✅ 数据格式适配器测试通过")
            return True
            
    except Exception as e:
        print(f"   ❌ 数据格式适配器测试失败: {str(e)}")
        return False

def test_filename_parsing_edge_cases():
    """测试文件名解析的边界情况"""
    print("🧪 测试文件名解析边界情况...")
    
    try:
        from new_data_preprocessor import NewDataPreprocessor
        
        preprocessor = NewDataPreprocessor("dummy", "dummy")
        
        # 测试各种文件名格式
        test_cases = [
            # 正常格式
            ("GW100001_20231101174605_AcceData_车道1_2轴-2.5t-100kmh.csv", True),
            ("GW100002_20231101180000_AcceData_车道2_3轴-25t-60kmh.csv", True),
            # 边界情况
            ("GW100003_20231101182000_AcceData_车道1_4轴-40.5t-120kmh.csv", True),
            ("GW100004_20231101184500_AcceData_车道3_2轴-5t-50km/h.csv", True),
            # 错误格式
            ("invalid_filename.csv", False),
            ("GW100001_AcceData_车道1.csv", False),
            ("normal_acce_file.csv", False)
        ]
        
        passed = 0
        for filename, should_pass in test_cases:
            parsed_info = preprocessor.parse_filename(filename)
            
            if should_pass and parsed_info:
                passed += 1
                print(f"   ✅ {filename}: 解析成功")
            elif not should_pass and not parsed_info:
                passed += 1
                print(f"   ✅ {filename}: 正确拒绝")
            else:
                print(f"   ❌ {filename}: 解析结果不符合预期")
        
        success_rate = passed / len(test_cases)
        print(f"   📊 边界情况测试: {passed}/{len(test_cases)} 通过 ({success_rate*100:.1f}%)")
        
        return success_rate >= 0.8
        
    except Exception as e:
        print(f"   ❌ 文件名解析边界测试失败: {str(e)}")
        return False

def test_data_cleaning_and_standardization():
    """测试数据清理和标准化"""
    print("🧪 测试数据清理和标准化...")
    
    try:
        from new_data_preprocessor import NewDataPreprocessor
        
        preprocessor = NewDataPreprocessor("dummy", "dummy")
        
        # 创建包含问题的测试数据（新格式：22列）
        data = {}

        # 第1列：count列
        data['0'] = range(1000)

        # 第2列：无用列
        data['1'] = np.random.randn(1000) * 0.1

        # 第3-22列：传感器数据（包含缺失值和异常值）
        for i in range(1, 21):
            signal = np.random.randn(1000)

            # 添加缺失值
            signal[100:110] = np.nan

            # 添加异常值
            signal[500] = 1000  # 极大异常值
            signal[600] = -1000  # 极小异常值

            data[f'acce{i:02d}'] = signal

        df = pd.DataFrame(data)
        
        # 模拟解析信息
        parsed_info = {
            'monitoring_point': 'GW100001',
            'monitoring_time': '20231101174605',
            'lane_number': 1,
            'axle_type': 2,
            'load_tons': 2.5,
            'speed_kmh': 100
        }
        
        # 执行数据清理
        cleaned_df = preprocessor.clean_and_standardize_data(df, parsed_info)
        
        if cleaned_df is None:
            print("   ❌ 数据清理返回None")
            return False
        
        # 检查清理结果
        # 1. 列名标准化
        expected_sensor_cols = [f'sensor_{i:02d}' for i in range(1, 21)]
        actual_sensor_cols = [col for col in cleaned_df.columns if col.startswith('sensor_')]
        
        if len(actual_sensor_cols) != 20:
            print(f"   ❌ 传感器列数不正确: {len(actual_sensor_cols)} (期望20)")
            return False
        
        # 2. 缺失值处理
        if cleaned_df[actual_sensor_cols].isnull().sum().sum() > 0:
            print("   ❌ 仍存在缺失值")
            return False
        
        # 3. 元数据添加
        metadata_cols = ['speed_kmh', 'load_tons', 'axle_type', 'lane_number', 'monitoring_point']
        missing_metadata = [col for col in metadata_cols if col not in cleaned_df.columns]
        
        if missing_metadata:
            print(f"   ❌ 缺少元数据列: {missing_metadata}")
            return False
        
        print("   ✅ 数据清理和标准化测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 数据清理和标准化测试失败: {str(e)}")
        return False

def test_output_compatibility():
    """测试输出兼容性"""
    print("🧪 测试输出兼容性...")
    
    try:
        from new_data_preprocessor import NewDataPreprocessor
        
        # 创建临时测试环境
        with tempfile.TemporaryDirectory() as temp_dir:
            input_dir = Path(temp_dir) / "input"
            output_dir = Path(temp_dir) / "output"
            input_dir.mkdir()
            
            # 创建测试文件（新格式：22列）
            test_file = input_dir / "GW100001_20231101174605_AcceData_车道1_2轴-2.5t-100kmh.csv"

            data = {}
            data['0'] = range(1000)  # count列
            data['1'] = np.random.randn(1000) * 0.1  # 无用列
            for i in range(1, 21):
                data[f'acce{i:02d}'] = np.random.randn(1000)

            df = pd.DataFrame(data)
            df.to_csv(test_file, index=False)
            
            # 执行预处理
            preprocessor = NewDataPreprocessor(str(input_dir), str(output_dir))
            summary = preprocessor.process_all_files()
            
            if not summary['success'] or summary['processed_files'] == 0:
                print("   ❌ 预处理失败")
                return False
            
            # 验证兼容性
            if not preprocessor.validate_output_compatibility():
                print("   ❌ 输出兼容性验证失败")
                return False
            
            # 检查输出文件结构
            output_files = list(Path(output_dir).glob("**/*.csv"))
            if not output_files:
                print("   ❌ 未生成输出文件")
                return False
            
            # 检查第一个输出文件
            output_file = output_files[0]
            output_df = pd.read_csv(output_file)
            
            # 验证列结构
            required_cols = ['count'] + [f'sensor_{i:02d}' for i in range(1, 21)]
            metadata_cols = ['speed_kmh', 'load_tons', 'axle_type', 'lane_number', 'monitoring_point']
            
            missing_required = [col for col in required_cols if col not in output_df.columns]
            missing_metadata = [col for col in metadata_cols if col not in output_df.columns]
            
            if missing_required:
                print(f"   ❌ 缺少必需列: {missing_required}")
                return False
            
            if missing_metadata:
                print(f"   ❌ 缺少元数据列: {missing_metadata}")
                return False
            
            print("   ✅ 输出兼容性测试通过")
            return True
            
    except Exception as e:
        print(f"   ❌ 输出兼容性测试失败: {str(e)}")
        return False

def test_main_system_integration():
    """测试主系统集成"""
    print("🧪 测试主系统集成...")
    
    try:
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 检查集成的属性和方法
        system = UnifiedVibrationAnalysisSystem()
        
        required_attributes = [
            'data_preprocessing_enabled',
            'preprocessing_results'
        ]
        
        for attr in required_attributes:
            if not hasattr(system, attr):
                print(f"   ❌ 缺少属性: {attr}")
                return False
        
        required_methods = [
            'preprocess_data_format'
        ]
        
        for method in required_methods:
            if not hasattr(system, method):
                print(f"   ❌ 缺少方法: {method}")
                return False
        
        # 检查默认设置
        if not system.data_preprocessing_enabled:
            print("   ⚠️  数据预处理默认未启用")
        
        print("   ✅ 主系统集成测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 主系统集成测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 新格式数据预处理系统全面测试")
    print("=" * 70)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("新数据预处理器", test_new_data_preprocessor()))
    test_results.append(("数据格式适配器", test_data_format_adapter()))
    test_results.append(("文件名解析边界情况", test_filename_parsing_edge_cases()))
    test_results.append(("数据清理和标准化", test_data_cleaning_and_standardization()))
    test_results.append(("输出兼容性", test_output_compatibility()))
    test_results.append(("主系统集成", test_main_system_integration()))
    
    # 显示测试结果
    print("\n" + "=" * 70)
    print("📊 测试结果汇总:")
    print("=" * 70)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 测试统计: {passed}/{total} 个测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！新格式数据预处理系统运行正常。")
        
        print("\n✅ 系统功能验证:")
        print("   - 新格式文件名解析 ✓")
        print("   - CSV结构验证 ✓")
        print("   - 数据清理和标准化 ✓")
        print("   - 输出格式兼容性 ✓")
        print("   - 主系统集成 ✓")
        
        print("\n🚀 使用方法:")
        print("   python unified_vibration_analysis.py")
        print("   系统将自动检测并处理新格式数据")
        
    elif passed >= total * 0.7:
        print("⚠️  大部分测试通过，但存在一些问题")
        print("💡 建议检查失败的测试项目")
    else:
        print("❌ 多项测试失败，系统可能存在严重问题")
        print("💡 建议重新检查代码实现")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
