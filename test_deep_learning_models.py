#!/usr/bin/env python3
"""
测试深度学习模型的简化脚本
"""

import numpy as np
import pandas as pd
import os
import sys

# 添加ml目录到路径
sys.path.append('./ml')

try:
    from ml.bp_neural_network import BPNeuralNetworkTrainer, BPNeuralNetworkOptimizer
    from ml.temporal_convolutional_network import TCNTrainer, TCNOptimizer
    import torch
    DEEP_LEARNING_AVAILABLE = True
    print("✅ 深度学习模块加载成功")
except ImportError as e:
    DEEP_LEARNING_AVAILABLE = False
    print(f"❌ 深度学习模块加载失败: {e}")

def test_bp_neural_network():
    """测试BP神经网络"""
    print("\n🧠 测试BP神经网络...")
    
    if not DEEP_LEARNING_AVAILABLE:
        print("❌ 深度学习模块不可用")
        return False
    
    try:
        # 创建测试数据
        from sklearn.datasets import make_regression
        X, y = make_regression(n_samples=500, n_features=10, noise=0.1, random_state=42)
        
        print(f"数据形状: X={X.shape}, y={y.shape}")
        
        # 创建训练器
        trainer = BPNeuralNetworkTrainer('regression')
        
        # 快速交叉验证测试
        score = trainer.cross_validate(
            X, y, 
            hidden_sizes=[64, 32], 
            epochs=20,  # 减少训练轮数
            cv_folds=3  # 减少折数
        )
        
        print(f"✅ BP神经网络测试成功，R² = {score:.4f}")
        return True
        
    except Exception as e:
        print(f"❌ BP神经网络测试失败: {str(e)}")
        return False

def test_tcn():
    """测试TCN"""
    print("\n🔄 测试时间卷积网络...")
    
    if not DEEP_LEARNING_AVAILABLE:
        print("❌ 深度学习模块不可用")
        return False
    
    try:
        # 创建测试数据
        from sklearn.datasets import make_regression
        X, y = make_regression(n_samples=300, n_features=15, noise=0.1, random_state=42)
        
        print(f"数据形状: X={X.shape}, y={y.shape}")
        
        # 创建训练器
        trainer = TCNTrainer('regression')
        
        # 快速交叉验证测试
        score = trainer.cross_validate(
            X, y,
            num_channels=[32, 64],  # 简化网络结构
            sequence_length=50,     # 减少序列长度
            epochs=15,              # 减少训练轮数
            cv_folds=3              # 减少折数
        )
        
        print(f"✅ TCN测试成功，R² = {score:.4f}")
        return True
        
    except Exception as e:
        print(f"❌ TCN测试失败: {str(e)}")
        return False

def test_with_real_data():
    """使用真实数据测试"""
    print("\n📊 使用真实数据测试...")
    
    # 检查数据文件
    data_files = [
        './training_datasets/speed_regression.csv',
        './training_datasets/load_regression.csv'
    ]
    
    for data_file in data_files:
        if not os.path.exists(data_file):
            print(f"❌ 数据文件不存在: {data_file}")
            continue
        
        print(f"\n测试数据集: {data_file}")
        
        try:
            # 加载数据
            df = pd.read_csv(data_file)
            print(f"数据形状: {df.shape}")
            
            # 准备特征和目标
            numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
            df_numeric = df[numeric_columns]
            
            # 使用最后一列作为目标
            target_col = df_numeric.columns[-1]
            X = df_numeric.drop(columns=[target_col]).values
            y = df_numeric[target_col].values
            
            print(f"特征形状: {X.shape}")
            print(f"目标范围: [{y.min():.2f}, {y.max():.2f}]")
            
            # 测试BP神经网络
            if DEEP_LEARNING_AVAILABLE:
                print("测试BP神经网络...")
                bp_trainer = BPNeuralNetworkTrainer('regression')
                bp_score = bp_trainer.cross_validate(
                    X[:200], y[:200],  # 使用部分数据以节省时间
                    hidden_sizes=[64, 32],
                    epochs=15,
                    cv_folds=3
                )
                print(f"BP神经网络 R² = {bp_score:.4f}")
                
                # 检查是否达到目标
                if bp_score > 0.75:
                    print("🎉 BP神经网络达到目标性能 (R² > 0.75)!")
                else:
                    print(f"📈 BP神经网络距离目标还差: {0.75 - bp_score:.4f}")
            
        except Exception as e:
            print(f"❌ 处理数据集失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 深度学习模型测试")
    print("=" * 50)
    
    # 检查GPU
    if DEEP_LEARNING_AVAILABLE and torch.cuda.is_available():
        print(f"✅ GPU可用: {torch.cuda.get_device_name(0)}")
    else:
        print("⚠️  使用CPU进行测试")
    
    # 运行测试
    tests = [
        ("BP神经网络", test_bp_neural_network),
        ("时间卷积网络", test_tcn)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*30}")
        print(f"测试: {test_name}")
        print(f"{'='*30}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            results[test_name] = False
    
    # 测试真实数据
    test_with_real_data()
    
    # 显示结果
    print(f"\n{'='*50}")
    print("🎉 测试结果总结")
    print(f"{'='*50}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试总结: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有深度学习模型测试通过！")
        print("💡 系统已准备好进行深度学习增强训练")
    elif passed >= total * 0.5:
        print("⚠️  部分深度学习功能正常")
        print("💡 可以开始部分深度学习训练")
    else:
        print("❌ 深度学习配置需要进一步调整")
    
    return passed == total

if __name__ == "__main__":
    main()
