#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Technical Workflow Visualization Generator
Creates comprehensive charts illustrating data processing and feature extraction workflow

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class TechnicalWorkflowVisualizer:
    """Technical Workflow Visualization Generator"""

    def __init__(self, output_base_dir: str = "unified_charts", file_prefix: str = "technical_"):
        self.output_base_dir = Path(output_base_dir)
        self.output_base_dir.mkdir(parents=True, exist_ok=True)
        self.file_prefix = file_prefix
        self.setup_academic_style()
        self.chart_registry = {}

    def setup_academic_style(self):
        """Setup academic publication style with Times New Roman font"""
        # Set Times New Roman font for all text elements
        plt.rcParams['font.family'] = 'serif'
        plt.rcParams['font.serif'] = ['Times New Roman', 'Times', 'DejaVu Serif']
        plt.rcParams['mathtext.fontset'] = 'stix'

        # Set academic-level parameters
        plt.rcParams['figure.dpi'] = 330  # 330 DPI for publication quality
        plt.rcParams['savefig.dpi'] = 330
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.titlesize'] = 16
        plt.rcParams['axes.labelsize'] = 14
        plt.rcParams['xtick.labelsize'] = 12
        plt.rcParams['ytick.labelsize'] = 12
        plt.rcParams['legend.fontsize'] = 12
        plt.rcParams['figure.titlesize'] = 18

        # Academic color scheme
        self.colors = {
            'primary': '#1f77b4',
            'secondary': '#ff7f0e',
            'success': '#2ca02c',
            'danger': '#d62728',
            'warning': '#ff7f0e',
            'info': '#17a2b8',
            'purple': '#9467bd',
            'brown': '#8c564b',
            'pink': '#e377c2',
            'gray': '#7f7f7f',
            'olive': '#bcbd22',
            'cyan': '#17becf'
        }

    def generate_all_technical_visualizations(self) -> Dict[str, List[str]]:
        """Generate all technical visualization charts"""
        print("📊 Generating Technical Workflow Visualizations...")
        print("=" * 80)

        # 1. Data Processing Workflow Diagrams
        self.generate_system_overview_diagram()
        self.generate_data_processing_pipeline()

        # 2. Sample Vibration Signal Plots
        self.generate_sample_vibration_signals()
        self.generate_multi_sensor_comparison()

        # 3. Feature Extraction Demonstrations
        self.generate_time_domain_features()
        self.generate_frequency_domain_features()
        self.generate_time_frequency_features()

        # 4. Signal Preprocessing Visualizations
        self.generate_signal_preprocessing_demo()
        self.generate_filtering_comparison()

        # 5. Comprehensive Workflow Diagram
        self.generate_comprehensive_workflow()

        # Generate summary report
        summary = self.generate_visualization_summary()

        print(f"\n✅ All technical visualizations generated successfully!")
        print(f"   Total charts: {len(self.chart_registry)}")
        print(f"   Output directory: {self.output_base_dir}")

        return summary

    def generate_system_overview_diagram(self):
        """Generate system overview diagram"""
        print("\n🔧 Generating System Overview Diagram...")

        fig, ax = plt.subplots(figsize=(14, 10))

        # Define system components
        components = [
            {"name": "Raw CSV Files\n(3,398 files)", "pos": (2, 8), "color": self.colors['info']},
            {"name": "Data Format\nDetection", "pos": (2, 6.5), "color": self.colors['warning']},
            {"name": "File Processing\nHistory", "pos": (0.5, 5), "color": self.colors['gray']},
            {"name": "Signal\nPreprocessing", "pos": (2, 5), "color": self.colors['secondary']},
            {"name": "Feature\nExtraction", "pos": (2, 3.5), "color": self.colors['primary']},
            {"name": "Data\nDeduplication", "pos": (4, 3.5), "color": self.colors['purple']},
            {"name": "Quality\nValidation", "pos": (2, 2), "color": self.colors['success']},
            {"name": "Combined Features\n(3,237 samples)", "pos": (2, 0.5), "color": self.colors['danger']}
        ]

        # Draw components
        for comp in components:
            bbox = FancyBboxPatch(
                (comp["pos"][0] - 0.6, comp["pos"][1] - 0.3),
                1.2, 0.6,
                boxstyle="round,pad=0.1",
                facecolor=comp["color"],
                edgecolor='black',
                alpha=0.7
            )
            ax.add_patch(bbox)
            ax.text(comp["pos"][0], comp["pos"][1], comp["name"],
                   ha='center', va='center', fontsize=11, fontweight='bold')

        # Draw connections
        connections = [
            ((2, 7.7), (2, 6.8)),  # CSV to Detection
            ((2, 6.2), (2, 5.3)),  # Detection to Preprocessing
            ((2, 4.7), (2, 3.8)),  # Preprocessing to Feature Extraction
            ((2.6, 3.5), (3.4, 3.5)),  # Feature Extraction to Deduplication
            ((2, 3.2), (2, 2.3)),  # Feature Extraction to Quality
            ((2, 1.7), (2, 0.8)),  # Quality to Combined Features
            ((1.1, 5), (1.4, 5)),  # History connection
        ]

        for start, end in connections:
            ax.annotate('', xy=end, xytext=start,
                       arrowprops=dict(arrowstyle='->', lw=2, color='black'))

        # Add side annotations
        ax.text(5.5, 6, "20 Accelerometer\nSensors per File",
               bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue'),
               fontsize=10, ha='center')

        ax.text(5.5, 4, "Time + Frequency +\nTime-Frequency\nFeatures",
               bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen'),
               fontsize=10, ha='center')

        ax.text(5.5, 2, "MD5 Hash +\nFile Path +\nFeature Value\nDeduplication",
               bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow'),
               fontsize=10, ha='center')

        ax.set_xlim(-1, 7)
        ax.set_ylim(0, 9)
        ax.set_title('Vibration Signal Analysis System Overview', fontsize=16, fontweight='bold', pad=20)
        ax.axis('off')

        # Save chart
        chart_path = self.output_base_dir / f"{self.file_prefix}system_overview_diagram.png"
        plt.savefig(chart_path, dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()

        self.chart_registry["System Overview"] = str(chart_path)
        print(f"   ✅ Saved: {chart_path}")

    def generate_data_processing_pipeline(self):
        """Generate data processing pipeline diagram"""
        print("\n🔄 Generating Data Processing Pipeline...")

        fig, ax = plt.subplots(figsize=(16, 8))

        # Pipeline stages
        stages = [
            {"name": "CSV File\nInput", "pos": (1, 4), "size": (1.5, 1)},
            {"name": "Format\nValidation", "pos": (3.5, 4), "size": (1.5, 1)},
            {"name": "Sensor Data\nExtraction", "pos": (6, 4), "size": (1.5, 1)},
            {"name": "Signal\nSegmentation", "pos": (8.5, 4), "size": (1.5, 1)},
            {"name": "Feature\nCalculation", "pos": (11, 4), "size": (1.5, 1)},
            {"name": "Quality\nCheck", "pos": (13.5, 4), "size": (1.5, 1)}
        ]

        # Draw pipeline stages
        for i, stage in enumerate(stages):
            # Main box
            rect = FancyBboxPatch(
                (stage["pos"][0] - stage["size"][0]/2, stage["pos"][1] - stage["size"][1]/2),
                stage["size"][0], stage["size"][1],
                boxstyle="round,pad=0.1",
                facecolor=list(self.colors.values())[i % len(self.colors)],
                edgecolor='black',
                alpha=0.7
            )
            ax.add_patch(rect)
            ax.text(stage["pos"][0], stage["pos"][1], stage["name"],
                   ha='center', va='center', fontsize=11, fontweight='bold')

            # Add arrows between stages
            if i < len(stages) - 1:
                start_x = stage["pos"][0] + stage["size"][0]/2
                end_x = stages[i+1]["pos"][0] - stages[i+1]["size"][0]/2
                ax.annotate('', xy=(end_x, 4), xytext=(start_x, 4),
                           arrowprops=dict(arrowstyle='->', lw=3, color='black'))

        # Add detailed annotations below
        details = [
            "GW100001_*.csv\n22 columns\n1000+ rows",
            "Check column\ncount and\ndata types",
            "Extract 20\nsensor signals\nRemove invalid",
            "1-second\nwindows\n1000 Hz sampling",
            "Time/Freq/\nTime-Freq\nfeatures",
            "Validate\ncompleteness\nand ranges"
        ]

        for i, (stage, detail) in enumerate(zip(stages, details)):
            ax.text(stage["pos"][0], 2.5, detail,
                   ha='center', va='center', fontsize=9,
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))

        ax.set_xlim(0, 15)
        ax.set_ylim(1, 6)
        ax.set_title('Data Processing Pipeline Workflow', fontsize=16, fontweight='bold', pad=20)
        ax.axis('off')

        # Save chart
        chart_path = self.output_base_dir / f"{self.file_prefix}data_processing_pipeline.png"
        plt.savefig(chart_path, dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()

        self.chart_registry["Data Processing Pipeline"] = str(chart_path)
        print(f"   ✅ Saved: {chart_path}")

    def generate_sample_vibration_signals(self):
        """Generate sample vibration signal waveforms"""
        print("\n📈 Generating Sample Vibration Signals...")

        # Generate realistic vibration signal data
        np.random.seed(42)
        time = np.linspace(0, 1, 1000)  # 1 second at 1000 Hz

        # Create different sensor signals with realistic characteristics
        signals = {}
        for i in range(4):  # Show 4 representative sensors
            # Base vibration with vehicle passing characteristics
            base_freq = 10 + i * 5  # Different base frequencies
            vehicle_impact = np.exp(-((time - 0.5) / 0.1)**2) * (2 + i * 0.5)  # Gaussian impact

            # Combine multiple frequency components
            signal = (
                vehicle_impact * np.sin(2 * np.pi * base_freq * time) +
                0.3 * np.sin(2 * np.pi * (base_freq * 3) * time) +
                0.1 * np.random.normal(0, 1, len(time))  # Noise
            )

            signals[f'Sensor_{i+1:02d}'] = signal

        # Create subplot for multiple sensors
        fig, axes = plt.subplots(2, 2, figsize=(14, 10))
        axes = axes.flatten()

        colors = [self.colors['primary'], self.colors['secondary'],
                 self.colors['success'], self.colors['danger']]

        for i, (sensor_name, signal) in enumerate(signals.items()):
            ax = axes[i]
            ax.plot(time, signal, color=colors[i], linewidth=1.5, alpha=0.8)
            ax.set_title(f'{sensor_name} - Raw Accelerometer Data', fontsize=14, fontweight='bold')
            ax.set_xlabel('Time (seconds)', fontsize=12)
            ax.set_ylabel('Acceleration (m/s²)', fontsize=12)
            ax.grid(True, alpha=0.3)
            ax.set_xlim(0, 1)

            # Add annotations for key features
            max_idx = np.argmax(np.abs(signal))
            ax.annotate(f'Peak: {signal[max_idx]:.2f} m/s²',
                       xy=(time[max_idx], signal[max_idx]),
                       xytext=(0.7, signal[max_idx] + 0.5),
                       arrowprops=dict(arrowstyle='->', color='red'),
                       fontsize=10, color='red')

        plt.suptitle('Sample Vibration Signal Waveforms from 20-Sensor Array',
                    fontsize=16, fontweight='bold')
        plt.tight_layout()

        # Save chart
        chart_path = self.output_base_dir / f"{self.file_prefix}sample_vibration_signals.png"
        plt.savefig(chart_path, dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()

        self.chart_registry["Sample Vibration Signals"] = str(chart_path)
        print(f"   ✅ Saved: {chart_path}")

    def generate_multi_sensor_comparison(self):
        """Generate multi-sensor comparison plot"""
        print("\n📊 Generating Multi-Sensor Comparison...")

        # Generate data for all 20 sensors
        np.random.seed(42)
        time = np.linspace(0, 1, 1000)

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))

        # Top plot: All 20 sensors overlaid
        colors = plt.cm.tab20(np.linspace(0, 1, 20))

        for i in range(20):
            base_freq = 8 + i * 0.5
            vehicle_impact = np.exp(-((time - 0.5) / 0.08)**2) * (1.5 + i * 0.1)
            signal = (
                vehicle_impact * np.sin(2 * np.pi * base_freq * time) +
                0.2 * np.sin(2 * np.pi * (base_freq * 2.5) * time) +
                0.05 * np.random.normal(0, 1, len(time))
            )
            ax1.plot(time, signal + i * 0.5, color=colors[i], linewidth=1, alpha=0.7,
                    label=f'Sensor {i+1:02d}' if i < 5 else "")

        ax1.set_title('All 20 Accelerometer Sensors - Vehicle Passing Event',
                     fontsize=14, fontweight='bold')
        ax1.set_xlabel('Time (seconds)', fontsize=12)
        ax1.set_ylabel('Acceleration + Offset (m/s²)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend(loc='upper right', fontsize=10)

        # Bottom plot: Sensor layout diagram
        ax2.set_xlim(0, 10)
        ax2.set_ylim(0, 4)

        # Draw road cross-section
        road_rect = patches.Rectangle((1, 1.5), 8, 1, linewidth=2,
                                    edgecolor='black', facecolor='gray', alpha=0.3)
        ax2.add_patch(road_rect)
        ax2.text(5, 2, 'Road Surface', ha='center', va='center', fontsize=12, fontweight='bold')

        # Draw sensor positions
        sensor_positions = [
            # Group 1 (5cm depth)
            [(2, 1.3), (3, 1.3), (4, 1.3), (5, 1.3), (6, 1.3)],
            # Group 2 (5cm depth)
            [(2, 1.1), (3, 1.1), (4, 1.1), (5, 1.1), (6, 1.1)],
            # Group 3 (3.5cm depth)
            [(2, 1.7), (3, 1.7), (4, 1.7), (5, 1.7), (6, 1.7)],
            # Group 4 (3.5cm depth)
            [(2, 1.9), (3, 1.9), (4, 1.9), (5, 1.9), (6, 1.9)]
        ]

        group_colors = [self.colors['primary'], self.colors['secondary'],
                       self.colors['success'], self.colors['danger']]

        sensor_num = 1
        for group_idx, group in enumerate(sensor_positions):
            for pos in group:
                circle = patches.Circle(pos, 0.1, color=group_colors[group_idx], alpha=0.8)
                ax2.add_patch(circle)
                ax2.text(pos[0], pos[1], str(sensor_num), ha='center', va='center',
                        fontsize=8, fontweight='bold', color='white')
                sensor_num += 1

        # Add depth annotations
        ax2.text(0.5, 1.2, '5.0 cm\ndepth', ha='center', va='center', fontsize=10,
                bbox=dict(boxstyle="round,pad=0.2", facecolor='lightblue'))
        ax2.text(0.5, 1.8, '3.5 cm\ndepth', ha='center', va='center', fontsize=10,
                bbox=dict(boxstyle="round,pad=0.2", facecolor='lightgreen'))

        ax2.set_title('20-Sensor Array Layout in Concrete Pavement',
                     fontsize=14, fontweight='bold')
        ax2.set_xlabel('Distance (meters)', fontsize=12)
        ax2.set_ylabel('Depth (meters)', fontsize=12)
        ax2.axis('equal')

        plt.tight_layout()

        # Save chart
        chart_path = self.output_base_dir / f"{self.file_prefix}multi_sensor_comparison.png"
        plt.savefig(chart_path, dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()

        self.chart_registry["Multi-Sensor Comparison"] = str(chart_path)
        print(f"   ✅ Saved: {chart_path}")

    def generate_time_domain_features(self):
        """Generate time-domain feature extraction demonstration"""
        print("\n⏱️ Generating Time-Domain Features Demo...")

        # Generate sample signal
        np.random.seed(42)
        time = np.linspace(0, 1, 1000)
        signal = (
            2 * np.exp(-((time - 0.5) / 0.1)**2) * np.sin(2 * np.pi * 15 * time) +
            0.5 * np.sin(2 * np.pi * 45 * time) +
            0.1 * np.random.normal(0, 1, len(time))
        )

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))

        # Original signal
        ax1.plot(time, signal, color=self.colors['primary'], linewidth=1.5)
        ax1.set_title('Original Vibration Signal', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Time (seconds)', fontsize=12)
        ax1.set_ylabel('Acceleration (m/s²)', fontsize=12)
        ax1.grid(True, alpha=0.3)

        # Add feature annotations
        mean_val = np.mean(signal)
        std_val = np.std(signal)
        rms_val = np.sqrt(np.mean(signal**2))
        peak_val = np.max(np.abs(signal))

        ax1.axhline(y=mean_val, color='red', linestyle='--', alpha=0.7, label=f'Mean: {mean_val:.3f}')
        ax1.axhline(y=mean_val + std_val, color='orange', linestyle='--', alpha=0.7,
                   label=f'Mean + STD: {mean_val + std_val:.3f}')
        ax1.axhline(y=mean_val - std_val, color='orange', linestyle='--', alpha=0.7)
        ax1.legend(fontsize=10)

        # Statistical distribution
        ax2.hist(signal, bins=30, alpha=0.7, color=self.colors['secondary'], edgecolor='black')
        ax2.axvline(x=mean_val, color='red', linestyle='--', linewidth=2, label=f'Mean: {mean_val:.3f}')
        ax2.axvline(x=mean_val + std_val, color='orange', linestyle='--', linewidth=2)
        ax2.axvline(x=mean_val - std_val, color='orange', linestyle='--', linewidth=2)
        ax2.set_title('Signal Distribution', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Acceleration (m/s²)', fontsize=12)
        ax2.set_ylabel('Frequency', fontsize=12)
        ax2.legend(fontsize=10)
        ax2.grid(True, alpha=0.3)

        # Time-domain features summary
        features = {
            'Mean': mean_val,
            'Standard Deviation': std_val,
            'RMS': rms_val,
            'Peak': peak_val,
            'Peak-to-Peak': np.max(signal) - np.min(signal),
            'Crest Factor': peak_val / rms_val,
            'Skewness': self.calculate_skewness(signal),
            'Kurtosis': self.calculate_kurtosis(signal)
        }

        feature_names = list(features.keys())
        feature_values = list(features.values())

        bars = ax3.barh(feature_names, feature_values, color=self.colors['success'], alpha=0.7)
        ax3.set_title('Extracted Time-Domain Features', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Feature Value', fontsize=12)
        ax3.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar, value in zip(bars, feature_values):
            ax3.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2,
                    f'{value:.3f}', va='center', fontsize=10)

        # Feature calculation formulas
        formulas = [
            "Mean = (1/N) × Σx(i)",
            "STD = √[(1/N) × Σ(x(i) - μ)²]",
            "RMS = √[(1/N) × Σx(i)²]",
            "Peak = max(|x(i)|)",
            "Crest Factor = Peak / RMS",
            "Skewness = E[(x-μ)³] / σ³",
            "Kurtosis = E[(x-μ)⁴] / σ⁴ - 3"
        ]

        ax4.text(0.05, 0.95, 'Time-Domain Feature Formulas:',
                transform=ax4.transAxes, fontsize=12, fontweight='bold', va='top')

        for i, formula in enumerate(formulas):
            ax4.text(0.05, 0.85 - i*0.11, formula, transform=ax4.transAxes,
                    fontsize=10, va='top', family='monospace')

        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')

        plt.suptitle('Time-Domain Feature Extraction Demonstration',
                    fontsize=16, fontweight='bold')
        plt.tight_layout()

        # Save chart
        chart_path = self.output_base_dir / f"{self.file_prefix}time_domain_features.png"
        plt.savefig(chart_path, dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()

        self.chart_registry["Time-Domain Features"] = str(chart_path)
        print(f"   ✅ Saved: {chart_path}")

    def calculate_skewness(self, data):
        """Calculate skewness"""
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0
        return np.mean(((data - mean) / std) ** 3)

    def calculate_kurtosis(self, data):
        """Calculate kurtosis"""
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0
        return np.mean(((data - mean) / std) ** 4) - 3

    def generate_frequency_domain_features(self):
        """Generate frequency-domain feature extraction demonstration"""
        print("\n🔊 Generating Frequency-Domain Features Demo...")

        # Generate sample signal with multiple frequency components
        np.random.seed(42)
        time = np.linspace(0, 1, 1000)
        fs = 1000  # Sampling frequency

        # Multi-component signal
        signal = (
            2.0 * np.sin(2 * np.pi * 10 * time) +    # 10 Hz component
            1.5 * np.sin(2 * np.pi * 25 * time) +    # 25 Hz component
            1.0 * np.sin(2 * np.pi * 50 * time) +    # 50 Hz component
            0.5 * np.sin(2 * np.pi * 100 * time) +   # 100 Hz component
            0.2 * np.random.normal(0, 1, len(time))   # Noise
        )

        # Compute FFT
        fft_signal = np.fft.fft(signal)
        fft_magnitude = np.abs(fft_signal[:len(fft_signal)//2])
        frequencies = np.fft.fftfreq(len(signal), 1/fs)[:len(fft_signal)//2]

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))

        # Time domain signal
        ax1.plot(time, signal, color=self.colors['primary'], linewidth=1.5)
        ax1.set_title('Multi-Component Vibration Signal', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Time (seconds)', fontsize=12)
        ax1.set_ylabel('Acceleration (m/s²)', fontsize=12)
        ax1.grid(True, alpha=0.3)

        # Frequency spectrum
        ax2.plot(frequencies, fft_magnitude, color=self.colors['secondary'], linewidth=1.5)
        ax2.set_title('Frequency Spectrum (FFT)', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Frequency (Hz)', fontsize=12)
        ax2.set_ylabel('Magnitude', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.set_xlim(0, 150)

        # Mark dominant frequencies
        peaks = [10, 25, 50, 100]
        for peak in peaks:
            peak_idx = np.argmin(np.abs(frequencies - peak))
            ax2.annotate(f'{peak} Hz', xy=(frequencies[peak_idx], fft_magnitude[peak_idx]),
                        xytext=(peak + 10, fft_magnitude[peak_idx] + 50),
                        arrowprops=dict(arrowstyle='->', color='red'),
                        fontsize=10, color='red')

        # Frequency-domain features
        dominant_freq = frequencies[np.argmax(fft_magnitude)]
        spectral_energy = np.sum(fft_magnitude**2)
        spectral_centroid = np.sum(frequencies * fft_magnitude) / np.sum(fft_magnitude)
        spectral_rolloff = self.calculate_spectral_rolloff(frequencies, fft_magnitude, 0.85)
        spectral_bandwidth = self.calculate_spectral_bandwidth(frequencies, fft_magnitude, spectral_centroid)

        freq_features = {
            'Dominant Frequency': dominant_freq,
            'Spectral Energy': spectral_energy,
            'Spectral Centroid': spectral_centroid,
            'Spectral Rolloff (85%)': spectral_rolloff,
            'Spectral Bandwidth': spectral_bandwidth,
            'Peak Magnitude': np.max(fft_magnitude)
        }

        feature_names = list(freq_features.keys())
        feature_values = list(freq_features.values())

        bars = ax3.barh(feature_names, feature_values, color=self.colors['success'], alpha=0.7)
        ax3.set_title('Extracted Frequency-Domain Features', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Feature Value', fontsize=12)
        ax3.grid(True, alpha=0.3)

        # Add value labels
        for bar, value in zip(bars, feature_values):
            ax3.text(bar.get_width() + max(feature_values)*0.01,
                    bar.get_y() + bar.get_height()/2,
                    f'{value:.1f}', va='center', fontsize=10)

        # Feature formulas
        formulas = [
            "FFT: X(k) = Σ x(n)e^(-j2πkn/N)",
            "Dominant Freq = arg max(|X(k)|)",
            "Spectral Energy = Σ |X(k)|²",
            "Spectral Centroid = Σ(f×|X(f)|)/Σ|X(f)|",
            "Spectral Rolloff = f where Σ|X(f)| = 0.85×Total",
            "Spectral Bandwidth = √(Σ(f-fc)²×|X(f)|/Σ|X(f)|)"
        ]

        ax4.text(0.05, 0.95, 'Frequency-Domain Feature Formulas:',
                transform=ax4.transAxes, fontsize=12, fontweight='bold', va='top')

        for i, formula in enumerate(formulas):
            ax4.text(0.05, 0.85 - i*0.13, formula, transform=ax4.transAxes,
                    fontsize=9, va='top', family='monospace')

        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')

        plt.suptitle('Frequency-Domain Feature Extraction Demonstration',
                    fontsize=16, fontweight='bold')
        plt.tight_layout()

        # Save chart
        chart_path = self.output_base_dir / f"{self.file_prefix}frequency_domain_features.png"
        plt.savefig(chart_path, dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()

        self.chart_registry["Frequency-Domain Features"] = str(chart_path)
        print(f"   ✅ Saved: {chart_path}")

    def calculate_spectral_rolloff(self, frequencies, magnitude, rolloff_percent):
        """Calculate spectral rolloff frequency"""
        total_energy = np.sum(magnitude)
        cumulative_energy = np.cumsum(magnitude)
        rolloff_idx = np.where(cumulative_energy >= rolloff_percent * total_energy)[0]
        return frequencies[rolloff_idx[0]] if len(rolloff_idx) > 0 else frequencies[-1]

    def calculate_spectral_bandwidth(self, frequencies, magnitude, centroid):
        """Calculate spectral bandwidth"""
        return np.sqrt(np.sum(((frequencies - centroid) ** 2) * magnitude) / np.sum(magnitude))

    def generate_time_frequency_features(self):
        """Generate time-frequency domain feature extraction demonstration"""
        print("\n🌊 Generating Time-Frequency Features Demo...")

        # Generate non-stationary signal
        np.random.seed(42)
        time = np.linspace(0, 2, 2000)

        # Chirp signal (frequency changes over time)
        f0, f1 = 10, 100
        signal = np.sin(2 * np.pi * (f0 + (f1 - f0) * time / 2) * time)

        # Add transient events
        for t_event in [0.5, 1.0, 1.5]:
            event_idx = int(t_event * 1000)
            if event_idx < len(signal):
                signal[event_idx:event_idx+50] += 3 * np.sin(2 * np.pi * 50 * time[event_idx:event_idx+50])

        # Add noise
        signal += 0.1 * np.random.normal(0, 1, len(signal))

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))

        # Time domain signal
        ax1.plot(time, signal, color=self.colors['primary'], linewidth=1)
        ax1.set_title('Non-Stationary Vibration Signal', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Time (seconds)', fontsize=12)
        ax1.set_ylabel('Acceleration (m/s²)', fontsize=12)
        ax1.grid(True, alpha=0.3)

        # Spectrogram (time-frequency representation)
        from scipy import signal as scipy_signal
        frequencies_spec, times_spec, Sxx = scipy_signal.spectrogram(
            signal, fs=1000, window='hann', nperseg=256, noverlap=128
        )

        im = ax2.pcolormesh(times_spec, frequencies_spec, 10 * np.log10(Sxx),
                           shading='gouraud', cmap='viridis')
        ax2.set_title('Spectrogram (Time-Frequency Analysis)', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Time (seconds)', fontsize=12)
        ax2.set_ylabel('Frequency (Hz)', fontsize=12)
        ax2.set_ylim(0, 150)
        plt.colorbar(im, ax=ax2, label='Power (dB)')

        # Wavelet transform demonstration
        from scipy import signal as scipy_signal
        widths = np.arange(1, 31)
        cwt_matrix = scipy_signal.cwt(signal[::4], scipy_signal.ricker, widths)  # Downsample for speed

        im2 = ax3.imshow(np.abs(cwt_matrix), extent=[0, 2, 1, 31], cmap='viridis',
                        aspect='auto', origin='lower')
        ax3.set_title('Continuous Wavelet Transform', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Time (seconds)', fontsize=12)
        ax3.set_ylabel('Scale', fontsize=12)
        plt.colorbar(im2, ax=ax3, label='Magnitude')

        # Time-frequency features summary
        tf_features = {
            'Spectral Centroid Variation': np.std([
                np.sum(frequencies_spec * Sxx[:, i]) / np.sum(Sxx[:, i])
                for i in range(Sxx.shape[1]) if np.sum(Sxx[:, i]) > 0
            ]),
            'Spectral Rolloff Variation': np.std([
                self.calculate_spectral_rolloff(frequencies_spec, Sxx[:, i], 0.85)
                for i in range(Sxx.shape[1])
            ]),
            'Spectral Flux': np.mean([
                np.sum((Sxx[:, i] - Sxx[:, i-1])**2)
                for i in range(1, Sxx.shape[1])
            ]),
            'Wavelet Energy': np.sum(np.abs(cwt_matrix)**2),
            'Instantaneous Frequency Range': f1 - f0,
            'Time-Frequency Entropy': -np.sum(
                Sxx * np.log(Sxx + 1e-10) / np.sum(Sxx)
            ) if np.sum(Sxx) > 0 else 0
        }

        # Display features as text
        ax4.text(0.05, 0.95, 'Time-Frequency Domain Features:',
                transform=ax4.transAxes, fontsize=12, fontweight='bold', va='top')

        y_pos = 0.85
        for feature, value in tf_features.items():
            ax4.text(0.05, y_pos, f'{feature}: {value:.3f}',
                    transform=ax4.transAxes, fontsize=11, va='top')
            y_pos -= 0.12

        ax4.text(0.05, 0.25, 'Key Concepts:',
                transform=ax4.transAxes, fontsize=12, fontweight='bold', va='top')

        concepts = [
            "• Spectrogram: STFT magnitude squared",
            "• Wavelet: Time-scale decomposition",
            "• Spectral Flux: Rate of spectral change",
            "• Instantaneous Frequency: Time-varying frequency"
        ]

        y_pos = 0.15
        for concept in concepts:
            ax4.text(0.05, y_pos, concept, transform=ax4.transAxes,
                    fontsize=10, va='top')
            y_pos -= 0.08

        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')

        plt.suptitle('Time-Frequency Domain Feature Extraction',
                    fontsize=16, fontweight='bold')
        plt.tight_layout()

        # Save chart
        chart_path = self.output_base_dir / f"{self.file_prefix}time_frequency_features.png"
        plt.savefig(chart_path, dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()

        self.chart_registry["Time-Frequency Features"] = str(chart_path)
        print(f"   ✅ Saved: {chart_path}")

    def generate_signal_preprocessing_demo(self):
        """Generate signal preprocessing demonstration"""
        print("\n🔧 Generating Signal Preprocessing Demo...")

        # Generate noisy signal
        np.random.seed(42)
        time = np.linspace(0, 1, 1000)

        # Clean signal
        clean_signal = (
            2 * np.exp(-((time - 0.5) / 0.1)**2) * np.sin(2 * np.pi * 20 * time) +
            0.5 * np.sin(2 * np.pi * 60 * time)
        )

        # Add various types of noise
        noise = 0.3 * np.random.normal(0, 1, len(time))
        outliers = np.zeros_like(time)
        outlier_indices = np.random.choice(len(time), 20, replace=False)
        outliers[outlier_indices] = np.random.normal(0, 2, 20)

        noisy_signal = clean_signal + noise + outliers

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))

        # Original noisy signal
        ax1.plot(time, noisy_signal, color=self.colors['danger'], linewidth=1, alpha=0.7, label='Noisy Signal')
        ax1.plot(time, clean_signal, color=self.colors['primary'], linewidth=2, label='Clean Signal')
        ax1.set_title('Raw Signal with Noise and Outliers', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Time (seconds)', fontsize=12)
        ax1.set_ylabel('Acceleration (m/s²)', fontsize=12)
        ax1.legend(fontsize=10)
        ax1.grid(True, alpha=0.3)

        # Filtered signal (moving average)
        window_size = 10
        filtered_signal = np.convolve(noisy_signal, np.ones(window_size)/window_size, mode='same')

        ax2.plot(time, noisy_signal, color=self.colors['danger'], linewidth=1, alpha=0.5, label='Noisy Signal')
        ax2.plot(time, filtered_signal, color=self.colors['success'], linewidth=2, label='Moving Average Filter')
        ax2.set_title('Moving Average Filtering', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Time (seconds)', fontsize=12)
        ax2.set_ylabel('Acceleration (m/s²)', fontsize=12)
        ax2.legend(fontsize=10)
        ax2.grid(True, alpha=0.3)

        # Outlier detection and removal
        from scipy import stats
        z_scores = np.abs(stats.zscore(noisy_signal))
        threshold = 2.5
        outlier_mask = z_scores > threshold
        cleaned_signal = noisy_signal.copy()
        cleaned_signal[outlier_mask] = np.median(noisy_signal)

        ax3.plot(time, noisy_signal, color=self.colors['danger'], linewidth=1, alpha=0.5, label='With Outliers')
        ax3.plot(time, cleaned_signal, color=self.colors['purple'], linewidth=2, label='Outliers Removed')
        ax3.scatter(time[outlier_mask], noisy_signal[outlier_mask],
                   color='red', s=30, alpha=0.8, label='Detected Outliers')
        ax3.set_title('Outlier Detection and Removal', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Time (seconds)', fontsize=12)
        ax3.set_ylabel('Acceleration (m/s²)', fontsize=12)
        ax3.legend(fontsize=10)
        ax3.grid(True, alpha=0.3)

        # Preprocessing steps summary
        preprocessing_steps = [
            "1. Data Validation",
            "   • Check for missing values",
            "   • Verify data types",
            "   • Validate sensor count",
            "",
            "2. Outlier Detection",
            "   • Z-score method (|z| > 2.5)",
            "   • Replace with median",
            "",
            "3. Noise Filtering",
            "   • Moving average filter",
            "   • Window size: 10 samples",
            "",
            "4. Signal Segmentation",
            "   • Extract 1-second windows",
            "   • Center on peak response",
            "",
            "5. Normalization",
            "   • Zero-mean centering",
            "   • Unit variance scaling"
        ]

        ax4.text(0.05, 0.95, 'Signal Preprocessing Pipeline:',
                transform=ax4.transAxes, fontsize=12, fontweight='bold', va='top')

        y_pos = 0.88
        for step in preprocessing_steps:
            if step.startswith(('1.', '2.', '3.', '4.', '5.')):
                ax4.text(0.05, y_pos, step, transform=ax4.transAxes,
                        fontsize=11, fontweight='bold', va='top', color=self.colors['primary'])
            elif step.startswith('   •'):
                ax4.text(0.1, y_pos, step, transform=ax4.transAxes,
                        fontsize=10, va='top')
            else:
                ax4.text(0.05, y_pos, step, transform=ax4.transAxes,
                        fontsize=10, va='top')
            y_pos -= 0.04

        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')

        plt.suptitle('Signal Preprocessing Demonstration',
                    fontsize=16, fontweight='bold')
        plt.tight_layout()

        # Save chart
        chart_path = self.output_base_dir / f"{self.file_prefix}signal_preprocessing_demo.png"
        plt.savefig(chart_path, dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()

        self.chart_registry["Signal Preprocessing"] = str(chart_path)
        print(f"   ✅ Saved: {chart_path}")

    def generate_filtering_comparison(self):
        """Generate filtering methods comparison"""
        print("\n🔍 Generating Filtering Comparison...")

        # Generate test signal
        np.random.seed(42)
        time = np.linspace(0, 1, 1000)
        fs = 1000

        # Signal with multiple frequency components + noise
        signal = (
            1.0 * np.sin(2 * np.pi * 10 * time) +    # Low frequency (keep)
            0.8 * np.sin(2 * np.pi * 30 * time) +    # Mid frequency (keep)
            0.6 * np.sin(2 * np.pi * 150 * time) +   # High frequency (remove)
            0.4 * np.sin(2 * np.pi * 300 * time) +   # Very high frequency (remove)
            0.2 * np.random.normal(0, 1, len(time))   # Noise
        )

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))

        # Original signal
        ax1.plot(time, signal, color=self.colors['danger'], linewidth=1, alpha=0.8)
        ax1.set_title('Original Signal (10Hz + 30Hz + 150Hz + 300Hz + Noise)',
                     fontsize=14, fontweight='bold')
        ax1.set_xlabel('Time (seconds)', fontsize=12)
        ax1.set_ylabel('Acceleration (m/s²)', fontsize=12)
        ax1.grid(True, alpha=0.3)

        # Low-pass filtering
        from scipy import signal as scipy_signal

        # Butterworth low-pass filter (cutoff at 50 Hz)
        nyquist = fs / 2
        cutoff = 50
        order = 4
        b, a = scipy_signal.butter(order, cutoff / nyquist, btype='low')
        filtered_lowpass = scipy_signal.filtfilt(b, a, signal)

        ax2.plot(time, signal, color=self.colors['danger'], linewidth=1, alpha=0.5, label='Original')
        ax2.plot(time, filtered_lowpass, color=self.colors['success'], linewidth=2, label='Low-pass (50Hz)')
        ax2.set_title('Butterworth Low-Pass Filter', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Time (seconds)', fontsize=12)
        ax2.set_ylabel('Acceleration (m/s²)', fontsize=12)
        ax2.legend(fontsize=10)
        ax2.grid(True, alpha=0.3)

        # Band-pass filtering
        low_cutoff = 5
        high_cutoff = 40
        b_bp, a_bp = scipy_signal.butter(order, [low_cutoff / nyquist, high_cutoff / nyquist], btype='band')
        filtered_bandpass = scipy_signal.filtfilt(b_bp, a_bp, signal)

        ax3.plot(time, signal, color=self.colors['danger'], linewidth=1, alpha=0.5, label='Original')
        ax3.plot(time, filtered_bandpass, color=self.colors['purple'], linewidth=2, label='Band-pass (5-40Hz)')
        ax3.set_title('Butterworth Band-Pass Filter', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Time (seconds)', fontsize=12)
        ax3.set_ylabel('Acceleration (m/s²)', fontsize=12)
        ax3.legend(fontsize=10)
        ax3.grid(True, alpha=0.3)

        # Filter comparison table
        filter_info = [
            ["Filter Type", "Cutoff Freq", "Order", "Application"],
            ["Low-pass", "50 Hz", "4", "Remove high-freq noise"],
            ["Band-pass", "5-40 Hz", "4", "Extract vehicle frequencies"],
            ["High-pass", "1 Hz", "2", "Remove DC offset"],
            ["Notch", "50/60 Hz", "2", "Remove power line noise"],
            ["Moving Avg", "N=10", "-", "Simple smoothing"],
            ["Median", "N=5", "-", "Remove impulse noise"]
        ]

        # Create table
        table_data = []
        for row in filter_info[1:]:  # Skip header
            table_data.append(row)

        table = ax4.table(cellText=table_data,
                         colLabels=filter_info[0],
                         cellLoc='center',
                         loc='center',
                         bbox=[0, 0.3, 1, 0.6])

        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 2)

        # Style the table
        for i in range(len(filter_info[0])):
            table[(0, i)].set_facecolor(self.colors['primary'])
            table[(0, i)].set_text_props(weight='bold', color='white')

        ax4.text(0.5, 0.9, 'Filter Comparison Summary',
                transform=ax4.transAxes, fontsize=12, fontweight='bold',
                ha='center', va='top')

        ax4.text(0.5, 0.2, 'Filter Selection Criteria:\n• Vehicle frequencies: 5-50 Hz\n• Sampling rate: 1000 Hz\n• Noise reduction: >20 dB',
                transform=ax4.transAxes, fontsize=10, ha='center', va='top',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))

        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')

        plt.suptitle('Signal Filtering Methods Comparison',
                    fontsize=16, fontweight='bold')
        plt.tight_layout()

        # Save chart
        chart_path = self.output_base_dir / f"{self.file_prefix}filtering_comparison.png"
        plt.savefig(chart_path, dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()

        self.chart_registry["Filtering Comparison"] = str(chart_path)
        print(f"   ✅ Saved: {chart_path}")

    def generate_comprehensive_workflow(self):
        """Generate comprehensive workflow diagram"""
        print("\n🔄 Generating Comprehensive Workflow Diagram...")

        fig, ax = plt.subplots(figsize=(16, 12))

        # Define workflow stages with positions
        stages = [
            # Input Stage
            {"name": "Raw CSV Files\n(3,398 files)\n22 columns each", "pos": (2, 10), "color": self.colors['info'], "size": (2, 1.2)},

            # Processing Stage
            {"name": "File Format\nValidation", "pos": (2, 8.5), "color": self.colors['warning'], "size": (1.8, 1)},
            {"name": "Sensor Data\nExtraction\n(20 sensors)", "pos": (2, 7), "color": self.colors['secondary'], "size": (1.8, 1)},
            {"name": "Signal\nPreprocessing", "pos": (2, 5.5), "color": self.colors['purple'], "size": (1.8, 1)},

            # Feature Extraction Stage
            {"name": "Time-Domain\nFeatures", "pos": (5.5, 7), "color": self.colors['primary'], "size": (1.8, 1)},
            {"name": "Frequency-Domain\nFeatures", "pos": (5.5, 5.5), "color": self.colors['success'], "size": (1.8, 1)},
            {"name": "Time-Frequency\nFeatures", "pos": (5.5, 4), "color": self.colors['brown'], "size": (1.8, 1)},

            # Data Management Stage
            {"name": "Feature\nCombination", "pos": (8.5, 5.5), "color": self.colors['cyan'], "size": (1.8, 1)},
            {"name": "Data\nDeduplication", "pos": (8.5, 4), "color": self.colors['pink'], "size": (1.8, 1)},
            {"name": "Quality\nValidation", "pos": (8.5, 2.5), "color": self.colors['olive'], "size": (1.8, 1)},

            # Output Stage
            {"name": "Combined Features\n(3,237 samples)\n320 features", "pos": (12, 5.5), "color": self.colors['danger'], "size": (2, 1.2)},

            # ML Stage
            {"name": "Machine Learning\nModels", "pos": (12, 3.5), "color": self.colors['gray'], "size": (2, 1)},
            {"name": "Performance\nOptimization", "pos": (12, 2), "color": self.colors['primary'], "size": (2, 1)}
        ]

        # Draw stages
        for stage in stages:
            bbox = FancyBboxPatch(
                (stage["pos"][0] - stage["size"][0]/2, stage["pos"][1] - stage["size"][1]/2),
                stage["size"][0], stage["size"][1],
                boxstyle="round,pad=0.1",
                facecolor=stage["color"],
                edgecolor='black',
                alpha=0.7,
                linewidth=1.5
            )
            ax.add_patch(bbox)
            ax.text(stage["pos"][0], stage["pos"][1], stage["name"],
                   ha='center', va='center', fontsize=10, fontweight='bold')

        # Draw connections
        connections = [
            # Main flow
            ((2, 9.4), (2, 9.0)),      # CSV to Validation
            ((2, 8.0), (2, 7.5)),      # Validation to Extraction
            ((2, 6.5), (2, 6.0)),      # Extraction to Preprocessing

            # To feature extraction
            ((2.9, 5.5), (4.6, 6.5)),  # Preprocessing to Time-Domain
            ((2.9, 5.5), (4.6, 5.5)),  # Preprocessing to Frequency-Domain
            ((2.9, 5.5), (4.6, 4.5)),  # Preprocessing to Time-Frequency

            # Feature combination
            ((6.4, 6.5), (7.6, 5.8)),  # Time-Domain to Combination
            ((6.4, 5.5), (7.6, 5.5)),  # Frequency-Domain to Combination
            ((6.4, 4.5), (7.6, 5.2)),  # Time-Frequency to Combination

            # Data management flow
            ((8.5, 5.0), (8.5, 4.5)),  # Combination to Deduplication
            ((8.5, 3.5), (8.5, 3.0)),  # Deduplication to Quality

            # To output
            ((9.4, 5.5), (11, 5.5)),   # Combination to Combined Features
            ((9.4, 2.5), (11, 3.0)),   # Quality to ML Models
            ((12, 3.0), (12, 2.5)),    # ML to Optimization
        ]

        for start, end in connections:
            ax.annotate('', xy=end, xytext=start,
                       arrowprops=dict(arrowstyle='->', lw=2, color='black'))

        # Add side annotations
        annotations = [
            {"text": "Input Data\n• 3,398 CSV files\n• 20 sensors per file\n• 1000 Hz sampling\n• Vehicle parameters",
             "pos": (0.5, 9), "color": 'lightblue'},

            {"text": "Feature Types\n• Mean, STD, RMS\n• Peak, Crest Factor\n• FFT, Spectral Centroid\n• Wavelet Coefficients",
             "pos": (5.5, 2), "color": 'lightgreen'},

            {"text": "Data Quality\n• Deduplication: 3 methods\n• Validation: Range checks\n• Completeness: 95.4%\n• Final: 3,237 samples",
             "pos": (10, 0.5), "color": 'lightyellow'},

            {"text": "ML Performance\n• Speed: R² = 0.9337\n• Load: R² = 0.9451\n• Axle: 99.26% accuracy\n• All targets achieved",
             "pos": (14, 2.5), "color": 'lightcoral'}
        ]

        for ann in annotations:
            ax.text(ann["pos"][0], ann["pos"][1], ann["text"],
                   bbox=dict(boxstyle="round,pad=0.3", facecolor=ann["color"], alpha=0.8),
                   fontsize=9, ha='center', va='center')

        ax.set_xlim(0, 16)
        ax.set_ylim(0, 11)
        ax.set_title('Comprehensive Vibration Signal Analysis Workflow',
                    fontsize=18, fontweight='bold', pad=20)
        ax.axis('off')

        # Save chart
        chart_path = self.output_base_dir / f"{self.file_prefix}comprehensive_workflow.png"
        plt.savefig(chart_path, dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()

        self.chart_registry["Comprehensive Workflow"] = str(chart_path)
        print(f"   ✅ Saved: {chart_path}")

    def generate_visualization_summary(self) -> Dict[str, List[str]]:
        """Generate summary of all visualizations"""
        print("\n📋 Generating Visualization Summary...")

        # Organize charts by category
        summary = {
            "System Overview": [],
            "Workflow Diagrams": [],
            "Signal Analysis": [],
            "Feature Extraction": [],
            "Signal Preprocessing": []
        }

        # Categorize charts
        for chart_name, chart_path in self.chart_registry.items():
            if "overview" in chart_path.lower():
                summary["System Overview"].append(chart_path)
            elif "workflow" in chart_path.lower():
                summary["Workflow Diagrams"].append(chart_path)
            elif "signal" in chart_path.lower():
                summary["Signal Analysis"].append(chart_path)
            elif "feature" in chart_path.lower():
                summary["Feature Extraction"].append(chart_path)
            elif "preprocessing" in chart_path.lower():
                summary["Signal Preprocessing"].append(chart_path)

        # Generate summary report
        summary_report = {
            "generation_timestamp": pd.Timestamp.now().isoformat(),
            "total_charts": len(self.chart_registry),
            "output_directory": str(self.output_base_dir),
            "chart_categories": summary,
            "chart_registry": self.chart_registry,
            "technical_specifications": {
                "resolution": "330 DPI",
                "font_family": "Times New Roman",
                "format": "PNG",
                "color_scheme": "Academic/IEEE Standard",
                "title_size": "16pt",
                "label_size": "14pt",
                "tick_size": "12pt"
            }
        }

        # Save summary report
        summary_path = self.output_base_dir / "visualization_summary_report.json"
        with open(summary_path, 'w', encoding='utf-8') as f:
            import json
            json.dump(summary_report, f, ensure_ascii=False, indent=2)

        # Print summary table
        print(f"\n📊 Technical Visualization Summary")
        print("=" * 80)
        print(f"Total Charts Generated: {len(self.chart_registry)}")
        print(f"Output Directory: {self.output_base_dir}")
        print(f"Resolution: 330 DPI")
        print(f"Font: Times New Roman")
        print(f"Format: PNG")
        print("\nChart Categories:")

        for category, charts in summary.items():
            print(f"  {category}: {len(charts)} charts")
            for chart in charts:
                chart_name = Path(chart).name
                print(f"    • {chart_name}")

        print(f"\n📋 Summary report saved: {summary_path}")

        return summary_report

def main():
    """Main function to generate all technical visualizations"""
    visualizer = TechnicalWorkflowVisualizer()
    summary = visualizer.generate_all_technical_visualizations()

    print(f"\n🎉 Technical Workflow Visualization Generation Complete!")
    print(f"   Total charts: {summary['total_charts']}")
    print(f"   Output directory: {summary['output_directory']}")
    print(f"   Summary report: visualization_summary_report.json")

    return summary

if __name__ == "__main__":
    main()