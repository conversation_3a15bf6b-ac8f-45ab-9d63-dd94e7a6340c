#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试unified_vibration_analysis.py中的数据源控制参数功能
验证三种数据源模式的正确性

作者: AI Assistant
日期: 2024-12-22
"""

import os
import pandas as pd
import sys

def test_data_source_mode(mode, expected_sources):
    """测试指定的数据源模式"""
    print(f"\n🧪 测试数据源模式: {mode}")
    print("="*60)
    
    try:
        # 导入系统
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 初始化分析器
        print(f"📦 初始化分析器（模式: {mode}）...")
        analyzer = UnifiedVibrationAnalysisSystem(
            data_dir='./data',
            data_source_mode=mode
        )
        
        # 验证模式设置
        if analyzer.data_source_mode != mode:
            print(f"❌ 模式设置失败: 期望{mode}, 实际{analyzer.data_source_mode}")
            return False
        
        print(f"✅ 模式设置成功: {analyzer.data_source_mode}")
        
        # 运行特征提取（使用简化方法以加快测试）
        print(f"📊 运行特征提取...")
        features_df = analyzer.extract_features_simple()
        
        if features_df is not None and not features_df.empty:
            print(f"✅ 特征提取成功")
            print(f"   数据形状: {features_df.shape}")
            
            # 验证数据源
            if 'data_source' in features_df.columns:
                source_counts = features_df['data_source'].value_counts()
                actual_sources = list(source_counts.keys())
                
                print(f"   📊 实际数据源分布:")
                for source, count in source_counts.items():
                    print(f"      {source}: {count} 样本")
                
                # 验证数据源是否符合预期
                if set(actual_sources).issubset(set(expected_sources)):
                    print(f"   ✅ 数据源验证通过")
                    print(f"      期望: {expected_sources}")
                    print(f"      实际: {actual_sources}")
                    return True
                else:
                    print(f"   ❌ 数据源验证失败")
                    print(f"      期望: {expected_sources}")
                    print(f"      实际: {actual_sources}")
                    return False
            else:
                print(f"   ❌ 缺少data_source列")
                return False
        else:
            print(f"❌ 特征提取失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_invalid_mode():
    """测试无效的数据源模式"""
    print(f"\n🧪 测试无效数据源模式")
    print("="*60)
    
    try:
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 使用无效模式
        invalid_mode = 'invalid_mode'
        print(f"📦 尝试使用无效模式: {invalid_mode}")
        
        analyzer = UnifiedVibrationAnalysisSystem(
            data_dir='./data',
            data_source_mode=invalid_mode
        )
        
        # 验证是否自动回退到默认模式
        if analyzer.data_source_mode == 'dual_format':
            print(f"✅ 无效模式处理正确，自动回退到默认模式: {analyzer.data_source_mode}")
            return True
        else:
            print(f"❌ 无效模式处理失败: {analyzer.data_source_mode}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_mode_consistency():
    """测试模式一致性"""
    print(f"\n🧪 测试模式一致性")
    print("="*60)
    
    try:
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 测试所有有效模式
        valid_modes = ['legacy_only', 'dual_format', 'new_only']
        
        for mode in valid_modes:
            print(f"\n   测试模式: {mode}")
            analyzer = UnifiedVibrationAnalysisSystem(
                data_dir='./data',
                data_source_mode=mode
            )
            
            if analyzer.data_source_mode == mode:
                print(f"   ✅ 模式 {mode} 设置正确")
            else:
                print(f"   ❌ 模式 {mode} 设置失败")
                return False
        
        print(f"✅ 所有模式一致性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def check_data_availability():
    """检查数据可用性"""
    print(f"\n📋 检查数据可用性")
    print("="*60)
    
    data_dir = './data'
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return False
    
    # 统计文件
    legacy_files = 0
    new_files = 0
    total_csv_files = 0
    
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.csv'):
                total_csv_files += 1
                
                # 检查文件类型
                if 'acce_' in file or 'sensor' in file:
                    legacy_files += 1
                elif 'GW100001' in file and 'AcceData' in file:
                    new_files += 1
    
    print(f"   📊 数据文件统计:")
    print(f"      总CSV文件: {total_csv_files}")
    print(f"      传统格式文件: {legacy_files}")
    print(f"      新格式文件: {new_files}")
    print(f"      其他文件: {total_csv_files - legacy_files - new_files}")
    
    # 检查是否有足够的数据进行测试
    has_legacy = legacy_files > 0
    has_new = new_files > 0
    
    print(f"\n   📋 模式支持检查:")
    print(f"      legacy_only: {'✅' if has_legacy else '❌'}")
    print(f"      new_only: {'✅' if has_new else '❌'}")
    print(f"      dual_format: {'✅' if has_legacy or has_new else '❌'}")
    
    return has_legacy or has_new

def generate_test_report(test_results):
    """生成测试报告"""
    print(f"\n" + "="*80)
    print(f"📋 数据源模式测试报告")
    print("="*80)
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    print(f"\n📊 测试结果总览:")
    print(f"   总测试项: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   失败测试: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 总体评估
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！数据源控制参数功能正常")
        recommendation = "数据源控制参数已成功实现，可以灵活选择数据处理模式"
    elif passed_tests >= total_tests * 0.75:
        print(f"\n✅ 大部分测试通过，功能基本正常")
        recommendation = "数据源控制参数基本可用，建议修复失败的测试项"
    else:
        print(f"\n❌ 多项测试失败，需要检查实现")
        recommendation = "建议重新检查数据源控制参数的实现逻辑"
    
    print(f"\n💡 建议: {recommendation}")
    
    # 保存测试报告
    report = {
        'test_summary': {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'pass_rate': passed_tests/total_tests*100
        },
        'test_results': test_results,
        'recommendation': recommendation
    }
    
    import json
    with open('data_source_modes_test_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试报告已保存到: data_source_modes_test_report.json")
    
    return passed_tests == total_tests

def main():
    """主函数"""
    print("🚀 启动数据源模式测试")
    
    # 检查数据可用性
    if not check_data_availability():
        print("❌ 数据不可用，无法进行测试")
        return False
    
    # 执行测试
    test_results = {}
    
    # 测试1：legacy_only模式
    test_results['legacy_only模式'] = test_data_source_mode('legacy_only', ['legacy_format'])
    
    # 测试2：new_only模式
    test_results['new_only模式'] = test_data_source_mode('new_only', ['new_format'])
    
    # 测试3：dual_format模式
    test_results['dual_format模式'] = test_data_source_mode('dual_format', ['legacy_format', 'new_format'])
    
    # 测试4：无效模式处理
    test_results['无效模式处理'] = test_invalid_mode()
    
    # 测试5：模式一致性
    test_results['模式一致性'] = test_mode_consistency()
    
    # 生成测试报告
    success = generate_test_report(test_results)
    
    if success:
        print(f"\n🎯 功能验证完成！")
        print(f"   ✅ 数据源控制参数功能正常")
        print(f"   ✅ 支持三种数据处理模式")
        print(f"   ✅ 参数验证和错误处理完善")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
