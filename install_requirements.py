#!/usr/bin/env python3
"""
简化的依赖安装脚本
专为统一振动信号分析系统设计
"""

import subprocess
import sys
import importlib

def install_package(package_name: str) -> bool:
    """安装单个包"""
    try:
        print(f"📦 安装 {package_name}...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', package_name
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {package_name} 安装异常: {str(e)}")
        return False

def check_and_install_packages():
    """检查并安装所需包"""
    print("🔧 统一振动信号分析系统 - 依赖安装")
    print("=" * 50)
    
    # 核心依赖包
    required_packages = [
        'numpy',
        'pandas', 
        'scikit-learn',
        'matplotlib',
        'seaborn',
        'xgboost',
        'tensorflow',
        'joblib',
        'scipy',
        'PyWavelets'
    ]
    
    # 检查已安装的包
    installed_packages = []
    missing_packages = []
    
    print("🔍 检查已安装的包...")
    for package in required_packages:
        try:
            if package == 'scikit-learn':
                importlib.import_module('sklearn')
            elif package == 'PyWavelets':
                importlib.import_module('pywt')
            else:
                importlib.import_module(package)
            
            print(f"  ✅ {package}")
            installed_packages.append(package)
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if not missing_packages:
        print("\n🎉 所有依赖包已安装!")
        return True
    
    print(f"\n📦 需要安装 {len(missing_packages)} 个包...")
    
    # 安装缺失的包
    success_count = 0
    for package in missing_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 安装结果: {success_count}/{len(missing_packages)} 成功")
    
    if success_count == len(missing_packages):
        print("🎉 所有包安装完成!")
        return True
    else:
        print("⚠️  部分包安装失败，系统可能无法完全正常工作")
        return False

def main():
    """主函数"""
    success = check_and_install_packages()
    
    if success:
        print("\n✅ 环境准备完成!")
        print("🚀 现在可以运行: python unified_vibration_analysis.py")
    else:
        print("\n❌ 环境准备失败")
        print("🔧 请手动安装缺失的包")

if __name__ == "__main__":
    main()
