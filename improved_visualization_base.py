#!/usr/bin/env python3
"""
改进的可视化基础类
解决中文字体问题，优化图表布局，提供统一的可视化基础功能
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
import os
import platform
import warnings
warnings.filterwarnings('ignore')

# 导入统一字体管理器
try:
    from unified_font_manager import apply_unified_font_config, get_font_manager
    # 确保字体配置已应用
    apply_unified_font_config()
    _font_manager_available = True
except ImportError:
    _font_manager_available = False
    # 备用字体配置
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

class ImprovedVisualizationBase:
    """改进的可视化基础类"""
    
    def __init__(self, output_dir='improved_visualizations'):
        """初始化可视化基础类"""
        self.output_dir = output_dir
        self.chinese_font = None

        # 学术期刊标准配色方案
        self.academic_colors = {
            'primary': '#1f77b4',      # 蓝色
            'secondary': '#ff7f0e',    # 橙色
            'success': '#2ca02c',      # 绿色
            'warning': '#d62728',      # 红色
            'info': '#9467bd',         # 紫色
            'accent': '#8c564b',       # 棕色
            'light': '#e377c2',        # 粉色
            'dark': '#7f7f7f',         # 灰色
            'bright': '#bcbd22',       # 黄绿色
            'cyan': '#17becf'          # 青色
        }

        # 图表配置
        self.chart_config = {
            'dpi': 330,
            'figsize_single': (10, 8),
            'figsize_double': (16, 8),
            'figsize_quad': (16, 12),
            'title_fontsize': 16,
            'label_fontsize': 14,
            'tick_fontsize': 12,
            'legend_fontsize': 12,
            'grid_alpha': 0.3,
            'grid_linewidth': 0.5
        }

        # 初始化字体和样式
        self.setup_fonts_and_style()
        self.ensure_output_dirs()
    
    def detect_chinese_font(self):
        """智能检测中文字体（优先使用统一字体管理器）"""
        print("🔍 检测中文字体...")

        # 优先使用统一字体管理器
        if _font_manager_available:
            try:
                font_manager = get_font_manager()
                detected_font = font_manager.selected_font
                if detected_font:
                    print(f"   ✅ 使用统一字体管理器: {detected_font}")
                    return detected_font
            except Exception as e:
                print(f"   ⚠️  统一字体管理器获取失败: {e}")

        # 备用字体检测逻辑
        print("   🔄 使用备用字体检测...")

        # 按优先级排序的中文字体列表
        font_candidates = [
            'Microsoft YaHei',  # 微软雅黑 (Windows) - 提高优先级
            'SimHei',           # 黑体 (Windows)
            'PingFang SC',      # 苹方 (macOS)
            'Hiragino Sans GB', # 冬青黑体 (macOS)
            'WenQuanYi Micro Hei', # 文泉驿微米黑 (Linux)
            'Noto Sans CJK SC', # 思源黑体 (Linux)
            'DejaVu Sans',      # 备用字体
            'Arial Unicode MS'  # 备用字体
        ]

        detected_font = None
        system_info = platform.system()

        print(f"   操作系统: {system_info}")

        for font_name in font_candidates:
            try:
                # 尝试查找字体文件
                font_path = fm.findfont(fm.FontProperties(family=font_name))

                if font_path and os.path.exists(font_path):
                    # 验证字体是否支持中文
                    if self._test_chinese_support(font_name):
                        detected_font = font_name
                        print(f"   ✅ 检测到可用中文字体: {font_name}")
                        print(f"   📁 字体路径: {font_path}")
                        break
                    else:
                        print(f"   ⚠️  字体 {font_name} 不支持中文显示")
            except Exception as e:
                print(f"   ❌ 字体 {font_name} 检测失败: {str(e)}")
                continue

        if detected_font is None:
            print("   ⚠️  未找到合适的中文字体，将使用系统默认字体")
            print("   💡 建议安装 Microsoft YaHei 或 SimHei 字体以获得最佳中文显示效果")
            detected_font = 'Microsoft YaHei'  # 优先使用微软雅黑

        return detected_font
    
    def _test_chinese_support(self, font_name):
        """测试字体是否支持中文"""
        try:
            # 创建一个小的测试图形
            fig, ax = plt.subplots(figsize=(1, 1))
            ax.text(0.5, 0.5, '测试中文', fontfamily=font_name, fontsize=12)
            plt.close(fig)
            return True
        except:
            return False
    
    def setup_fonts_and_style(self):
        """设置字体和样式"""
        print("🎨 配置可视化样式...")
        
        # 检测中文字体
        self.chinese_font = self.detect_chinese_font()
        
        # 配置matplotlib全局参数
        plt.rcParams.update({
            # 字体设置
            'font.sans-serif': [self.chinese_font, 'DejaVu Sans', 'Arial'],
            'axes.unicode_minus': False,  # 解决负号显示问题
            
            # 图形设置
            'figure.figsize': self.chart_config['figsize_single'],
            'figure.dpi': self.chart_config['dpi'],
            'savefig.dpi': self.chart_config['dpi'],
            'savefig.bbox': 'tight',
            'savefig.facecolor': 'white',
            'savefig.edgecolor': 'none',
            'savefig.transparent': False,
            
            # 字体大小设置
            'font.size': self.chart_config['tick_fontsize'],
            'axes.titlesize': self.chart_config['title_fontsize'],
            'axes.labelsize': self.chart_config['label_fontsize'],
            'xtick.labelsize': self.chart_config['tick_fontsize'],
            'ytick.labelsize': self.chart_config['tick_fontsize'],
            'legend.fontsize': self.chart_config['legend_fontsize'],
            
            # 线条和网格设置
            'lines.linewidth': 2,
            'axes.linewidth': 1,
            'grid.alpha': self.chart_config['grid_alpha'],
            'grid.linewidth': self.chart_config['grid_linewidth'],
            'axes.grid': True,
            'axes.axisbelow': True,
            
            # 颜色和样式
            'axes.prop_cycle': plt.cycler('color', list(self.academic_colors.values())),
            'axes.facecolor': 'white',
            'axes.edgecolor': 'black',
            'axes.spines.left': True,
            'axes.spines.bottom': True,
            'axes.spines.top': False,
            'axes.spines.right': False,
            
            # 图例设置
            'legend.frameon': True,
            'legend.fancybox': True,
            'legend.shadow': False,
            'legend.framealpha': 0.9,
            'legend.edgecolor': 'gray',
            
            # 其他设置
            'figure.autolayout': False,
            'figure.constrained_layout.use': False
        })
        
        # 设置seaborn样式
        sns.set_style("whitegrid", {
            'axes.grid': True,
            'grid.alpha': self.chart_config['grid_alpha'],
            'grid.linewidth': self.chart_config['grid_linewidth']
        })
        
        # 设置调色板
        sns.set_palette(list(self.academic_colors.values()))
        
        print(f"   ✅ 字体配置完成: {self.chinese_font}")
        print(f"   ✅ 图表DPI: {self.chart_config['dpi']}")
        print(f"   ✅ 样式配置完成")
    
    def ensure_output_dirs(self):
        """确保输出目录存在"""
        dirs = [
            self.output_dir,
            os.path.join(self.output_dir, 'chinese'),
            os.path.join(self.output_dir, 'english'),
            os.path.join(self.output_dir, 'regression_analysis'),
            os.path.join(self.output_dir, 'classification_analysis'),
            os.path.join(self.output_dir, 'model_comparison'),
            os.path.join(self.output_dir, 'feature_analysis'),
            os.path.join(self.output_dir, 'optimization_analysis'),
            os.path.join(self.output_dir, 'sensor_analysis')
        ]
        
        for dir_path in dirs:
            os.makedirs(dir_path, exist_ok=True)
    
    def get_optimal_legend_position(self, ax, data_bounds=None):
        """获取最佳图例位置"""
        if data_bounds is None:
            # 自动检测数据边界
            xlim = ax.get_xlim()
            ylim = ax.get_ylim()
            data_bounds = {'xmin': xlim[0], 'xmax': xlim[1], 'ymin': ylim[0], 'ymax': ylim[1]}
        
        # 定义可能的图例位置
        positions = [
            'upper right', 'upper left', 'lower right', 'lower left',
            'center right', 'center left', 'upper center', 'lower center'
        ]
        
        # 简单策略：根据数据分布选择位置
        # 这里可以实现更复杂的算法来避免与数据重叠
        return 'upper right'  # 默认位置
    
    def optimize_text_positions(self, ax, texts, avoid_overlap=True):
        """优化文本位置以避免重叠"""
        if not avoid_overlap or len(texts) <= 1:
            return
        
        # 简单的文本位置优化算法
        # 在实际应用中可以使用adjustText库
        for i, text in enumerate(texts):
            if hasattr(text, 'get_position'):
                x, y = text.get_position()
                # 添加小的偏移以避免重叠
                offset = i * 0.02
                text.set_position((x, y + offset))
    
    def save_chart(self, fig, filename, language='chinese', subdir=None):
        """保存图表到指定目录"""
        # 确定保存目录
        if subdir:
            save_dir = os.path.join(self.output_dir, subdir, language)
        else:
            save_dir = os.path.join(self.output_dir, language)
        
        os.makedirs(save_dir, exist_ok=True)
        
        # 构建完整文件路径
        filepath = os.path.join(save_dir, filename)
        
        # 保存图表
        fig.savefig(filepath, 
                   dpi=self.chart_config['dpi'],
                   bbox_inches='tight',
                   facecolor='white',
                   edgecolor='none',
                   transparent=False)
        
        return filepath
    
    def create_chart_metadata(self, chart_info):
        """创建图表元数据"""
        metadata = {
            'chart_type': chart_info.get('type', 'unknown'),
            'model_name': chart_info.get('model', 'unknown'),
            'language': chart_info.get('language', 'chinese'),
            'description': chart_info.get('description', ''),
            'data_source': chart_info.get('data_source', ''),
            'analysis': chart_info.get('analysis', ''),
            'created_at': pd.Timestamp.now().isoformat(),
            'dpi': self.chart_config['dpi'],
            'font': self.chinese_font
        }
        return metadata
    
    def get_color_palette(self, n_colors):
        """获取指定数量的颜色调色板"""
        if n_colors <= len(self.academic_colors):
            return list(self.academic_colors.values())[:n_colors]
        else:
            # 如果需要更多颜色，使用seaborn生成
            return sns.color_palette("husl", n_colors)

def main():
    """测试函数"""
    print("🧪 测试改进的可视化基础类...")
    
    # 初始化基础类
    viz_base = ImprovedVisualizationBase()
    
    # 测试字体检测
    print(f"\n检测到的中文字体: {viz_base.chinese_font}")
    
    # 测试简单图表生成
    fig, ax = plt.subplots(figsize=viz_base.chart_config['figsize_single'])
    
    # 生成测试数据
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)
    
    # 绘制测试图表
    ax.plot(x, y1, label='正弦波', linewidth=2)
    ax.plot(x, y2, label='余弦波', linewidth=2)
    
    ax.set_xlabel('时间 (秒)', fontweight='bold')
    ax.set_ylabel('幅值', fontweight='bold')
    ax.set_title('中文字体显示测试', fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=viz_base.chart_config['grid_alpha'])
    
    # 保存测试图表
    filepath = viz_base.save_chart(fig, 'font_test_chinese.png', 'chinese')
    plt.close(fig)
    
    print(f"✅ 测试图表已保存: {filepath}")
    print("✅ 基础类测试完成!")

if __name__ == "__main__":
    main()
