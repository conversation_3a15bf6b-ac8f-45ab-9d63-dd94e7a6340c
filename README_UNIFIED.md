# 🚀 统一振动信号分析系统 v2.0

## 📋 系统简介

这是一个重新设计的统一振动信号分析系统，集成了三种先进的机器学习算法：
- **XGBoost**: 梯度提升算法，支持GPU加速
- **CNN-LSTM**: 卷积神经网络+长短期记忆网络
- **BP神经网络**: 反向传播神经网络

## 🎯 主要功能

- ✅ **自动特征提取**: 从原始振动信号中提取时域、频域特征
- ✅ **多任务预测**: 支持速度预测、轴重预测、轴型分类
- ✅ **GPU加速**: 自动检测并使用GPU进行训练
- ✅ **一键运行**: 简化的操作流程，一个命令完成所有分析
- ✅ **自动报告**: 生成详细的分析报告和模型对比

## 🚀 快速开始

### 1. 安装依赖
```bash
python install_requirements.py
```

### 2. 准备数据
将您的振动信号数据按以下结构组织：
```
data/
├── 2吨/双轴/40km_h/acce_*.csv
├── 25吨/三轴/60km_h/acce_*.csv
└── ...
```

### 3. 运行分析
```bash
python unified_vibration_analysis.py
```

## 📊 输出结果

运行完成后，系统会生成：
- `combined_features.csv` - 提取的特征数据
- `analysis_report.md` - 详细分析报告
- `model_*.pkl` / `model_*.h5` - 训练好的模型文件

## 🔧 系统要求

- **Python**: 3.8+
- **内存**: 8GB+ (推荐16GB+)
- **GPU**: NVIDIA GPU (可选，用于加速)
- **存储**: 2GB+ 可用空间

## 📈 算法说明

### XGBoost
- 梯度提升决策树
- 支持GPU加速训练
- 适合结构化数据
- 高精度和快速训练

### CNN-LSTM
- 结合卷积和循环神经网络
- 自动提取时序特征
- 适合振动信号的时序特性
- 深度学习方法

### BP神经网络
- 多层感知机
- 批量归一化和Dropout
- 自适应学习率
- 经典深度学习方法

## 🎯 性能目标

- **速度预测**: R² > 0.75
- **轴重预测**: R² > 0.75
- **轴型分类**: 准确率 > 0.85

## 🔍 故障排除

### 常见问题

1. **依赖包安装失败**
   ```bash
   pip install --upgrade pip
   python install_requirements.py
   ```

2. **找不到数据目录**
   - 确保数据目录名为 `data`
   - 检查CSV文件是否存在

3. **GPU不可用**
   - 系统会自动使用CPU训练
   - 安装CUDA和cuDNN可启用GPU

4. **内存不足**
   - 减少数据量
   - 关闭其他程序

## 📞 技术支持

如果遇到问题：
1. 检查错误信息
2. 确认数据格式正确
3. 验证依赖包安装
4. 查看生成的日志文件

## 🎉 更新日志

### v2.0 (2024-12-07)
- ✅ 重新设计统一架构
- ✅ 集成XGBoost、CNN-LSTM、BP算法
- ✅ 简化操作流程
- ✅ 优化性能和稳定性
- ✅ 统一文件结构

### v1.0
- 基础振动信号分析功能
- 传统机器学习算法
- 特征提取和预处理

---

**开发团队**: AI Assistant  
**版本**: 2.0  
**更新时间**: 2024-12-07
