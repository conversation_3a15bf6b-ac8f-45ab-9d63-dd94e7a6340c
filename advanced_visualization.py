#!/usr/bin/env python3
"""
高级可视化模块
为振动信号分析系统提供全面的模型性能可视化功能
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, roc_curve, auc, precision_recall_curve
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.preprocessing import label_binarize
from scipy import stats
import os
import warnings
warnings.filterwarnings('ignore')

class AdvancedVisualization:
    """高级可视化器"""
    
    def __init__(self):
        """初始化可视化器"""
        self.setup_style()
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72', 
            'accent': '#F18F01',
            'success': '#C73E1D',
            'info': '#7209B7',
            'warning': '#F77F00',
            'train': '#2E86AB',
            'test': '#A23B72',
            'target': '#C73E1D'
        }
        
    def setup_style(self):
        """设置专业的图表样式"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置专业样式
        plt.style.use('seaborn-v0_8-whitegrid')
        plt.rcParams.update({
            'figure.figsize': (12, 8),
            'figure.dpi': 300,
            'savefig.dpi': 300,
            'savefig.bbox': 'tight',
            'font.size': 12,
            'axes.titlesize': 16,
            'axes.labelsize': 14,
            'xtick.labelsize': 12,
            'ytick.labelsize': 12,
            'legend.fontsize': 12,
            'lines.linewidth': 2,
            'grid.alpha': 0.3
        })
    
    def create_prediction_scatter(self, y_true, y_pred, y_train_true=None, y_train_pred=None, 
                                task_name='prediction', model_name='Model', save_path=None):
        """创建预测值vs实际值散点图"""
        try:
            fig, ax = plt.subplots(figsize=(10, 8))
            
            # 计算性能指标
            r2 = r2_score(y_true, y_pred)
            rmse = np.sqrt(mean_squared_error(y_true, y_pred))
            mae = mean_absolute_error(y_true, y_pred)
            
            # 绘制测试集散点图
            ax.scatter(y_true, y_pred, alpha=0.6, color=self.colors['test'], 
                      s=50, label=f'测试集 (Test Set)', edgecolors='white', linewidth=0.5)
            
            # 如果有训练集数据，也绘制
            if y_train_true is not None and y_train_pred is not None:
                r2_train = r2_score(y_train_true, y_train_pred)
                ax.scatter(y_train_true, y_train_pred, alpha=0.4, color=self.colors['train'], 
                          s=30, label=f'训练集 (Train Set, R²={r2_train:.3f})', 
                          edgecolors='white', linewidth=0.5)
            
            # 计算范围
            all_values = np.concatenate([y_true, y_pred])
            if y_train_true is not None:
                all_values = np.concatenate([all_values, y_train_true, y_train_pred])
            
            min_val, max_val = np.min(all_values), np.max(all_values)
            range_val = max_val - min_val
            plot_min = min_val - 0.1 * range_val
            plot_max = max_val + 0.1 * range_val
            
            # 理想拟合线 y=x
            ax.plot([plot_min, plot_max], [plot_min, plot_max], 
                   'k--', alpha=0.8, linewidth=2, label='理想拟合线 (y=x)')
            
            # 实际拟合线
            z = np.polyfit(y_true, y_pred, 1)
            p = np.poly1d(z)
            ax.plot([plot_min, plot_max], p([plot_min, plot_max]), 
                   color=self.colors['accent'], linewidth=2, 
                   label=f'实际拟合线 (y={z[0]:.3f}x+{z[1]:.3f})')
            
            # 设置坐标轴
            ax.set_xlim(plot_min, plot_max)
            ax.set_ylim(plot_min, plot_max)
            ax.set_xlabel('实际值 (Actual Values)', fontsize=14, fontweight='bold')
            ax.set_ylabel('预测值 (Predicted Values)', fontsize=14, fontweight='bold')
            
            # 标题和性能指标
            title = f'{task_name} - {model_name} 预测效果\nPrediction Performance'
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            
            # 添加性能指标文本框
            metrics_text = f'R² = {r2:.4f}\nRMSE = {rmse:.4f}\nMAE = {mae:.4f}'
            ax.text(0.05, 0.95, metrics_text, transform=ax.transAxes, 
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                   verticalalignment='top', fontsize=12, fontweight='bold')
            
            # 图例
            ax.legend(loc='lower right', frameon=True, fancybox=True, shadow=True)
            
            # 网格
            ax.grid(True, alpha=0.3)
            ax.set_aspect('equal', adjustable='box')
            
            plt.tight_layout()
            
            # 保存图片
            if save_path:
                save_dir = os.path.dirname(save_path)
                if save_dir:  # 只有当目录不为空时才创建
                    os.makedirs(save_dir, exist_ok=True)
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 预测散点图已保存: {save_path}")
            
            plt.close()
            
        except Exception as e:
            print(f"❌ 创建预测散点图失败: {str(e)}")
    
    def create_performance_comparison(self, results_dict, task_type='regression', 
                                    target_score=0.75, save_path=None):
        """创建模型性能对比图"""
        try:
            # 提取数据
            models = []
            scores = []
            training_times = []
            
            metric_key = 'r2_score' if task_type == 'regression' else 'accuracy'
            
            for model_name, metrics in results_dict.items():
                if 'error' not in metrics and metric_key in metrics:
                    models.append(model_name)
                    scores.append(metrics[metric_key])
                    training_times.append(metrics.get('training_time', 0))
            
            if not models:
                print("⚠️  没有有效的性能数据")
                return
            
            # 创建子图
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
            
            # 性能对比柱状图
            bars1 = ax1.bar(models, scores, color=self.colors['primary'], alpha=0.7, 
                           edgecolor='white', linewidth=1)
            
            # 添加目标线
            ax1.axhline(y=target_score, color=self.colors['target'], linestyle='--', 
                       linewidth=2, alpha=0.8, label=f'目标线 (Target: {target_score})')
            
            # 添加数值标签
            for bar, score in zip(bars1, scores):
                height = bar.get_height()
                color = 'green' if score >= target_score else 'red'
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{score:.3f}', ha='center', va='bottom', 
                        fontweight='bold', color=color)
            
            ax1.set_ylabel('性能分数 (Performance Score)', fontsize=14, fontweight='bold')
            ax1.set_title('模型性能对比\nModel Performance Comparison', 
                         fontsize=16, fontweight='bold')
            ax1.set_xticklabels(models, rotation=45, ha='right')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 训练时间对比图
            bars2 = ax2.bar(models, training_times, color=self.colors['secondary'], alpha=0.7,
                           edgecolor='white', linewidth=1)
            
            # 添加数值标签
            for bar, time_val in zip(bars2, training_times):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + max(training_times)*0.01,
                        f'{time_val:.2f}s', ha='center', va='bottom', fontweight='bold')
            
            ax2.set_ylabel('训练时间 (Training Time, seconds)', fontsize=14, fontweight='bold')
            ax2.set_title('训练时间对比\nTraining Time Comparison', 
                         fontsize=16, fontweight='bold')
            ax2.set_xticklabels(models, rotation=45, ha='right')
            ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图片
            if save_path:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 性能对比图已保存: {save_path}")
            
            plt.close()
            
        except Exception as e:
            print(f"❌ 创建性能对比图失败: {str(e)}")
    
    def create_error_analysis(self, y_true, y_pred, model_name='Model', save_path=None):
        """创建误差分析图"""
        try:
            # 计算残差
            residuals = y_true - y_pred
            
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            
            # 1. 残差vs预测值散点图
            ax1.scatter(y_pred, residuals, alpha=0.6, color=self.colors['primary'], s=50)
            ax1.axhline(y=0, color='red', linestyle='--', linewidth=2)
            ax1.set_xlabel('预测值 (Predicted Values)', fontweight='bold')
            ax1.set_ylabel('残差 (Residuals)', fontweight='bold')
            ax1.set_title('残差图 (Residual Plot)', fontweight='bold')
            ax1.grid(True, alpha=0.3)
            
            # 2. 残差分布直方图
            ax2.hist(residuals, bins=30, alpha=0.7, color=self.colors['secondary'], 
                    edgecolor='white', density=True)
            
            # 添加正态分布拟合
            mu, sigma = stats.norm.fit(residuals)
            x = np.linspace(residuals.min(), residuals.max(), 100)
            ax2.plot(x, stats.norm.pdf(x, mu, sigma), 'r-', linewidth=2, 
                    label=f'正态拟合 (μ={mu:.3f}, σ={sigma:.3f})')
            
            ax2.set_xlabel('残差 (Residuals)', fontweight='bold')
            ax2.set_ylabel('密度 (Density)', fontweight='bold')
            ax2.set_title('残差分布 (Residual Distribution)', fontweight='bold')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 3. Q-Q图
            stats.probplot(residuals, dist="norm", plot=ax3)
            ax3.set_title('Q-Q图 (Q-Q Plot)', fontweight='bold')
            ax3.grid(True, alpha=0.3)
            
            # 4. 误差统计
            mae = mean_absolute_error(y_true, y_pred)
            rmse = np.sqrt(mean_squared_error(y_true, y_pred))
            r2 = r2_score(y_true, y_pred)
            
            stats_text = f"""误差统计 (Error Statistics):
MAE: {mae:.4f}
RMSE: {rmse:.4f}
R²: {r2:.4f}
残差均值: {np.mean(residuals):.4f}
残差标准差: {np.std(residuals):.4f}
Shapiro-Wilk p值: {stats.shapiro(residuals)[1]:.4f}"""
            
            ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, 
                    fontsize=12, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
            ax4.set_xlim(0, 1)
            ax4.set_ylim(0, 1)
            ax4.axis('off')
            
            plt.suptitle(f'{model_name} - 误差分析\nError Analysis', 
                        fontsize=18, fontweight='bold')
            plt.tight_layout()
            
            # 保存图片
            if save_path:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 误差分析图已保存: {save_path}")
            
            plt.close()
            
        except Exception as e:
            print(f"❌ 创建误差分析图失败: {str(e)}")
    
    def create_confusion_matrix(self, y_true, y_pred, class_names=None, 
                              model_name='Model', save_path=None):
        """创建混淆矩阵热力图"""
        try:
            # 计算混淆矩阵
            cm = confusion_matrix(y_true, y_pred)
            
            # 归一化混淆矩阵
            cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
            
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
            
            # 原始混淆矩阵
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax1,
                       xticklabels=class_names, yticklabels=class_names,
                       cbar_kws={'label': '样本数量 (Count)'})
            ax1.set_title('混淆矩阵 (Confusion Matrix)', fontweight='bold')
            ax1.set_xlabel('预测类别 (Predicted Class)', fontweight='bold')
            ax1.set_ylabel('真实类别 (True Class)', fontweight='bold')
            
            # 归一化混淆矩阵
            sns.heatmap(cm_normalized, annot=True, fmt='.3f', cmap='Blues', ax=ax2,
                       xticklabels=class_names, yticklabels=class_names,
                       cbar_kws={'label': '比例 (Proportion)'})
            ax2.set_title('归一化混淆矩阵 (Normalized Confusion Matrix)', fontweight='bold')
            ax2.set_xlabel('预测类别 (Predicted Class)', fontweight='bold')
            ax2.set_ylabel('真实类别 (True Class)', fontweight='bold')
            
            plt.suptitle(f'{model_name} - 分类结果分析\nClassification Analysis', 
                        fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            # 保存图片
            if save_path:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 混淆矩阵已保存: {save_path}")
            
            plt.close()
            
        except Exception as e:
            print(f"❌ 创建混淆矩阵失败: {str(e)}")
    
    def create_roc_curves(self, y_true, y_pred_proba, class_names=None, 
                         model_name='Model', save_path=None):
        """创建ROC曲线"""
        try:
            n_classes = len(np.unique(y_true))
            
            if n_classes == 2:
                # 二分类ROC
                fpr, tpr, _ = roc_curve(y_true, y_pred_proba[:, 1])
                roc_auc = auc(fpr, tpr)
                
                plt.figure(figsize=(8, 8))
                plt.plot(fpr, tpr, color=self.colors['primary'], linewidth=2,
                        label=f'ROC曲线 (AUC = {roc_auc:.3f})')
                plt.plot([0, 1], [0, 1], 'k--', linewidth=2, label='随机分类器')
                
                plt.xlim([0.0, 1.0])
                plt.ylim([0.0, 1.05])
                plt.xlabel('假正率 (False Positive Rate)', fontweight='bold')
                plt.ylabel('真正率 (True Positive Rate)', fontweight='bold')
                plt.title(f'{model_name} - ROC曲线\nROC Curve', fontweight='bold')
                plt.legend(loc="lower right")
                plt.grid(True, alpha=0.3)
                
            else:
                # 多分类ROC
                y_true_bin = label_binarize(y_true, classes=range(n_classes))
                
                plt.figure(figsize=(10, 8))
                
                # 计算每个类别的ROC
                for i in range(n_classes):
                    fpr, tpr, _ = roc_curve(y_true_bin[:, i], y_pred_proba[:, i])
                    roc_auc = auc(fpr, tpr)
                    class_name = class_names[i] if class_names else f'类别 {i}'
                    plt.plot(fpr, tpr, linewidth=2,
                            label=f'{class_name} (AUC = {roc_auc:.3f})')
                
                # 计算macro平均ROC
                all_fpr = np.unique(np.concatenate([roc_curve(y_true_bin[:, i], y_pred_proba[:, i])[0] 
                                                   for i in range(n_classes)]))
                mean_tpr = np.zeros_like(all_fpr)
                for i in range(n_classes):
                    fpr, tpr, _ = roc_curve(y_true_bin[:, i], y_pred_proba[:, i])
                    mean_tpr += np.interp(all_fpr, fpr, tpr)
                mean_tpr /= n_classes
                
                mean_auc = auc(all_fpr, mean_tpr)
                plt.plot(all_fpr, mean_tpr, 'k--', linewidth=2,
                        label=f'Macro平均 (AUC = {mean_auc:.3f})')
                
                plt.plot([0, 1], [0, 1], 'k:', linewidth=2, label='随机分类器')
                
                plt.xlim([0.0, 1.0])
                plt.ylim([0.0, 1.05])
                plt.xlabel('假正率 (False Positive Rate)', fontweight='bold')
                plt.ylabel('真正率 (True Positive Rate)', fontweight='bold')
                plt.title(f'{model_name} - 多分类ROC曲线\nMulti-class ROC Curves', fontweight='bold')
                plt.legend(loc="lower right")
                plt.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图片
            if save_path:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ ROC曲线已保存: {save_path}")
            
            plt.close()
            
        except Exception as e:
            print(f"❌ 创建ROC曲线失败: {str(e)}")
    
    def create_feature_importance(self, importance_scores, feature_names=None, 
                                model_name='Model', top_n=20, save_path=None):
        """创建特征重要性图"""
        try:
            if len(importance_scores) == 0:
                print("⚠️  没有特征重要性数据")
                return
            
            # 选择前N个最重要的特征
            if feature_names is None:
                feature_names = [f'特征_{i}' for i in range(len(importance_scores))]
            
            # 排序
            indices = np.argsort(importance_scores)[-top_n:]
            top_scores = importance_scores[indices]
            top_names = [feature_names[i] for i in indices]
            
            plt.figure(figsize=(12, 8))
            bars = plt.barh(range(len(top_scores)), top_scores, 
                           color=self.colors['primary'], alpha=0.7)
            
            # 添加数值标签
            for i, (bar, score) in enumerate(zip(bars, top_scores)):
                plt.text(score + max(top_scores)*0.01, bar.get_y() + bar.get_height()/2,
                        f'{score:.4f}', va='center', fontweight='bold')
            
            plt.yticks(range(len(top_scores)), top_names)
            plt.xlabel('重要性分数 (Importance Score)', fontweight='bold')
            plt.ylabel('特征 (Features)', fontweight='bold')
            plt.title(f'{model_name} - 特征重要性排序\nFeature Importance Ranking', 
                     fontweight='bold')
            plt.grid(True, alpha=0.3, axis='x')
            
            plt.tight_layout()
            
            # 保存图片
            if save_path:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 特征重要性图已保存: {save_path}")
            
            plt.close()
            
        except Exception as e:
            print(f"❌ 创建特征重要性图失败: {str(e)}")

    def create_training_history(self, history_dict, model_name='Model', save_path=None):
        """创建训练历史图"""
        try:
            if not history_dict or 'loss' not in history_dict:
                print("⚠️  没有训练历史数据")
                return

            epochs = range(1, len(history_dict['loss']) + 1)

            fig, axes = plt.subplots(1, 2, figsize=(16, 6))

            # 损失曲线
            axes[0].plot(epochs, history_dict['loss'], 'b-', linewidth=2,
                        label='训练损失 (Training Loss)')
            if 'val_loss' in history_dict:
                axes[0].plot(epochs, history_dict['val_loss'], 'r-', linewidth=2,
                           label='验证损失 (Validation Loss)')

            axes[0].set_xlabel('训练轮次 (Epochs)', fontweight='bold')
            axes[0].set_ylabel('损失 (Loss)', fontweight='bold')
            axes[0].set_title('训练损失曲线\nTraining Loss Curve', fontweight='bold')
            axes[0].legend()
            axes[0].grid(True, alpha=0.3)

            # 指标曲线
            metric_key = None
            for key in ['mae', 'accuracy', 'mse']:
                if key in history_dict:
                    metric_key = key
                    break

            if metric_key:
                axes[1].plot(epochs, history_dict[metric_key], 'b-', linewidth=2,
                           label=f'训练{metric_key.upper()} (Training {metric_key.upper()})')
                if f'val_{metric_key}' in history_dict:
                    axes[1].plot(epochs, history_dict[f'val_{metric_key}'], 'r-', linewidth=2,
                               label=f'验证{metric_key.upper()} (Validation {metric_key.upper()})')

                axes[1].set_xlabel('训练轮次 (Epochs)', fontweight='bold')
                axes[1].set_ylabel(f'{metric_key.upper()}', fontweight='bold')
                axes[1].set_title(f'{metric_key.upper()}曲线\n{metric_key.upper()} Curve', fontweight='bold')
                axes[1].legend()
                axes[1].grid(True, alpha=0.3)
            else:
                axes[1].text(0.5, 0.5, '无可用指标数据\nNo metric data available',
                           transform=axes[1].transAxes, ha='center', va='center',
                           fontsize=14, bbox=dict(boxstyle='round', facecolor='lightgray'))
                axes[1].set_title('指标曲线\nMetric Curve', fontweight='bold')

            plt.suptitle(f'{model_name} - 训练历史\nTraining History',
                        fontsize=16, fontweight='bold')
            plt.tight_layout()

            # 保存图片
            if save_path:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 训练历史图已保存: {save_path}")

            plt.close()

        except Exception as e:
            print(f"❌ 创建训练历史图失败: {str(e)}")

    def create_performance_heatmap(self, results_matrix, model_names, task_names, save_path=None):
        """创建性能热力图矩阵"""
        try:
            # 修复matplotlib布局引擎兼容性问题
            plt.rcParams['figure.constrained_layout.use'] = False

            fig, ax = plt.subplots(figsize=(12, 8))

            # 创建热力图，避免colorbar布局冲突
            sns.heatmap(results_matrix, annot=True, fmt='.3f', cmap='RdYlGn',
                       xticklabels=task_names, yticklabels=model_names,
                       cbar_kws={'label': '性能分数 (Performance Score)', 'shrink': 0.8},
                       linewidths=0.5, linecolor='white', ax=ax)

            ax.set_title('模型性能矩阵\nModel Performance Matrix',
                        fontsize=16, fontweight='bold', pad=20)
            ax.set_xlabel('预测任务 (Prediction Tasks)', fontweight='bold')
            ax.set_ylabel('机器学习模型 (ML Models)', fontweight='bold')

            # 旋转x轴标签
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
            plt.setp(ax.get_yticklabels(), rotation=0)

            plt.tight_layout()

            # 保存图片
            if save_path:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 性能热力图已保存: {save_path}")

            plt.close(fig)

        except Exception as e:
            print(f"❌ 创建性能热力图失败: {str(e)}")

    def create_correlation_heatmap(self, correlation_matrix, feature_names=None, save_path=None):
        """创建特征相关性热力图"""
        try:
            if feature_names is None:
                feature_names = [f'特征_{i}' for i in range(correlation_matrix.shape[0])]

            # 修复matplotlib布局引擎兼容性问题
            plt.rcParams['figure.constrained_layout.use'] = False

            fig, ax = plt.subplots(figsize=(14, 12))

            # 创建遮罩（只显示下三角）
            mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))

            # 创建热力图，避免colorbar布局冲突
            sns.heatmap(correlation_matrix, mask=mask, annot=False, cmap='coolwarm',
                       center=0, square=True, linewidths=0.5,
                       xticklabels=feature_names, yticklabels=feature_names,
                       cbar_kws={'label': '相关系数 (Correlation Coefficient)', 'shrink': 0.8},
                       ax=ax)

            ax.set_title('特征相关性矩阵\nFeature Correlation Matrix',
                        fontsize=16, fontweight='bold', pad=20)
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
            plt.setp(ax.get_yticklabels(), rotation=0)

            plt.tight_layout()

            # 保存图片
            if save_path:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 特征相关性热力图已保存: {save_path}")

            plt.close(fig)

        except Exception as e:
            print(f"❌ 创建特征相关性热力图失败: {str(e)}")

    def create_precision_recall_curve(self, y_true, y_pred_proba, class_names=None,
                                    model_name='Model', save_path=None):
        """创建精确率-召回率曲线"""
        try:
            n_classes = len(np.unique(y_true))

            if n_classes == 2:
                # 二分类PR曲线
                precision, recall, _ = precision_recall_curve(y_true, y_pred_proba[:, 1])
                pr_auc = auc(recall, precision)

                plt.figure(figsize=(8, 8))
                plt.plot(recall, precision, color=self.colors['primary'], linewidth=2,
                        label=f'PR曲线 (AUC = {pr_auc:.3f})')

                # 基线（随机分类器）
                baseline = np.sum(y_true) / len(y_true)
                plt.axhline(y=baseline, color='red', linestyle='--', linewidth=2,
                           label=f'基线 (Baseline = {baseline:.3f})')

                plt.xlim([0.0, 1.0])
                plt.ylim([0.0, 1.05])
                plt.xlabel('召回率 (Recall)', fontweight='bold')
                plt.ylabel('精确率 (Precision)', fontweight='bold')
                plt.title(f'{model_name} - 精确率-召回率曲线\nPrecision-Recall Curve', fontweight='bold')
                plt.legend(loc="lower left")
                plt.grid(True, alpha=0.3)

            else:
                # 多分类PR曲线
                y_true_bin = label_binarize(y_true, classes=range(n_classes))

                plt.figure(figsize=(10, 8))

                # 计算每个类别的PR曲线
                for i in range(n_classes):
                    precision, recall, _ = precision_recall_curve(y_true_bin[:, i], y_pred_proba[:, i])
                    pr_auc = auc(recall, precision)
                    class_name = class_names[i] if class_names else f'类别 {i}'
                    plt.plot(recall, precision, linewidth=2,
                            label=f'{class_name} (AUC = {pr_auc:.3f})')

                plt.xlim([0.0, 1.0])
                plt.ylim([0.0, 1.05])
                plt.xlabel('召回率 (Recall)', fontweight='bold')
                plt.ylabel('精确率 (Precision)', fontweight='bold')
                plt.title(f'{model_name} - 多分类精确率-召回率曲线\nMulti-class Precision-Recall Curves',
                         fontweight='bold')
                plt.legend(loc="lower left")
                plt.grid(True, alpha=0.3)

            plt.tight_layout()

            # 保存图片
            if save_path:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 精确率-召回率曲线已保存: {save_path}")

            plt.close()

        except Exception as e:
            print(f"❌ 创建精确率-召回率曲线失败: {str(e)}")

    def create_optimization_history(self, study_data, model_name='Model', save_path=None):
        """创建超参数优化历史图"""
        try:
            if not study_data or 'trials' not in study_data:
                print("⚠️  没有优化历史数据")
                return

            trials = study_data['trials']
            if len(trials) == 0:
                return

            # 提取试验数据
            trial_numbers = list(range(1, len(trials) + 1))
            values = [trial.get('value', 0) for trial in trials]

            # 计算最佳值历史
            best_values = []
            current_best = float('-inf')
            for value in values:
                if value > current_best:
                    current_best = value
                best_values.append(current_best)

            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

            # 优化历史
            ax1.plot(trial_numbers, values, 'o-', alpha=0.6, color=self.colors['primary'],
                    label='试验值 (Trial Values)')
            ax1.plot(trial_numbers, best_values, 'r-', linewidth=2,
                    label='最佳值 (Best Value)')

            ax1.set_xlabel('试验次数 (Trial Number)', fontweight='bold')
            ax1.set_ylabel('目标值 (Objective Value)', fontweight='bold')
            ax1.set_title('超参数优化历史\nHyperparameter Optimization History', fontweight='bold')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 参数重要性（如果有的话）
            if 'param_importances' in study_data:
                importances = study_data['param_importances']
                param_names = list(importances.keys())
                importance_values = list(importances.values())

                bars = ax2.barh(param_names, importance_values, color=self.colors['secondary'], alpha=0.7)
                ax2.set_xlabel('重要性 (Importance)', fontweight='bold')
                ax2.set_ylabel('参数 (Parameters)', fontweight='bold')
                ax2.set_title('参数重要性\nParameter Importance', fontweight='bold')
                ax2.grid(True, alpha=0.3, axis='x')

                # 添加数值标签
                for bar, value in zip(bars, importance_values):
                    ax2.text(value + max(importance_values)*0.01, bar.get_y() + bar.get_height()/2,
                            f'{value:.3f}', va='center', fontweight='bold')
            else:
                ax2.text(0.5, 0.5, '无参数重要性数据\nNo parameter importance data',
                        transform=ax2.transAxes, ha='center', va='center',
                        fontsize=14, bbox=dict(boxstyle='round', facecolor='lightgray'))
                ax2.set_title('参数重要性\nParameter Importance', fontweight='bold')

            plt.suptitle(f'{model_name} - 超参数优化分析\nHyperparameter Optimization Analysis',
                        fontsize=16, fontweight='bold')
            plt.tight_layout()

            # 保存图片
            if save_path:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 优化历史图已保存: {save_path}")

            plt.close()

        except Exception as e:
            print(f"❌ 创建优化历史图失败: {str(e)}")

    def create_error_boxplot(self, error_data_dict, save_path=None):
        """创建误差箱线图对比"""
        try:
            models = list(error_data_dict.keys())
            errors = list(error_data_dict.values())

            plt.figure(figsize=(12, 8))

            # 创建箱线图
            box_plot = plt.boxplot(errors, labels=models, patch_artist=True,
                                  boxprops=dict(facecolor=self.colors['primary'], alpha=0.7),
                                  medianprops=dict(color='red', linewidth=2),
                                  whiskerprops=dict(color='black', linewidth=1.5),
                                  capprops=dict(color='black', linewidth=1.5))

            # 设置不同颜色
            colors = [self.colors['primary'], self.colors['secondary'], self.colors['accent'],
                     self.colors['success'], self.colors['info'], self.colors['warning']]
            for patch, color in zip(box_plot['boxes'], colors[:len(models)]):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)

            plt.xlabel('模型 (Models)', fontweight='bold')
            plt.ylabel('预测误差 (Prediction Error)', fontweight='bold')
            plt.title('模型误差分布对比\nModel Error Distribution Comparison',
                     fontweight='bold', pad=20)
            plt.xticks(rotation=45, ha='right')
            plt.grid(True, alpha=0.3, axis='y')

            # 添加统计信息
            stats_text = []
            for i, (model, error_list) in enumerate(error_data_dict.items()):
                median_error = np.median(error_list)
                mean_error = np.mean(error_list)
                std_error = np.std(error_list)
                stats_text.append(f'{model}: 中位数={median_error:.3f}, 均值={mean_error:.3f}, 标准差={std_error:.3f}')

            plt.figtext(0.02, 0.02, '\n'.join(stats_text), fontsize=10,
                       bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

            plt.tight_layout()

            # 保存图片
            if save_path:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 误差箱线图已保存: {save_path}")

            plt.close()

        except Exception as e:
            print(f"❌ 创建误差箱线图失败: {str(e)}")

def main():
    """测试函数"""
    print("🧪 测试高级可视化模块...")
    
    # 创建测试数据
    np.random.seed(42)
    n_samples = 100
    
    # 回归测试数据
    y_true_reg = np.random.randn(n_samples)
    y_pred_reg = y_true_reg + 0.3 * np.random.randn(n_samples)
    
    # 分类测试数据
    y_true_cls = np.random.randint(0, 3, n_samples)
    y_pred_cls = y_true_cls.copy()
    # 添加一些错误预测
    error_indices = np.random.choice(n_samples, size=20, replace=False)
    y_pred_cls[error_indices] = np.random.randint(0, 3, 20)
    
    # 初始化可视化器
    viz = AdvancedVisualization()
    
    # 测试预测散点图
    viz.create_prediction_scatter(
        y_true_reg, y_pred_reg, 
        task_name='速度预测', model_name='Random Forest',
        save_path='test_prediction_scatter.png'
    )
    
    # 测试误差分析
    viz.create_error_analysis(
        y_true_reg, y_pred_reg, 
        model_name='Random Forest',
        save_path='test_error_analysis.png'
    )
    
    # 测试混淆矩阵
    viz.create_confusion_matrix(
        y_true_cls, y_pred_cls,
        class_names=['双轴', '三轴', '四轴'],
        model_name='XGBoost',
        save_path='test_confusion_matrix.png'
    )
    
    # 测试特征重要性
    importance = np.random.rand(20)
    feature_names = [f'特征_{i}' for i in range(20)]
    viz.create_feature_importance(
        importance, feature_names,
        model_name='Random Forest',
        save_path='test_feature_importance.png'
    )
    
    print("✅ 测试完成!")

if __name__ == "__main__":
    main()
