#!/usr/bin/env python3
"""
测试enhanced_datasets变量修复效果
验证unified_vibration_analysis.py中的变量引用问题是否已解决
"""

import numpy as np
import pandas as pd
import os
import tempfile
import shutil

def create_test_data():
    """创建测试数据"""
    print("📊 创建测试数据...")
    
    # 创建临时数据目录
    test_data_dir = 'test_data_temp'
    os.makedirs(test_data_dir, exist_ok=True)
    
    # 生成模拟的传感器数据
    np.random.seed(42)
    n_samples = 500
    
    # 创建20个传感器的数据
    sensor_data = {}
    for i in range(1, 21):
        sensor_name = f'sensor_{i:02d}'
        # 生成振动信号
        signal = np.random.normal(0, 1.0, n_samples)
        # 添加车辆通过事件
        for j in range(0, n_samples, 100):
            if j + 20 < n_samples:
                signal[j:j+20] += np.random.normal(3.0, 0.5, 20)
        sensor_data[sensor_name] = signal
    
    # 添加目标变量
    sensor_data['speed_kmh'] = np.random.uniform(20, 80, n_samples)
    sensor_data['axle_weight_ton'] = np.random.uniform(5, 40, n_samples)
    sensor_data['axle_type'] = np.random.choice([2, 3, 4], n_samples)
    
    # 保存为CSV文件
    df = pd.DataFrame(sensor_data)
    csv_path = os.path.join(test_data_dir, 'test_vibration_data.csv')
    df.to_csv(csv_path, index=False)
    
    print(f"   ✅ 测试数据已创建: {df.shape}")
    print(f"   📁 保存路径: {csv_path}")
    
    return test_data_dir

def test_unified_system_initialization():
    """测试统一系统初始化"""
    print("\n🔧 测试统一系统初始化...")
    
    try:
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 创建测试数据
        test_data_dir = create_test_data()
        
        # 初始化系统
        system = UnifiedVibrationAnalysisSystem(data_dir=test_data_dir)
        
        print("   ✅ 系统初始化成功")
        
        # 清理测试数据
        if os.path.exists(test_data_dir):
            shutil.rmtree(test_data_dir)
        
        return system
        
    except Exception as e:
        print(f"   ❌ 系统初始化失败: {str(e)}")
        return None

def test_data_loading():
    """测试数据加载功能"""
    print("\n📊 测试数据加载功能...")
    
    try:
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 创建测试数据
        test_data_dir = create_test_data()
        
        # 初始化系统
        system = UnifiedVibrationAnalysisSystem(data_dir=test_data_dir)
        
        # 测试数据加载
        datasets = system.load_and_process_data()
        
        if datasets:
            print(f"   ✅ 数据加载成功")
            print(f"   📊 数据集数量: {len(datasets)}")
            for name, dataset in datasets.items():
                print(f"      - {name}: {dataset['X'].shape}")
        else:
            print("   ⚠️  数据加载返回空结果")
        
        # 清理测试数据
        if os.path.exists(test_data_dir):
            shutil.rmtree(test_data_dir)
        
        return datasets
        
    except Exception as e:
        print(f"   ❌ 数据加载失败: {str(e)}")
        return None

def test_train_models_method():
    """测试train_models方法（重点测试enhanced_datasets变量问题）"""
    print("\n🤖 测试train_models方法...")
    
    try:
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 创建测试数据
        test_data_dir = create_test_data()
        
        # 初始化系统
        system = UnifiedVibrationAnalysisSystem(data_dir=test_data_dir)
        
        # 禁用一些可能导致问题的功能以专注测试核心问题
        system.process_visualization_enabled = False
        system.sensor_optimization_enabled = False
        
        # 加载数据
        datasets = system.load_and_process_data()
        
        if not datasets:
            print("   ❌ 无法加载测试数据")
            return False
        
        print(f"   📊 开始测试train_models方法...")
        print(f"   🎯 重点验证: enhanced_datasets变量引用问题是否已修复")
        
        # 调用train_models方法
        results = system.train_models(datasets)
        
        if results:
            print(f"   ✅ train_models方法执行成功!")
            print(f"   📈 训练结果: {len(results)} 个数据集")
            
            # 显示结果概要
            for dataset_name, dataset_results in results.items():
                successful_models = len([m for m in dataset_results.values() if 'error' not in m])
                total_models = len(dataset_results)
                print(f"      - {dataset_name}: {successful_models}/{total_models} 个模型成功")
        else:
            print(f"   ⚠️  train_models方法返回空结果")
        
        # 清理测试数据
        if os.path.exists(test_data_dir):
            shutil.rmtree(test_data_dir)
        
        return True
        
    except NameError as e:
        if 'enhanced_datasets' in str(e):
            print(f"   ❌ enhanced_datasets变量问题仍然存在: {str(e)}")
            return False
        else:
            print(f"   ❌ 其他NameError: {str(e)}")
            return False
    except Exception as e:
        print(f"   ❌ train_models方法测试失败: {str(e)}")
        return False

def test_complete_system_run():
    """测试完整系统运行"""
    print("\n🚀 测试完整系统运行...")
    
    try:
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 创建测试数据
        test_data_dir = create_test_data()
        
        # 初始化系统（禁用可能导致问题的功能）
        system = UnifiedVibrationAnalysisSystem(data_dir=test_data_dir)
        system.process_visualization_enabled = False
        system.sensor_optimization_enabled = False
        
        print(f"   🎯 运行完整分析流程...")
        
        # 运行完整分析
        results = system.run_analysis()
        
        if results:
            print(f"   ✅ 完整系统运行成功!")
            print(f"   📊 分析结果: {len(results)} 个数据集")
        else:
            print(f"   ⚠️  完整系统运行返回空结果")
        
        # 清理测试数据
        if os.path.exists(test_data_dir):
            shutil.rmtree(test_data_dir)
        
        return True
        
    except NameError as e:
        if 'enhanced_datasets' in str(e):
            print(f"   ❌ enhanced_datasets变量问题仍然存在: {str(e)}")
            return False
        else:
            print(f"   ❌ 其他NameError: {str(e)}")
            return False
    except Exception as e:
        print(f"   ❌ 完整系统运行失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔧 enhanced_datasets变量修复验证测试")
    print("=" * 80)
    print("🎯 目标: 验证 'name 'enhanced_datasets' is not defined' 错误已修复")
    print("🔍 测试范围: 系统初始化、数据加载、模型训练、完整运行")
    print("=" * 80)
    
    test_results = []
    
    # 1. 测试系统初始化
    system = test_unified_system_initialization()
    test_results.append(system is not None)
    
    # 2. 测试数据加载
    datasets = test_data_loading()
    test_results.append(datasets is not None)
    
    # 3. 测试train_models方法（核心测试）
    train_models_success = test_train_models_method()
    test_results.append(train_models_success)
    
    # 4. 测试完整系统运行
    complete_run_success = test_complete_system_run()
    test_results.append(complete_run_success)
    
    # 总结测试结果
    print("\n" + "=" * 80)
    print("🎉 修复验证结果总结")
    print("=" * 80)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"📊 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    test_names = [
        "系统初始化",
        "数据加载", 
        "train_models方法",
        "完整系统运行"
    ]
    
    for i, (test_name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {i+1}. {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n✅ 所有测试通过!")
        print("🎯 enhanced_datasets变量问题已成功修复!")
        print("💡 原错误 'name 'enhanced_datasets' is not defined' 已解决")
        print("\n🚀 建议下一步:")
        print("   运行 python unified_vibration_analysis.py 进行完整测试")
        return True
    else:
        print("\n❌ 部分测试失败")
        print("🔧 需要进一步检查和修复")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
