# Vibration Signal Analysis System - Data Processing Flow Visualization Chart Index
================================================================================

**Generated Time**: 2025-06-08 22:45:24

## Chart Overview

This document contains visualization charts for the complete data processing flow 
of the vibration signal analysis system. All charts follow IEEE/Elsevier journal 
standards with 300 DPI high-resolution output, suitable for academic papers and 
technical reports.

## Chart List

### 1. Raw Vibration Signal Time-Domain Waveforms

**Filename**: `raw_signal_process_diagram_english.png`

**描述**: Shows the original vibration signals collected by road-embedded accelerometer sensors, including time-domain waveform data from multiple sensors with vehicle passing characteristics.

**Data Source**: Real-time data collected from road-embedded accelerometer sensors

**Analysis**: The time-domain waveforms clearly show the characteristic features of vehicle passing events, providing fundamental data for subsequent event detection and feature extraction.

**LaTeX Reference**: `\ref{fig:raw_signal_en}`

---

### 2. Vehicle Passing Event Detection and Data Segmentation

**Filename**: `event_detection_process_diagram_english.png`

**描述**: Demonstrates the automatic detection process of vehicle passing events by locating signal maximum values to identify vehicle passing moments and extracting 1-second effective data segments centered on these moments.

**Data Source**: Fused sensor signal data

**Analysis**: The event detection algorithm accurately identifies vehicle passing moments, and the extracted 1-second data segments contain complete vehicle passing information, providing high-quality data foundation for feature extraction.

**LaTeX Reference**: `\ref{fig:event_detection_en}`

---

### 3. Feature Extraction Results Visualization

**Filename**: `feature_extraction_process_diagram_english.png`

**描述**: Shows time-domain and frequency-domain features extracted from segmented vibration signals, including statistical features, spectral analysis, and power spectral density analysis.

**Data Source**: Extracted 1-second vehicle passing data segments

**Analysis**: The extracted features effectively characterize vehicle weight, axle type, and speed information, providing effective input features for machine learning models.

**LaTeX Reference**: `\ref{fig:feature_extraction_en}`

---

### 4. Data Preprocessing Effects Comparison

**Filename**: `data_preprocessing_process_diagram_english.png`

**描述**: Shows the distribution comparison of feature data before and after standardization, validating the effectiveness and necessity of data preprocessing.

**Data Source**: Raw extracted feature data

**Analysis**: Standardization processing eliminates dimensional differences between different features, enabling machine learning algorithms to better learn relationships between features.

**LaTeX Reference**: `\ref{fig:preprocessing_en}`

---

### 5. Model Training Process and Convergence Analysis

**Filename**: `model_training_process_diagram_english.png`

**描述**: Shows the training process of different machine learning algorithms, including convergence curves for traditional machine learning and deep learning models.

**Data Source**: Preprocessed feature data

**Analysis**: Training curves show good model convergence with stable validation performance, indicating good generalization capability of the models.

**LaTeX Reference**: `\ref{fig:training_process_en}`

---

### 6. Final Prediction Results Comparison Analysis

**Filename**: `prediction_results_process_diagram_english.png`

**描述**: Shows the final performance of different algorithms on speed prediction tasks, evaluating model accuracy through scatter plots of predicted vs. actual values.

**Data Source**: Test set data and model prediction results

**Analysis**: The ensemble model performs best with R² exceeding 0.92, meeting the accuracy requirements for practical applications.

**LaTeX Reference**: `\ref{fig:prediction_results_en}`

---

### 7. Time-Frequency Analysis: Wavelet Transform and STFT

**Filename**: `time_frequency_analysis_process_diagram_english.png`

**描述**: Shows time-frequency characteristics analysis of vibration signals, including STFT, wavelet transform, and instantaneous frequency analysis.

**Data Source**: Vibration signals from vehicle passing events

**Analysis**: Time-frequency analysis reveals the temporal variation patterns of frequency components in vibration signals, providing important information for understanding vehicle-pavement interactions.

**LaTeX Reference**: `\ref{fig:time_frequency_en}`

---

### 8. Multi-Sensor Data Fusion Process

**Filename**: `sensor_fusion_process_diagram_english.png`

**描述**: Shows the fusion process of multiple sensor data and comparison of different fusion strategy effects.

**Data Source**: Synchronized data from multiple road-embedded sensors

**Analysis**: RMS fusion method effectively reduces noise while maintaining signal characteristics, improving system reliability.

**LaTeX Reference**: `\ref{fig:sensor_fusion_en}`

---

### 9. Algorithm Performance Comparison Radar Chart

**Filename**: `algorithm_performance_radar_process_diagram_english.png`

**描述**: Compares the performance of different algorithms from multiple dimensions including accuracy, speed, robustness, interpretability, and memory usage.

**Data Source**: Algorithm performance evaluation results

**Analysis**: The ensemble model excels in accuracy and robustness, while Random Forest has advantages in interpretability, providing reference for algorithm selection in practical applications.

**LaTeX Reference**: `\ref{fig:algorithm_radar_en}`

---

## Technical Specifications

- **Resolution**: 300 DPI
- **Image Format**: PNG
- **Color Mode**: RGB
- **Font**: Times New Roman
- **Chart Size**: Single column 8.5cm, Double column 17.8cm
- **Grid Transparency**: 30%
- **Standards**: IEEE/Elsevier journal format