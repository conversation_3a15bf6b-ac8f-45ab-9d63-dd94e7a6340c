#!/usr/bin/env python3
"""检查优化依赖库"""

def check_dependencies():
    """检查必要的依赖库"""
    dependencies = {}
    
    # 检查Optuna
    try:
        import optuna
        dependencies['optuna'] = optuna.__version__
        print(f"✅ Optuna可用: {optuna.__version__}")
    except ImportError:
        dependencies['optuna'] = None
        print("❌ Optuna不可用")
    
    # 检查scikit-optimize
    try:
        import skopt
        dependencies['scikit-optimize'] = skopt.__version__
        print(f"✅ scikit-optimize可用: {skopt.__version__}")
    except ImportError:
        dependencies['scikit-optimize'] = None
        print("❌ scikit-optimize不可用")
    
    # 检查XGBoost
    try:
        import xgboost
        dependencies['xgboost'] = xgboost.__version__
        print(f"✅ XGBoost可用: {xgboost.__version__}")
    except ImportError:
        dependencies['xgboost'] = None
        print("❌ XGBoost不可用")
    
    # 检查TensorFlow
    try:
        import tensorflow as tf
        dependencies['tensorflow'] = tf.__version__
        print(f"✅ TensorFlow可用: {tf.__version__}")
    except ImportError:
        dependencies['tensorflow'] = None
        print("❌ TensorFlow不可用")
    
    return dependencies

if __name__ == "__main__":
    check_dependencies()
