# 新格式数据预处理系统使用指南

## 📋 概述

本系统为振动信号分析系统新增了完整的数据预处理功能，能够自动识别和处理新命名格式的CSV文件，使其与现有的分析系统完全兼容。

## 🔧 新格式数据特征

### 文件命名规则
```
监测点位_监测时间_AcceData_车道_轴型_载重_速度.csv
```

### 命名示例
```
GW100001_20231101174605_AcceData_车道1_2轴-2.5t-100kmh.csv
GW100002_20231101180000_AcceData_车道1_3轴-25t-60kmh.csv
GW100003_20231101182000_AcceData_车道2_2轴-5t-80kmh.csv
GW100004_20231101184500_AcceData_车道1_4轴-40t-50kmh.csv
```

### 命名组成部分
- **监测点位**: 如GW100001（监测站点标识）
- **监测时间**: YYYYMMDDHHMMSS格式（20231101174605）
- **固定标识**: AcceData（数据类型标识）
- **车道信息**: 如车道1、车道2
- **轴型**: 如2轴、3轴、4轴
- **载重**: 如2.5t、25t、40t
- **速度**: 如100kmh、60km/h

### 数据结构
- **总列数**: 21列
- **第1列**: count列（计数列）
- **第2-21列**: 20个加速度传感器数据（acc01, acc02, ..., acc20）

## 🚀 使用方法

### 1. 自动预处理（推荐）

最简单的使用方式是直接运行主系统：

```bash
python unified_vibration_analysis.py
```

系统将自动：
1. 检测数据格式类型
2. 如果是新格式，自动执行预处理
3. 转换为兼容格式
4. 继续执行完整的分析流程

### 2. 手动预处理

如果需要单独进行数据预处理：

```python
from data_format_adapter import auto_preprocess_data

# 自动预处理数据
compatible_dir = auto_preprocess_data("your_new_format_data_dir")

if compatible_dir:
    print(f"预处理完成，兼容目录: {compatible_dir}")
else:
    print("预处理失败")
```

### 3. 详细预处理控制

如果需要更详细的控制：

```python
from new_data_preprocessor import NewDataPreprocessor

# 初始化预处理器
preprocessor = NewDataPreprocessor(
    input_dir="your_input_dir",
    output_dir="your_output_dir"
)

# 执行预处理
summary = preprocessor.process_all_files()

# 查看结果
print(f"成功处理: {summary['processed_files']}/{summary['total_files']}")
print(f"成功率: {summary['success_rate']:.1f}%")
```

## 📁 输出结构

预处理后的数据将按以下层次结构组织：

```
preprocessed_data/
├── 2.5吨/                          # 按载重分类
│   └── 双轴/                       # 按轴型分类
│       └── 100km_h/                # 按速度分类
│           └── acce_GW100001_20231101174605_lane1.csv
├── 25.0吨/
│   └── 三轴/
│       └── 60km_h/
│           └── acce_GW100002_20231101180000_lane1.csv
├── 5.0吨/
│   └── 双轴/
│       └── 80km_h/
│           └── acce_GW100003_20231101182000_lane2.csv
├── 40.0吨/
│   └── 四轴/
│       └── 50km_h/
│           └── acce_GW100004_20231101184500_lane1.csv
├── data_info.json                  # 数据信息文件
├── preprocessing_summary.json      # 预处理摘要
└── preprocessing_report.md         # 预处理报告
```

## 🔄 数据转换过程

### 1. 文件名解析
- 提取监测点位、时间、车道、轴型、载重、速度信息
- 验证命名格式的正确性

### 2. CSV结构验证
- 检查列数（期望21列）
- 验证第一列为count列
- 确认传感器列格式（acc01-acc20）

### 3. 数据清理和标准化
- **列名标准化**: acc01 → sensor_01, acc02 → sensor_02, ...
- **数据类型转换**: 确保数值列为正确的数据类型
- **缺失值处理**: 使用前向/后向填充和中位数填充
- **异常值处理**: 使用3σ准则检测和处理异常值
- **元数据添加**: 添加speed_kmh, load_tons, axle_type等列

### 4. 输出格式生成
- 按载重/轴型/速度创建目录结构
- 生成标准化的文件名
- 保存处理后的CSV文件

## ✅ 兼容性验证

系统会自动验证输出数据的兼容性：

- ✅ 列结构正确性（count + 20个sensor列 + 元数据列）
- ✅ 数据类型一致性（数值型传感器数据）
- ✅ 元数据完整性（速度、载重、轴型等信息）
- ✅ 文件格式兼容性（与现有系统兼容）

## 📊 处理结果

### 控制台输出示例
```
🔄 检查数据格式兼容性...
📊 检测到数据格式: new_format
   🔄 需要预处理新格式数据
✅ 新格式数据预处理完成
   成功处理: 4/4 个文件
   成功率: 100.0%
✅ 数据预处理完成
   原始目录: your_data_dir
   兼容目录: your_data_dir_preprocessed
```

### 生成的报告文件
- **preprocessing_summary.json**: JSON格式的详细摘要
- **preprocessing_report.md**: Markdown格式的可读报告
- **data_info.json**: 数据信息和兼容性标识

## 🛠️ 故障排除

### 常见问题及解决方案

1. **文件名解析失败**
   - 检查文件命名是否严格遵循格式
   - 确认包含所有必需的组成部分
   - 验证中文字符编码正确

2. **CSV结构验证失败**
   - 确认文件包含21列数据
   - 检查第一列是否为count列
   - 验证传感器列命名格式

3. **数据读取失败**
   - 检查文件编码（支持UTF-8、GBK、GB2312等）
   - 确认CSV格式正确
   - 验证文件完整性

4. **兼容性验证失败**
   - 查看详细错误信息
   - 检查数据类型转换
   - 验证元数据完整性

### 调试方法

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 测试单个文件
from new_data_preprocessor import NewDataPreprocessor
preprocessor = NewDataPreprocessor("input", "output")

# 解析文件名
parsed_info = preprocessor.parse_filename("your_file.csv")
print(parsed_info)

# 验证CSV结构
is_valid = preprocessor.validate_csv_structure("your_file.csv")
print(f"CSV结构有效: {is_valid}")
```

## 🔧 高级配置

### 自定义输出目录结构

```python
from new_data_preprocessor import NewDataPreprocessor

class CustomPreprocessor(NewDataPreprocessor):
    def generate_output_structure(self, parsed_info):
        # 自定义目录结构
        custom_subdir = f"{parsed_info['monitoring_point']}/{parsed_info['monitoring_time'][:8]}"
        custom_filename = f"data_{parsed_info['monitoring_time']}.csv"
        return custom_subdir, custom_filename
```

### 自定义数据清理规则

```python
def custom_clean_data(self, df, parsed_info):
    # 自定义数据清理逻辑
    df_cleaned = df.copy()
    
    # 添加自定义处理
    # ...
    
    return df_cleaned
```

## 📞 技术支持

如遇到问题，请：

1. 运行测试脚本进行诊断：
   ```bash
   python test_new_data_preprocessing.py
   python demo_new_data_preprocessing.py
   ```

2. 查看生成的报告文件了解详细信息

3. 检查控制台输出的错误信息

4. 验证输入数据格式的正确性

## 🔄 版本更新

### v1.0 (2024-12-07)
- ✅ 完整的新格式数据预处理功能
- ✅ 自动格式检测和转换
- ✅ 与主系统无缝集成
- ✅ 全面的兼容性验证
- ✅ 详细的处理报告和日志

---

**总结**: 新格式数据预处理系统已完全集成到振动信号分析系统中，用户只需运行主程序即可自动处理新格式数据，无需额外配置。
