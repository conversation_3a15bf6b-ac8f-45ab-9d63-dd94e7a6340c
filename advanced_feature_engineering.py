#!/usr/bin/env python3
"""
高级特征工程模块
包含小波变换、统计矩、频域能量分布、特征选择和特征交互
"""

import numpy as np
import pandas as pd
from sklearn.feature_selection import SelectKBest, f_regression, f_classif, mutual_info_regression, mutual_info_classif
from sklearn.feature_selection import RFE, SelectFromModel
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.preprocessing import PolynomialFeatures, StandardScaler
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

class AdvancedFeatureEngineering:
    """高级特征工程器"""
    
    def __init__(self, random_state=42):
        """初始化特征工程器"""
        self.random_state = random_state
        self.feature_selectors = {}
        self.feature_transformers = {}
        self.feature_importance_scores = {}
        
    def extract_wavelet_features(self, signals_df, wavelet='db4', levels=4):
        """提取小波变换特征"""
        print("    🌊 提取小波变换特征...")
        
        try:
            import pywt
            
            wavelet_features = []
            
            # 获取信号列
            signal_columns = [col for col in signals_df.columns if 
                            ('sensor' in col.lower() or 'acce' in col.lower()) and 
                            col.lower() not in ['time', 'timestamp']]
            
            for idx, row in signals_df.iterrows():
                row_features = {}
                
                for col in signal_columns[:10]:  # 限制处理的传感器数量
                    try:
                        signal = row[col]
                        if isinstance(signal, (list, np.ndarray)) and len(signal) > 2**levels:
                            # 小波分解
                            coeffs = pywt.wavedec(signal, wavelet, level=levels)
                            
                            # 提取每层系数的统计特征
                            for level, coeff in enumerate(coeffs):
                                if len(coeff) > 0:
                                    prefix = f"{col}_wavelet_L{level}"
                                    row_features[f"{prefix}_mean"] = np.mean(coeff)
                                    row_features[f"{prefix}_std"] = np.std(coeff)
                                    row_features[f"{prefix}_energy"] = np.sum(coeff**2)
                                    row_features[f"{prefix}_entropy"] = self._calculate_entropy(coeff)
                        
                    except Exception as e:
                        continue
                
                wavelet_features.append(row_features)
            
            wavelet_df = pd.DataFrame(wavelet_features)
            print(f"      提取了 {wavelet_df.shape[1]} 个小波特征")
            
            return wavelet_df
            
        except ImportError:
            print("      ⚠️  PyWavelets未安装，跳过小波特征提取")
            return pd.DataFrame()
        except Exception as e:
            print(f"      ❌ 小波特征提取失败: {str(e)}")
            return pd.DataFrame()
    
    def extract_statistical_moments(self, signals_df, max_moment=4):
        """提取统计矩特征"""
        print("    📊 提取统计矩特征...")
        
        try:
            moment_features = []
            
            # 获取信号列
            signal_columns = [col for col in signals_df.columns if 
                            ('sensor' in col.lower() or 'acce' in col.lower()) and 
                            col.lower() not in ['time', 'timestamp']]
            
            for idx, row in signals_df.iterrows():
                row_features = {}
                
                for col in signal_columns[:10]:  # 限制处理的传感器数量
                    try:
                        signal = row[col]
                        if isinstance(signal, (list, np.ndarray)) and len(signal) > 10:
                            signal = np.array(signal)
                            
                            # 计算各阶矩
                            mean = np.mean(signal)
                            std = np.std(signal)
                            
                            if std > 0:
                                # 标准化信号
                                normalized_signal = (signal - mean) / std
                                
                                for moment in range(1, max_moment + 1):
                                    moment_value = np.mean(normalized_signal ** moment)
                                    row_features[f"{col}_moment_{moment}"] = moment_value
                                
                                # 中心矩
                                for moment in range(2, max_moment + 1):
                                    central_moment = np.mean((signal - mean) ** moment)
                                    row_features[f"{col}_central_moment_{moment}"] = central_moment
                        
                    except Exception as e:
                        continue
                
                moment_features.append(row_features)
            
            moment_df = pd.DataFrame(moment_features)
            print(f"      提取了 {moment_df.shape[1]} 个统计矩特征")
            
            return moment_df
            
        except Exception as e:
            print(f"      ❌ 统计矩特征提取失败: {str(e)}")
            return pd.DataFrame()
    
    def extract_frequency_energy_distribution(self, signals_df, fs=1000, n_bands=10):
        """提取频域能量分布特征"""
        print("    🎵 提取频域能量分布特征...")
        
        try:
            freq_features = []
            
            # 获取信号列
            signal_columns = [col for col in signals_df.columns if 
                            ('sensor' in col.lower() or 'acce' in col.lower()) and 
                            col.lower() not in ['time', 'timestamp']]
            
            for idx, row in signals_df.iterrows():
                row_features = {}
                
                for col in signal_columns[:10]:  # 限制处理的传感器数量
                    try:
                        signal = row[col]
                        if isinstance(signal, (list, np.ndarray)) and len(signal) > 64:
                            signal = np.array(signal)
                            
                            # FFT变换
                            fft_signal = np.fft.fft(signal)
                            freqs = np.fft.fftfreq(len(signal), 1/fs)
                            
                            # 只取正频率部分
                            positive_freqs = freqs[:len(freqs)//2]
                            positive_magnitude = np.abs(fft_signal[:len(fft_signal)//2])
                            
                            # 计算总能量
                            total_energy = np.sum(positive_magnitude**2)
                            
                            if total_energy > 0:
                                # 分频带计算能量分布
                                max_freq = fs // 2
                                band_width = max_freq / n_bands
                                
                                for band in range(n_bands):
                                    freq_start = band * band_width
                                    freq_end = (band + 1) * band_width
                                    
                                    # 找到频带内的频率索引
                                    band_mask = (positive_freqs >= freq_start) & (positive_freqs < freq_end)
                                    band_energy = np.sum(positive_magnitude[band_mask]**2)
                                    
                                    # 能量比例
                                    energy_ratio = band_energy / total_energy
                                    row_features[f"{col}_freq_band_{band}_energy_ratio"] = energy_ratio
                                
                                # 频谱质心和扩散
                                spectral_centroid = np.sum(positive_freqs * positive_magnitude**2) / total_energy
                                spectral_spread = np.sqrt(np.sum(((positive_freqs - spectral_centroid)**2) * positive_magnitude**2) / total_energy)
                                
                                row_features[f"{col}_spectral_centroid"] = spectral_centroid
                                row_features[f"{col}_spectral_spread"] = spectral_spread
                                
                                # 频谱滚降点
                                cumulative_energy = np.cumsum(positive_magnitude**2)
                                rolloff_index = np.where(cumulative_energy >= 0.85 * total_energy)[0]
                                if len(rolloff_index) > 0:
                                    spectral_rolloff = positive_freqs[rolloff_index[0]]
                                    row_features[f"{col}_spectral_rolloff"] = spectral_rolloff
                        
                    except Exception as e:
                        continue
                
                freq_features.append(row_features)
            
            freq_df = pd.DataFrame(freq_features)
            print(f"      提取了 {freq_df.shape[1]} 个频域能量分布特征")
            
            return freq_df
            
        except Exception as e:
            print(f"      ❌ 频域能量分布特征提取失败: {str(e)}")
            return pd.DataFrame()
    
    def _calculate_entropy(self, signal):
        """计算信号熵"""
        try:
            # 计算概率分布
            hist, _ = np.histogram(signal, bins=50, density=True)
            hist = hist[hist > 0]  # 移除零值
            
            # 计算熵
            entropy = -np.sum(hist * np.log2(hist))
            return entropy
        except:
            return 0
    
    def select_features_univariate(self, X, y, task_type='regression', k=50):
        """单变量特征选择"""
        print(f"    🎯 单变量特征选择 (选择前{k}个特征)...")
        
        try:
            if task_type == 'regression':
                # 使用F检验和互信息
                f_selector = SelectKBest(score_func=f_regression, k=min(k, X.shape[1]))
                mi_selector = SelectKBest(score_func=mutual_info_regression, k=min(k, X.shape[1]))
            else:
                f_selector = SelectKBest(score_func=f_classif, k=min(k, X.shape[1]))
                mi_selector = SelectKBest(score_func=mutual_info_classif, k=min(k, X.shape[1]))
            
            # F检验选择
            X_f_selected = f_selector.fit_transform(X, y)
            f_scores = f_selector.scores_
            f_selected_features = f_selector.get_support()
            
            # 互信息选择
            X_mi_selected = mi_selector.fit_transform(X, y)
            mi_scores = mi_selector.scores_
            mi_selected_features = mi_selector.get_support()
            
            # 保存选择器
            self.feature_selectors['f_test'] = f_selector
            self.feature_selectors['mutual_info'] = mi_selector
            
            # 保存特征重要性分数
            self.feature_importance_scores['f_test'] = f_scores
            self.feature_importance_scores['mutual_info'] = mi_scores
            
            print(f"      F检验选择了 {X_f_selected.shape[1]} 个特征")
            print(f"      互信息选择了 {X_mi_selected.shape[1]} 个特征")
            
            return {
                'f_test': (X_f_selected, f_selected_features),
                'mutual_info': (X_mi_selected, mi_selected_features)
            }
            
        except Exception as e:
            print(f"      ❌ 单变量特征选择失败: {str(e)}")
            return {}
    
    def select_features_model_based(self, X, y, task_type='regression'):
        """基于模型的特征选择"""
        print(f"    🌲 基于模型的特征选择...")
        
        try:
            if task_type == 'regression':
                base_model = RandomForestRegressor(n_estimators=100, random_state=self.random_state)
            else:
                base_model = RandomForestClassifier(n_estimators=100, random_state=self.random_state)
            
            # 训练模型
            base_model.fit(X, y)
            
            # 基于重要性选择特征
            selector = SelectFromModel(base_model, prefit=True)
            X_selected = selector.transform(X)
            selected_features = selector.get_support()
            
            # 递归特征消除
            rfe_selector = RFE(base_model, n_features_to_select=min(30, X.shape[1]), step=1)
            X_rfe_selected = rfe_selector.fit_transform(X, y)
            rfe_selected_features = rfe_selector.get_support()
            
            # 保存选择器
            self.feature_selectors['model_based'] = selector
            self.feature_selectors['rfe'] = rfe_selector
            
            # 保存特征重要性
            self.feature_importance_scores['model_based'] = base_model.feature_importances_
            self.feature_importance_scores['rfe'] = rfe_selector.ranking_
            
            print(f"      模型选择了 {X_selected.shape[1]} 个特征")
            print(f"      RFE选择了 {X_rfe_selected.shape[1]} 个特征")
            
            return {
                'model_based': (X_selected, selected_features),
                'rfe': (X_rfe_selected, rfe_selected_features)
            }
            
        except Exception as e:
            print(f"      ❌ 基于模型的特征选择失败: {str(e)}")
            return {}
    
    def create_polynomial_features(self, X, degree=2, interaction_only=True):
        """创建多项式特征"""
        print(f"    🔢 创建多项式特征 (度数={degree})...")
        
        try:
            # 限制特征数量以避免维度爆炸
            max_features = min(20, X.shape[1])
            X_limited = X[:, :max_features]
            
            poly = PolynomialFeatures(
                degree=degree, 
                interaction_only=interaction_only,
                include_bias=False
            )
            
            X_poly = poly.fit_transform(X_limited)
            
            # 保存转换器
            self.feature_transformers['polynomial'] = poly
            
            print(f"      生成了 {X_poly.shape[1]} 个多项式特征")
            
            return X_poly
            
        except Exception as e:
            print(f"      ❌ 多项式特征创建失败: {str(e)}")
            return X
    
    def apply_pca_transformation(self, X, n_components=0.95):
        """应用PCA降维"""
        print(f"    🔄 应用PCA降维 (保留{n_components*100 if n_components < 1 else n_components}{'%' if n_components < 1 else '个'}方差)...")
        
        try:
            pca = PCA(n_components=n_components, random_state=self.random_state)
            X_pca = pca.fit_transform(X)
            
            # 保存转换器
            self.feature_transformers['pca'] = pca
            
            explained_variance = np.sum(pca.explained_variance_ratio_)
            print(f"      PCA降维到 {X_pca.shape[1]} 个主成分")
            print(f"      解释方差比例: {explained_variance:.4f}")
            
            return X_pca
            
        except Exception as e:
            print(f"      ❌ PCA降维失败: {str(e)}")
            return X
    
    def create_feature_interactions(self, X, feature_names=None, max_interactions=100):
        """创建特征交互"""
        print(f"    🔗 创建特征交互 (最多{max_interactions}个)...")
        
        try:
            if feature_names is None:
                feature_names = [f"feature_{i}" for i in range(X.shape[1])]
            
            # 限制特征数量
            max_features = min(15, X.shape[1])
            X_limited = X[:, :max_features]
            limited_names = feature_names[:max_features]
            
            interaction_features = []
            interaction_names = []
            
            # 创建两两交互
            count = 0
            for i in range(max_features):
                for j in range(i+1, max_features):
                    if count >= max_interactions:
                        break
                    
                    # 乘法交互
                    interaction = X_limited[:, i] * X_limited[:, j]
                    interaction_features.append(interaction)
                    interaction_names.append(f"{limited_names[i]}_x_{limited_names[j]}")
                    count += 1
                    
                    # 除法交互（避免除零）
                    if count < max_interactions and np.all(X_limited[:, j] != 0):
                        interaction = X_limited[:, i] / (X_limited[:, j] + 1e-8)
                        interaction_features.append(interaction)
                        interaction_names.append(f"{limited_names[i]}_div_{limited_names[j]}")
                        count += 1
                
                if count >= max_interactions:
                    break
            
            if interaction_features:
                X_interactions = np.column_stack(interaction_features)
                print(f"      创建了 {len(interaction_features)} 个交互特征")
                
                return X_interactions, interaction_names
            else:
                return np.array([]).reshape(X.shape[0], 0), []
                
        except Exception as e:
            print(f"      ❌ 特征交互创建失败: {str(e)}")
            return np.array([]).reshape(X.shape[0], 0), []
    
    def get_feature_importance_summary(self):
        """获取特征重要性总结"""
        summary = {}
        
        for method, scores in self.feature_importance_scores.items():
            if len(scores) > 0:
                summary[method] = {
                    'top_10_indices': np.argsort(scores)[-10:][::-1].tolist(),
                    'top_10_scores': np.sort(scores)[-10:][::-1].tolist(),
                    'mean_score': np.mean(scores),
                    'std_score': np.std(scores)
                }
        
        return summary

def main():
    """测试函数"""
    from sklearn.datasets import make_regression
    
    print("🧪 测试高级特征工程...")
    
    # 创建测试数据
    X, y = make_regression(n_samples=500, n_features=20, noise=0.1, random_state=42)
    
    # 初始化特征工程器
    fe = AdvancedFeatureEngineering()
    
    # 特征选择
    univariate_results = fe.select_features_univariate(X, y, 'regression', k=15)
    model_based_results = fe.select_features_model_based(X, y, 'regression')
    
    # 多项式特征
    X_poly = fe.create_polynomial_features(X, degree=2)
    
    # PCA降维
    X_pca = fe.apply_pca_transformation(X, n_components=0.95)
    
    # 特征交互
    X_interactions, interaction_names = fe.create_feature_interactions(X)
    
    # 特征重要性总结
    importance_summary = fe.get_feature_importance_summary()
    
    print(f"\n📊 特征工程结果:")
    print(f"   原始特征: {X.shape[1]}")
    print(f"   多项式特征: {X_poly.shape[1]}")
    print(f"   PCA特征: {X_pca.shape[1]}")
    print(f"   交互特征: {X_interactions.shape[1]}")
    
    print("✅ 测试完成!")

if __name__ == "__main__":
    main()
