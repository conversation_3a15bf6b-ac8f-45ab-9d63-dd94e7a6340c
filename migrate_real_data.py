#!/usr/bin/env python3
"""
真实数据迁移和格式转换脚本
解决Windows编码问题并将data文件夹数据迁移到raw_data结构
"""

import os
import pandas as pd
import numpy as np
import shutil
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
import chardet
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealDataMigrator:
    """真实数据迁移器"""
    
    def __init__(self, source_dir: str = './data', target_dir: str = './raw_data'):
        """
        初始化数据迁移器
        
        Args:
            source_dir: 源数据目录
            target_dir: 目标数据目录
        """
        self.source_dir = source_dir
        self.target_dir = target_dir
        self.migration_log = []
        
        # 创建目标目录结构
        self.setup_target_directories()
        
        # 车辆信息映射
        self.vehicle_info = {
            '2吨': {'load': 2.0, 'axle_count': 2, 'vehicle_type': 'light_truck'},
            '25吨': {'load': 25.0, 'axle_count': 3, 'vehicle_type': 'heavy_truck'},
            '34.98吨': {'load': 34.98, 'axle_count': 3, 'vehicle_type': 'heavy_truck'},
            '45.39吨': {'load': 45.39, 'axle_count': 3, 'vehicle_type': 'heavy_truck'},
            '55.62吨': {'load': 55.62, 'axle_count': 3, 'vehicle_type': 'heavy_truck'}
        }
    
    def setup_target_directories(self):
        """设置目标目录结构"""
        logger.info("📁 设置目标目录结构...")
        
        directories = [
            os.path.join(self.target_dir, 'vibration_signals'),
            os.path.join(self.target_dir, 'speed_labels'),
            os.path.join(self.target_dir, 'load_labels'),
            os.path.join(self.target_dir, 'type_labels')
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"✅ 目录已创建: {directory}")
    
    def detect_file_encoding(self, file_path: str) -> str:
        """检测文件编码"""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # 读取前10KB检测编码
                result = chardet.detect(raw_data)
                encoding = result['encoding']
                confidence = result['confidence']
                
                logger.info(f"文件 {os.path.basename(file_path)} 编码检测: {encoding} (置信度: {confidence:.2f})")
                
                # 如果检测到GBK或GB2312，使用gbk编码
                if encoding and ('gb' in encoding.lower() or 'gbk' in encoding.lower()):
                    return 'gbk'
                # 如果检测到UTF-8，使用utf-8
                elif encoding and 'utf' in encoding.lower():
                    return 'utf-8'
                # 默认尝试gbk（Windows中文系统常用）
                else:
                    return 'gbk'
        except Exception as e:
            logger.warning(f"编码检测失败: {str(e)}，使用默认编码 gbk")
            return 'gbk'
    
    def safe_read_csv(self, file_path: str) -> Optional[pd.DataFrame]:
        """安全读取CSV文件，自动处理编码问题"""
        encodings_to_try = ['gbk', 'utf-8', 'gb2312', 'latin1', 'cp1252']
        
        # 首先尝试自动检测的编码
        detected_encoding = self.detect_file_encoding(file_path)
        if detected_encoding not in encodings_to_try:
            encodings_to_try.insert(0, detected_encoding)
        
        for encoding in encodings_to_try:
            try:
                logger.info(f"尝试使用编码 {encoding} 读取文件: {os.path.basename(file_path)}")
                df = pd.read_csv(file_path, encoding=encoding)
                logger.info(f"✅ 成功使用编码 {encoding} 读取文件")
                return df
            except UnicodeDecodeError as e:
                logger.warning(f"编码 {encoding} 失败: {str(e)}")
                continue
            except Exception as e:
                logger.error(f"读取文件失败 (编码 {encoding}): {str(e)}")
                continue
        
        logger.error(f"❌ 无法读取文件: {file_path}")
        return None
    
    def extract_speed_from_path(self, file_path: str) -> Optional[float]:
        """从文件路径提取速度信息"""
        path_parts = file_path.replace('\\', '/').split('/')
        
        for part in path_parts:
            if 'km_h' in part or 'km/h' in part:
                # 提取数字部分
                speed_str = part.replace('km_h', '').replace('km/h', '').replace('_repeat2', '').replace('_repeat', '')
                try:
                    speed = float(speed_str)
                    return speed
                except ValueError:
                    continue
        
        return None
    
    def extract_vehicle_info_from_path(self, file_path: str) -> Dict[str, any]:
        """从文件路径提取车辆信息"""
        path_parts = file_path.replace('\\', '/').split('/')
        
        vehicle_info = {
            'load': None,
            'axle_count': None,
            'vehicle_type': None
        }
        
        for part in path_parts:
            if part in self.vehicle_info:
                vehicle_info.update(self.vehicle_info[part])
                break
        
        return vehicle_info
    
    def convert_vibration_signal(self, file_path: str, output_path: str) -> bool:
        """转换振动信号文件格式"""
        try:
            # 读取原始数据
            df = self.safe_read_csv(file_path)
            if df is None:
                return False
            
            logger.info(f"原始数据形状: {df.shape}")
            logger.info(f"原始列名: {list(df.columns)}")
            
            # 创建时间戳
            n_samples = len(df)
            # 假设采样率为1000Hz
            sampling_rate = 1000
            start_time = datetime(2022, 12, 9, 9, 39, 56)  # 基于文件名中的时间
            time_stamps = pd.date_range(start_time, periods=n_samples, freq=f'{1000//sampling_rate}ms')
            
            # 重新组织数据格式
            new_data = {'timestamp': time_stamps}
            
            # 将加速度计数据重命名为传感器格式
            acce_columns = [col for col in df.columns if col.startswith('acce')]
            
            # 假设每3个加速度计组成一个传感器的x,y,z轴
            sensor_count = 0
            for i in range(0, len(acce_columns), 3):
                if i + 2 < len(acce_columns):
                    sensor_count += 1
                    new_data[f'sensor_{sensor_count}_x'] = df[acce_columns[i]].values
                    new_data[f'sensor_{sensor_count}_y'] = df[acce_columns[i+1]].values
                    new_data[f'sensor_{sensor_count}_z'] = df[acce_columns[i+2]].values
                    
                    if sensor_count >= 6:  # 限制传感器数量
                        break
            
            # 添加环境数据（模拟）
            new_data['temperature'] = np.random.normal(23.5, 1.0, n_samples)
            new_data['humidity'] = np.random.normal(45.0, 5.0, n_samples)
            
            # 创建新的DataFrame
            new_df = pd.DataFrame(new_data)
            
            # 保存转换后的文件
            new_df.to_csv(output_path, index=False, encoding='utf-8')
            
            logger.info(f"✅ 振动信号文件转换完成: {output_path}")
            logger.info(f"转换后数据形状: {new_df.shape}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 振动信号文件转换失败: {str(e)}")
            return False
    
    def migrate_vibration_signals(self) -> List[Dict]:
        """迁移振动信号文件"""
        logger.info("🔄 开始迁移振动信号文件...")
        
        migrated_files = []
        file_counter = 0
        
        # 遍历源目录
        for root, dirs, files in os.walk(self.source_dir):
            for file in files:
                if file.endswith('.csv') and 'acce_' in file:
                    file_counter += 1
                    source_path = os.path.join(root, file)
                    
                    # 生成新的文件名
                    timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
                    new_filename = f"vibration_{timestamp_str}_{file_counter:03d}.csv"
                    target_path = os.path.join(self.target_dir, 'vibration_signals', new_filename)
                    
                    # 转换文件
                    if self.convert_vibration_signal(source_path, target_path):
                        # 提取元数据
                        speed = self.extract_speed_from_path(source_path)
                        vehicle_info = self.extract_vehicle_info_from_path(source_path)
                        
                        file_info = {
                            'original_path': source_path,
                            'new_path': target_path,
                            'new_filename': new_filename,
                            'speed_kmh': speed,
                            'load_tons': vehicle_info['load'],
                            'axle_count': vehicle_info['axle_count'],
                            'vehicle_type': vehicle_info['vehicle_type'],
                            'timestamp': datetime.now()
                        }
                        
                        migrated_files.append(file_info)
                        logger.info(f"✅ 已迁移: {file} -> {new_filename}")
                    
                    # 限制文件数量以避免过多数据
                    if file_counter >= 50:  # 限制为50个文件
                        logger.info("⚠️  已达到文件数量限制，停止迁移")
                        break
            
            if file_counter >= 50:
                break
        
        logger.info(f"✅ 振动信号文件迁移完成，共迁移 {len(migrated_files)} 个文件")
        return migrated_files
    
    def generate_label_files(self, migrated_files: List[Dict]):
        """生成标签文件"""
        logger.info("📝 生成标签文件...")
        
        # 生成速度标签
        speed_data = []
        load_data = []
        type_data = []
        
        for i, file_info in enumerate(migrated_files):
            vehicle_id = f"VH{i+1:03d}"
            timestamp = file_info['timestamp']
            
            # 速度标签
            if file_info['speed_kmh'] is not None:
                speed_data.append({
                    'timestamp': timestamp,
                    'vehicle_id': vehicle_id,
                    'speed_kmh': file_info['speed_kmh'],
                    'confidence': 0.95
                })
            
            # 轴重标签
            if file_info['load_tons'] is not None:
                load_data.append({
                    'timestamp': timestamp,
                    'vehicle_id': vehicle_id,
                    'axle_1_load': file_info['load_tons'] * 0.4,
                    'axle_2_load': file_info['load_tons'] * 0.4,
                    'axle_3_load': file_info['load_tons'] * 0.2 if file_info['axle_count'] == 3 else 0,
                    'total_load': file_info['load_tons'],
                    'confidence': 0.92
                })
            
            # 轴型标签
            if file_info['vehicle_type'] is not None:
                # 映射车辆类型到数字
                type_mapping = {
                    'light_truck': 1,
                    'heavy_truck': 2
                }
                
                type_data.append({
                    'timestamp': timestamp,
                    'vehicle_id': vehicle_id,
                    'axle_type': type_mapping.get(file_info['vehicle_type'], 1),
                    'axle_count': file_info['axle_count'],
                    'vehicle_class': file_info['vehicle_type'],
                    'confidence': 0.96
                })
        
        # 保存标签文件
        if speed_data:
            speed_df = pd.DataFrame(speed_data)
            speed_file = os.path.join(self.target_dir, 'speed_labels', 'speed_labels.csv')
            speed_df.to_csv(speed_file, index=False, encoding='utf-8')
            logger.info(f"✅ 速度标签文件已生成: {speed_file} ({len(speed_data)} 条记录)")
        
        if load_data:
            load_df = pd.DataFrame(load_data)
            load_file = os.path.join(self.target_dir, 'load_labels', 'load_labels.csv')
            load_df.to_csv(load_file, index=False, encoding='utf-8')
            logger.info(f"✅ 轴重标签文件已生成: {load_file} ({len(load_data)} 条记录)")
        
        if type_data:
            type_df = pd.DataFrame(type_data)
            type_file = os.path.join(self.target_dir, 'type_labels', 'type_labels.csv')
            type_df.to_csv(type_file, index=False, encoding='utf-8')
            logger.info(f"✅ 轴型标签文件已生成: {type_file} ({len(type_data)} 条记录)")
    
    def generate_migration_report(self, migrated_files: List[Dict]):
        """生成迁移报告"""
        logger.info("📊 生成迁移报告...")
        
        report_file = os.path.join(self.target_dir, 'migration_report.txt')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("真实数据迁移报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"迁移时间: {datetime.now().isoformat()}\n")
            f.write(f"源目录: {self.source_dir}\n")
            f.write(f"目标目录: {self.target_dir}\n\n")
            
            f.write(f"📊 迁移统计\n")
            f.write("-" * 30 + "\n")
            f.write(f"总迁移文件数: {len(migrated_files)}\n")
            
            # 按车辆类型统计
            vehicle_types = {}
            speeds = []
            loads = []
            
            for file_info in migrated_files:
                vtype = file_info.get('vehicle_type', 'unknown')
                vehicle_types[vtype] = vehicle_types.get(vtype, 0) + 1
                
                if file_info.get('speed_kmh'):
                    speeds.append(file_info['speed_kmh'])
                if file_info.get('load_tons'):
                    loads.append(file_info['load_tons'])
            
            f.write(f"\n车辆类型分布:\n")
            for vtype, count in vehicle_types.items():
                f.write(f"  {vtype}: {count} 个文件\n")
            
            if speeds:
                f.write(f"\n速度范围: {min(speeds):.1f} - {max(speeds):.1f} km/h\n")
                f.write(f"平均速度: {np.mean(speeds):.1f} km/h\n")
            
            if loads:
                f.write(f"\n载重范围: {min(loads):.1f} - {max(loads):.1f} 吨\n")
                f.write(f"平均载重: {np.mean(loads):.1f} 吨\n")
            
            f.write(f"\n📋 详细文件列表\n")
            f.write("-" * 30 + "\n")
            
            for i, file_info in enumerate(migrated_files, 1):
                f.write(f"\n{i}. {file_info['new_filename']}\n")
                f.write(f"   原始路径: {file_info['original_path']}\n")
                f.write(f"   速度: {file_info.get('speed_kmh', 'N/A')} km/h\n")
                f.write(f"   载重: {file_info.get('load_tons', 'N/A')} 吨\n")
                f.write(f"   车辆类型: {file_info.get('vehicle_type', 'N/A')}\n")
        
        logger.info(f"✅ 迁移报告已生成: {report_file}")
    
    def run_migration(self):
        """执行完整的数据迁移"""
        logger.info("🚀 开始真实数据迁移...")
        
        if not os.path.exists(self.source_dir):
            logger.error(f"❌ 源目录不存在: {self.source_dir}")
            return False
        
        try:
            # 1. 迁移振动信号文件
            migrated_files = self.migrate_vibration_signals()
            
            if not migrated_files:
                logger.error("❌ 没有成功迁移任何文件")
                return False
            
            # 2. 生成标签文件
            self.generate_label_files(migrated_files)
            
            # 3. 生成迁移报告
            self.generate_migration_report(migrated_files)
            
            logger.info("🎉 数据迁移完成!")
            logger.info(f"✅ 成功迁移 {len(migrated_files)} 个文件")
            logger.info(f"📁 目标目录: {self.target_dir}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据迁移失败: {str(e)}")
            return False

def main():
    """主函数"""
    print("🔄 真实数据迁移和格式转换工具")
    print("=" * 60)
    print("解决Windows编码问题并迁移data文件夹数据")
    print("=" * 60)
    
    # 检查源目录
    if not os.path.exists('./data'):
        print("❌ 源目录 './data' 不存在")
        print("请确保data文件夹在当前目录下")
        return
    
    # 创建迁移器并执行迁移
    migrator = RealDataMigrator('./data', './raw_data')
    
    success = migrator.run_migration()
    
    if success:
        print("\n🎉 数据迁移成功完成!")
        print("\n📋 下一步操作:")
        print("1. 检查生成的文件: ./raw_data/")
        print("2. 运行数据验证: python data_validation.py")
        print("3. 开始训练: python run_complete_training.py")
    else:
        print("\n❌ 数据迁移失败")
        print("请检查错误日志并重试")

if __name__ == "__main__":
    main()
