# 深度学习增强振动信号分析系统 - 完整依赖包清单
# 适用于 Windows 环境，Python 3.8+

# ===== 核心数据处理包 =====
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# ===== 机器学习核心包 =====
scikit-learn>=1.0.0
joblib>=1.1.0

# ===== 深度学习框架 =====
# PyTorch (CPU版本，GPU版本需要单独安装)
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# ===== 梯度提升和优化 =====
xgboost>=1.6.0
lightgbm>=3.3.0
optuna>=3.0.0

# ===== 数据可视化 =====
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# ===== 文件处理和编码 =====
chardet>=4.0.0
openpyxl>=3.0.0

# ===== 信号处理 =====
PyWavelets>=1.1.0

# ===== 进度条和日志 =====
tqdm>=4.62.0

# ===== 统计和信号处理 =====
statsmodels>=0.13.0

# ===== 系统工具 =====
psutil>=5.8.0
