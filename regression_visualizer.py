#!/usr/bin/env python3
"""
回归任务专用可视化模块
针对速度预测、轴重预测等回归任务的专业可视化
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from scipy import stats
from improved_visualization_base import ImprovedVisualizationBase
import warnings
warnings.filterwarnings('ignore')

class RegressionVisualizer(ImprovedVisualizationBase):
    """回归任务可视化器"""
    
    def __init__(self, output_dir='regression_visualizations'):
        """初始化回归可视化器"""
        super().__init__(output_dir)
        print("🎯 回归任务可视化器初始化完成")
    
    def create_prediction_accuracy_plot(self, y_true, y_pred, model_name, task_name='回归预测', language='chinese'):
        """创建预测精度分析图（单独图表）"""
        print(f"   📊 生成{model_name}预测精度分析图 ({language})...")
        
        # 计算评估指标
        r2 = r2_score(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        mae = mean_absolute_error(y_true, y_pred)
        mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
        
        # 创建图表
        fig, ax = plt.subplots(figsize=self.chart_config['figsize_single'])
        
        # 绘制散点图
        scatter = ax.scatter(y_true, y_pred, alpha=0.6, s=50, 
                           c=self.academic_colors['primary'], edgecolors='white', linewidth=0.5)
        
        # 绘制完美拟合线
        min_val = min(np.min(y_true), np.min(y_pred))
        max_val = max(np.max(y_true), np.max(y_pred))
        perfect_line = np.linspace(min_val, max_val, 100)
        ax.plot(perfect_line, perfect_line, '--', color=self.academic_colors['warning'], 
               linewidth=3, alpha=0.8, label='完美拟合线' if language == 'chinese' else 'Perfect Fit Line')
        
        # 设置标签和标题
        if language == 'chinese':
            ax.set_xlabel('真实值', fontweight='bold')
            ax.set_ylabel('预测值', fontweight='bold')
            title = f'{model_name} - {task_name}精度分析'
            ax.set_title(title, fontweight='bold', pad=20)
        else:
            ax.set_xlabel('True Values', fontweight='bold')
            ax.set_ylabel('Predicted Values', fontweight='bold')
            title = f'{model_name} - {task_name} Accuracy Analysis'
            ax.set_title(title, fontweight='bold', pad=20)
        
        # 添加性能指标文本框
        if language == 'chinese':
            metrics_text = f'性能指标:\nR² = {r2:.4f}\nRMSE = {rmse:.4f}\nMAE = {mae:.4f}\nMAPE = {mape:.2f}%'
        else:
            metrics_text = f'Performance Metrics:\nR² = {r2:.4f}\nRMSE = {rmse:.4f}\nMAE = {mae:.4f}\nMAPE = {mape:.2f}%'
        
        # 计算文本框位置（避免与数据重叠）
        x_range = max_val - min_val
        y_range = max_val - min_val
        text_x = min_val + 0.05 * x_range
        text_y = max_val - 0.25 * y_range
        
        ax.text(text_x, text_y, metrics_text, 
               bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8),
               fontsize=self.chart_config['legend_fontsize'], fontweight='bold')
        
        # 设置坐标轴范围
        margin = 0.05 * (max_val - min_val)
        ax.set_xlim(min_val - margin, max_val + margin)
        ax.set_ylim(min_val - margin, max_val + margin)
        
        # 添加图例
        ax.legend(loc=self.get_optimal_legend_position(ax))
        
        # 网格设置
        ax.grid(True, alpha=self.chart_config['grid_alpha'], linewidth=self.chart_config['grid_linewidth'])
        
        plt.tight_layout()
        
        # 保存图表
        filename = f'{model_name}_prediction_accuracy_{language}.png'
        filepath = self.save_chart(fig, filename, language, 'regression_analysis')
        plt.close(fig)
        
        print(f"      ✅ 预测精度图已保存: {filepath}")
        
        # 返回图表元数据
        return self.create_chart_metadata({
            'type': 'prediction_accuracy',
            'model': model_name,
            'language': language,
            'description': f'{task_name}预测精度分析' if language == 'chinese' else f'{task_name} Prediction Accuracy Analysis',
            'metrics': {'r2': r2, 'rmse': rmse, 'mae': mae, 'mape': mape}
        })
    
    def create_residual_analysis_plot(self, y_true, y_pred, model_name, task_name='回归预测', language='chinese'):
        """创建残差分析图（单独图表）"""
        print(f"   📊 生成{model_name}残差分析图 ({language})...")
        
        # 计算残差
        residuals = y_pred - y_true
        
        # 创建2x2子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.chart_config['figsize_quad'])
        
        if language == 'chinese':
            fig.suptitle(f'{model_name} - {task_name}残差分析', fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        else:
            fig.suptitle(f'{model_name} - {task_name} Residual Analysis', fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        
        # 1. 残差vs预测值散点图
        ax1.scatter(y_pred, residuals, alpha=0.6, s=30, color=self.academic_colors['secondary'])
        ax1.axhline(y=0, color=self.academic_colors['warning'], linestyle='--', linewidth=2)
        
        if language == 'chinese':
            ax1.set_xlabel('预测值', fontweight='bold')
            ax1.set_ylabel('残差 (预测值 - 真实值)', fontweight='bold')
            ax1.set_title('残差分布图', fontweight='bold')
        else:
            ax1.set_xlabel('Predicted Values', fontweight='bold')
            ax1.set_ylabel('Residuals (Predicted - True)', fontweight='bold')
            ax1.set_title('Residual Distribution Plot', fontweight='bold')
        
        ax1.grid(True, alpha=self.chart_config['grid_alpha'])
        
        # 2. 残差直方图
        ax2.hist(residuals, bins=30, alpha=0.7, color=self.academic_colors['success'], 
                edgecolor='black', linewidth=0.5)
        ax2.axvline(x=0, color=self.academic_colors['warning'], linestyle='--', linewidth=2)
        
        if language == 'chinese':
            ax2.set_xlabel('残差值', fontweight='bold')
            ax2.set_ylabel('频次', fontweight='bold')
            ax2.set_title('残差分布直方图', fontweight='bold')
        else:
            ax2.set_xlabel('Residual Values', fontweight='bold')
            ax2.set_ylabel('Frequency', fontweight='bold')
            ax2.set_title('Residual Distribution Histogram', fontweight='bold')
        
        ax2.grid(True, alpha=self.chart_config['grid_alpha'])
        
        # 3. QQ图（正态性检验）
        stats.probplot(residuals, dist="norm", plot=ax3)
        if language == 'chinese':
            ax3.set_title('残差正态性检验 (QQ图)', fontweight='bold')
            ax3.set_xlabel('理论分位数', fontweight='bold')
            ax3.set_ylabel('样本分位数', fontweight='bold')
        else:
            ax3.set_title('Residual Normality Test (QQ Plot)', fontweight='bold')
            ax3.set_xlabel('Theoretical Quantiles', fontweight='bold')
            ax3.set_ylabel('Sample Quantiles', fontweight='bold')
        
        ax3.grid(True, alpha=self.chart_config['grid_alpha'])
        
        # 4. 残差统计信息
        ax4.axis('off')
        
        # 计算统计指标
        residual_mean = np.mean(residuals)
        residual_std = np.std(residuals)
        residual_skew = stats.skew(residuals)
        residual_kurt = stats.kurtosis(residuals)
        
        if language == 'chinese':
            stats_text = f"""残差统计信息:
            
均值: {residual_mean:.4f}
标准差: {residual_std:.4f}
偏度: {residual_skew:.4f}
峰度: {residual_kurt:.4f}

样本数量: {len(residuals)}
最大残差: {np.max(residuals):.4f}
最小残差: {np.min(residuals):.4f}
残差范围: {np.max(residuals) - np.min(residuals):.4f}"""
        else:
            stats_text = f"""Residual Statistics:
            
Mean: {residual_mean:.4f}
Std Dev: {residual_std:.4f}
Skewness: {residual_skew:.4f}
Kurtosis: {residual_kurt:.4f}

Sample Size: {len(residuals)}
Max Residual: {np.max(residuals):.4f}
Min Residual: {np.min(residuals):.4f}
Residual Range: {np.max(residuals) - np.min(residuals):.4f}"""
        
        ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, fontsize=12,
                verticalalignment='top', 
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存图表
        filename = f'{model_name}_residual_analysis_{language}.png'
        filepath = self.save_chart(fig, filename, language, 'regression_analysis')
        plt.close(fig)
        
        print(f"      ✅ 残差分析图已保存: {filepath}")
        
        return self.create_chart_metadata({
            'type': 'residual_analysis',
            'model': model_name,
            'language': language,
            'description': f'{task_name}残差分析' if language == 'chinese' else f'{task_name} Residual Analysis',
            'residual_stats': {
                'mean': residual_mean, 'std': residual_std, 
                'skewness': residual_skew, 'kurtosis': residual_kurt
            }
        })
    
    def create_model_performance_comparison(self, results_dict, task_name='回归预测', language='chinese'):
        """创建模型性能对比图（单独图表）"""
        print(f"   📊 生成模型性能对比图 ({language})...")
        
        # 准备数据
        models = list(results_dict.keys())
        r2_scores = []
        rmse_scores = []
        mae_scores = []
        
        for model in models:
            result = results_dict[model]
            if 'y_true' in result and 'y_pred' in result:
                y_true, y_pred = result['y_true'], result['y_pred']
                r2_scores.append(r2_score(y_true, y_pred))
                rmse_scores.append(np.sqrt(mean_squared_error(y_true, y_pred)))
                mae_scores.append(mean_absolute_error(y_true, y_pred))
            else:
                # 使用提供的指标
                r2_scores.append(result.get('r2_score', 0))
                rmse_scores.append(result.get('rmse', 0))
                mae_scores.append(result.get('mae', 0))
        
        # 创建图表
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))
        
        if language == 'chinese':
            fig.suptitle(f'{task_name} - 模型性能对比分析', fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        else:
            fig.suptitle(f'{task_name} - Model Performance Comparison', fontsize=self.chart_config['title_fontsize'], fontweight='bold')
        
        # 获取颜色
        colors = self.get_color_palette(len(models))
        
        # 1. R²对比
        bars1 = ax1.bar(models, r2_scores, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)
        if language == 'chinese':
            ax1.set_title('决定系数 (R²) 对比', fontweight='bold')
            ax1.set_ylabel('R² 分数', fontweight='bold')
        else:
            ax1.set_title('Coefficient of Determination (R²)', fontweight='bold')
            ax1.set_ylabel('R² Score', fontweight='bold')
        
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=self.chart_config['grid_alpha'])
        
        # 添加数值标签
        for bar, value in zip(bars1, r2_scores):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 2. RMSE对比
        bars2 = ax2.bar(models, rmse_scores, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)
        if language == 'chinese':
            ax2.set_title('均方根误差 (RMSE) 对比', fontweight='bold')
            ax2.set_ylabel('RMSE 值', fontweight='bold')
        else:
            ax2.set_title('Root Mean Square Error (RMSE)', fontweight='bold')
            ax2.set_ylabel('RMSE Value', fontweight='bold')
        
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=self.chart_config['grid_alpha'])
        
        # 添加数值标签
        for bar, value in zip(bars2, rmse_scores):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 3. MAE对比
        bars3 = ax3.bar(models, mae_scores, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)
        if language == 'chinese':
            ax3.set_title('平均绝对误差 (MAE) 对比', fontweight='bold')
            ax3.set_ylabel('MAE 值', fontweight='bold')
        else:
            ax3.set_title('Mean Absolute Error (MAE)', fontweight='bold')
            ax3.set_ylabel('MAE Value', fontweight='bold')
        
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=self.chart_config['grid_alpha'])
        
        # 添加数值标签
        for bar, value in zip(bars3, mae_scores):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图表
        filename = f'model_performance_comparison_{language}.png'
        filepath = self.save_chart(fig, filename, language, 'regression_analysis')
        plt.close(fig)
        
        print(f"      ✅ 模型性能对比图已保存: {filepath}")
        
        return self.create_chart_metadata({
            'type': 'model_performance_comparison',
            'language': language,
            'description': f'{task_name}模型性能对比' if language == 'chinese' else f'{task_name} Model Performance Comparison',
            'models': models,
            'metrics': {'r2_scores': r2_scores, 'rmse_scores': rmse_scores, 'mae_scores': mae_scores}
        })

def main():
    """测试函数"""
    print("🧪 测试回归任务可视化器...")
    
    # 初始化可视化器
    reg_viz = RegressionVisualizer()
    
    # 生成测试数据
    np.random.seed(42)
    n_samples = 200
    y_true = np.random.uniform(20, 80, n_samples)
    y_pred = y_true + np.random.normal(0, 5, n_samples)
    
    # 测试预测精度图
    for language in ['chinese', 'english']:
        reg_viz.create_prediction_accuracy_plot(y_true, y_pred, 'Random Forest', '速度预测', language)
        reg_viz.create_residual_analysis_plot(y_true, y_pred, 'Random Forest', '速度预测', language)
    
    # 测试模型性能对比
    results_dict = {
        'Random Forest': {'y_true': y_true, 'y_pred': y_pred},
        'XGBoost': {'y_true': y_true, 'y_pred': y_true + np.random.normal(0, 3, n_samples)},
        'SVM': {'y_true': y_true, 'y_pred': y_true + np.random.normal(0, 7, n_samples)}
    }
    
    for language in ['chinese', 'english']:
        reg_viz.create_model_performance_comparison(results_dict, '速度预测', language)
    
    print("✅ 回归可视化器测试完成!")

if __name__ == "__main__":
    main()
