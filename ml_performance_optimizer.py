#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习性能优化器
第三阶段：性能优化 (通过超参数优化和集成学习)

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import os
import json
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# 机器学习库
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold, KFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier, VotingRegressor, VotingClassifier
from sklearn.metrics import mean_squared_error, r2_score, accuracy_score
import xgboost as xgb
import joblib
from datetime import datetime

# 超参数优化
import optuna
from optuna.samplers import TPESampler

class MLPerformanceOptimizer:
    """机器学习性能优化器"""
    
    def __init__(self, data_file: str = "combined_features.csv", output_dir: str = "ml_models_optimized"):
        self.data_file = data_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.optimized_models = {}
        self.ensemble_models = {}
        self.scalers = {}
        self.encoders = {}
        
    def optimize_all_models(self) -> Dict:
        """优化所有模型的主流程"""
        print("🚀 开始机器学习性能优化...")
        print("=" * 80)
        
        # 步骤1: 加载和预处理数据
        X, y_speed, y_load, y_axle = self.load_and_preprocess_data()
        
        # 步骤2: 速度预测模型优化
        speed_results = self.optimize_speed_prediction(X, y_speed)
        
        # 步骤3: 载重预测模型优化
        load_results = self.optimize_load_prediction(X, y_load)
        
        # 步骤4: 轴型分类模型优化
        axle_results = self.optimize_axle_classification(X, y_axle)
        
        # 步骤5: 集成学习
        ensemble_results = self.create_ensemble_models(X, y_speed, y_load, y_axle)
        
        # 步骤6: 生成最终报告
        final_report = self.generate_final_report(speed_results, load_results, axle_results, ensemble_results)
        
        return final_report
    
    def load_and_preprocess_data(self) -> Tuple[pd.DataFrame, pd.Series, pd.Series, pd.Series]:
        """加载和预处理数据"""
        print("\n📁 步骤1: 加载和预处理数据")
        print("-" * 50)
        
        # 加载数据
        df = pd.read_csv(self.data_file)
        print(f"   📊 原始数据: {df.shape[0]} 个样本, {df.shape[1]} 个特征")
        
        # 分离特征和目标变量
        target_columns = ['speed_kmh', 'load_tons', 'axle_type']
        feature_columns = [col for col in df.columns if col not in target_columns + ['file_path', 'file_name', 'data_source']]
        
        X = df[feature_columns].copy()
        X = X.select_dtypes(include=[np.number])
        
        # 处理目标变量
        y_speed = pd.to_numeric(df['speed_kmh'], errors='coerce')
        y_load = pd.to_numeric(df['load_tons'], errors='coerce') 
        y_axle = pd.to_numeric(df['axle_type'], errors='coerce')
        
        # 数据清洗
        X = X.fillna(X.mean())
        
        # 移除常数特征
        constant_features = X.columns[X.std() == 0]
        if len(constant_features) > 0:
            X = X.drop(columns=constant_features)
            print(f"   🗑️  移除常数特征: {len(constant_features)} 个")
        
        # 特征选择 - 保留最重要的特征
        if X.shape[1] > 100:
            from sklearn.feature_selection import SelectKBest, f_regression
            selector = SelectKBest(score_func=f_regression, k=100)
            X_selected = selector.fit_transform(X, y_speed.fillna(y_speed.mean()))
            selected_features = X.columns[selector.get_support()]
            X = pd.DataFrame(X_selected, columns=selected_features, index=X.index)
            print(f"   🎯 特征选择: 保留前100个重要特征")
        
        print(f"   📊 最终特征数: {X.shape[1]} 个")
        
        return X, y_speed, y_load, y_axle
    
    def optimize_speed_prediction(self, X: pd.DataFrame, y_speed: pd.Series) -> Dict:
        """优化速度预测模型"""
        print("\n🚗 步骤2: 速度预测模型优化 (目标R²>0.90)")
        print("-" * 50)
        
        # 过滤有效数据
        valid_mask = (y_speed > 0) & (y_speed <= 150) & y_speed.notna()
        X_speed = X[valid_mask].copy()
        y_speed_clean = y_speed[valid_mask].copy()
        
        print(f"   📊 速度预测数据: {len(X_speed)} 个样本")
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X_speed, y_speed_clean, test_size=0.2, random_state=42
        )
        
        # 特征标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        self.scalers['speed'] = scaler
        
        # XGBoost超参数优化
        print("   🔧 优化XGBoost模型...")
        
        def objective_xgb_speed(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 500),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'random_state': 42,
                'n_jobs': -1
            }
            
            model = xgb.XGBRegressor(**params)
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            return r2_score(y_test, y_pred)
        
        study_xgb = optuna.create_study(direction='maximize', sampler=TPESampler())
        study_xgb.optimize(objective_xgb_speed, n_trials=30)  # 减少试验次数以加快速度
        
        # 训练最优XGBoost模型
        best_xgb = xgb.XGBRegressor(**study_xgb.best_params, random_state=42, n_jobs=-1)
        best_xgb.fit(X_train, y_train)
        xgb_pred = best_xgb.predict(X_test)
        xgb_r2 = r2_score(y_test, xgb_pred)
        
        print(f"      XGBoost最优R²: {xgb_r2:.4f}")
        print(f"      最优参数: {study_xgb.best_params}")
        
        # RandomForest超参数优化
        print("   🔧 优化RandomForest模型...")
        
        def objective_rf_speed(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 300),
                'max_depth': trial.suggest_int('max_depth', 5, 20),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 10),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 5),
                'random_state': 42,
                'n_jobs': -1
            }
            
            model = RandomForestRegressor(**params)
            model.fit(X_train_scaled, y_train)
            y_pred = model.predict(X_test_scaled)
            return r2_score(y_test, y_pred)
        
        study_rf = optuna.create_study(direction='maximize', sampler=TPESampler())
        study_rf.optimize(objective_rf_speed, n_trials=30)
        
        # 训练最优RandomForest模型
        best_rf = RandomForestRegressor(**study_rf.best_params, random_state=42, n_jobs=-1)
        best_rf.fit(X_train_scaled, y_train)
        rf_pred = best_rf.predict(X_test_scaled)
        rf_r2 = r2_score(y_test, rf_pred)
        
        print(f"      RandomForest最优R²: {rf_r2:.4f}")
        print(f"      最优参数: {study_rf.best_params}")
        
        # 保存优化后的模型
        joblib.dump(best_xgb, self.output_dir / "speed_xgboost_optimized.joblib")
        joblib.dump(best_rf, self.output_dir / "speed_randomforest_optimized.joblib")
        joblib.dump(scaler, self.output_dir / "speed_scaler_optimized.joblib")
        
        results = {
            'XGBoost': {
                'model': best_xgb,
                'test_r2': xgb_r2,
                'best_params': study_xgb.best_params,
                'target_achieved': xgb_r2 > 0.90
            },
            'RandomForest': {
                'model': best_rf,
                'test_r2': rf_r2,
                'best_params': study_rf.best_params,
                'target_achieved': rf_r2 > 0.90
            }
        }
        
        self.optimized_models['speed'] = results
        return results
    
    def optimize_load_prediction(self, X: pd.DataFrame, y_load: pd.Series) -> Dict:
        """优化载重预测模型"""
        print("\n⚖️  步骤3: 载重预测模型优化 (目标R²>0.85)")
        print("-" * 50)
        
        # 过滤有效数据
        valid_mask = (y_load > 0) & (y_load <= 100) & y_load.notna()
        X_load = X[valid_mask].copy()
        y_load_clean = y_load[valid_mask].copy()
        
        print(f"   📊 载重预测数据: {len(X_load)} 个样本")
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X_load, y_load_clean, test_size=0.2, random_state=42
        )
        
        # 特征标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        self.scalers['load'] = scaler
        
        # XGBoost超参数优化
        print("   🔧 优化XGBoost模型...")
        
        def objective_xgb_load(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 500),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'random_state': 42,
                'n_jobs': -1
            }
            
            model = xgb.XGBRegressor(**params)
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            return r2_score(y_test, y_pred)
        
        study_xgb = optuna.create_study(direction='maximize', sampler=TPESampler())
        study_xgb.optimize(objective_xgb_load, n_trials=30)
        
        # 训练最优XGBoost模型
        best_xgb = xgb.XGBRegressor(**study_xgb.best_params, random_state=42, n_jobs=-1)
        best_xgb.fit(X_train, y_train)
        xgb_pred = best_xgb.predict(X_test)
        xgb_r2 = r2_score(y_test, xgb_pred)
        
        print(f"      XGBoost最优R²: {xgb_r2:.4f}")
        
        # RandomForest超参数优化
        print("   🔧 优化RandomForest模型...")
        
        def objective_rf_load(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 300),
                'max_depth': trial.suggest_int('max_depth', 5, 20),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 10),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 5),
                'random_state': 42,
                'n_jobs': -1
            }
            
            model = RandomForestRegressor(**params)
            model.fit(X_train_scaled, y_train)
            y_pred = model.predict(X_test_scaled)
            return r2_score(y_test, y_pred)
        
        study_rf = optuna.create_study(direction='maximize', sampler=TPESampler())
        study_rf.optimize(objective_rf_load, n_trials=30)
        
        # 训练最优RandomForest模型
        best_rf = RandomForestRegressor(**study_rf.best_params, random_state=42, n_jobs=-1)
        best_rf.fit(X_train_scaled, y_train)
        rf_pred = best_rf.predict(X_test_scaled)
        rf_r2 = r2_score(y_test, rf_pred)
        
        print(f"      RandomForest最优R²: {rf_r2:.4f}")
        
        # 保存优化后的模型
        joblib.dump(best_xgb, self.output_dir / "load_xgboost_optimized.joblib")
        joblib.dump(best_rf, self.output_dir / "load_randomforest_optimized.joblib")
        joblib.dump(scaler, self.output_dir / "load_scaler_optimized.joblib")
        
        results = {
            'XGBoost': {
                'model': best_xgb,
                'test_r2': xgb_r2,
                'best_params': study_xgb.best_params,
                'target_achieved': xgb_r2 > 0.85
            },
            'RandomForest': {
                'model': best_rf,
                'test_r2': rf_r2,
                'best_params': study_rf.best_params,
                'target_achieved': rf_r2 > 0.85
            }
        }
        
        self.optimized_models['load'] = results
        return results

    def optimize_axle_classification(self, X: pd.DataFrame, y_axle: pd.Series) -> Dict:
        """优化轴型分类模型"""
        print("\n🚛 步骤4: 轴型分类模型优化 (目标准确率>90%)")
        print("-" * 50)

        # 过滤有效数据
        valid_mask = y_axle.isin([2, 3, 4, 5, 6]) & y_axle.notna()
        X_axle = X[valid_mask].copy()
        y_axle_clean = y_axle[valid_mask].copy()

        print(f"   📊 轴型分类数据: {len(X_axle)} 个样本")

        # 标签编码
        label_encoder = LabelEncoder()
        y_axle_encoded = label_encoder.fit_transform(y_axle_clean)
        self.encoders['axle'] = label_encoder

        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X_axle, y_axle_encoded, test_size=0.2, random_state=42, stratify=y_axle_encoded
        )

        # 特征标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        self.scalers['axle'] = scaler

        # XGBoost超参数优化
        print("   🔧 优化XGBoost模型...")

        def objective_xgb_axle(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 500),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'random_state': 42,
                'n_jobs': -1
            }

            model = xgb.XGBClassifier(**params)
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            return accuracy_score(y_test, y_pred)

        study_xgb = optuna.create_study(direction='maximize', sampler=TPESampler())
        study_xgb.optimize(objective_xgb_axle, n_trials=30)

        # 训练最优XGBoost模型
        best_xgb = xgb.XGBClassifier(**study_xgb.best_params, random_state=42, n_jobs=-1)
        best_xgb.fit(X_train, y_train)
        xgb_pred = best_xgb.predict(X_test)
        xgb_accuracy = accuracy_score(y_test, xgb_pred)

        print(f"      XGBoost最优准确率: {xgb_accuracy:.4f}")

        # RandomForest超参数优化
        print("   🔧 优化RandomForest模型...")

        def objective_rf_axle(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 300),
                'max_depth': trial.suggest_int('max_depth', 5, 20),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 10),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 5),
                'random_state': 42,
                'n_jobs': -1
            }

            model = RandomForestClassifier(**params)
            model.fit(X_train_scaled, y_train)
            y_pred = model.predict(X_test_scaled)
            return accuracy_score(y_test, y_pred)

        study_rf = optuna.create_study(direction='maximize', sampler=TPESampler())
        study_rf.optimize(objective_rf_axle, n_trials=30)

        # 训练最优RandomForest模型
        best_rf = RandomForestClassifier(**study_rf.best_params, random_state=42, n_jobs=-1)
        best_rf.fit(X_train_scaled, y_train)
        rf_pred = best_rf.predict(X_test_scaled)
        rf_accuracy = accuracy_score(y_test, rf_pred)

        print(f"      RandomForest最优准确率: {rf_accuracy:.4f}")

        # 保存优化后的模型
        joblib.dump(best_xgb, self.output_dir / "axle_xgboost_optimized.joblib")
        joblib.dump(best_rf, self.output_dir / "axle_randomforest_optimized.joblib")
        joblib.dump(scaler, self.output_dir / "axle_scaler_optimized.joblib")
        joblib.dump(label_encoder, self.output_dir / "axle_encoder_optimized.joblib")

        results = {
            'XGBoost': {
                'model': best_xgb,
                'test_accuracy': xgb_accuracy,
                'best_params': study_xgb.best_params,
                'target_achieved': xgb_accuracy > 0.90
            },
            'RandomForest': {
                'model': best_rf,
                'test_accuracy': rf_accuracy,
                'best_params': study_rf.best_params,
                'target_achieved': rf_accuracy > 0.90
            }
        }

        self.optimized_models['axle'] = results
        return results

    def create_ensemble_models(self, X: pd.DataFrame, y_speed: pd.Series, y_load: pd.Series, y_axle: pd.Series) -> Dict:
        """创建集成学习模型"""
        print("\n🎯 步骤5: 创建集成学习模型")
        print("-" * 50)

        ensemble_results = {}

        # 速度预测集成模型
        if 'speed' in self.optimized_models:
            print("   🚗 创建速度预测集成模型...")

            valid_mask = (y_speed > 0) & (y_speed <= 150) & y_speed.notna()
            X_speed = X[valid_mask].copy()
            y_speed_clean = y_speed[valid_mask].copy()

            X_train, X_test, y_train, y_test = train_test_split(
                X_speed, y_speed_clean, test_size=0.2, random_state=42
            )

            scaler = self.scalers['speed']
            X_train_scaled = scaler.transform(X_train)
            X_test_scaled = scaler.transform(X_test)

            # Voting回归器
            voting_regressor = VotingRegressor([
                ('xgb', self.optimized_models['speed']['XGBoost']['model']),
                ('rf', self.optimized_models['speed']['RandomForest']['model'])
            ])

            # 训练集成模型
            voting_regressor.fit(X_train, y_train)
            ensemble_pred = voting_regressor.predict(X_test)
            ensemble_r2 = r2_score(y_test, ensemble_pred)

            print(f"      集成模型R²: {ensemble_r2:.4f}")

            # 保存集成模型
            joblib.dump(voting_regressor, self.output_dir / "speed_ensemble_voting.joblib")

            ensemble_results['speed'] = {
                'ensemble_r2': ensemble_r2,
                'target_achieved': ensemble_r2 > 0.90
            }

        # 载重预测集成模型
        if 'load' in self.optimized_models:
            print("   ⚖️  创建载重预测集成模型...")

            valid_mask = (y_load > 0) & (y_load <= 100) & y_load.notna()
            X_load = X[valid_mask].copy()
            y_load_clean = y_load[valid_mask].copy()

            X_train, X_test, y_train, y_test = train_test_split(
                X_load, y_load_clean, test_size=0.2, random_state=42
            )

            # Voting回归器
            voting_regressor = VotingRegressor([
                ('xgb', self.optimized_models['load']['XGBoost']['model']),
                ('rf', self.optimized_models['load']['RandomForest']['model'])
            ])

            voting_regressor.fit(X_train, y_train)
            ensemble_pred = voting_regressor.predict(X_test)
            ensemble_r2 = r2_score(y_test, ensemble_pred)

            print(f"      集成模型R²: {ensemble_r2:.4f}")

            joblib.dump(voting_regressor, self.output_dir / "load_ensemble_voting.joblib")

            ensemble_results['load'] = {
                'ensemble_r2': ensemble_r2,
                'target_achieved': ensemble_r2 > 0.85
            }

        # 轴型分类集成模型
        if 'axle' in self.optimized_models:
            print("   🚛 创建轴型分类集成模型...")

            valid_mask = y_axle.isin([2, 3, 4, 5, 6]) & y_axle.notna()
            X_axle = X[valid_mask].copy()
            y_axle_clean = y_axle[valid_mask].copy()

            label_encoder = self.encoders['axle']
            y_axle_encoded = label_encoder.transform(y_axle_clean)

            X_train, X_test, y_train, y_test = train_test_split(
                X_axle, y_axle_encoded, test_size=0.2, random_state=42, stratify=y_axle_encoded
            )

            # Voting分类器
            voting_classifier = VotingClassifier([
                ('xgb', self.optimized_models['axle']['XGBoost']['model']),
                ('rf', self.optimized_models['axle']['RandomForest']['model'])
            ], voting='hard')

            voting_classifier.fit(X_train, y_train)
            ensemble_pred = voting_classifier.predict(X_test)
            ensemble_accuracy = accuracy_score(y_test, ensemble_pred)

            print(f"      集成模型准确率: {ensemble_accuracy:.4f}")

            joblib.dump(voting_classifier, self.output_dir / "axle_ensemble_voting.joblib")

            ensemble_results['axle'] = {
                'ensemble_accuracy': ensemble_accuracy,
                'target_achieved': ensemble_accuracy > 0.90
            }

        self.ensemble_models = ensemble_results
        return ensemble_results

    def generate_final_report(self, speed_results: Dict, load_results: Dict, axle_results: Dict, ensemble_results: Dict) -> Dict:
        """生成最终优化报告"""
        print("\n📋 步骤6: 生成最终优化报告")
        print("-" * 50)

        report = {
            "optimization_summary": {
                "timestamp": datetime.now().isoformat(),
                "data_file": self.data_file,
                "optimization_method": "Optuna贝叶斯优化 + 集成学习",
                "trials_per_model": 30
            },
            "optimized_models": {
                "speed_prediction": speed_results,
                "load_prediction": load_results,
                "axle_classification": axle_results
            },
            "ensemble_models": ensemble_results,
            "final_performance": {}
        }

        # 计算最终性能
        best_speed_r2 = 0
        best_load_r2 = 0
        best_axle_accuracy = 0

        # 速度预测最佳性能
        if speed_results:
            for model_result in speed_results.values():
                if model_result['test_r2'] > best_speed_r2:
                    best_speed_r2 = model_result['test_r2']

        if 'speed' in ensemble_results:
            if ensemble_results['speed']['ensemble_r2'] > best_speed_r2:
                best_speed_r2 = ensemble_results['speed']['ensemble_r2']

        # 载重预测最佳性能
        if load_results:
            for model_result in load_results.values():
                if model_result['test_r2'] > best_load_r2:
                    best_load_r2 = model_result['test_r2']

        if 'load' in ensemble_results:
            if ensemble_results['load']['ensemble_r2'] > best_load_r2:
                best_load_r2 = ensemble_results['load']['ensemble_r2']

        # 轴型分类最佳性能
        if axle_results:
            for model_result in axle_results.values():
                if model_result['test_accuracy'] > best_axle_accuracy:
                    best_axle_accuracy = model_result['test_accuracy']

        if 'axle' in ensemble_results:
            if ensemble_results['axle']['ensemble_accuracy'] > best_axle_accuracy:
                best_axle_accuracy = ensemble_results['axle']['ensemble_accuracy']

        report["final_performance"] = {
            "best_speed_r2": float(best_speed_r2),
            "speed_target_achieved": best_speed_r2 > 0.90,
            "best_load_r2": float(best_load_r2),
            "load_target_achieved": best_load_r2 > 0.85,
            "best_axle_accuracy": float(best_axle_accuracy),
            "axle_target_achieved": best_axle_accuracy > 0.90,
            "all_targets_achieved": (best_speed_r2 > 0.90) and (best_load_r2 > 0.85) and (best_axle_accuracy > 0.90)
        }

        # 保存报告
        report_path = self.output_dir / "ml_optimization_final_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        # 打印最终摘要
        print(f"   🎯 最终性能摘要:")
        print(f"      速度预测最佳R²: {best_speed_r2:.4f} {'✅' if best_speed_r2 > 0.90 else '❌'} (目标: >0.90)")
        print(f"      载重预测最佳R²: {best_load_r2:.4f} {'✅' if best_load_r2 > 0.85 else '❌'} (目标: >0.85)")
        print(f"      轴型分类最佳准确率: {best_axle_accuracy:.4f} {'✅' if best_axle_accuracy > 0.90 else '❌'} (目标: >90%)")
        print(f"      所有目标达成: {'✅' if report['final_performance']['all_targets_achieved'] else '❌'}")
        print(f"   📋 最终报告已保存: {report_path}")

        return report

def main():
    """主函数"""
    optimizer = MLPerformanceOptimizer()
    report = optimizer.optimize_all_models()

    print(f"\n🎉 机器学习性能优化完成！")
    print(f"   所有目标达成: {'✅' if report['final_performance']['all_targets_achieved'] else '❌'}")

    return report

if __name__ == "__main__":
    main()
