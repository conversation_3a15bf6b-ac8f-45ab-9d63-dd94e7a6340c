#!/usr/bin/env python3
"""
快速开始演示脚本
演示完整的端到端数据处理流程
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_demo_directory_structure():
    """创建演示目录结构"""
    logger.info("📁 创建演示目录结构...")
    
    directories = [
        './raw_data/vibration_signals',
        './raw_data/speed_labels',
        './raw_data/load_labels',
        './raw_data/type_labels',
        './training_datasets',
        './models',
        './results/logs',
        './results/reports',
        './results/plots'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"✅ 目录已创建: {directory}")

def generate_demo_vibration_signals(n_files: int = 3, n_samples: int = 1000):
    """生成演示振动信号数据"""
    logger.info(f"🎭 生成 {n_files} 个演示振动信号文件...")
    
    np.random.seed(42)
    
    for i in range(n_files):
        # 生成时间戳
        start_time = datetime(2024, 12, 7, 14, 30, 0) + timedelta(minutes=i*5)
        time_stamps = pd.date_range(start_time, periods=n_samples, freq='10ms')
        
        # 模拟不同车辆的振动特征
        if i == 0:  # 轿车
            base_amplitude = 0.05
            freq_factor = 1.0
            speed = 60 + np.random.normal(0, 5)
            load = 1.5 + np.random.normal(0, 0.2)
            vehicle_type = 0  # passenger_car
        elif i == 1:  # 轻型卡车
            base_amplitude = 0.08
            freq_factor = 1.2
            speed = 55 + np.random.normal(0, 8)
            load = 3.5 + np.random.normal(0, 0.5)
            vehicle_type = 1  # light_truck
        else:  # 重型卡车
            base_amplitude = 0.12
            freq_factor = 0.8
            speed = 45 + np.random.normal(0, 10)
            load = 8.5 + np.random.normal(0, 1.0)
            vehicle_type = 2  # heavy_truck
        
        # 生成振动信号
        t = np.linspace(0, n_samples * 0.01, n_samples)
        
        # 基础振动 + 车辆特征 + 噪声
        sensor_1_x = (base_amplitude * np.sin(2 * np.pi * 10 * freq_factor * t) + 
                     0.02 * np.sin(2 * np.pi * 50 * freq_factor * t) +
                     np.random.normal(0, 0.01, n_samples))
        
        sensor_1_y = (base_amplitude * 0.8 * np.cos(2 * np.pi * 12 * freq_factor * t) + 
                     0.015 * np.cos(2 * np.pi * 45 * freq_factor * t) +
                     np.random.normal(0, 0.008, n_samples))
        
        sensor_1_z = (base_amplitude * 1.2 * np.sin(2 * np.pi * 8 * freq_factor * t) + 
                     0.025 * np.sin(2 * np.pi * 35 * freq_factor * t) +
                     np.random.normal(0, 0.012, n_samples))
        
        sensor_2_x = (base_amplitude * 0.9 * np.sin(2 * np.pi * 11 * freq_factor * t) + 
                     0.018 * np.sin(2 * np.pi * 48 * freq_factor * t) +
                     np.random.normal(0, 0.009, n_samples))
        
        sensor_2_y = (base_amplitude * 0.7 * np.cos(2 * np.pi * 13 * freq_factor * t) + 
                     0.012 * np.cos(2 * np.pi * 42 * freq_factor * t) +
                     np.random.normal(0, 0.007, n_samples))
        
        sensor_2_z = (base_amplitude * 1.1 * np.sin(2 * np.pi * 9 * freq_factor * t) + 
                     0.022 * np.sin(2 * np.pi * 38 * freq_factor * t) +
                     np.random.normal(0, 0.011, n_samples))
        
        # 环境数据
        temperature = np.random.normal(23.5, 1.0, n_samples)
        humidity = np.random.normal(45.0, 5.0, n_samples)
        
        # 创建DataFrame
        data = {
            'timestamp': time_stamps,
            'sensor_1_x': sensor_1_x,
            'sensor_1_y': sensor_1_y,
            'sensor_1_z': sensor_1_z,
            'sensor_2_x': sensor_2_x,
            'sensor_2_y': sensor_2_y,
            'sensor_2_z': sensor_2_z,
            'temperature': temperature,
            'humidity': humidity
        }
        
        df = pd.DataFrame(data)
        
        # 保存文件
        filename = f"vibration_{start_time.strftime('%Y%m%d_%H%M%S')}_sensor{i+1:02d}.csv"
        filepath = os.path.join('./raw_data/vibration_signals', filename)
        df.to_csv(filepath, index=False)
        
        logger.info(f"✅ 振动信号文件已生成: {filename}")
        
        # 生成对应的标签数据
        vehicle_id = f"VH{i+1:03d}"
        
        # 速度标签
        speed_data = {
            'timestamp': [start_time],
            'vehicle_id': [vehicle_id],
            'speed_kmh': [speed],
            'confidence': [0.95]
        }
        speed_df = pd.DataFrame(speed_data)
        
        if i == 0:
            speed_df.to_csv('./raw_data/speed_labels/speed_labels.csv', index=False)
        else:
            speed_df.to_csv('./raw_data/speed_labels/speed_labels.csv', mode='a', header=False, index=False)
        
        # 轴重标签
        load_data = {
            'timestamp': [start_time],
            'vehicle_id': [vehicle_id],
            'axle_1_load': [load * 0.4],
            'axle_2_load': [load * 0.6],
            'total_load': [load],
            'confidence': [0.92]
        }
        load_df = pd.DataFrame(load_data)
        
        if i == 0:
            load_df.to_csv('./raw_data/load_labels/load_labels.csv', index=False)
        else:
            load_df.to_csv('./raw_data/load_labels/load_labels.csv', mode='a', header=False, index=False)
        
        # 轴型标签
        vehicle_classes = ['passenger_car', 'light_truck', 'heavy_truck']
        axle_counts = [2, 2, 3]
        
        type_data = {
            'timestamp': [start_time],
            'vehicle_id': [vehicle_id],
            'axle_type': [vehicle_type],
            'axle_count': [axle_counts[i]],
            'vehicle_class': [vehicle_classes[i]],
            'confidence': [0.96]
        }
        type_df = pd.DataFrame(type_data)
        
        if i == 0:
            type_df.to_csv('./raw_data/type_labels/type_labels.csv', index=False)
        else:
            type_df.to_csv('./raw_data/type_labels/type_labels.csv', mode='a', header=False, index=False)
    
    logger.info(f"✅ 演示数据生成完成")

def run_demo_pipeline():
    """运行演示管道"""
    logger.info("🚀 开始演示管道...")
    
    print("🚀 深度学习增强振动信号分析系统 - 快速演示")
    print("=" * 70)
    print("本演示将展示完整的端到端数据处理流程")
    print("=" * 70)
    
    # 步骤1: 创建目录结构
    print("\n📁 步骤1: 创建目录结构")
    print("-" * 40)
    create_demo_directory_structure()
    print("✅ 目录结构创建完成")
    
    # 步骤2: 生成演示数据
    print("\n🎭 步骤2: 生成演示数据")
    print("-" * 40)
    generate_demo_vibration_signals()
    print("✅ 演示数据生成完成")
    
    # 步骤3: 数据验证
    print("\n🔍 步骤3: 数据验证")
    print("-" * 40)
    try:
        import subprocess
        result = subprocess.run(['python', 'data_validation.py'], 
                              capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ 数据验证通过")
        else:
            print("⚠️  数据验证有警告，但可以继续")
    except Exception as e:
        print(f"⚠️  数据验证跳过: {str(e)}")
    
    # 步骤4: 数据预处理
    print("\n🔄 步骤4: 数据预处理")
    print("-" * 40)
    try:
        import subprocess
        result = subprocess.run(['python', 'data_preprocessing.py'], 
                              capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print("✅ 数据预处理完成")
            
            # 检查生成的数据集
            datasets = ['speed_regression.csv', 'load_regression.csv', 'type_classification.csv']
            for dataset in datasets:
                dataset_path = os.path.join('./training_datasets', dataset)
                if os.path.exists(dataset_path):
                    df = pd.read_csv(dataset_path)
                    print(f"   📊 {dataset}: {len(df)} 条记录")
        else:
            print("❌ 数据预处理失败")
            print(f"错误: {result.stderr}")
    except Exception as e:
        print(f"❌ 数据预处理异常: {str(e)}")
    
    # 步骤5: 快速模型训练演示
    print("\n🤖 步骤5: 快速模型训练演示")
    print("-" * 40)
    
    # 使用现有数据集进行快速训练演示
    try:
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import r2_score
        from sklearn.preprocessing import StandardScaler
        
        # 检查是否有训练数据
        speed_file = './training_datasets/speed_regression.csv'
        if os.path.exists(speed_file):
            df = pd.read_csv(speed_file)
            
            # 准备数据
            feature_columns = [col for col in df.columns if col not in ['timestamp', 'speed_kmh']]
            if len(feature_columns) > 0 and 'speed_kmh' in df.columns:
                X = df[feature_columns].fillna(0)
                y = df['speed_kmh']
                
                # 分割数据
                X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
                
                # 标准化
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)
                
                # 训练模型
                model = RandomForestRegressor(n_estimators=50, random_state=42)
                model.fit(X_train_scaled, y_train)
                
                # 预测和评估
                y_pred = model.predict(X_test_scaled)
                r2 = r2_score(y_test, y_pred)
                
                print(f"   🎯 随机森林模型 R² = {r2:.4f}")
                
                if r2 > 0.5:
                    print("   ✅ 模型训练成功，性能良好")
                else:
                    print("   📈 模型性能一般，实际应用中需要更多数据和优化")
            else:
                print("   ⚠️  训练数据不足，跳过模型训练")
        else:
            print("   ⚠️  未找到训练数据，跳过模型训练")
            
    except Exception as e:
        print(f"   ❌ 快速训练失败: {str(e)}")
    
    # 步骤6: 总结
    print("\n🎉 演示完成总结")
    print("-" * 40)
    print("✅ 目录结构已创建")
    print("✅ 演示数据已生成")
    print("✅ 数据预处理流程已演示")
    print("✅ 快速模型训练已演示")
    
    print(f"\n📁 生成的文件:")
    print(f"   原始数据: ./raw_data/")
    print(f"   训练数据: ./training_datasets/")
    print(f"   结果目录: ./results/")
    
    print(f"\n💡 下一步操作:")
    print(f"   1. 运行完整训练: python run_complete_training.py")
    print(f"   2. 分析结果: python analyze_results.py")
    print(f"   3. 部署模型: python deploy_models.py")
    print(f"   4. 查看完整指南: COMPLETE_USER_GUIDE.md")
    
    print(f"\n🚀 系统已准备就绪，可以开始使用!")

def main():
    """主函数"""
    try:
        run_demo_pipeline()
    except KeyboardInterrupt:
        print("\n⚠️  用户中断演示")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {str(e)}")
        logger.error(f"演示失败: {str(e)}")

if __name__ == "__main__":
    main()
