#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnostic Script for Sensor Waveform Analyzer Data Compatibility
Checks and verifies data format compatibility and processing consistency

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import sys
import numpy as np
import pandas as pd
from pathlib import Path
import glob

def create_test_22_column_data():
    """Create test data in 22-column format (count + 20 sensors + timestamp)"""
    print("🔧 Creating test 22-column format data...")
    
    try:
        # Parameters for realistic vehicle passage simulation
        duration = 5.0  # seconds (longer to test extraction)
        sampling_rate = 1000  # Hz
        n_samples = int(duration * sampling_rate)
        
        # Create realistic vehicle passage event
        np.random.seed(42)
        
        # Vehicle parameters
        vehicle_speed = 72  # km/h
        axle_count = 3
        axle_weights = [8, 12, 10]  # tons per axle
        axle_spacing = 0.15  # seconds between axles
        
        # Event timing (vehicle passes around 2.5 seconds)
        event_center = 2.5
        
        # Create data structure: count + 20 sensors + timestamp
        data = {}
        
        # Column 1: count
        data['count'] = range(n_samples)
        
        # Columns 2-21: Sensor_01 to Sensor_20
        for sensor_id in range(1, 21):
            # Initialize with background noise
            signal = np.random.normal(0, 0.3, n_samples)
            
            # Add vehicle passage event
            for axle_idx, (axle_time_offset, axle_weight) in enumerate(zip(
                [0, axle_spacing, 2*axle_spacing], axle_weights)):
                
                # Axle passage time
                axle_time = event_center + axle_time_offset
                axle_sample = int(axle_time * sampling_rate)
                
                # Create realistic axle response
                response_duration = 0.2  # seconds
                response_samples = int(response_duration * sampling_rate)
                
                start_idx = max(0, axle_sample - response_samples//2)
                end_idx = min(n_samples, axle_sample + response_samples//2)
                
                # Generate damped oscillation response
                t_response = np.linspace(0, response_duration, end_idx - start_idx)
                
                # Sensor-specific frequency response
                if sensor_id <= 5:  # Group 1
                    frequency = 45 + sensor_id * 2
                    depth_factor = 1.2  # Deeper sensors, stronger response
                elif sensor_id <= 10:  # Group 2
                    frequency = 55 + (sensor_id-5) * 2
                    depth_factor = 1.2
                elif sensor_id <= 15:  # Group 3
                    frequency = 40 + (sensor_id-10) * 2
                    depth_factor = 1.0  # Shallower sensors
                else:  # Group 4
                    frequency = 50 + (sensor_id-15) * 2
                    depth_factor = 1.0
                
                # Amplitude based on axle weight and sensor characteristics
                base_amplitude = axle_weight * 0.8 * depth_factor
                
                # Add some sensor-specific variation
                if sensor_id in [6, 16]:  # Overtaking lane sensors
                    base_amplitude *= 0.6  # Weaker response
                
                # Damped sinusoidal response
                damping = 8.0
                envelope = np.exp(-damping * t_response)
                axle_response = base_amplitude * envelope * np.sin(2 * np.pi * frequency * t_response)
                
                # Add to signal
                signal[start_idx:end_idx] += axle_response
            
            # Store sensor data
            data[f'Sensor_{sensor_id:02d}'] = signal
        
        # Column 22: timestamp
        timestamps = pd.date_range('2024-01-01 10:00:00', periods=n_samples, freq='1ms')
        data['timestamp'] = timestamps
        
        # Create DataFrame and save
        df = pd.DataFrame(data)
        test_file_path = 'test_22_column_data.csv'
        df.to_csv(test_file_path, index=False)
        
        print(f"   ✅ Test 22-column data created: {test_file_path}")
        print(f"   📊 Data shape: {df.shape}")
        print(f"   📋 Columns: {list(df.columns)}")
        print(f"   ⏱️ Duration: {duration} seconds")
        print(f"   📈 Sampling rate: {sampling_rate} Hz")
        print(f"   🔢 Format: count + 20 sensors + timestamp")
        
        return test_file_path, df
        
    except Exception as e:
        print(f"   ❌ Error creating test 22-column data: {str(e)}")
        return None, None

def diagnose_data_format_compatibility():
    """Diagnose data format compatibility issues"""
    print("\n🔍 Diagnosing data format compatibility...")
    
    try:
        # Create test data
        test_file, test_df = create_test_22_column_data()
        if test_file is None:
            return False
        
        # Test 1: Check column detection
        print("\n   📋 Test 1: Column Detection")
        print(f"      Data shape: {test_df.shape}")
        print(f"      Expected: (5000, 22) - count + 20 sensors + timestamp")
        
        # Check for sensor columns
        sensor_columns = [col for col in test_df.columns if 'Sensor_' in col]
        print(f"      Sensor columns found: {len(sensor_columns)}")
        print(f"      Expected: 20 sensor columns")
        print(f"      Sensor columns: {sensor_columns[:5]}...{sensor_columns[-5:]}")
        
        # Check for other columns
        other_columns = [col for col in test_df.columns if 'Sensor_' not in col]
        print(f"      Other columns: {other_columns}")
        print(f"      Expected: ['count', 'timestamp']")
        
        # Test 2: Check data types
        print("\n   🔢 Test 2: Data Types")
        for col in test_df.columns:
            dtype = test_df[col].dtype
            if 'Sensor_' in col:
                print(f"      {col}: {dtype} (should be float64)")
            else:
                print(f"      {col}: {dtype}")
        
        # Test 3: Check data ranges
        print("\n   📊 Test 3: Data Ranges")
        for sensor_col in sensor_columns[:3]:  # Check first 3 sensors
            data_min = test_df[sensor_col].min()
            data_max = test_df[sensor_col].max()
            data_std = test_df[sensor_col].std()
            print(f"      {sensor_col}: min={data_min:.3f}, max={data_max:.3f}, std={data_std:.3f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Data format compatibility diagnosis failed: {str(e)}")
        return False

def diagnose_sensor_analyzer_compatibility():
    """Diagnose sensor analyzer compatibility with 22-column data"""
    print("\n🔍 Diagnosing sensor analyzer compatibility...")
    
    try:
        from sensor_waveform_analyzer import SensorWaveformAnalyzer
        
        # Create test data
        test_file, test_df = create_test_22_column_data()
        if test_file is None:
            return False
        
        # Initialize analyzer
        analyzer = SensorWaveformAnalyzer(
            output_dir='test_diagnosis_charts',
            file_prefix='diagnosis_test_'
        )
        
        # Test 1: Data loading
        print("\n   📂 Test 1: Data Loading")
        try:
            success = analyzer.load_sensor_waveform_data(test_file, 'Sensor_01')
            if success:
                print("      ✅ Data loading successful")
                print(f"      📊 Loaded data points: {len(analyzer.sensor_data)}")
                print(f"      ⏱️ Time duration: {analyzer.time_vector[-1]:.3f} seconds")
                print(f"      📈 Data range: {np.min(analyzer.sensor_data):.3f} to {np.max(analyzer.sensor_data):.3f}")
            else:
                print("      ❌ Data loading failed")
                return False
        except Exception as e:
            print(f"      ❌ Data loading exception: {str(e)}")
            return False
        
        # Test 2: Vehicle passage detection
        print("\n   🚗 Test 2: Vehicle Passage Detection")
        if analyzer.sensor_data is not None:
            expected_length = 1000  # 1 second at 1000 Hz
            actual_length = len(analyzer.sensor_data)
            
            print(f"      Expected data length: {expected_length} points (1 second)")
            print(f"      Actual data length: {actual_length} points")
            print(f"      Duration: {analyzer.time_vector[-1]:.3f} seconds")
            
            if 800 <= actual_length <= 1200:  # Allow some tolerance
                print("      ✅ Data extraction length is reasonable")
            else:
                print("      ⚠️ Data extraction length may be incorrect")
                print("      This suggests vehicle passage detection may not be working properly")
        
        # Test 3: Feature extraction
        print("\n   🔧 Test 3: Feature Extraction")
        try:
            success = analyzer.extract_30_features()
            if success:
                print("      ✅ Feature extraction successful")
                print(f"      📊 Features extracted: {len(analyzer.features)}")
                if len(analyzer.features) == 30:
                    print("      ✅ Correct number of features (30)")
                else:
                    print(f"      ⚠️ Expected 30 features, got {len(analyzer.features)}")
            else:
                print("      ❌ Feature extraction failed")
        except Exception as e:
            print(f"      ❌ Feature extraction exception: {str(e)}")
        
        # Test 4: Visualization generation
        print("\n   📊 Test 4: Visualization Generation")
        try:
            waveform_success = analyzer.generate_waveform_plot()
            if waveform_success:
                print("      ✅ Waveform plot generation successful")
            else:
                print("      ❌ Waveform plot generation failed")
            
            if analyzer.features:
                features_success = analyzer.generate_features_visualization()
                if features_success:
                    print("      ✅ Features visualization successful")
                else:
                    print("      ❌ Features visualization failed")
        except Exception as e:
            print(f"      ❌ Visualization generation exception: {str(e)}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Cannot import sensor_waveform_analyzer: {str(e)}")
        return False
    except Exception as e:
        print(f"   ❌ Sensor analyzer compatibility diagnosis failed: {str(e)}")
        return False

def check_existing_data_files():
    """Check existing data files for format compatibility"""
    print("\n🔍 Checking existing data files...")
    
    try:
        # Look for existing data files
        data_patterns = [
            'data/*.csv',
            '*.csv',
            '**/GW*.csv',
            '**/*AcceData*.csv'
        ]
        
        found_files = []
        for pattern in data_patterns:
            files = glob.glob(pattern, recursive=True)
            found_files.extend(files)
        
        # Remove duplicates
        found_files = list(set(found_files))
        
        if not found_files:
            print("   ⚠️ No existing data files found")
            return False
        
        print(f"   📁 Found {len(found_files)} data files")
        
        # Check first few files
        for i, file_path in enumerate(found_files[:3]):
            print(f"\n   📄 File {i+1}: {file_path}")
            try:
                df = pd.read_csv(file_path, nrows=5)  # Read only first 5 rows
                print(f"      Shape: {df.shape}")
                print(f"      Columns: {list(df.columns)}")
                
                # Check if it's 22-column format
                sensor_cols = [col for col in df.columns if 'Sensor_' in col]
                other_cols = [col for col in df.columns if 'Sensor_' not in col]
                
                print(f"      Sensor columns: {len(sensor_cols)}")
                print(f"      Other columns: {other_cols}")
                
                if len(sensor_cols) == 20 and len(df.columns) == 22:
                    print("      ✅ Appears to be 22-column format")
                elif len(sensor_cols) == 20 and len(df.columns) == 21:
                    print("      ⚠️ Appears to be 21-column format (old)")
                else:
                    print("      ❓ Unknown format")
                    
            except Exception as e:
                print(f"      ❌ Error reading file: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error checking existing data files: {str(e)}")
        return False

def provide_compatibility_recommendations():
    """Provide recommendations for compatibility issues"""
    print("\n💡 Compatibility Recommendations:")
    print("=" * 60)
    
    print("\n1. **Data Format Verification**:")
    print("   - Ensure your data files have exactly 22 columns")
    print("   - Format: count + Sensor_01 to Sensor_20 + timestamp")
    print("   - Check column names match exactly: 'Sensor_01', 'Sensor_02', etc.")
    
    print("\n2. **Sensor Column Detection**:")
    print("   - The program looks for columns containing 'Sensor_'")
    print("   - Ensure sensor columns are named 'Sensor_01' to 'Sensor_20'")
    print("   - Avoid variations like 'sensor_01' or 'SENSOR_01'")
    
    print("\n3. **Vehicle Passage Detection**:")
    print("   - The program needs at least 2 valid sensors for fusion")
    print("   - Sensors must have >100 data points and std > 1e-6")
    print("   - Check if your data contains actual vehicle passage events")
    
    print("\n4. **Data Quality Requirements**:")
    print("   - Sampling rate should be 1000 Hz")
    print("   - Data should contain clear vehicle passage signatures")
    print("   - Avoid files with all-zero or constant values")
    
    print("\n5. **Troubleshooting Steps**:")
    print("   - Run this diagnostic script with your actual data files")
    print("   - Check console output for specific error messages")
    print("   - Verify data file paths and permissions")
    print("   - Ensure all required Python packages are installed")

def cleanup_test_files():
    """Clean up test files"""
    print("\n🧹 Cleaning up test files...")
    
    try:
        # Remove test data file
        test_file = 'test_22_column_data.csv'
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"   ✅ Removed: {test_file}")
        
        # Remove test chart directory
        import shutil
        test_dir = Path('test_diagnosis_charts')
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"   ✅ Removed: {test_dir}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Cleanup failed: {str(e)}")
        return False

def main():
    """Main diagnostic function"""
    print("🔍 Sensor Waveform Analyzer Data Compatibility Diagnosis")
    print("=" * 80)
    
    # Run all diagnostic tests
    tests = [
        ("Data Format Compatibility", diagnose_data_format_compatibility),
        ("Sensor Analyzer Compatibility", diagnose_sensor_analyzer_compatibility),
        ("Existing Data Files Check", check_existing_data_files)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 Diagnostic Results Summary")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    # Provide recommendations
    provide_compatibility_recommendations()
    
    # Cleanup
    cleanup_test_files()
    
    if passed == total:
        print("\n🎉 All compatibility tests passed!")
        print("Your sensor_waveform_analyzer.py should work correctly with 22-column data.")
    elif passed >= total * 0.5:
        print("\n⚠️ Some compatibility issues detected.")
        print("Please review the recommendations above.")
    else:
        print("\n❌ Multiple compatibility issues detected.")
        print("Please check your data format and program configuration.")
    
    return passed >= total * 0.5

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
