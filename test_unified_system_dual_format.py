#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的unified_vibration_analysis.py系统
验证是否能够同时处理两种格式的振动数据

作者: AI Assistant
日期: 2024-12-22
"""

import os
import pandas as pd
import sys

def test_dual_format_processing():
    """测试双格式数据处理"""
    print("🧪 测试修改后的振动信号分析系统")
    print("="*80)
    print("🎯 目标：验证系统能够同时处理两种格式的数据")
    print("   - 新格式数据（22列CSV）：GW100001_datetime_AcceData_车道X_Y轴-Z.Zt-Wkmh.csv")
    print("   - 传统格式数据（21列CSV）：层级目录结构")
    print("-"*80)
    
    try:
        # 导入修改后的系统
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem

        print("\n📦 步骤1：初始化分析器...")
        analyzer = UnifiedVibrationAnalysisSystem(data_dir='./data')

        # 配置测试参数
        analyzer.force_feature_extraction = True  # 强制重新提取特征
        analyzer.optimization_enabled = False     # 禁用优化以加快测试
        analyzer.denoising_enabled = False        # 禁用降噪以加快测试
        analyzer.process_visualization_enabled = False  # 禁用可视化以加快测试
        
        print("✅ 分析器初始化成功")
        
        print("\n📊 步骤2：运行特征提取...")
        features_df = analyzer.extract_features_enhanced()
        
        if features_df is not None and not features_df.empty:
            print(f"✅ 特征提取成功")
            print(f"   数据形状: {features_df.shape}")
            
            # 验证数据源
            if 'data_source' in features_df.columns:
                source_counts = features_df['data_source'].value_counts()
                print(f"   📊 数据源分布:")
                for source, count in source_counts.items():
                    print(f"      {source}: {count} 样本")
                
                # 检查是否包含两种格式的数据
                has_new_format = 'new_format' in source_counts
                has_legacy_format = 'legacy_format' in source_counts
                
                print(f"\n🔍 格式支持验证:")
                print(f"   新格式数据: {'✅' if has_new_format else '❌'}")
                print(f"   传统格式数据: {'✅' if has_legacy_format else '❌'}")
                
                if has_new_format and has_legacy_format:
                    print("🎉 系统成功支持两种数据格式！")
                    return True
                elif has_new_format:
                    print("⚠️  系统仅支持新格式数据")
                    return False
                elif has_legacy_format:
                    print("⚠️  系统仅支持传统格式数据")
                    return False
                else:
                    print("❌ 系统未能识别任何格式的数据")
                    return False
            else:
                print("⚠️  特征数据中缺少data_source列")
                return False
        else:
            print("❌ 特征提取失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def verify_feature_file():
    """验证生成的特征文件"""
    print("\n📋 步骤3：验证生成的特征文件...")
    
    if not os.path.exists('combined_features.csv'):
        print("❌ combined_features.csv 文件不存在")
        return False
    
    try:
        df = pd.read_csv('combined_features.csv')
        print(f"✅ 特征文件读取成功")
        print(f"   文件大小: {os.path.getsize('combined_features.csv') / (1024*1024):.2f} MB")
        print(f"   数据形状: {df.shape}")
        
        # 检查关键列
        required_columns = ['speed_kmh', 'load_tons', 'axle_type', 'data_source']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"⚠️  缺少关键列: {missing_columns}")
            
            # 检查替代列名
            alternative_columns = {
                'load_tons': ['axle_load_tons', 'weight_tons', 'load'],
                'axle_type': ['axle_count', 'axles']
            }
            
            for missing_col in missing_columns:
                if missing_col in alternative_columns:
                    alternatives = [col for col in alternative_columns[missing_col] if col in df.columns]
                    if alternatives:
                        print(f"   找到替代列 {missing_col}: {alternatives}")
        
        # 检查数据源分布
        if 'data_source' in df.columns:
            source_counts = df['data_source'].value_counts()
            print(f"   📊 数据源验证:")
            for source, count in source_counts.items():
                print(f"      {source}: {count} 样本 ({count/len(df)*100:.1f}%)")
        
        # 检查目标变量的有效性
        target_vars = ['speed_kmh', 'load_tons', 'axle_type']
        for var in target_vars:
            if var in df.columns:
                valid_count = df[var].notna().sum()
                print(f"   {var}: {valid_count}/{len(df)} 有效值 ({valid_count/len(df)*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 特征文件验证失败: {str(e)}")
        return False

def check_data_coverage():
    """检查数据覆盖率"""
    print("\n📈 步骤4：检查数据处理覆盖率...")
    
    try:
        import glob
        
        # 统计原始数据文件
        csv_files = glob.glob('./data/**/*.csv', recursive=True)
        
        new_format_files = [f for f in csv_files if 'GW100001' in os.path.basename(f) and 'AcceData' in os.path.basename(f)]
        legacy_format_files = [f for f in csv_files if f not in new_format_files and any(keyword in os.path.basename(f) for keyword in ['acce_', 'sensor', '吨', '轴'])]
        
        print(f"   📁 原始数据文件统计:")
        print(f"      总CSV文件: {len(csv_files)}")
        print(f"      新格式文件: {len(new_format_files)}")
        print(f"      传统格式文件: {len(legacy_format_files)}")
        print(f"      其他文件: {len(csv_files) - len(new_format_files) - len(legacy_format_files)}")
        
        # 检查处理后的样本数
        if os.path.exists('combined_features.csv'):
            df = pd.read_csv('combined_features.csv')
            processed_samples = len(df)
            
            print(f"   📊 处理结果:")
            print(f"      处理后样本数: {processed_samples}")
            
            if len(new_format_files) > 0:
                coverage_rate = processed_samples / len(new_format_files) * 100
                print(f"      覆盖率估算: {coverage_rate:.1f}%")
                
                if coverage_rate >= 80:
                    print("✅ 数据覆盖率良好")
                    return True
                elif coverage_rate >= 50:
                    print("⚠️  数据覆盖率中等")
                    return True
                else:
                    print("❌ 数据覆盖率较低")
                    return False
            else:
                print("⚠️  无法计算覆盖率（未找到新格式文件）")
                return processed_samples > 1000  # 至少处理了1000个样本
        else:
            print("❌ 未找到处理后的特征文件")
            return False
            
    except Exception as e:
        print(f"❌ 数据覆盖率检查失败: {str(e)}")
        return False

def generate_test_report(test_results):
    """生成测试报告"""
    print("\n" + "="*80)
    print("📋 双格式数据处理测试报告")
    print("="*80)
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    print(f"\n📊 测试结果总览:")
    print(f"   总测试项: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   失败测试: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 总体评估
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！系统已成功支持双格式数据处理")
        recommendation = "系统已准备就绪，可以处理两种格式的振动数据"
    elif passed_tests >= total_tests * 0.75:
        print(f"\n✅ 大部分测试通过，系统基本可用")
        recommendation = "系统基本可用，建议修复失败的测试项"
    elif passed_tests >= total_tests * 0.5:
        print(f"\n⚠️  部分测试通过，系统需要改进")
        recommendation = "系统需要进一步改进以完全支持双格式数据"
    else:
        print(f"\n❌ 大部分测试失败，系统需要重大修复")
        recommendation = "系统需要重大修复才能支持双格式数据处理"
    
    print(f"\n💡 建议: {recommendation}")
    
    # 保存测试报告
    report = {
        'test_summary': {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'pass_rate': passed_tests/total_tests*100
        },
        'test_results': test_results,
        'recommendation': recommendation
    }
    
    import json
    with open('dual_format_test_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试报告已保存到: dual_format_test_report.json")
    
    return passed_tests == total_tests

def main():
    """主函数"""
    print("🚀 启动双格式数据处理测试")
    
    # 执行测试
    test_results = {}
    
    # 测试1：双格式数据处理
    test_results['双格式数据处理'] = test_dual_format_processing()
    
    # 测试2：特征文件验证
    test_results['特征文件验证'] = verify_feature_file()
    
    # 测试3：数据覆盖率检查
    test_results['数据覆盖率检查'] = check_data_coverage()
    
    # 生成测试报告
    success = generate_test_report(test_results)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
