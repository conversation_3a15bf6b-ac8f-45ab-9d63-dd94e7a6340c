# 传感器波形分析器使用说明

## 📋 **程序概述**

传感器波形分析器（`sensor_waveform_analyzer.py`）是振动信号分析系统的专用工具，用于绘制指定传感器在车辆通过时的振动数据分析图表，包括时序波形图和30个时频域特征的可视化。

## ✨ **主要功能**

### 🌊 **时序波形图绘制**
- **原始振动波形**: 绘制指定传感器的完整振动时序波形
- **车辆通过分析**: 自动识别车辆通过的关键时刻和峰值点
- **轴型检测**: 检测并标注车辆轴数和轴的位置
- **信号包络**: 显示信号包络和详细视图
- **统计信息**: 显示持续时间、峰值、RMS值等关键统计信息

### 📊 **30个时频域特征可视化**
- **时域特征** (10个): 均值、标准差、方差、RMS、峰值、峰峰值、峰值因子、偏度、峰度、能量
- **频域特征** (10个): 过零率、主频、平均频率、频率标准差、频谱质心、频谱滚降、频谱通量、总功率、低频功率、中频功率
- **时频域特征** (10个): 高频功率、频谱图统计、时间带宽积、小波能量分解等
- **多种图表**: 柱状图、雷达图、相关性热图、特征重要性分析

### 🎨 **高质量可视化输出**
- **学术级质量**: 330 DPI分辨率，Times New Roman字体，英文标签
- **统一管理**: 所有图表保存到unified_charts目录
- **规范命名**: 使用"waveform_analysis_"前缀，便于识别和管理

## 🚀 **使用方法**

### **命令行使用**

#### **基本用法**
```bash
# 分析指定传感器的波形数据
python sensor_waveform_analyzer.py experiment_data.csv Sensor_01

# 分析特定实验的传感器数据
python sensor_waveform_analyzer.py 25吨_三轴_40.0kmh_实验1_merged_data.csv Sensor_05

# 自定义输出目录和文件前缀
python sensor_waveform_analyzer.py data.csv Sensor_10 --output-dir my_charts --prefix my_waveform_
```

#### **参数说明**
- `file_path`: merged_data.csv文件路径（必需）
- `sensor_id`: 传感器ID，如Sensor_01（必需）
- `--output-dir`: 输出目录（默认：unified_charts）
- `--prefix`: 文件前缀（默认：waveform_analysis_）

### **Python代码使用**

```python
from sensor_waveform_analyzer import SensorWaveformAnalyzer

# 创建分析器实例
analyzer = SensorWaveformAnalyzer(
    output_dir='unified_charts',
    file_prefix='waveform_analysis_'
)

# 执行完整波形分析
success = analyzer.analyze_sensor_waveform(
    file_path='25吨_三轴_40.0kmh_实验1_merged_data.csv',
    sensor_id='Sensor_01'
)

if success:
    print("波形分析完成！")
else:
    print("波形分析失败！")
```

## 📊 **输出文件说明**

### **生成的图表文件**
每次分析会生成2个高质量图表文件：

1. **时序波形图** (`{prefix}{sensor_id}_waveform.png`)
   - 主波形图：完整的车辆通过振动波形
   - 详细视图：车辆通过期间的放大视图
   - 关键点标注：车辆轴的位置和时刻
   - 统计信息：持续时间、峰值、RMS等

2. **30特征可视化图** (`{prefix}{sensor_id}_features.png`)
   - 时域特征柱状图（归一化显示）
   - 频域特征柱状图（归一化显示）
   - 时频域特征柱状图（归一化显示）
   - 关键特征雷达图
   - 特征相关性热图
   - 特征重要性分布图

### **文件命名示例**
```
unified_charts/
├── waveform_analysis_Sensor_01_waveform.png
├── waveform_analysis_Sensor_01_features.png
├── waveform_analysis_Sensor_05_waveform.png
├── waveform_analysis_Sensor_05_features.png
└── waveform_analysis_Sensor_10_waveform.png
└── waveform_analysis_Sensor_10_features.png
```

## 🔧 **技术规格**

### **数据要求**
- **文件格式**: CSV格式的merged_data.csv文件
- **数据结构**: 包含传感器列（Sensor_01到Sensor_20）
- **采样率**: 1000 Hz
- **数据长度**: 通常1000个数据点（1秒车辆通过事件）
- **数据类型**: 数值型振动加速度数据（mg单位）

### **处理能力**
- **传感器支持**: 支持Sensor_01到Sensor_20的任意传感器
- **数据量**: 适合处理1000-10000个数据点的时序数据
- **特征提取**: 自动提取30个时频域特征
- **车辆检测**: 自动检测车辆通过事件和轴数

### **输出质量**
- **分辨率**: 330 DPI（学术发表级）
- **字体**: Times New Roman（国际标准）
- **语言**: 英文标签和说明
- **格式**: PNG格式，白色背景
- **图表尺寸**: 优化的尺寸适合学术发表

## 📈 **30个特征详细说明**

### **时域特征** (10个)
1. **Mean**: 信号均值
2. **Std Dev**: 标准差
3. **Variance**: 方差
4. **RMS**: 均方根值
5. **Peak**: 峰值
6. **Peak-to-Peak**: 峰峰值
7. **Crest Factor**: 峰值因子
8. **Skewness**: 偏度
9. **Kurtosis**: 峰度
10. **Energy**: 信号能量

### **频域特征** (10个)
1. **Zero Cross Rate**: 过零率
2. **Dominant Freq**: 主频
3. **Mean Freq**: 平均频率
4. **Freq Std**: 频率标准差
5. **Spectral Centroid**: 频谱质心
6. **Spectral Rolloff**: 频谱滚降
7. **Spectral Flux**: 频谱通量
8. **Total Power**: 总功率
9. **Low Freq Power**: 低频功率比例
10. **Mid Freq Power**: 中频功率比例

### **时频域特征** (10个)
1. **High Freq Power**: 高频功率比例
2. **Spectrogram Mean**: 频谱图均值
3. **Spectrogram Std**: 频谱图标准差
4. **Spectrogram Max**: 频谱图最大值
5. **Time-BW Product**: 时间带宽积
6. **Wavelet Energy D1**: 小波能量1级分解
7. **Wavelet Energy D2**: 小波能量2级分解
8. **Wavelet Energy D3**: 小波能量3级分解
9. **Wavelet Energy D4**: 小波能量4级分解
10. **Wavelet Energy A4**: 小波近似能量

## 🎯 **应用场景**

### **科研应用**
- **学术论文**: 生成高质量的传感器波形分析图表
- **信号分析**: 详细的时频域特征分析
- **车辆检测**: 车辆通过事件的可视化分析

### **工程应用**
- **传感器验证**: 验证传感器的工作状态和响应特性
- **信号质量评估**: 评估振动信号的质量和特征
- **故障诊断**: 通过波形分析识别异常情况

### **教学应用**
- **信号处理教学**: 演示时频域分析方法
- **特征提取教学**: 展示30个特征的计算和意义
- **数据可视化**: 学习专业的科学可视化方法

## ⚠️ **注意事项**

### **数据格式要求**
- CSV文件必须包含指定的传感器列
- 数据应为数值型，避免缺失值和非数值字符
- 建议数据长度至少500个点以获得良好的频域分析效果

### **性能考虑**
- 大数据文件（>10000点）可能需要较长处理时间
- 特征提取和可视化生成需要一定计算时间
- 高分辨率图表生成需要足够的内存

### **输出管理**
- 重复运行会覆盖同名文件
- 建议使用不同的前缀区分不同的分析任务
- 定期清理输出目录以节省存储空间

## 🔗 **与主程序集成**

传感器波形分析器可以与主程序`unified_vibration_analysis.py`协同工作：

```python
# 在主程序中调用波形分析
from sensor_waveform_analyzer import SensorWaveformAnalyzer

def analyze_sensor_waveforms(experiment_dir, sensor_list):
    analyzer = SensorWaveformAnalyzer()
    for sensor_id in sensor_list:
        data_file = f"{experiment_dir}/{experiment_dir}_merged_data.csv"
        analyzer.analyze_sensor_waveform(data_file, sensor_id)
```

## 📞 **技术支持**

### **常见问题**
1. **传感器ID不存在**: 检查CSV文件中的列名是否正确
2. **内存不足**: 处理大文件时可能需要更多内存
3. **图表质量问题**: 确保安装了正确的字体和图形库

### **错误处理**
程序包含完整的错误处理机制，会提供详细的错误信息和解决建议。

### **性能优化建议**
- 对于大数据文件，建议先检查数据质量
- 确保有足够的磁盘空间保存高分辨率图表
- 使用SSD存储可以提高文件读写速度

## 🎊 **示例输出**

### **时序波形图特点**
- 清晰显示车辆通过的完整过程
- 自动标注车辆轴的位置和时刻
- 包含信号包络和详细视图
- 提供关键统计信息

### **30特征可视化特点**
- 分类展示时域、频域、时频域特征
- 归一化显示便于比较
- 雷达图展示关键特征关系
- 相关性分析和重要性评估

---

**版本**: 1.0  
**更新日期**: 2024-12-07  
**兼容性**: Python 3.7+, 与振动信号分析系统完全兼容
