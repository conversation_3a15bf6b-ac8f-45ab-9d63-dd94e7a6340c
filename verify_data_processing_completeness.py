#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证数据处理完整性检查工具
全面检查unified_vibration_analysis.py是否正确处理两种格式的数据

作者: AI Assistant
日期: 2024-12-22
"""

import os
import pandas as pd
import re
import glob
from pathlib import Path

def check_file_filtering_logic():
    """检查文件过滤逻辑"""
    print("🔍 A. 检查文件识别和过滤逻辑")
    print("-" * 50)
    
    # 检查unified_vibration_analysis.py中的文件过滤逻辑
    if os.path.exists('unified_vibration_analysis.py'):
        with open('unified_vibration_analysis.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找文件过滤条件
        filter_patterns = [
            r"file\.endswith\('\.csv'\).*?and.*?\(",
            r"'acce_'.*?in.*?file",
            r"'sensor'.*?in.*?file", 
            r"'GW100001'.*?in.*?file",
            r"'AcceData'.*?in.*?file"
        ]
        
        results = {}
        for pattern in filter_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            results[pattern] = len(matches) > 0
        
        print("   文件过滤条件检查:")
        csv_check = len(re.findall(r'file\.endswith.*?csv', content)) > 0
        legacy_check = 'acce_' in content and 'sensor' in content
        new_check = 'GW100001' in content and 'AcceData' in content
        or_logic_found = 'or' in content and 'GW100001' in content

        print(f"      ✅ CSV文件检查: {'✅' if csv_check else '❌'}")
        print(f"      ✅ 传统格式关键词 ('acce_', 'sensor'): {'✅' if legacy_check else '❌'}")
        print(f"      ✅ 新格式关键词 ('GW100001', 'AcceData'): {'✅' if new_check else '❌'}")
        print(f"      ✅ OR逻辑连接: {'✅' if or_logic_found else '❌'}")
        
        return {
            'has_csv_filter': csv_check,
            'has_legacy_keywords': legacy_check,
            'has_new_keywords': new_check,
            'has_or_logic': or_logic_found
        }
    else:
        print("   ❌ unified_vibration_analysis.py 文件不存在")
        return {}

def check_data_reading_parsing():
    """检查数据读取和解析功能"""
    print("\n🔍 B. 检查数据读取和解析")
    print("-" * 50)
    
    # 扫描实际数据文件
    csv_files = glob.glob('./data/**/*.csv', recursive=True)
    
    new_format_files = []
    legacy_format_files = []
    
    for file_path in csv_files[:20]:  # 检查前20个文件作为样本
        filename = os.path.basename(file_path)
        
        # 检测新格式
        if re.match(r'GW\d+_\d+_AcceData_车道\d+_\d+轴-[0-9.]+t-\d+kmh\.csv', filename):
            new_format_files.append(file_path)
        # 检测传统格式
        elif re.match(r'[0-9.]+吨_[^_]+_[0-9.]+kmh?_实验\d+', filename):
            legacy_format_files.append(file_path)
    
    print(f"   数据文件样本分析 (前20个文件):")
    print(f"      新格式文件: {len(new_format_files)} 个")
    print(f"      传统格式文件: {len(legacy_format_files)} 个")
    
    # 测试文件名解析
    parsing_results = {}
    
    if new_format_files:
        sample_new = new_format_files[0]
        new_info = parse_new_format_filename(os.path.basename(sample_new))
        parsing_results['new_format'] = new_info
        print(f"      新格式解析示例: {os.path.basename(sample_new)}")
        print(f"         速度: {new_info.get('speed_kmh')} km/h")
        print(f"         载重: {new_info.get('load_tons')} 吨")
        print(f"         轴型: {new_info.get('axle_type')}")
        print(f"         车道: {new_info.get('lane')}")
    
    if legacy_format_files:
        sample_legacy = legacy_format_files[0]
        legacy_info = parse_legacy_format_filename(os.path.basename(sample_legacy))
        parsing_results['legacy_format'] = legacy_info
        print(f"      传统格式解析示例: {os.path.basename(sample_legacy)}")
        print(f"         速度: {legacy_info.get('speed_kmh')} km/h")
        print(f"         载重: {legacy_info.get('load_tons')} 吨")
        print(f"         轴型: {legacy_info.get('axle_type')}")
    
    # 检查列结构
    column_check = {}
    if new_format_files:
        try:
            df_new = pd.read_csv(new_format_files[0], nrows=1)
            column_check['new_format'] = {
                'columns': df_new.shape[1],
                'has_acce_columns': any('acce' in col.lower() for col in df_new.columns),
                'sample_columns': list(df_new.columns[:5])
            }
            print(f"      新格式列结构: {df_new.shape[1]} 列")
            print(f"         包含acce列: {'✅' if column_check['new_format']['has_acce_columns'] else '❌'}")
        except Exception as e:
            print(f"      新格式文件读取失败: {str(e)}")
    
    if legacy_format_files:
        try:
            df_legacy = pd.read_csv(legacy_format_files[0], nrows=1)
            column_check['legacy_format'] = {
                'columns': df_legacy.shape[1],
                'has_sensor_columns': any('sensor' in col.lower() or 'acce' in col.lower() for col in df_legacy.columns),
                'sample_columns': list(df_legacy.columns[:5])
            }
            print(f"      传统格式列结构: {df_legacy.shape[1]} 列")
            print(f"         包含传感器列: {'✅' if column_check['legacy_format']['has_sensor_columns'] else '❌'}")
        except Exception as e:
            print(f"      传统格式文件读取失败: {str(e)}")
    
    return {
        'new_format_count': len(new_format_files),
        'legacy_format_count': len(legacy_format_files),
        'parsing_results': parsing_results,
        'column_check': column_check
    }

def check_feature_extraction_integration():
    """检查特征提取集成"""
    print("\n🔍 C. 检查特征提取集成")
    print("-" * 50)
    
    integration_status = {}
    
    # 检查enhanced_data_expansion_processor调用
    if os.path.exists('unified_vibration_analysis.py'):
        with open('unified_vibration_analysis.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        integration_status['calls_expansion_processor'] = 'enhanced_data_expansion_processor' in content
        integration_status['has_import_statement'] = 'from enhanced_data_expansion_processor import' in content
        integration_status['has_fallback_logic'] = 'except' in content and 'enhanced_feature_extractor' in content
        
        print(f"   增强数据扩展处理器集成:")
        print(f"      ✅ 调用扩展处理器: {'✅' if integration_status['calls_expansion_processor'] else '❌'}")
        print(f"      ✅ 导入语句: {'✅' if integration_status['has_import_statement'] else '❌'}")
        print(f"      ✅ 备用逻辑: {'✅' if integration_status['has_fallback_logic'] else '❌'}")
    
    # 检查enhanced_data_expansion_processor文件
    if os.path.exists('enhanced_data_expansion_processor.py'):
        with open('enhanced_data_expansion_processor.py', 'r', encoding='utf-8') as f:
            processor_content = f.read()
        
        integration_status['processor_exists'] = True
        integration_status['handles_new_format'] = 'GW100001' in processor_content
        integration_status['has_filename_parsing'] = 'extract_file_info' in processor_content or 'parse_filename' in processor_content
        
        print(f"   enhanced_data_expansion_processor.py:")
        print(f"      ✅ 文件存在: ✅")
        print(f"      ✅ 处理新格式: {'✅' if integration_status['handles_new_format'] else '❌'}")
        print(f"      ✅ 文件名解析: {'✅' if integration_status['has_filename_parsing'] else '❌'}")
    else:
        integration_status['processor_exists'] = False
        print(f"   enhanced_data_expansion_processor.py: ❌ 文件不存在")
    
    return integration_status

def check_data_processing_coverage():
    """检查数据处理覆盖率"""
    print("\n🔍 D. 检查数据处理覆盖率")
    print("-" * 50)
    
    coverage_results = {}
    
    # 检查combined_features.csv
    if os.path.exists('combined_features.csv'):
        df = pd.read_csv('combined_features.csv')
        coverage_results['features_file_exists'] = True
        coverage_results['total_samples'] = len(df)
        
        # 检查数据来源
        if 'data_source' in df.columns:
            data_sources = df['data_source'].value_counts().to_dict()
            coverage_results['data_sources'] = data_sources
            coverage_results['has_new_format_data'] = 'new_format' in data_sources
            coverage_results['new_format_samples'] = data_sources.get('new_format', 0)
        else:
            coverage_results['has_data_source_column'] = False
        
        # 检查目标变量
        target_vars = ['speed_kmh', 'load_tons', 'axle_type']
        coverage_results['target_variables'] = {}
        for var in target_vars:
            if var in df.columns:
                valid_count = df[var].notna().sum()
                coverage_results['target_variables'][var] = {
                    'exists': True,
                    'valid_count': valid_count,
                    'coverage_rate': valid_count / len(df)
                }
            else:
                coverage_results['target_variables'][var] = {'exists': False}
        
        print(f"   combined_features.csv 分析:")
        print(f"      ✅ 文件存在: ✅")
        print(f"      📊 总样本数: {coverage_results['total_samples']}")
        
        if 'data_sources' in coverage_results:
            print(f"      📊 数据来源分布:")
            for source, count in coverage_results['data_sources'].items():
                print(f"         {source}: {count} 样本")
        
        print(f"      📊 目标变量覆盖率:")
        for var, info in coverage_results['target_variables'].items():
            if info['exists']:
                print(f"         {var}: {info['valid_count']}/{coverage_results['total_samples']} ({info['coverage_rate']:.1%})")
            else:
                print(f"         {var}: ❌ 不存在")
    
    else:
        coverage_results['features_file_exists'] = False
        print(f"   combined_features.csv: ❌ 文件不存在")
    
    # 检查处理历史
    if os.path.exists('processing_history.json'):
        import json
        with open('processing_history.json', 'r', encoding='utf-8') as f:
            history = json.load(f)
        
        coverage_results['processing_history'] = {
            'total_processed': len(history.get('processed_files', {})),
            'has_new_format_files': any('GW100001' in f for f in history.get('processed_files', {}))
        }
        
        print(f"   处理历史记录:")
        print(f"      ✅ 已处理文件数: {coverage_results['processing_history']['total_processed']}")
        print(f"      ✅ 包含新格式: {'✅' if coverage_results['processing_history']['has_new_format_files'] else '❌'}")
    
    return coverage_results

def parse_new_format_filename(filename):
    """解析新格式文件名"""
    pattern = r'GW\d+_\d+_AcceData_车道(\d+)_(\d+)轴-([0-9.]+)t-(\d+)kmh\.csv'
    match = re.match(pattern, filename)
    
    if match:
        return {
            'lane': int(match.group(1)),
            'axle_type': f"{match.group(2)}轴",
            'load_tons': float(match.group(3)),
            'speed_kmh': float(match.group(4))
        }
    return {}

def parse_legacy_format_filename(filename):
    """解析传统格式文件名"""
    pattern = r'([0-9.]+)吨_([^_]+)_([0-9.]+)kmh?_实验\d+'
    match = re.search(pattern, filename)
    
    if match:
        return {
            'load_tons': float(match.group(1)),
            'axle_type': match.group(2),
            'speed_kmh': float(match.group(3))
        }
    return {}

def generate_comprehensive_report(filter_check, parsing_check, integration_check, coverage_check):
    """生成综合报告"""
    print("\n" + "="*80)
    print("📋 数据处理完整性验证报告")
    print("="*80)
    
    # 总体评分
    total_score = 0
    max_score = 0
    
    # A. 文件识别和过滤逻辑评分
    filter_score = 0
    filter_max = 4
    if filter_check.get('has_csv_filter'): filter_score += 1
    if filter_check.get('has_legacy_keywords'): filter_score += 1
    if filter_check.get('has_new_keywords'): filter_score += 1
    if filter_check.get('has_or_logic'): filter_score += 1
    
    print(f"\n🔍 A. 文件识别和过滤逻辑: {filter_score}/{filter_max} ({'✅' if filter_score >= 3 else '❌'})")
    print(f"   - CSV文件过滤: {'✅' if filter_check.get('has_csv_filter') else '❌'}")
    print(f"   - 传统格式关键词: {'✅' if filter_check.get('has_legacy_keywords') else '❌'}")
    print(f"   - 新格式关键词: {'✅' if filter_check.get('has_new_keywords') else '❌'}")
    print(f"   - OR逻辑连接: {'✅' if filter_check.get('has_or_logic') else '❌'}")
    
    # B. 数据读取和解析评分
    parsing_score = 0
    parsing_max = 4
    if parsing_check.get('new_format_count', 0) > 0: parsing_score += 1
    if parsing_check.get('legacy_format_count', 0) > 0: parsing_score += 1
    if parsing_check.get('parsing_results', {}).get('new_format'): parsing_score += 1
    if parsing_check.get('column_check', {}).get('new_format', {}).get('has_acce_columns'): parsing_score += 1
    
    print(f"\n🔍 B. 数据读取和解析: {parsing_score}/{parsing_max} ({'✅' if parsing_score >= 3 else '❌'})")
    print(f"   - 新格式文件识别: {'✅' if parsing_check.get('new_format_count', 0) > 0 else '❌'}")
    print(f"   - 传统格式文件识别: {'✅' if parsing_check.get('legacy_format_count', 0) > 0 else '❌'}")
    print(f"   - 新格式文件名解析: {'✅' if parsing_check.get('parsing_results', {}).get('new_format') else '❌'}")
    print(f"   - 新格式列结构验证: {'✅' if parsing_check.get('column_check', {}).get('new_format', {}).get('has_acce_columns') else '❌'}")
    
    # C. 特征提取集成评分
    integration_score = 0
    integration_max = 4
    if integration_check.get('calls_expansion_processor'): integration_score += 1
    if integration_check.get('has_import_statement'): integration_score += 1
    if integration_check.get('processor_exists'): integration_score += 1
    if integration_check.get('handles_new_format'): integration_score += 1
    
    print(f"\n🔍 C. 特征提取集成: {integration_score}/{integration_max} ({'✅' if integration_score >= 3 else '❌'})")
    print(f"   - 调用扩展处理器: {'✅' if integration_check.get('calls_expansion_processor') else '❌'}")
    print(f"   - 导入语句正确: {'✅' if integration_check.get('has_import_statement') else '❌'}")
    print(f"   - 处理器文件存在: {'✅' if integration_check.get('processor_exists') else '❌'}")
    print(f"   - 处理新格式数据: {'✅' if integration_check.get('handles_new_format') else '❌'}")
    
    # D. 数据处理覆盖率评分
    coverage_score = 0
    coverage_max = 4
    if coverage_check.get('features_file_exists'): coverage_score += 1
    if coverage_check.get('total_samples', 0) >= 3000: coverage_score += 1
    if coverage_check.get('has_new_format_data'): coverage_score += 1
    if coverage_check.get('new_format_samples', 0) >= 1000: coverage_score += 1
    
    print(f"\n🔍 D. 数据处理覆盖率: {coverage_score}/{coverage_max} ({'✅' if coverage_score >= 3 else '❌'})")
    print(f"   - 特征文件存在: {'✅' if coverage_check.get('features_file_exists') else '❌'}")
    print(f"   - 样本数量充足: {'✅' if coverage_check.get('total_samples', 0) >= 3000 else '❌'} ({coverage_check.get('total_samples', 0)})")
    print(f"   - 包含新格式数据: {'✅' if coverage_check.get('has_new_format_data') else '❌'}")
    print(f"   - 新格式样本充足: {'✅' if coverage_check.get('new_format_samples', 0) >= 1000 else '❌'} ({coverage_check.get('new_format_samples', 0)})")
    
    # 总体评分
    total_score = filter_score + parsing_score + integration_score + coverage_score
    max_score = filter_max + parsing_max + integration_max + coverage_max
    overall_percentage = (total_score / max_score) * 100
    
    print(f"\n🎯 总体评分: {total_score}/{max_score} ({overall_percentage:.1f}%)")
    
    if overall_percentage >= 85:
        print("✅ 系统已正确配置，能够处理两种格式的数据")
    elif overall_percentage >= 70:
        print("⚠️  系统基本配置正确，但有部分问题需要修复")
    else:
        print("❌ 系统配置存在重大问题，需要全面修复")
    
    # 建议
    print(f"\n💡 修复建议:")
    if filter_score < filter_max:
        print("   1. 检查并修复unified_vibration_analysis.py中的文件过滤逻辑")
    if integration_score < integration_max:
        print("   2. 确保enhanced_data_expansion_processor正确集成")
    if coverage_score < coverage_max:
        print("   3. 重新运行数据处理以包含所有新格式文件")
    
    return {
        'total_score': total_score,
        'max_score': max_score,
        'percentage': overall_percentage,
        'sections': {
            'filter': filter_score,
            'parsing': parsing_score,
            'integration': integration_score,
            'coverage': coverage_score
        }
    }

def main():
    """主函数"""
    print("🔍 振动信号分析系统数据处理完整性验证")
    print("="*80)
    
    # 执行各项检查
    filter_check = check_file_filtering_logic()
    parsing_check = check_data_reading_parsing()
    integration_check = check_feature_extraction_integration()
    coverage_check = check_data_processing_coverage()
    
    # 生成综合报告
    report = generate_comprehensive_report(filter_check, parsing_check, integration_check, coverage_check)
    
    # 保存报告
    import json
    with open('data_processing_verification_report.json', 'w', encoding='utf-8') as f:
        json.dump({
            'filter_check': filter_check,
            'parsing_check': parsing_check,
            'integration_check': integration_check,
            'coverage_check': coverage_check,
            'overall_report': report
        }, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n💾 详细报告已保存到: data_processing_verification_report.json")
    
    return report

if __name__ == "__main__":
    main()
