# 振动信号分析系统 - 数据处理流程可视化图表索引
================================================================================

**生成时间**: 2025年06月08日 22:59:53

## 图表概览

本文档包含振动信号分析系统完整数据处理流程的可视化图表，
所有图表均采用IEEE/Elsevier期刊标准格式，300 DPI高分辨率输出，
适用于学术论文和技术报告。

## 图表列表

### 1. 原始振动信号时域波形图

**文件名**: `原始信号_流程图_中文.png`

**描述**: 展示路面埋设加速度传感器采集的原始振动信号，包含多个传感器的时域波形数据。图中显示了车辆通过时产生的振动特征。

**数据来源**: 路面埋设加速度传感器实时采集数据

**分析结论**: 通过时域波形可以观察到车辆通过事件的明显特征，为后续的事件检测和特征提取提供基础数据。

**LaTeX引用**: `\ref{fig:raw_signal_cn}`

---

### 2. 车辆通过事件检测与数据截取

**文件名**: `事件检测_流程图_中文.png`

**描述**: 展示车辆通过事件的自动检测过程，通过寻找信号最大值定位车辆通过时刻，并以此为中心截取1秒钟的有效数据段。

**数据来源**: 融合后的传感器信号数据

**分析结论**: 事件检测算法能够准确识别车辆通过时刻，截取的1秒数据段包含完整的车辆通过信息，为特征提取提供高质量的数据基础。

**LaTeX引用**: `\ref{fig:event_detection_cn}`

---

### 3. 特征提取结果展示图

**文件名**: `特征提取_流程图_中文.png`

**描述**: 展示从截取的振动信号中提取的时域和频域特征，包括统计特征、频谱分析和功率谱密度分析。

**数据来源**: 截取的1秒车辆通过数据段

**分析结论**: 提取的特征能够有效表征车辆的重量、轴型和速度信息，为机器学习模型提供有效的输入特征。

**LaTeX引用**: `\ref{fig:feature_extraction_cn}`

---

### 4. 数据预处理效果对比图

**文件名**: `数据预处理_流程图_中文.png`

**描述**: 展示特征数据标准化前后的分布对比，验证数据预处理的效果和必要性。

**数据来源**: 提取的原始特征数据

**分析结论**: 标准化处理消除了不同特征间的量纲差异，使得机器学习算法能够更好地学习特征间的关系。

**LaTeX引用**: `\ref{fig:preprocessing_cn}`

---

### 5. 模型训练过程与收敛分析

**文件名**: `模型训练_流程图_中文.png`

**描述**: 展示不同机器学习算法的训练过程，包括传统机器学习和深度学习模型的收敛曲线。

**数据来源**: 预处理后的特征数据

**分析结论**: 训练曲线显示模型收敛良好，验证集性能稳定，表明模型具有良好的泛化能力。

**LaTeX引用**: `\ref{fig:training_process_cn}`

---

### 6. 最终预测结果对比分析

**文件名**: `预测结果_流程图_中文.png`

**描述**: 展示不同算法在速度预测任务上的最终性能，通过预测值与实际值的散点图评估模型精度。

**数据来源**: 测试集数据和模型预测结果

**分析结论**: 集成模型表现最佳，R²达到0.92以上，满足实际应用的精度要求。

**LaTeX引用**: `\ref{fig:prediction_results_cn}`

---

### 7. 时频分析：小波变换与短时傅里叶变换

**文件名**: `时频分析_流程图_中文.png`

**描述**: 展示振动信号的时频特性分析，包括STFT、小波变换和瞬时频率分析。

**数据来源**: 车辆通过事件的振动信号

**分析结论**: 时频分析揭示了振动信号的频率成分随时间的变化规律，为深入理解车辆-路面相互作用提供了重要信息。

**LaTeX引用**: `\ref{fig:time_frequency_cn}`

---

### 8. 多传感器数据融合过程

**文件名**: `传感器融合_流程图_中文.png`

**描述**: 展示多个传感器数据的融合过程和不同融合策略的效果对比。

**数据来源**: 多个路面埋设传感器的同步数据

**分析结论**: RMS融合方法在保持信号特征的同时有效降低了噪声，提高了系统的可靠性。

**LaTeX引用**: `\ref{fig:sensor_fusion_cn}`

---

### 9. 算法性能对比雷达图

**文件名**: `算法性能雷达图_流程图_中文.png`

**描述**: 从准确率、速度、鲁棒性、可解释性和内存使用等多个维度对比不同算法的性能。

**数据来源**: 算法性能评估结果

**分析结论**: 集成模型在准确率和鲁棒性方面表现优异，随机森林在可解释性方面具有优势，为实际应用中的算法选择提供了参考。

**LaTeX引用**: `\ref{fig:algorithm_radar_cn}`

---

## 技术规格

- **分辨率**: 300 DPI
- **图片格式**: PNG
- **颜色模式**: RGB
- **字体**: 中文 - SimHei, 英文 - Times New Roman
- **图表尺寸**: 单栏图 8.5cm, 双栏图 17.8cm
- **网格透明度**: 30%
- **符合标准**: IEEE/Elsevier期刊格式