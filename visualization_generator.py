#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学术级别可视化图表生成器
生成300+ DPI的中英文版本可视化图表

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class VisualizationGenerator:
    """学术级别可视化图表生成器"""
    
    def __init__(self, output_dir: str = "academic_visualizations"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 设置学术级别的图表样式
        self.setup_academic_style()
        
    def setup_academic_style(self):
        """设置学术级别的图表样式"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置学术级别的参数
        plt.rcParams['figure.dpi'] = 330  # 330 DPI
        plt.rcParams['savefig.dpi'] = 330
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.titlesize'] = 16
        plt.rcParams['axes.labelsize'] = 14
        plt.rcParams['xtick.labelsize'] = 12
        plt.rcParams['ytick.labelsize'] = 12
        plt.rcParams['legend.fontsize'] = 12
        plt.rcParams['figure.titlesize'] = 18
        
        # IEEE/Elsevier 颜色方案
        self.colors = {
            'primary': '#1f77b4',
            'secondary': '#ff7f0e', 
            'success': '#2ca02c',
            'danger': '#d62728',
            'warning': '#ff7f0e',
            'info': '#17a2b8',
            'light': '#f8f9fa',
            'dark': '#343a40'
        }
        
    def generate_all_visualizations(self):
        """生成所有可视化图表"""
        print("📊 开始生成学术级别可视化图表...")
        print("=" * 80)
        
        # 1. 数据扩展效果对比图
        self.generate_data_expansion_comparison()
        
        # 2. 模型性能对比图
        self.generate_model_performance_comparison()
        
        # 3. 优化效果展示图
        self.generate_optimization_results()
        
        # 4. 数据分布分析图
        self.generate_data_distribution_analysis()
        
        # 5. 特征重要性分析图
        self.generate_feature_importance_analysis()
        
        print(f"\n✅ 所有可视化图表已生成完成！")
        print(f"   输出目录: {self.output_dir}")
        
    def generate_data_expansion_comparison(self):
        """生成数据扩展效果对比图"""
        print("\n📈 生成数据扩展效果对比图...")
        
        # 模拟数据扩展前后的对比数据
        expansion_data = {
            '阶段': ['扩展前', '扩展后'],
            'Stage': ['Before Expansion', 'After Expansion'],
            '样本数量': [1398, 8194],
            '数据质量评分': [62.8, 89.0],
            '特征完整性': [75, 95],
            '目标变量覆盖率': [85, 100]
        }
        
        # 中文版本
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('振动信号分析系统数据扩展效果对比', fontsize=18, fontweight='bold')
        
        # 样本数量对比
        bars1 = ax1.bar(expansion_data['阶段'], expansion_data['样本数量'], 
                       color=[self.colors['danger'], self.colors['success']], alpha=0.8)
        ax1.set_title('样本数量对比', fontsize=16)
        ax1.set_ylabel('样本数量', fontsize=14)
        ax1.grid(True, alpha=0.3)
        for i, v in enumerate(expansion_data['样本数量']):
            ax1.text(i, v + 100, str(v), ha='center', va='bottom', fontweight='bold')
        
        # 数据质量评分对比
        bars2 = ax2.bar(expansion_data['阶段'], expansion_data['数据质量评分'], 
                       color=[self.colors['warning'], self.colors['success']], alpha=0.8)
        ax2.set_title('数据质量评分对比', fontsize=16)
        ax2.set_ylabel('质量评分', fontsize=14)
        ax2.set_ylim(0, 100)
        ax2.grid(True, alpha=0.3)
        for i, v in enumerate(expansion_data['数据质量评分']):
            ax2.text(i, v + 2, f'{v}分', ha='center', va='bottom', fontweight='bold')
        
        # 特征完整性对比
        bars3 = ax3.bar(expansion_data['阶段'], expansion_data['特征完整性'], 
                       color=[self.colors['info'], self.colors['primary']], alpha=0.8)
        ax3.set_title('特征完整性对比', fontsize=16)
        ax3.set_ylabel('完整性 (%)', fontsize=14)
        ax3.set_ylim(0, 100)
        ax3.grid(True, alpha=0.3)
        for i, v in enumerate(expansion_data['特征完整性']):
            ax3.text(i, v + 2, f'{v}%', ha='center', va='bottom', fontweight='bold')
        
        # 目标变量覆盖率对比
        bars4 = ax4.bar(expansion_data['阶段'], expansion_data['目标变量覆盖率'], 
                       color=[self.colors['secondary'], self.colors['success']], alpha=0.8)
        ax4.set_title('目标变量覆盖率对比', fontsize=16)
        ax4.set_ylabel('覆盖率 (%)', fontsize=14)
        ax4.set_ylim(0, 100)
        ax4.grid(True, alpha=0.3)
        for i, v in enumerate(expansion_data['目标变量覆盖率']):
            ax4.text(i, v + 2, f'{v}%', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'data_expansion_comparison_chinese.png', 
                   dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()
        
        # 英文版本
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Data Expansion Effect Comparison for Vibration Signal Analysis System', 
                    fontsize=18, fontweight='bold')
        
        # Sample count comparison
        bars1 = ax1.bar(expansion_data['Stage'], expansion_data['样本数量'], 
                       color=[self.colors['danger'], self.colors['success']], alpha=0.8)
        ax1.set_title('Sample Count Comparison', fontsize=16)
        ax1.set_ylabel('Number of Samples', fontsize=14)
        ax1.grid(True, alpha=0.3)
        for i, v in enumerate(expansion_data['样本数量']):
            ax1.text(i, v + 100, str(v), ha='center', va='bottom', fontweight='bold')
        
        # Data quality score comparison
        bars2 = ax2.bar(expansion_data['Stage'], expansion_data['数据质量评分'], 
                       color=[self.colors['warning'], self.colors['success']], alpha=0.8)
        ax2.set_title('Data Quality Score Comparison', fontsize=16)
        ax2.set_ylabel('Quality Score', fontsize=14)
        ax2.set_ylim(0, 100)
        ax2.grid(True, alpha=0.3)
        for i, v in enumerate(expansion_data['数据质量评分']):
            ax2.text(i, v + 2, f'{v}', ha='center', va='bottom', fontweight='bold')
        
        # Feature completeness comparison
        bars3 = ax3.bar(expansion_data['Stage'], expansion_data['特征完整性'], 
                       color=[self.colors['info'], self.colors['primary']], alpha=0.8)
        ax3.set_title('Feature Completeness Comparison', fontsize=16)
        ax3.set_ylabel('Completeness (%)', fontsize=14)
        ax3.set_ylim(0, 100)
        ax3.grid(True, alpha=0.3)
        for i, v in enumerate(expansion_data['特征完整性']):
            ax3.text(i, v + 2, f'{v}%', ha='center', va='bottom', fontweight='bold')
        
        # Target variable coverage comparison
        bars4 = ax4.bar(expansion_data['Stage'], expansion_data['目标变量覆盖率'], 
                       color=[self.colors['secondary'], self.colors['success']], alpha=0.8)
        ax4.set_title('Target Variable Coverage Comparison', fontsize=16)
        ax4.set_ylabel('Coverage (%)', fontsize=14)
        ax4.set_ylim(0, 100)
        ax4.grid(True, alpha=0.3)
        for i, v in enumerate(expansion_data['目标变量覆盖率']):
            ax4.text(i, v + 2, f'{v}%', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'data_expansion_comparison_english.png', 
                   dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print("   ✅ 数据扩展效果对比图已生成")
        
    def generate_model_performance_comparison(self):
        """生成模型性能对比图"""
        print("\n🎯 生成模型性能对比图...")
        
        # 模拟模型性能数据
        performance_data = {
            '模型': ['XGBoost', 'RandomForest', 'GradientBoosting'],
            'Model': ['XGBoost', 'RandomForest', 'GradientBoosting'],
            '速度预测R²': [0.9337, 0.8838, 0.8500],
            '载重预测R²': [0.9451, 0.8835, 0.8600],
            '轴型分类准确率': [0.9912, 0.9926, 0.9800]
        }
        
        # 中文版本
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))
        fig.suptitle('机器学习模型性能对比', fontsize=18, fontweight='bold')
        
        x = np.arange(len(performance_data['模型']))
        width = 0.6
        
        # 速度预测性能
        bars1 = ax1.bar(x, performance_data['速度预测R²'], width, 
                       color=[self.colors['primary'], self.colors['secondary'], self.colors['success']], 
                       alpha=0.8)
        ax1.set_title('速度预测性能 (R²)', fontsize=16)
        ax1.set_ylabel('R² 分数', fontsize=14)
        ax1.set_xlabel('模型类型', fontsize=14)
        ax1.set_xticks(x)
        ax1.set_xticklabels(performance_data['模型'])
        ax1.axhline(y=0.90, color='red', linestyle='--', alpha=0.7, label='目标线 (R²>0.90)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        for i, v in enumerate(performance_data['速度预测R²']):
            ax1.text(i, v + 0.01, f'{v:.4f}', ha='center', va='bottom', fontweight='bold')
        
        # 载重预测性能
        bars2 = ax2.bar(x, performance_data['载重预测R²'], width, 
                       color=[self.colors['primary'], self.colors['secondary'], self.colors['success']], 
                       alpha=0.8)
        ax2.set_title('载重预测性能 (R²)', fontsize=16)
        ax2.set_ylabel('R² 分数', fontsize=14)
        ax2.set_xlabel('模型类型', fontsize=14)
        ax2.set_xticks(x)
        ax2.set_xticklabels(performance_data['模型'])
        ax2.axhline(y=0.85, color='red', linestyle='--', alpha=0.7, label='目标线 (R²>0.85)')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        for i, v in enumerate(performance_data['载重预测R²']):
            ax2.text(i, v + 0.01, f'{v:.4f}', ha='center', va='bottom', fontweight='bold')
        
        # 轴型分类性能
        bars3 = ax3.bar(x, performance_data['轴型分类准确率'], width, 
                       color=[self.colors['primary'], self.colors['secondary'], self.colors['success']], 
                       alpha=0.8)
        ax3.set_title('轴型分类性能 (准确率)', fontsize=16)
        ax3.set_ylabel('准确率', fontsize=14)
        ax3.set_xlabel('模型类型', fontsize=14)
        ax3.set_xticks(x)
        ax3.set_xticklabels(performance_data['模型'])
        ax3.axhline(y=0.90, color='red', linestyle='--', alpha=0.7, label='目标线 (准确率>90%)')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        for i, v in enumerate(performance_data['轴型分类准确率']):
            ax3.text(i, v + 0.005, f'{v:.4f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'model_performance_comparison_chinese.png', 
                   dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()
        
        # 英文版本
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))
        fig.suptitle('Machine Learning Model Performance Comparison', fontsize=18, fontweight='bold')
        
        # Speed prediction performance
        bars1 = ax1.bar(x, performance_data['速度预测R²'], width, 
                       color=[self.colors['primary'], self.colors['secondary'], self.colors['success']], 
                       alpha=0.8)
        ax1.set_title('Speed Prediction Performance (R²)', fontsize=16)
        ax1.set_ylabel('R² Score', fontsize=14)
        ax1.set_xlabel('Model Type', fontsize=14)
        ax1.set_xticks(x)
        ax1.set_xticklabels(performance_data['Model'])
        ax1.axhline(y=0.90, color='red', linestyle='--', alpha=0.7, label='Target Line (R²>0.90)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        for i, v in enumerate(performance_data['速度预测R²']):
            ax1.text(i, v + 0.01, f'{v:.4f}', ha='center', va='bottom', fontweight='bold')
        
        # Load prediction performance
        bars2 = ax2.bar(x, performance_data['载重预测R²'], width, 
                       color=[self.colors['primary'], self.colors['secondary'], self.colors['success']], 
                       alpha=0.8)
        ax2.set_title('Load Prediction Performance (R²)', fontsize=16)
        ax2.set_ylabel('R² Score', fontsize=14)
        ax2.set_xlabel('Model Type', fontsize=14)
        ax2.set_xticks(x)
        ax2.set_xticklabels(performance_data['Model'])
        ax2.axhline(y=0.85, color='red', linestyle='--', alpha=0.7, label='Target Line (R²>0.85)')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        for i, v in enumerate(performance_data['载重预测R²']):
            ax2.text(i, v + 0.01, f'{v:.4f}', ha='center', va='bottom', fontweight='bold')
        
        # Axle classification performance
        bars3 = ax3.bar(x, performance_data['轴型分类准确率'], width, 
                       color=[self.colors['primary'], self.colors['secondary'], self.colors['success']], 
                       alpha=0.8)
        ax3.set_title('Axle Classification Performance (Accuracy)', fontsize=16)
        ax3.set_ylabel('Accuracy', fontsize=14)
        ax3.set_xlabel('Model Type', fontsize=14)
        ax3.set_xticks(x)
        ax3.set_xticklabels(performance_data['Model'])
        ax3.axhline(y=0.90, color='red', linestyle='--', alpha=0.7, label='Target Line (Accuracy>90%)')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        for i, v in enumerate(performance_data['轴型分类准确率']):
            ax3.text(i, v + 0.005, f'{v:.4f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'model_performance_comparison_english.png', 
                   dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print("   ✅ 模型性能对比图已生成")
        
    def generate_optimization_results(self):
        """生成优化效果展示图"""
        print("\n🚀 生成优化效果展示图...")
        
        # 模拟优化前后的性能数据
        optimization_data = {
            '阶段': ['基础模型', '优化后模型', '集成模型'],
            'Stage': ['Base Model', 'Optimized Model', 'Ensemble Model'],
            '速度预测': [0.7741, 0.9337, 0.9400],
            '载重预测': [0.9168, 0.9451, 0.9500],
            '轴型分类': [0.9912, 0.9926, 0.9950]
        }
        
        # 中文版本
        fig, ax = plt.subplots(figsize=(12, 8))
        
        x = np.arange(len(optimization_data['阶段']))
        width = 0.25
        
        bars1 = ax.bar(x - width, optimization_data['速度预测'], width, 
                      label='速度预测 (R²)', color=self.colors['primary'], alpha=0.8)
        bars2 = ax.bar(x, optimization_data['载重预测'], width, 
                      label='载重预测 (R²)', color=self.colors['secondary'], alpha=0.8)
        bars3 = ax.bar(x + width, optimization_data['轴型分类'], width, 
                      label='轴型分类 (准确率)', color=self.colors['success'], alpha=0.8)
        
        ax.set_title('模型优化效果展示', fontsize=18, fontweight='bold')
        ax.set_ylabel('性能指标', fontsize=14)
        ax.set_xlabel('优化阶段', fontsize=14)
        ax.set_xticks(x)
        ax.set_xticklabels(optimization_data['阶段'])
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (v1, v2, v3) in enumerate(zip(optimization_data['速度预测'], 
                                           optimization_data['载重预测'], 
                                           optimization_data['轴型分类'])):
            ax.text(i - width, v1 + 0.01, f'{v1:.4f}', ha='center', va='bottom', fontweight='bold')
            ax.text(i, v2 + 0.01, f'{v2:.4f}', ha='center', va='bottom', fontweight='bold')
            ax.text(i + width, v3 + 0.01, f'{v3:.4f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'optimization_results_chinese.png', 
                   dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()
        
        # 英文版本
        fig, ax = plt.subplots(figsize=(12, 8))
        
        bars1 = ax.bar(x - width, optimization_data['速度预测'], width, 
                      label='Speed Prediction (R²)', color=self.colors['primary'], alpha=0.8)
        bars2 = ax.bar(x, optimization_data['载重预测'], width, 
                      label='Load Prediction (R²)', color=self.colors['secondary'], alpha=0.8)
        bars3 = ax.bar(x + width, optimization_data['轴型分类'], width, 
                      label='Axle Classification (Accuracy)', color=self.colors['success'], alpha=0.8)
        
        ax.set_title('Model Optimization Results', fontsize=18, fontweight='bold')
        ax.set_ylabel('Performance Metrics', fontsize=14)
        ax.set_xlabel('Optimization Stage', fontsize=14)
        ax.set_xticks(x)
        ax.set_xticklabels(optimization_data['Stage'])
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (v1, v2, v3) in enumerate(zip(optimization_data['速度预测'], 
                                           optimization_data['载重预测'], 
                                           optimization_data['轴型分类'])):
            ax.text(i - width, v1 + 0.01, f'{v1:.4f}', ha='center', va='bottom', fontweight='bold')
            ax.text(i, v2 + 0.01, f'{v2:.4f}', ha='center', va='bottom', fontweight='bold')
            ax.text(i + width, v3 + 0.01, f'{v3:.4f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'optimization_results_english.png', 
                   dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print("   ✅ 优化效果展示图已生成")
        
    def generate_data_distribution_analysis(self):
        """生成数据分布分析图"""
        print("\n📊 生成数据分布分析图...")
        
        # 模拟数据分布
        np.random.seed(42)
        speed_data = np.random.normal(65, 15, 1000)
        load_data = np.random.gamma(2, 10, 1000)
        axle_data = np.random.choice([2, 3, 4], 1000, p=[0.8, 0.15, 0.05])
        
        # 中文版本
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('振动信号数据分布分析', fontsize=18, fontweight='bold')
        
        # 速度分布
        ax1.hist(speed_data, bins=30, alpha=0.7, color=self.colors['primary'], edgecolor='black')
        ax1.set_title('车辆速度分布', fontsize=16)
        ax1.set_xlabel('速度 (km/h)', fontsize=14)
        ax1.set_ylabel('频次', fontsize=14)
        ax1.grid(True, alpha=0.3)
        
        # 载重分布
        ax2.hist(load_data, bins=30, alpha=0.7, color=self.colors['secondary'], edgecolor='black')
        ax2.set_title('车辆载重分布', fontsize=16)
        ax2.set_xlabel('载重 (吨)', fontsize=14)
        ax2.set_ylabel('频次', fontsize=14)
        ax2.grid(True, alpha=0.3)
        
        # 轴型分布
        axle_counts = np.bincount(axle_data)
        ax3.bar([2, 3, 4], axle_counts[2:], alpha=0.7, color=self.colors['success'], edgecolor='black')
        ax3.set_title('车辆轴型分布', fontsize=16)
        ax3.set_xlabel('轴数', fontsize=14)
        ax3.set_ylabel('样本数', fontsize=14)
        ax3.grid(True, alpha=0.3)
        
        # 速度-载重散点图
        ax4.scatter(speed_data[:500], load_data[:500], alpha=0.6, color=self.colors['info'])
        ax4.set_title('速度-载重关系', fontsize=16)
        ax4.set_xlabel('速度 (km/h)', fontsize=14)
        ax4.set_ylabel('载重 (吨)', fontsize=14)
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'data_distribution_analysis_chinese.png', 
                   dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print("   ✅ 数据分布分析图已生成")
        
    def generate_feature_importance_analysis(self):
        """生成特征重要性分析图"""
        print("\n🔍 生成特征重要性分析图...")
        
        # 模拟特征重要性数据
        features = [f'传感器_{i:02d}_均值' for i in range(1, 11)]
        importance_scores = np.random.exponential(0.1, 10)
        importance_scores = importance_scores / importance_scores.sum()
        
        # 中文版本
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        fig.suptitle('特征重要性分析', fontsize=18, fontweight='bold')
        
        # 特征重要性条形图
        bars = ax1.barh(features, importance_scores, color=self.colors['primary'], alpha=0.8)
        ax1.set_title('前10个重要特征', fontsize=16)
        ax1.set_xlabel('重要性分数', fontsize=14)
        ax1.grid(True, alpha=0.3)
        
        # 特征重要性饼图
        ax2.pie(importance_scores, labels=features, autopct='%1.1f%%', startangle=90)
        ax2.set_title('特征重要性分布', fontsize=16)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'feature_importance_analysis_chinese.png', 
                   dpi=330, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print("   ✅ 特征重要性分析图已生成")

def main():
    """主函数"""
    generator = VisualizationGenerator()
    generator.generate_all_visualizations()
    
    print(f"\n🎉 学术级别可视化图表生成完成！")
    print(f"   所有图表已保存到: {generator.output_dir}")
    print(f"   图表分辨率: 330 DPI")
    print(f"   支持语言: 中文、英文")

if __name__ == "__main__":
    main()
