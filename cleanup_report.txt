项目清理报告
==================================================

删除的文件 (72):
------------------------------
- quick_start.py
- quick_start_demo.py
- run_complete_training.py
- run_deep_learning_optimization.py
- setup_real_data_training.py
- run_enhanced_system.py
- run_ml_training.py
- data_validation.py
- data_preprocessing.py
- migrate_real_data.py
- deploy_models.py
- generate_features.py
- test_deep_learning_models.py
- test_gpu_integration.py
- test_pytorch_gpu.py
- test_tensorflow_gpu.py
- test_tensorflow_simple.py
- check_dependencies.py
- check_packages.py
- check_pipeline_status.py
- diagnose_feature_extraction_detailed.py
- diagnose_ml_paths.py
- verify_installation.py
- verify_pipeline.py
- fix_chinese_fonts.py
- fix_data_format.py
- fix_ml_paths.py
- fix_package_detection.py
- quick_fix_ml_paths.py
- install_dependencies.py
- install_windows.bat
- font_config.py
- chinese_font_test.png
- tune_parameters.py
- data_validation_report.txt
- installation_report.txt
- verification_report.txt
- gpu_configuration_summary.md
- gpu_optimization.log
- processing_summary.json
- project_info.json
- deep_learning_optimization_results.json
- data_format_guide.md
- analyze_results.py
- COMPLETE_USER_GUIDE.md
- 增强版使用指南.md
- ./ml\axle_load_optimization.py
- ./ml\axle_type_optimization.py
- ./ml\bayesian_optimization.py
- ./ml\bp_neural_network.py
- ./ml\complete_ml_pipeline.py
- ./ml\deep_learning_enhanced_training.py
- ./ml\deep_learning_models.py
- ./ml\diagnose_ml_dataset.py
- ./ml\fix_ml_dataset.py
- ./ml\gpu_accelerated_training.py
- ./ml\gpu_check.py
- ./ml\gpu_optimized_training.py
- ./ml\model_optimization.py
- ./ml\model_replacer.py
- ./ml\performance_comparison.py
- ./ml\quick_optimization.py
- ./ml\temporal_convolutional_network.py
- ./ml\train_enhanced_models.py
- ./ml\train_ml_models.py
- ./ml\train_optimized_models.py
- ./ml\optimization.log
- ./ml\gpu_optimization_report.md
- ./ml\optimization_summary_report.md
- ./ml\轴型_classification_results.png
- ./ml\轴重_regression_results.png
- ./ml\速度_regression_results.png

删除的目录 (9):
------------------------------
- archive
- examples
- tools
- results
- raw_data
- training_datasets_clean
- docs
- analysis_results
- __pycache__

保留的核心文件:
------------------------------
- unified_vibration_analysis.py
- unified_ml_models.py
- install_requirements.py
- README_UNIFIED.md
- combined_features.csv
- requirements.txt
- core/
- ml/
- data/
- venv/
- 25吨_三轴_*
- 2吨_双轴_*
- 34.98吨_三轴_*
- 45.39吨_三轴_*
- 55.62吨_三轴_*
- training_datasets/
- models/
