#!/usr/bin/env python3
"""
优化版机器学习模型模块
使用超参数优化结果训练模型
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import r2_score, accuracy_score, classification_report, confusion_matrix
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.ensemble import GradientBoostingRegressor, GradientBoostingClassifier
from sklearn.ensemble import AdaBoostRegressor, AdaBoostClassifier
from sklearn.ensemble import ExtraTreesRegressor, ExtraTreesClassifier
from sklearn.svm import SVR, SVC
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
import time
import warnings
warnings.filterwarnings('ignore')

class OptimizedMLModels:
    """优化版机器学习模型类"""
    
    def __init__(self):
        """初始化"""
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.training_times = {}
        self.feature_importances = {}
        self.training_histories = {}
        
    def create_optimized_model(self, model_name: str, task_type: str, optimized_params: dict):
        """使用优化参数创建模型"""
        try:
            params = optimized_params.get(model_name, {})
            
            if model_name == 'Random Forest':
                if task_type == 'regression':
                    return RandomForestRegressor(**params) if params else RandomForestRegressor(n_estimators=100, random_state=42)
                else:
                    return RandomForestClassifier(**params) if params else RandomForestClassifier(n_estimators=100, random_state=42)
            
            elif model_name == 'Extra Trees':
                if task_type == 'regression':
                    return ExtraTreesRegressor(**params) if params else ExtraTreesRegressor(n_estimators=100, random_state=42)
                else:
                    return ExtraTreesClassifier(**params) if params else ExtraTreesClassifier(n_estimators=100, random_state=42)
            
            elif model_name == 'Gradient Boosting':
                if task_type == 'regression':
                    return GradientBoostingRegressor(**params) if params else GradientBoostingRegressor(n_estimators=100, random_state=42)
                else:
                    return GradientBoostingClassifier(**params) if params else GradientBoostingClassifier(n_estimators=100, random_state=42)
            
            elif model_name == 'XGBoost':
                try:
                    import xgboost as xgb
                    if task_type == 'regression':
                        return xgb.XGBRegressor(**params) if params else xgb.XGBRegressor(n_estimators=100, random_state=42)
                    else:
                        return xgb.XGBClassifier(**params) if params else xgb.XGBClassifier(n_estimators=100, random_state=42)
                except ImportError:
                    return None
            
            elif model_name == 'AdaBoost':
                if task_type == 'regression':
                    return AdaBoostRegressor(**params) if params else AdaBoostRegressor(n_estimators=100, random_state=42)
                else:
                    return AdaBoostClassifier(**params) if params else AdaBoostClassifier(n_estimators=100, random_state=42)
            
            elif model_name == 'SVM':
                if task_type == 'regression':
                    return SVR(**params) if params else SVR(kernel='rbf', C=1.0)
                else:
                    return SVC(**params) if params else SVC(kernel='rbf', C=1.0, probability=True, random_state=42)
            
            elif model_name == 'BP Neural Network':
                return self.create_optimized_bp_network(params, task_type)
            
            elif model_name == 'CNN-LSTM':
                return self.create_optimized_cnn_lstm(params, task_type)
            
            elif model_name == 'TCN':
                return self.create_optimized_tcn(params, task_type)
            
            else:
                return None
                
        except Exception as e:
            print(f"      ❌ {model_name} 创建失败: {str(e)}")
            return None
    
    def create_optimized_bp_network(self, params: dict, task_type: str):
        """创建优化的BP神经网络"""
        try:
            import tensorflow as tf
            from tensorflow.keras.models import Sequential
            from tensorflow.keras.layers import Dense, Dropout, BatchNormalization
            from tensorflow.keras.optimizers import Adam
            
            # 默认参数
            default_params = {
                'hidden_layers': 3,
                'neurons_layer1': 128,
                'neurons_layer2': 64,
                'neurons_layer3': 32,
                'dropout_rate': 0.3,
                'learning_rate': 0.001,
                'activation': 'relu',
                'batch_norm': True
            }
            
            # 合并优化参数
            model_params = {**default_params, **params}
            
            def create_model(input_dim):
                model = Sequential()
                
                # 输入层
                model.add(Dense(model_params['neurons_layer1'], 
                               input_dim=input_dim, 
                               activation=model_params['activation']))
                if model_params['batch_norm']:
                    model.add(BatchNormalization())
                model.add(Dropout(model_params['dropout_rate']))
                
                # 隐藏层
                neurons = [model_params['neurons_layer2'], model_params['neurons_layer3']]
                for i in range(model_params['hidden_layers'] - 1):
                    if i < len(neurons):
                        model.add(Dense(neurons[i], activation=model_params['activation']))
                        if model_params['batch_norm']:
                            model.add(BatchNormalization())
                        model.add(Dropout(model_params['dropout_rate']))
                
                # 输出层
                if task_type == 'regression':
                    model.add(Dense(1, activation='linear'))
                    model.compile(
                        optimizer=Adam(learning_rate=model_params['learning_rate']),
                        loss='mse',
                        metrics=['mae']
                    )
                else:
                    model.add(Dense(10, activation='softmax'))
                    model.compile(
                        optimizer=Adam(learning_rate=model_params['learning_rate']),
                        loss='sparse_categorical_crossentropy',
                        metrics=['accuracy']
                    )
                
                return model
            
            return create_model
            
        except ImportError:
            return None
    
    def create_optimized_cnn_lstm(self, params: dict, task_type: str):
        """创建优化的CNN-LSTM"""
        try:
            import tensorflow as tf
            from tensorflow.keras.models import Sequential
            from tensorflow.keras.layers import Dense, LSTM, Conv1D, MaxPooling1D, Dropout
            from tensorflow.keras.optimizers import Adam
            
            # 默认参数
            default_params = {
                'timesteps': 20,
                'cnn_filters1': 64,
                'cnn_filters2': 32,
                'cnn_kernel_size': 3,
                'lstm_units1': 50,
                'lstm_units2': 25,
                'dense_units': 25,
                'dropout_rate': 0.3,
                'learning_rate': 0.001
            }
            
            model_params = {**default_params, **params}
            
            def create_model(input_dim):
                timesteps = model_params['timesteps']
                features_per_step = input_dim // timesteps
                if features_per_step == 0:
                    features_per_step = 1
                    timesteps = input_dim
                
                model = Sequential()
                
                # CNN层
                model.add(Conv1D(
                    filters=model_params['cnn_filters1'],
                    kernel_size=model_params['cnn_kernel_size'],
                    activation='relu',
                    input_shape=(timesteps, features_per_step)
                ))
                model.add(MaxPooling1D(pool_size=2))
                model.add(Dropout(model_params['dropout_rate']))
                
                model.add(Conv1D(
                    filters=model_params['cnn_filters2'],
                    kernel_size=model_params['cnn_kernel_size'],
                    activation='relu'
                ))
                model.add(Dropout(model_params['dropout_rate']))
                
                # LSTM层
                model.add(LSTM(model_params['lstm_units1'], return_sequences=True))
                model.add(Dropout(model_params['dropout_rate']))
                model.add(LSTM(model_params['lstm_units2']))
                model.add(Dropout(model_params['dropout_rate']))
                
                # 全连接层
                model.add(Dense(model_params['dense_units'], activation='relu'))
                model.add(Dropout(model_params['dropout_rate']))
                
                # 输出层
                if task_type == 'regression':
                    model.add(Dense(1, activation='linear'))
                    model.compile(
                        optimizer=Adam(learning_rate=model_params['learning_rate']),
                        loss='mse',
                        metrics=['mae']
                    )
                else:
                    model.add(Dense(10, activation='softmax'))
                    model.compile(
                        optimizer=Adam(learning_rate=model_params['learning_rate']),
                        loss='sparse_categorical_crossentropy',
                        metrics=['accuracy']
                    )
                
                return model, timesteps, features_per_step
            
            return create_model
            
        except ImportError:
            return None
    
    def create_optimized_tcn(self, params: dict, task_type: str):
        """创建优化的TCN"""
        try:
            import tensorflow as tf
            from tensorflow.keras.models import Model
            from tensorflow.keras.layers import Dense, Conv1D, Dropout, BatchNormalization, Add, Activation, Input, GlobalAveragePooling1D
            from tensorflow.keras.optimizers import Adam
            
            # 默认参数
            default_params = {
                'timesteps': 25,
                'nb_filters': 64,
                'kernel_size': 3,
                'nb_stacks': 2,
                'dilations': [1, 2, 4, 8],
                'dropout_rate': 0.2,
                'learning_rate': 0.001
            }
            
            model_params = {**default_params, **params}
            
            def create_model(input_dim):
                timesteps = model_params['timesteps']
                features_per_step = input_dim // timesteps
                if features_per_step == 0:
                    features_per_step = 1
                    timesteps = input_dim
                
                def residual_block(x, dilation_rate, nb_filters, kernel_size, dropout_rate):
                    conv1 = Conv1D(filters=nb_filters, kernel_size=kernel_size,
                                  dilation_rate=dilation_rate, padding='causal',
                                  activation='relu')(x)
                    conv1 = BatchNormalization()(conv1)
                    conv1 = Dropout(dropout_rate)(conv1)
                    
                    conv2 = Conv1D(filters=nb_filters, kernel_size=kernel_size,
                                  dilation_rate=dilation_rate, padding='causal',
                                  activation='relu')(conv1)
                    conv2 = BatchNormalization()(conv2)
                    conv2 = Dropout(dropout_rate)(conv2)
                    
                    if x.shape[-1] != nb_filters:
                        x = Conv1D(nb_filters, 1, padding='same')(x)
                    
                    res = Add()([x, conv2])
                    return Activation('relu')(res)
                
                inputs = Input(shape=(timesteps, features_per_step))
                x = inputs
                
                # TCN层
                for stack in range(model_params['nb_stacks']):
                    for dilation in model_params['dilations']:
                        x = residual_block(x, dilation, model_params['nb_filters'], 
                                         model_params['kernel_size'], model_params['dropout_rate'])
                
                # 全局平均池化
                x = GlobalAveragePooling1D()(x)
                
                # 全连接层
                x = Dense(64, activation='relu')(x)
                x = BatchNormalization()(x)
                x = Dropout(model_params['dropout_rate'])(x)
                
                # 输出层
                if task_type == 'regression':
                    outputs = Dense(1, activation='linear')(x)
                    model = Model(inputs=inputs, outputs=outputs)
                    model.compile(
                        optimizer=Adam(learning_rate=model_params['learning_rate']),
                        loss='mse',
                        metrics=['mae']
                    )
                else:
                    outputs = Dense(10, activation='softmax')(x)
                    model = Model(inputs=inputs, outputs=outputs)
                    model.compile(
                        optimizer=Adam(learning_rate=model_params['learning_rate']),
                        loss='sparse_categorical_crossentropy',
                        metrics=['accuracy']
                    )
                
                return model, timesteps, features_per_step
            
            return create_model
            
        except ImportError:
            return None
    
    def train_all_models(self, X: np.ndarray, y: np.ndarray, task_type: str, 
                        dataset_name: str, optimized_params: dict = None) -> dict:
        """训练所有优化模型"""
        print(f"  🔧 使用优化参数训练模型...")
        
        if optimized_params is None:
            optimized_params = {}
        
        # 数据预处理
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 保存scaler
        self.scalers[dataset_name] = scaler
        
        # 处理分类标签
        if task_type == 'classification':
            encoder = LabelEncoder()
            y_encoded = encoder.fit_transform(y)
            self.encoders[dataset_name] = encoder
            y_processed = y_encoded
        else:
            y_processed = y
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y_processed, test_size=0.2, random_state=42
        )
        
        print(f"  训练集: {X_train.shape}, 测试集: {X_test.shape}")
        
        results = {}
        
        # 模型列表
        model_names = [
            'Random Forest', 'Extra Trees', 'Gradient Boosting', 
            'XGBoost', 'AdaBoost', 'SVM',
            'BP Neural Network', 'CNN-LSTM', 'TCN'
        ]
        
        for model_name in model_names:
            try:
                print(f"    训练 {model_name}...")
                start_time = time.time()
                
                # 创建优化模型
                model_creator = self.create_optimized_model(model_name, task_type, optimized_params)
                
                if model_creator is None:
                    print(f"      ⚠️  {model_name} 不可用，跳过")
                    continue
                
                # 训练模型
                if model_name in ['BP Neural Network', 'CNN-LSTM', 'TCN']:
                    # 深度学习模型
                    if callable(model_creator):
                        if model_name == 'BP Neural Network':
                            model = model_creator(X_scaled.shape[1])
                            history = model.fit(X_train, y_train, epochs=100, batch_size=32, 
                                              validation_split=0.2, verbose=0)
                            y_pred = model.predict(X_test, verbose=0)
                            if task_type == 'regression':
                                y_pred = y_pred.flatten()
                            else:
                                y_pred = np.argmax(y_pred, axis=1)
                        else:
                            # CNN-LSTM 或 TCN
                            model_result = model_creator(X_scaled.shape[1])
                            if len(model_result) == 3:
                                model, timesteps, features_per_step = model_result
                                X_train_reshaped = self._reshape_for_sequence(X_train, timesteps, features_per_step)
                                X_test_reshaped = self._reshape_for_sequence(X_test, timesteps, features_per_step)
                                
                                history = model.fit(X_train_reshaped, y_train, epochs=50, batch_size=32,
                                                  validation_split=0.2, verbose=0)
                                y_pred = model.predict(X_test_reshaped, verbose=0)
                                if task_type == 'regression':
                                    y_pred = y_pred.flatten()
                                else:
                                    y_pred = np.argmax(y_pred, axis=1)
                            else:
                                continue
                    else:
                        continue
                else:
                    # 传统机器学习模型
                    model = model_creator
                    model.fit(X_train, y_train)
                    y_pred = model.predict(X_test)
                    
                    # 保存特征重要性
                    if hasattr(model, 'feature_importances_'):
                        self.feature_importances[model_name] = model.feature_importances_
                
                # 计算评估指标
                training_time = time.time() - start_time
                
                if task_type == 'regression':
                    r2 = r2_score(y_test, y_pred)
                    mae = np.mean(np.abs(y_test - y_pred))
                    rmse = np.sqrt(np.mean((y_test - y_pred) ** 2))
                    
                    metrics = {
                        'r2_score': r2,
                        'mae': mae,
                        'rmse': rmse,
                        'training_time': training_time
                    }
                    
                    print(f"      R² = {r2:.4f}, MAE = {mae:.4f} (时间: {training_time:.2f}s)")
                    
                else:
                    accuracy = accuracy_score(y_test, y_pred)
                    
                    metrics = {
                        'accuracy': accuracy,
                        'training_time': training_time
                    }
                    
                    print(f"      准确率 = {accuracy:.4f} (时间: {training_time:.2f}s)")
                
                results[model_name] = metrics
                
                # 保存模型
                model_filename = f"optimized_model_{model_name.lower().replace('-', '_').replace(' ', '_')}.pkl"
                if hasattr(model, 'save'):
                    model.save(model_filename.replace('.pkl', '.h5'))
                else:
                    joblib.dump(model, model_filename)
                
            except Exception as e:
                print(f"      ❌ {model_name} 训练失败: {str(e)}")
                results[model_name] = {'error': str(e)}
                continue
        
        return results
    
    def _reshape_for_sequence(self, X: np.ndarray, timesteps: int, features_per_step: int) -> np.ndarray:
        """为序列模型重塑数据"""
        n_samples = X.shape[0]
        total_features_needed = timesteps * features_per_step
        
        if X.shape[1] >= total_features_needed:
            X_reshaped = X[:, :total_features_needed]
        else:
            padding = np.zeros((n_samples, total_features_needed - X.shape[1]))
            X_reshaped = np.concatenate([X, padding], axis=1)
        
        return X_reshaped.reshape(n_samples, timesteps, features_per_step)
    
    def create_visualizations(self, results: dict, dataset_name: str, task_type: str):
        """创建可视化图表"""
        # 重用原有的可视化代码
        from unified_ml_models import UnifiedMLModels
        base_trainer = UnifiedMLModels()
        base_trainer.feature_importances = self.feature_importances
        base_trainer.training_histories = self.training_histories
        base_trainer.create_visualizations(results, dataset_name, task_type)

    def select_best_model(self, results: dict, task_type: str) -> dict:
        """选择最佳模型"""
        print(f"    🏆 选择最佳模型...")

        if not results:
            return {
                'best_model': 'None',
                'best_score': 0,
                'recommendation_reason': '没有成功训练的模型'
            }

        # 过滤出成功训练的模型
        valid_results = {name: metrics for name, metrics in results.items()
                        if 'error' not in metrics}

        if not valid_results:
            return {
                'best_model': 'None',
                'best_score': 0,
                'recommendation_reason': '所有模型训练失败'
            }

        # 根据任务类型选择评估指标
        if task_type == 'regression':
            score_key = 'r2_score'
            score_name = 'R²'
        else:
            score_key = 'accuracy'
            score_name = '准确率'

        # 找到最佳模型
        best_model = None
        best_score = -float('inf')

        for model_name, metrics in valid_results.items():
            score = metrics.get(score_key, 0)
            if score > best_score:
                best_score = score
                best_model = model_name

        # 生成推荐理由
        if best_model:
            training_time = valid_results[best_model].get('training_time', 0)

            # 分析模型特点
            model_characteristics = {
                'Random Forest': '集成学习，鲁棒性强，特征重要性明确',
                'Extra Trees': '极端随机树，泛化能力强，训练速度快',
                'Gradient Boosting': '梯度提升，预测精度高，适合复杂模式',
                'XGBoost': '优化的梯度提升，性能卓越，工业标准',
                'AdaBoost': '自适应提升，对噪声敏感度低',
                'SVM': '支持向量机，适合高维数据，理论基础扎实',
                'BP Neural Network': '深度学习，非线性拟合能力强',
                'CNN-LSTM': '时序卷积网络，适合序列数据',
                'TCN': '时间卷积网络，长期依赖建模能力强'
            }

            characteristic = model_characteristics.get(best_model, '优秀的机器学习模型')

            recommendation_reason = f"{best_model}在{len(valid_results)}个模型中表现最佳，{score_name}达到{best_score:.4f}。{characteristic}，训练时间{training_time:.2f}秒。"

            # 添加性能等级评估
            if task_type == 'regression':
                if best_score >= 0.9:
                    performance_level = "优秀"
                elif best_score >= 0.8:
                    performance_level = "良好"
                elif best_score >= 0.7:
                    performance_level = "中等"
                else:
                    performance_level = "需要改进"
            else:
                if best_score >= 0.95:
                    performance_level = "优秀"
                elif best_score >= 0.9:
                    performance_level = "良好"
                elif best_score >= 0.8:
                    performance_level = "中等"
                else:
                    performance_level = "需要改进"

            recommendation_reason += f" 性能等级：{performance_level}。"

        else:
            recommendation_reason = "未找到有效的最佳模型"

        return {
            'best_model': best_model or 'None',
            'best_score': best_score if best_model else 0,
            'recommendation_reason': recommendation_reason,
            'total_models': len(results),
            'successful_models': len(valid_results),
            'score_type': score_name
        }

    def generate_performance_statistics(self, results: dict, task_type: str) -> dict:
        """生成性能统计信息"""
        print(f"    📊 生成性能统计...")

        if not results:
            return {'error': '没有结果数据'}

        # 过滤出成功训练的模型
        valid_results = {name: metrics for name, metrics in results.items()
                        if 'error' not in metrics}

        if not valid_results:
            return {'error': '所有模型训练失败'}

        # 根据任务类型选择评估指标
        if task_type == 'regression':
            score_key = 'r2_score'
            score_name = 'R²'
        else:
            score_key = 'accuracy'
            score_name = '准确率'

        # 收集分数和训练时间
        scores = []
        training_times = []
        model_names = []

        for model_name, metrics in valid_results.items():
            score = metrics.get(score_key, 0)
            time_taken = metrics.get('training_time', 0)

            scores.append(score)
            training_times.append(time_taken)
            model_names.append(model_name)

        if not scores:
            return {'error': '没有有效的性能数据'}

        # 计算统计信息
        statistics = {
            'performance_stats': {
                'mean_score': np.mean(scores),
                'std_score': np.std(scores),
                'min_score': np.min(scores),
                'max_score': np.max(scores),
                'median_score': np.median(scores),
                'score_type': score_name
            },
            'timing_stats': {
                'mean_time': np.mean(training_times),
                'std_time': np.std(training_times),
                'min_time': np.min(training_times),
                'max_time': np.max(training_times),
                'total_time': np.sum(training_times)
            },
            'model_ranking': [],
            'summary': {}
        }

        # 模型排名
        model_scores = list(zip(model_names, scores, training_times))
        model_scores.sort(key=lambda x: x[1], reverse=True)  # 按分数降序排列

        for i, (name, score, time_taken) in enumerate(model_scores):
            statistics['model_ranking'].append({
                'rank': i + 1,
                'model': name,
                'score': score,
                'training_time': time_taken,
                'score_type': score_name
            })

        # 生成总结
        best_model = model_scores[0][0]
        best_score = model_scores[0][1]
        worst_model = model_scores[-1][0]
        worst_score = model_scores[-1][1]

        fastest_model = min(model_scores, key=lambda x: x[2])
        slowest_model = max(model_scores, key=lambda x: x[2])

        statistics['summary'] = {
            'total_models': len(results),
            'successful_models': len(valid_results),
            'failed_models': len(results) - len(valid_results),
            'best_performer': {
                'model': best_model,
                'score': best_score,
                'score_type': score_name
            },
            'worst_performer': {
                'model': worst_model,
                'score': worst_score,
                'score_type': score_name
            },
            'fastest_model': {
                'model': fastest_model[0],
                'time': fastest_model[2]
            },
            'slowest_model': {
                'model': slowest_model[0],
                'time': slowest_model[2]
            },
            'performance_range': best_score - worst_score,
            'average_performance': statistics['performance_stats']['mean_score']
        }

        return statistics

def main():
    """测试函数"""
    from sklearn.datasets import make_regression
    
    print("🧪 测试优化版机器学习模型...")
    
    # 创建测试数据
    X, y = make_regression(n_samples=500, n_features=20, noise=0.1, random_state=42)
    
    # 模拟优化参数
    optimized_params = {
        'Random Forest': {'n_estimators': 150, 'max_depth': 12, 'random_state': 42},
        'Extra Trees': {'n_estimators': 120, 'max_depth': 15, 'random_state': 42}
    }
    
    # 初始化训练器
    trainer = OptimizedMLModels()
    
    # 训练模型
    results = trainer.train_all_models(X, y, 'regression', 'test', optimized_params)
    
    print(f"\n📊 训练结果:")
    for model_name, metrics in results.items():
        if 'error' not in metrics:
            score = metrics.get('r2_score', 0)
            time_taken = metrics.get('training_time', 0)
            print(f"   {model_name}: R² = {score:.4f} (时间: {time_taken:.2f}s)")
    
    print("✅ 测试完成!")

if __name__ == "__main__":
    main()
