#!/usr/bin/env python3
"""
振动信号数据验证脚本
验证原始数据格式、完整性和质量
"""

import pandas as pd
import numpy as np
import os
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataValidator:
    """数据验证器"""
    
    def __init__(self, raw_data_dir: str = './raw_data'):
        """
        初始化数据验证器
        
        Args:
            raw_data_dir: 原始数据目录路径
        """
        self.raw_data_dir = raw_data_dir
        self.validation_results = {}
        
    def validate_directory_structure(self) -> bool:
        """验证目录结构"""
        logger.info("🔍 验证目录结构...")
        
        required_dirs = [
            'vibration_signals',
            'speed_labels', 
            'load_labels',
            'type_labels'
        ]
        
        missing_dirs = []
        for dir_name in required_dirs:
            dir_path = os.path.join(self.raw_data_dir, dir_name)
            if not os.path.exists(dir_path):
                missing_dirs.append(dir_name)
                logger.warning(f"❌ 缺少目录: {dir_path}")
            else:
                logger.info(f"✅ 目录存在: {dir_path}")
        
        if missing_dirs:
            logger.error(f"❌ 目录结构验证失败，缺少: {missing_dirs}")
            return False
        
        logger.info("✅ 目录结构验证通过")
        return True
    
    def safe_read_csv(self, file_path: str) -> Optional[pd.DataFrame]:
        """安全读取CSV文件，自动处理编码问题"""
        import chardet

        # 尝试检测编码
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)
                result = chardet.detect(raw_data)
                detected_encoding = result['encoding']
        except:
            detected_encoding = 'gbk'

        # 尝试多种编码
        encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
        if detected_encoding and detected_encoding not in encodings_to_try:
            encodings_to_try.insert(0, detected_encoding)

        for encoding in encodings_to_try:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                logger.info(f"✅ 成功使用编码 {encoding} 读取文件")
                return df
            except UnicodeDecodeError:
                continue
            except Exception as e:
                logger.warning(f"读取文件失败 (编码 {encoding}): {str(e)}")
                continue

        logger.error(f"❌ 无法读取文件: {file_path}")
        return None

    def validate_vibration_file(self, file_path: str) -> Dict:
        """验证单个振动信号文件"""
        logger.info(f"🔍 验证振动信号文件: {os.path.basename(file_path)}")

        validation_result = {
            'file_path': file_path,
            'valid': False,
            'errors': [],
            'warnings': [],
            'stats': {}
        }

        try:
            # 安全读取文件
            df = self.safe_read_csv(file_path)
            if df is None:
                validation_result['errors'].append("文件读取失败，可能是编码问题")
                return validation_result
            
            # 检查必需列
            required_columns = ['timestamp']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                validation_result['errors'].append(f"缺少必需列: {missing_columns}")
            
            # 检查传感器列
            sensor_columns = [col for col in df.columns if 'sensor' in col.lower()]
            if len(sensor_columns) < 3:
                validation_result['warnings'].append(f"传感器列数量较少: {len(sensor_columns)}")
            
            # 检查时间戳格式
            try:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                validation_result['stats']['timestamp_format'] = 'valid'
            except:
                validation_result['errors'].append("时间戳格式无效")
            
            # 检查数据完整性
            total_rows = len(df)
            missing_values = df.isnull().sum().sum()
            missing_ratio = missing_values / (total_rows * len(df.columns))
            
            validation_result['stats'].update({
                'total_rows': total_rows,
                'total_columns': len(df.columns),
                'missing_values': missing_values,
                'missing_ratio': missing_ratio,
                'file_size_mb': os.path.getsize(file_path) / (1024 * 1024)
            })
            
            # 检查缺失值比例
            if missing_ratio > 0.05:
                validation_result['errors'].append(f"缺失值比例过高: {missing_ratio:.2%}")
            elif missing_ratio > 0.01:
                validation_result['warnings'].append(f"缺失值比例较高: {missing_ratio:.2%}")
            
            # 检查文件大小
            if validation_result['stats']['file_size_mb'] > 100:
                validation_result['warnings'].append(f"文件过大: {validation_result['stats']['file_size_mb']:.1f}MB")
            
            # 检查采样率
            if len(df) > 1:
                time_diff = (df['timestamp'].iloc[1] - df['timestamp'].iloc[0]).total_seconds()
                sampling_rate = 1 / time_diff if time_diff > 0 else 0
                validation_result['stats']['sampling_rate_hz'] = sampling_rate
                
                if sampling_rate < 500:
                    validation_result['warnings'].append(f"采样率较低: {sampling_rate:.1f}Hz")
            
            # 检查数值范围
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                if 'sensor' in col.lower():
                    col_std = df[col].std()
                    col_mean = abs(df[col].mean())
                    if col_std == 0:
                        validation_result['warnings'].append(f"传感器列 {col} 无变化")
                    elif col_mean > 100:
                        validation_result['warnings'].append(f"传感器列 {col} 数值异常大")
            
            # 如果没有错误，标记为有效
            if not validation_result['errors']:
                validation_result['valid'] = True
                logger.info(f"✅ 文件验证通过: {os.path.basename(file_path)}")
            else:
                logger.error(f"❌ 文件验证失败: {os.path.basename(file_path)}")
                
        except Exception as e:
            validation_result['errors'].append(f"文件读取错误: {str(e)}")
            logger.error(f"❌ 文件读取失败: {os.path.basename(file_path)} - {str(e)}")
        
        return validation_result
    
    def validate_label_file(self, file_path: str, label_type: str) -> Dict:
        """验证标签文件"""
        logger.info(f"🔍 验证{label_type}标签文件: {os.path.basename(file_path)}")

        validation_result = {
            'file_path': file_path,
            'label_type': label_type,
            'valid': False,
            'errors': [],
            'warnings': [],
            'stats': {}
        }

        try:
            # 安全读取文件
            df = self.safe_read_csv(file_path)
            if df is None:
                validation_result['errors'].append("标签文件读取失败，可能是编码问题")
                return validation_result
            
            # 检查必需列
            required_columns = ['timestamp']
            if label_type == 'speed':
                required_columns.extend(['speed_kmh'])
            elif label_type == 'load':
                required_columns.extend(['total_load'])
            elif label_type == 'type':
                required_columns.extend(['axle_type'])
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                validation_result['errors'].append(f"缺少必需列: {missing_columns}")
            
            # 检查时间戳
            try:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
            except:
                validation_result['errors'].append("时间戳格式无效")
            
            # 统计信息
            validation_result['stats'] = {
                'total_records': len(df),
                'date_range': f"{df['timestamp'].min()} 到 {df['timestamp'].max()}" if 'timestamp' in df.columns else 'N/A'
            }
            
            # 特定验证
            if label_type == 'speed' and 'speed_kmh' in df.columns:
                speed_stats = {
                    'speed_min': df['speed_kmh'].min(),
                    'speed_max': df['speed_kmh'].max(),
                    'speed_mean': df['speed_kmh'].mean()
                }
                validation_result['stats'].update(speed_stats)
                
                if speed_stats['speed_max'] > 200:
                    validation_result['warnings'].append(f"速度值异常高: {speed_stats['speed_max']}")
                if speed_stats['speed_min'] < 0:
                    validation_result['errors'].append(f"速度值为负: {speed_stats['speed_min']}")
            
            if not validation_result['errors']:
                validation_result['valid'] = True
                logger.info(f"✅ {label_type}标签文件验证通过")
            else:
                logger.error(f"❌ {label_type}标签文件验证失败")
                
        except Exception as e:
            validation_result['errors'].append(f"文件读取错误: {str(e)}")
            logger.error(f"❌ {label_type}标签文件读取失败: {str(e)}")
        
        return validation_result
    
    def validate_all_data(self) -> Dict:
        """验证所有数据"""
        logger.info("🚀 开始完整数据验证...")
        
        # 验证目录结构
        if not self.validate_directory_structure():
            return {'success': False, 'error': '目录结构验证失败'}
        
        results = {
            'success': True,
            'vibration_files': [],
            'label_files': {},
            'summary': {}
        }
        
        # 验证振动信号文件
        vibration_dir = os.path.join(self.raw_data_dir, 'vibration_signals')
        if os.path.exists(vibration_dir):
            vibration_files = [f for f in os.listdir(vibration_dir) if f.endswith('.csv')]
            logger.info(f"发现 {len(vibration_files)} 个振动信号文件")
            
            for file_name in vibration_files:
                file_path = os.path.join(vibration_dir, file_name)
                result = self.validate_vibration_file(file_path)
                results['vibration_files'].append(result)
        
        # 验证标签文件
        label_types = ['speed', 'load', 'type']
        for label_type in label_types:
            label_dir = os.path.join(self.raw_data_dir, f'{label_type}_labels')
            if os.path.exists(label_dir):
                label_files = [f for f in os.listdir(label_dir) if f.endswith('.csv')]
                results['label_files'][label_type] = []
                
                for file_name in label_files:
                    file_path = os.path.join(label_dir, file_name)
                    result = self.validate_label_file(file_path, label_type)
                    results['label_files'][label_type].append(result)
        
        # 生成总结
        total_files = len(results['vibration_files'])
        valid_files = sum(1 for r in results['vibration_files'] if r['valid'])
        
        results['summary'] = {
            'total_vibration_files': total_files,
            'valid_vibration_files': valid_files,
            'validation_success_rate': valid_files / total_files if total_files > 0 else 0,
            'total_label_files': sum(len(files) for files in results['label_files'].values()),
            'validation_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"📊 数据验证完成:")
        logger.info(f"   振动信号文件: {valid_files}/{total_files} 通过验证")
        logger.info(f"   验证成功率: {results['summary']['validation_success_rate']:.1%}")
        
        return results
    
    def generate_validation_report(self, results: Dict, output_file: str = 'data_validation_report.txt'):
        """生成验证报告"""
        logger.info(f"📝 生成验证报告: {output_file}")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("振动信号数据验证报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"验证时间: {results['summary']['validation_timestamp']}\n\n")
            
            # 总结
            f.write("📊 验证总结\n")
            f.write("-" * 30 + "\n")
            f.write(f"振动信号文件总数: {results['summary']['total_vibration_files']}\n")
            f.write(f"验证通过文件数: {results['summary']['valid_vibration_files']}\n")
            f.write(f"验证成功率: {results['summary']['validation_success_rate']:.1%}\n")
            f.write(f"标签文件总数: {results['summary']['total_label_files']}\n\n")
            
            # 详细结果
            f.write("📋 详细验证结果\n")
            f.write("-" * 30 + "\n")
            
            for i, result in enumerate(results['vibration_files'], 1):
                f.write(f"\n{i}. {os.path.basename(result['file_path'])}\n")
                f.write(f"   状态: {'✅ 通过' if result['valid'] else '❌ 失败'}\n")
                
                if result['stats']:
                    f.write(f"   行数: {result['stats'].get('total_rows', 'N/A')}\n")
                    f.write(f"   列数: {result['stats'].get('total_columns', 'N/A')}\n")
                    f.write(f"   缺失值比例: {result['stats'].get('missing_ratio', 0):.2%}\n")
                    f.write(f"   文件大小: {result['stats'].get('file_size_mb', 0):.1f}MB\n")
                    if 'sampling_rate_hz' in result['stats']:
                        f.write(f"   采样率: {result['stats']['sampling_rate_hz']:.1f}Hz\n")
                
                if result['errors']:
                    f.write(f"   错误: {'; '.join(result['errors'])}\n")
                if result['warnings']:
                    f.write(f"   警告: {'; '.join(result['warnings'])}\n")
        
        logger.info(f"✅ 验证报告已保存: {output_file}")

def main():
    """主函数"""
    print("🔍 振动信号数据验证工具")
    print("=" * 50)
    
    # 创建验证器
    validator = DataValidator('./raw_data')
    
    # 执行验证
    results = validator.validate_all_data()
    
    if results['success']:
        # 生成报告
        validator.generate_validation_report(results)
        
        # 显示结果
        summary = results['summary']
        print(f"\n📊 验证完成:")
        print(f"   总文件数: {summary['total_vibration_files']}")
        print(f"   通过验证: {summary['valid_vibration_files']}")
        print(f"   成功率: {summary['validation_success_rate']:.1%}")
        
        if summary['validation_success_rate'] >= 0.8:
            print("✅ 数据质量良好，可以进行下一步处理")
        else:
            print("⚠️  数据质量需要改善，请检查错误文件")
    else:
        print(f"❌ 验证失败: {results.get('error', '未知错误')}")

if __name__ == "__main__":
    main()
