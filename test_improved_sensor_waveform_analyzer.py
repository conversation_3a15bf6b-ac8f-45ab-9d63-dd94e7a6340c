#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script for Improved Sensor Waveform Analyzer
Tests the enhanced functionality with consistent data extraction and detailed feature analysis

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import sys
import numpy as np
import pandas as pd
from pathlib import Path
import time

def create_realistic_vehicle_passage_data():
    """Create realistic vehicle passage data with proper event structure"""
    print("🔧 Creating realistic vehicle passage test data...")
    
    try:
        # Parameters for realistic vehicle passage simulation
        total_duration = 5.0  # seconds (longer to test extraction)
        sampling_rate = 1000  # Hz
        n_samples = int(total_duration * sampling_rate)
        time_vector = np.linspace(0, total_duration, n_samples)
        
        # Create realistic vehicle passage event
        np.random.seed(42)
        
        # Vehicle parameters
        vehicle_speed = 72  # km/h
        axle_count = 3
        axle_weights = [8, 12, 10]  # tons per axle
        axle_spacing = 0.15  # seconds between axles
        
        # Event timing (vehicle passes around 2.5 seconds)
        event_center = 2.5
        
        # Create base signal with background noise
        data = {'count': range(n_samples)}
        
        for sensor_id in range(1, 21):
            # Initialize with background noise
            signal = np.random.normal(0, 0.3, n_samples)
            
            # Add vehicle passage event
            for axle_idx, (axle_time_offset, axle_weight) in enumerate(zip(
                [0, axle_spacing, 2*axle_spacing], axle_weights)):
                
                # Axle passage time
                axle_time = event_center + axle_time_offset
                axle_sample = int(axle_time * sampling_rate)
                
                # Create realistic axle response
                response_duration = 0.2  # seconds
                response_samples = int(response_duration * sampling_rate)
                
                start_idx = max(0, axle_sample - response_samples//2)
                end_idx = min(n_samples, axle_sample + response_samples//2)
                
                # Generate damped oscillation response
                t_response = np.linspace(0, response_duration, end_idx - start_idx)
                
                # Sensor-specific frequency response
                if sensor_id <= 5:  # Group 1
                    frequency = 45 + sensor_id * 2
                    depth_factor = 1.2  # Deeper sensors, stronger response
                elif sensor_id <= 10:  # Group 2
                    frequency = 55 + (sensor_id-5) * 2
                    depth_factor = 1.2
                elif sensor_id <= 15:  # Group 3
                    frequency = 40 + (sensor_id-10) * 2
                    depth_factor = 1.0  # Shallower sensors
                else:  # Group 4
                    frequency = 50 + (sensor_id-15) * 2
                    depth_factor = 1.0
                
                # Amplitude based on axle weight and sensor characteristics
                base_amplitude = axle_weight * 0.8 * depth_factor
                
                # Add some sensor-specific variation
                if sensor_id in [6, 16]:  # Overtaking lane sensors
                    base_amplitude *= 0.6  # Weaker response
                
                # Damped sinusoidal response
                damping = 8.0
                envelope = np.exp(-damping * t_response)
                axle_response = base_amplitude * envelope * np.sin(2 * np.pi * frequency * t_response)
                
                # Add to signal
                signal[start_idx:end_idx] += axle_response
            
            # Add some structural vibrations
            structural_freq = 25 + sensor_id * 0.5
            structural_amplitude = 0.4
            signal += structural_amplitude * np.sin(2 * np.pi * structural_freq * time_vector) * \
                     np.exp(-((time_vector - event_center)**2) / (2 * 0.5**2))  # Gaussian envelope
            
            # Add some random spikes (measurement artifacts)
            spike_indices = np.random.choice(n_samples, 3, replace=False)
            signal[spike_indices] += np.random.uniform(-2, 2, 3)
            
            # Store in dataframe
            data[f'Sensor_{sensor_id:02d}'] = signal
        
        # Add timestamp column
        timestamps = pd.date_range('2024-01-01 10:00:00', periods=n_samples, freq='1ms')
        data['timestamp'] = timestamps
        
        # Create DataFrame and save
        df = pd.DataFrame(data)
        test_file_path = 'test_realistic_vehicle_passage.csv'
        df.to_csv(test_file_path, index=False)
        
        print(f"   ✅ Realistic test data created: {test_file_path}")
        print(f"   📊 Data shape: {df.shape}")
        print(f"   ⏱️ Total duration: {total_duration} seconds")
        print(f"   📈 Sampling rate: {sampling_rate} Hz")
        print(f"   🔢 Sensors: {len([col for col in df.columns if 'Sensor' in col])}")
        print(f"   🚗 Simulated vehicle: {axle_count} axles, {vehicle_speed} km/h")
        print(f"   📍 Event center: {event_center}s")
        
        return test_file_path, df
        
    except Exception as e:
        print(f"   ❌ Error creating realistic test data: {str(e)}")
        return None, None

def test_improved_data_extraction():
    """Test improved data extraction with vehicle passage detection"""
    print("\n🧪 Testing improved data extraction...")
    
    try:
        from sensor_waveform_analyzer import SensorWaveformAnalyzer
        
        # Create test data
        test_file, test_df = create_realistic_vehicle_passage_data()
        if test_file is None:
            return False
        
        # Initialize analyzer
        analyzer = SensorWaveformAnalyzer(
            output_dir='test_improved_charts',
            file_prefix='improved_test_'
        )
        
        # Test data loading with extraction
        print("   📂 Testing improved data loading with vehicle passage detection...")
        success = analyzer.load_sensor_waveform_data(test_file, 'Sensor_01')
        if not success:
            print("   ❌ Improved data loading failed")
            return False
        
        # Verify extracted data length
        expected_length = 1000  # 1 second at 1000 Hz
        actual_length = len(analyzer.sensor_data)
        
        print(f"   📊 Extracted data length: {actual_length} points")
        print(f"   📊 Expected length: {expected_length} points")
        print(f"   📊 Duration: {analyzer.time_vector[-1]:.3f} seconds")
        
        # Check if extraction is reasonable (should be close to 1 second)
        if 800 <= actual_length <= 1200:  # Allow some tolerance
            print("   ✅ Data extraction length is reasonable")
        else:
            print("   ⚠️ Data extraction length may be incorrect")
        
        # Test feature extraction
        print("   🔧 Testing feature extraction on extracted data...")
        success = analyzer.extract_30_features()
        if not success:
            print("   ❌ Feature extraction failed")
            return False
        
        # Verify exactly 30 features
        if len(analyzer.features) == 30:
            print(f"   ✅ Exactly 30 features extracted")
        else:
            print(f"   ❌ Expected 30 features, got {len(analyzer.features)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Improved data extraction test failed: {str(e)}")
        return False

def test_enhanced_peak_detection():
    """Test enhanced peak detection with positive/negative peaks"""
    print("\n🧪 Testing enhanced peak detection...")
    
    try:
        from sensor_waveform_analyzer import SensorWaveformAnalyzer
        
        # Create test data
        test_file, test_df = create_realistic_vehicle_passage_data()
        if test_file is None:
            return False
        
        # Initialize analyzer
        analyzer = SensorWaveformAnalyzer(
            output_dir='test_improved_charts',
            file_prefix='peak_test_'
        )
        
        # Load data
        success = analyzer.load_sensor_waveform_data(test_file, 'Sensor_05')
        if not success:
            return False
        
        # Test waveform plot generation with enhanced peak detection
        print("   📈 Testing enhanced waveform plot with peak detection...")
        success = analyzer.generate_waveform_plot()
        if not success:
            print("   ❌ Enhanced waveform plot generation failed")
            return False
        
        # Verify peak detection results
        max_positive = np.max(analyzer.sensor_data)
        min_negative = np.min(analyzer.sensor_data)
        peak_to_peak = max_positive - min_negative
        
        print(f"   📊 Max positive peak: {max_positive:.3f} mg")
        print(f"   📊 Min negative peak: {min_negative:.3f} mg")
        print(f"   📊 Peak-to-peak: {peak_to_peak:.3f} mg")
        
        # Check if peaks are reasonable
        if abs(max_positive) > 0.1 and abs(min_negative) > 0.1:
            print("   ✅ Peak detection found reasonable values")
        else:
            print("   ⚠️ Peak values may be too small")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Enhanced peak detection test failed: {str(e)}")
        return False

def test_detailed_features_analysis():
    """Test detailed features analysis generation"""
    print("\n🧪 Testing detailed features analysis...")
    
    try:
        from sensor_waveform_analyzer import SensorWaveformAnalyzer
        
        # Create test data
        test_file, test_df = create_realistic_vehicle_passage_data()
        if test_file is None:
            return False
        
        # Initialize analyzer
        analyzer = SensorWaveformAnalyzer(
            output_dir='test_improved_charts',
            file_prefix='detailed_test_'
        )
        
        # Load data and extract features
        success = analyzer.load_sensor_waveform_data(test_file, 'Sensor_10')
        if not success:
            return False
        
        success = analyzer.extract_30_features()
        if not success:
            return False
        
        # Test detailed features analysis
        print("   🔬 Testing detailed features analysis generation...")
        success = analyzer.generate_detailed_features_analysis()
        if not success:
            print("   ❌ Detailed features analysis generation failed")
            return False
        
        print("   ✅ Detailed features analysis generated successfully")
        return True
        
    except Exception as e:
        print(f"   ❌ Detailed features analysis test failed: {str(e)}")
        return False

def test_complete_improved_workflow():
    """Test complete improved workflow"""
    print("\n🧪 Testing complete improved workflow...")
    
    try:
        from sensor_waveform_analyzer import SensorWaveformAnalyzer
        
        # Create test data
        test_file, test_df = create_realistic_vehicle_passage_data()
        if test_file is None:
            return False
        
        # Initialize analyzer
        analyzer = SensorWaveformAnalyzer(
            output_dir='test_improved_charts',
            file_prefix='complete_test_'
        )
        
        # Test complete workflow
        print("   🚀 Running complete improved workflow...")
        start_time = time.time()
        
        success = analyzer.analyze_sensor_waveform(
            file_path=test_file,
            sensor_id='Sensor_15'
        )
        
        end_time = time.time()
        
        if success:
            print(f"   ✅ Complete improved workflow successful ({end_time - start_time:.1f}s)")
            
            # Check generated files
            output_dir = Path('test_improved_charts')
            chart_files = list(output_dir.glob('complete_test_Sensor_15_*.png'))
            
            print(f"   📊 Generated {len(chart_files)} chart files:")
            for chart_file in chart_files:
                print(f"      - {chart_file.name}")
            
            # Should have 3 files: waveform, features, detailed_features
            if len(chart_files) >= 3:
                print("   ✅ All expected chart files generated")
                return True
            else:
                print("   ⚠️ Some chart files may be missing")
                return False
        else:
            print("   ❌ Complete improved workflow failed")
            return False
        
    except Exception as e:
        print(f"   ❌ Complete improved workflow test failed: {str(e)}")
        return False

def cleanup_test_files():
    """Clean up test files"""
    print("\n🧹 Cleaning up test files...")
    
    try:
        # Remove test data file
        test_file = 'test_realistic_vehicle_passage.csv'
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"   ✅ Removed: {test_file}")
        
        # Remove test chart directory
        import shutil
        test_dir = Path('test_improved_charts')
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"   ✅ Removed: {test_dir}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Cleanup failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Improved Sensor Waveform Analyzer Tests")
    print("=" * 80)
    
    # Run all tests
    tests = [
        ("Improved Data Extraction", test_improved_data_extraction),
        ("Enhanced Peak Detection", test_enhanced_peak_detection),
        ("Detailed Features Analysis", test_detailed_features_analysis),
        ("Complete Improved Workflow", test_complete_improved_workflow)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 Improved Sensor Waveform Analyzer Test Results")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Improved Sensor Waveform Analyzer is working correctly.")
        print("📊 New features include:")
        print("   - Consistent vehicle passage detection and data extraction")
        print("   - Enhanced peak detection (positive/negative peaks)")
        print("   - Detailed analysis for each of the 30 features")
        print("   - Improved visualization with physical meaning demonstration")
    elif passed >= total * 0.75:
        print("⚠️  Most tests passed. System is functional with minor issues.")
    else:
        print("❌ Multiple test failures. Please check the implementation.")
    
    # Cleanup
    cleanup_test_files()
    
    return passed >= total * 0.75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
