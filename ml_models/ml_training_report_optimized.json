{"training_summary": {"timestamp": "2025-06-10T23:44:19.918571", "data_file": "combined_features.csv", "total_models_trained": 6, "optimization": "使用优化参数以加快训练速度"}, "speed_prediction": {"task": "速度预测", "target": "R² > 0.90", "models": {"XGBoost": {"test_r2": 0.42935548918262434, "train_r2": 0.8824985780751995, "cv_mean": 0.39802656611800097, "cv_std": 0.029074689869276096, "test_rmse": 11.230801759591364, "target_achieved": false}, "RandomForest": {"test_r2": 0.4278288669854149, "train_r2": 0.6805411097246266, "cv_mean": 0.3878654179439114, "cv_std": 0.047543492415571445, "test_rmse": 11.245814380660603, "target_achieved": false}}}, "load_prediction": {"task": "载重预测", "target": "R² > 0.85", "models": {"XGBoost": {"test_r2": 0.8060001953416815, "train_r2": 0.993339941646242, "cv_mean": 0.7388842559535695, "cv_std": 0.057164786171051155, "test_rmse": 2.6095506326855458, "target_achieved": false}, "RandomForest": {"test_r2": 0.7646366253472614, "train_r2": 0.9612074416329754, "cv_mean": 0.7319395443919968, "cv_std": 0.043128294933710495, "test_rmse": 2.87431606800038, "target_achieved": false}}}, "axle_classification": {"task": "轴型分类", "target": "准确率 > 90%", "models": {"XGBoost": {"test_accuracy": 0.9783950617283951, "train_accuracy": 1.0, "cv_mean": 0.9737330942150221, "cv_std": 0.0011538120864031172, "target_achieved": true}, "RandomForest": {"test_accuracy": 0.9799382716049383, "train_accuracy": 1.0, "cv_mean": 0.9762050915235081, "cv_std": 0.003412912284428956, "target_achieved": true}}}, "overall_performance": {"best_speed_model": "XGBoost", "best_speed_r2": 0.42935548918262434, "speed_target_achieved": false, "best_load_model": "XGBoost", "best_load_r2": 0.8060001953416815, "load_target_achieved": false, "best_axle_model": "RandomForest", "best_axle_accuracy": 0.9799382716049383, "axle_target_achieved": true, "all_targets_achieved": false}}