#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新格式数据预处理演示脚本
展示如何处理新命名格式的CSV文件

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import os
import numpy as np
import pandas as pd
from pathlib import Path

def create_demo_new_format_data():
    """创建演示用的新格式数据"""
    print("🔧 创建演示用的新格式数据...")
    
    # 创建演示数据目录
    demo_dir = Path("demo_new_format_data")
    demo_dir.mkdir(exist_ok=True)
    
    # 生成演示文件
    demo_files = [
        "GW100001_20231101174605_AcceData_车道1_2轴-2.5t-100kmh.csv",  # 正常情况
        "GW100002_20231101180000_AcceData_车道1_3轴-25t-60kmh.csv",   # 正常情况
        "GW100003_20231101182000_AcceData_车道2_2轴-5t-80kmh.csv",    # 部分传感器损坏
        "GW100004_20231101184500_AcceData_车道1_4轴-40t-50kmh.csv"    # 正常情况
    ]
    
    created_files = []

    for i, filename in enumerate(demo_files):
        file_path = demo_dir / filename

        # 生成模拟的传感器数据
        n_samples = 2000  # 2秒数据，1000Hz采样率

        # 创建数据框（新格式：22列）
        data = {}

        # 第1列：count列（没有表头，用数字索引）
        data['0'] = range(n_samples)

        # 第2列：无用列（随机数据）
        data['1'] = np.random.randn(n_samples) * 0.1

        # 第3-22列：20个传感器列
        # 为第3个文件模拟传感器损坏情况
        if i == 2:  # GW100003文件，模拟部分传感器损坏
            print(f"   🔧 为 {filename} 模拟传感器损坏情况...")

            # 前12个传感器正常
            for j in range(1, 13):
                t = np.arange(n_samples) / 1000
                base_freq = 10 + j * 2
                vehicle_signal = 2 * np.sin(2 * np.pi * base_freq * t) * np.exp(-((t-1)**2)/0.5)
                noise = 0.3 * np.random.randn(n_samples)
                signal = vehicle_signal + noise
                data[f'acce{j:02d}'] = signal

            # 后8个传感器损坏
            data['acce13'] = np.zeros(n_samples)  # 全零
            data['acce14'] = np.full(n_samples, np.nan)  # 全NaN
            data['acce15'] = np.full(n_samples, 5.0)  # 无变化
            data['acce16'] = np.random.randn(n_samples) * 1000  # 异常值过多

            # 剩余传感器零值过多
            for j in range(17, 21):
                zeros = np.zeros(n_samples)
                zeros[::200] = np.random.randn(n_samples//200)  # 只有0.5%非零值
                data[f'acce{j:02d}'] = zeros

            print(f"      - 正常传感器: 12个 (acce01-acce12)")
            print(f"      - 损坏传感器: 8个 (acce13-acce20)")

        else:
            # 正常传感器数据
            for j in range(1, 21):
                t = np.arange(n_samples) / 1000
                base_freq = 10 + j * 2
                vehicle_signal = 2 * np.sin(2 * np.pi * base_freq * t) * np.exp(-((t-1)**2)/0.5)
                noise = 0.3 * np.random.randn(n_samples)
                signal = vehicle_signal + noise
                data[f'acce{j:02d}'] = signal

        # 创建数据框并保存
        df = pd.DataFrame(data)
        df.to_csv(file_path, index=False, encoding='utf-8')
        created_files.append(str(file_path))

        print(f"   ✅ 创建文件: {filename}")
    
    print(f"   📁 演示数据目录: {demo_dir}")
    print(f"   📊 创建了 {len(created_files)} 个演示文件")
    
    return str(demo_dir), created_files

def test_filename_parsing():
    """测试文件名解析功能"""
    print("\n🧪 测试文件名解析功能...")
    
    try:
        from new_data_preprocessor import NewDataPreprocessor
        
        # 创建预处理器实例
        preprocessor = NewDataPreprocessor("dummy", "dummy")
        
        # 测试文件名
        test_filenames = [
            "GW100001_20231101174605_AcceData_车道1_2轴-2.5t-100kmh.csv",
            "GW100002_20231101180000_AcceData_车道1_3轴-25t-60kmh.csv",
            "GW100003_20231101182000_AcceData_车道2_2轴-5t-80kmh.csv",
            "GW100004_20231101184500_AcceData_车道1_4轴-40t-50kmh.csv"
        ]
        
        for filename in test_filenames:
            print(f"\n   📄 解析文件: {filename}")
            parsed_info = preprocessor.parse_filename(filename)
            
            if parsed_info:
                print(f"      监测点位: {parsed_info['monitoring_point']}")
                print(f"      监测时间: {parsed_info['monitoring_time']}")
                print(f"      车道: {parsed_info['lane_number']}")
                print(f"      轴型: {parsed_info['axle_type']}")
                print(f"      载重: {parsed_info['load_tons']}吨")
                print(f"      速度: {parsed_info['speed_kmh']}km/h")
            else:
                print(f"      ❌ 解析失败")
        
        print(f"\n   ✅ 文件名解析测试完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 文件名解析测试失败: {str(e)}")
        return False

def test_data_preprocessing():
    """测试数据预处理功能"""
    print("\n🧪 测试数据预处理功能...")
    
    try:
        from new_data_preprocessor import NewDataPreprocessor
        
        # 创建演示数据
        input_dir, created_files = create_demo_new_format_data()
        output_dir = "demo_preprocessed_data"
        
        # 初始化预处理器
        preprocessor = NewDataPreprocessor(input_dir, output_dir)
        
        # 执行预处理
        summary = preprocessor.process_all_files()
        
        if summary['success']:
            print(f"\n   ✅ 预处理成功完成")
            print(f"      总文件数: {summary['total_files']}")
            print(f"      成功处理: {summary['processed_files']}")
            print(f"      处理失败: {summary['failed_files']}")
            print(f"      成功率: {summary['success_rate']:.1f}%")
            print(f"      输出目录: {summary['output_directory']}")
            
            return True
        else:
            print(f"   ❌ 预处理失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据预处理测试失败: {str(e)}")
        return False

def test_format_adapter():
    """测试格式适配器功能"""
    print("\n🧪 测试格式适配器功能...")
    
    try:
        from data_format_adapter import DataFormatAdapter
        
        # 创建演示数据
        input_dir, _ = create_demo_new_format_data()
        
        # 初始化适配器
        adapter = DataFormatAdapter()
        
        # 检测数据格式
        format_type = adapter.detect_data_format(input_dir)
        print(f"   📊 检测到数据格式: {format_type}")
        
        # 获取兼容的数据目录
        compatible_dir = adapter.get_compatible_data_dir(input_dir)
        
        if compatible_dir:
            print(f"   ✅ 获取兼容数据目录成功")
            print(f"      原始目录: {input_dir}")
            print(f"      兼容目录: {compatible_dir}")
            
            # 获取预处理信息
            preprocessing_info = adapter.get_preprocessing_info()
            if preprocessing_info:
                print(f"      预处理文件数: {preprocessing_info.get('total_files', 0)}")
                print(f"      预处理日期: {preprocessing_info.get('preprocessing_date', 'N/A')}")
            
            return True
        else:
            print(f"   ❌ 获取兼容数据目录失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 格式适配器测试失败: {str(e)}")
        return False

def test_main_system_integration():
    """测试与主系统的集成"""
    print("\n🧪 测试与主系统的集成...")
    
    try:
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 创建演示数据
        input_dir, _ = create_demo_new_format_data()
        
        # 创建系统实例
        system = UnifiedVibrationAnalysisSystem()
        
        # 测试数据预处理方法
        if hasattr(system, 'preprocess_data_format'):
            print(f"   ✅ 数据预处理方法已集成")
            
            # 测试预处理功能
            compatible_dir = system.preprocess_data_format(input_dir)
            
            if compatible_dir:
                print(f"   ✅ 数据预处理集成测试成功")
                print(f"      兼容目录: {compatible_dir}")
                return True
            else:
                print(f"   ❌ 数据预处理返回空目录")
                return False
        else:
            print(f"   ❌ 数据预处理方法未集成")
            return False
            
    except Exception as e:
        print(f"   ❌ 主系统集成测试失败: {str(e)}")
        return False

def cleanup_demo_data():
    """清理演示数据"""
    print("\n🧹 清理演示数据...")
    
    import shutil
    
    dirs_to_remove = [
        "demo_new_format_data",
        "demo_preprocessed_data",
        "demo_new_format_data_preprocessed"
    ]
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"   ✅ 删除目录: {dir_name}")
            except Exception as e:
                print(f"   ⚠️  删除目录失败 {dir_name}: {str(e)}")

def main():
    """主演示函数"""
    print("🚀 新格式数据预处理功能演示")
    print("=" * 60)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("文件名解析", test_filename_parsing()))
    test_results.append(("数据预处理", test_data_preprocessing()))
    test_results.append(("格式适配器", test_format_adapter()))
    test_results.append(("主系统集成", test_main_system_integration()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 测试统计: {passed}/{total} 个测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！新格式数据预处理功能已成功实现。")
        
        print("\n💡 使用方法:")
        print("   1. 将新格式CSV文件放入数据目录")
        print("   2. 运行: python unified_vibration_analysis.py")
        print("   3. 系统将自动检测并预处理新格式数据")
        
        print("\n📁 新格式文件命名规则:")
        print("   监测点位_监测时间_AcceData_车道_轴型_载重_速度.csv")
        print("   示例: GW100001_20231101174605_AcceData_车道1_2轴-2.5t-100kmh.csv")
        
        print("\n🔧 手动预处理:")
        print("   from data_format_adapter import auto_preprocess_data")
        print("   compatible_dir = auto_preprocess_data('your_data_dir')")
        
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
    
    # 询问是否清理演示数据
    try:
        choice = input("\n是否清理演示数据？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            cleanup_demo_data()
        else:
            print("   保留演示数据用于进一步测试")
    except:
        print("   保留演示数据")
    
    return passed == total

if __name__ == "__main__":
    main()
