# Denoising Visualization Charts Index

## English Version Charts
- `*_denoising_comparison.png` - Denoising Effect Comparison
- `*_frequency_analysis.png` - Frequency Domain Analysis
- `denoising_evaluation_results.png` - Evaluation Results
- `snr_improvement_comparison.png` - SNR Improvement Comparison
- `processing_efficiency_comparison.png` - Processing Efficiency Comparison

## Chart Descriptions
1. **Denoising Effect Comparison**: Shows time-domain waveform comparison between original and denoised signals
2. **Frequency Domain Analysis**: Shows power spectral density changes before and after denoising
3. **Evaluation Results**: Comprehensive performance evaluation of different denoising methods
4. **SNR Improvement Comparison**: Compares SNR improvement effects of different methods
5. **Processing Efficiency Comparison**: Compares computational efficiency of different methods

## Usage Recommendations
- Check denoising comparison charts for time-domain feature preservation
- Check frequency analysis charts for frequency feature preservation
- Check evaluation results charts to select the most suitable denoising method
