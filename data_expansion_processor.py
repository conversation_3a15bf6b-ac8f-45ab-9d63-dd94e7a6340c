#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集扩展处理器
第一阶段：数据集扩展 (目标：样本数量从1,398提升到3,000+)

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import os
import json
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
import re
import shutil
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class DataExpansionProcessor:
    """数据集扩展处理器"""
    
    def __init__(self, data_dir: str = "data", output_file: str = "combined_features_expanded.csv"):
        self.data_dir = Path(data_dir)
        self.output_file = output_file
        self.existing_features_file = "combined_features.csv"
        self.processing_log = []
        
    def expand_dataset(self) -> Dict:
        """扩展数据集主流程"""
        print("🚀 开始数据集扩展处理...")
        print("=" * 80)
        
        # 步骤1: 加载现有数据
        existing_df = self.load_existing_features()
        
        # 步骤2: 处理新格式CSV文件
        new_features_df = self.process_new_format_files()
        
        # 步骤3: 合并数据集
        expanded_df = self.merge_datasets(existing_df, new_features_df)
        
        # 步骤4: 数据质量验证
        quality_report = self.validate_data_quality(expanded_df)
        
        # 步骤5: 保存扩展后的数据集
        self.save_expanded_dataset(expanded_df)
        
        # 步骤6: 生成统计报告
        expansion_report = self.generate_expansion_report(existing_df, expanded_df, quality_report)
        
        return expansion_report
    
    def load_existing_features(self) -> pd.DataFrame:
        """加载现有特征数据"""
        print("\n📁 步骤1: 加载现有特征数据")
        print("-" * 50)
        
        if os.path.exists(self.existing_features_file):
            df = pd.read_csv(self.existing_features_file)
            print(f"   ✅ 加载现有数据: {df.shape[0]} 个样本, {df.shape[1]} 个特征")
            
            # 添加数据来源标记
            df['data_source'] = 'existing'
            
            return df
        else:
            print(f"   ⚠️  现有特征文件不存在: {self.existing_features_file}")
            return pd.DataFrame()
    
    def process_new_format_files(self) -> pd.DataFrame:
        """处理新格式CSV文件"""
        print("\n🔄 步骤2: 处理新格式CSV文件")
        print("-" * 50)
        
        # 查找所有新格式CSV文件
        new_format_files = list(self.data_dir.glob("GW100001_*.csv"))
        print(f"   📊 发现新格式文件: {len(new_format_files)} 个")
        
        if len(new_format_files) == 0:
            print("   ⚠️  未找到新格式CSV文件")
            return pd.DataFrame()
        
        # 处理文件
        all_features = []
        processed_count = 0
        error_count = 0
        
        for i, csv_file in enumerate(new_format_files):
            try:
                # 显示进度
                if (i + 1) % 100 == 0:
                    print(f"   📄 处理进度: {i + 1}/{len(new_format_files)} ({(i + 1)/len(new_format_files)*100:.1f}%)")
                
                # 提取文件信息
                file_info = self.extract_file_info(csv_file.name)
                
                # 读取CSV文件
                df = pd.read_csv(csv_file)
                
                if df.empty or len(df) < 100:  # 确保有足够的数据点
                    continue
                
                # 提取特征
                features = self.extract_comprehensive_features(df, str(csv_file), file_info)
                
                if features:
                    features['data_source'] = 'new_format'
                    all_features.append(features)
                    processed_count += 1
                
            except Exception as e:
                error_count += 1
                self.processing_log.append(f"处理文件失败 {csv_file.name}: {str(e)}")
                continue
        
        print(f"   ✅ 处理完成: {processed_count} 个文件成功, {error_count} 个文件失败")
        
        if all_features:
            features_df = pd.DataFrame(all_features)
            print(f"   📊 新特征数据: {features_df.shape[0]} 个样本, {features_df.shape[1]} 个特征")
            return features_df
        else:
            print("   ❌ 没有成功提取任何新特征")
            return pd.DataFrame()
    
    def extract_file_info(self, filename: str) -> Dict:
        """从文件名提取信息"""
        # 文件名格式: GW100001_日期时间_AcceData_车道_轴数-重量-速度.csv
        info = {
            'speed_kmh': None,
            'load_tons': None,
            'axle_type': None,
            'lane_number': None,
            'monitoring_point': 'GW100001',
            'datetime': None
        }
        
        try:
            # 解析文件名
            parts = filename.replace('.csv', '').split('_')
            
            if len(parts) >= 5:
                # 提取日期时间
                if len(parts[1]) == 14:  # YYYYMMDDHHMMSS
                    info['datetime'] = parts[1]
                
                # 提取车道信息
                lane_part = parts[3]
                if '车道' in lane_part:
                    lane_match = re.search(r'车道(\d+)', lane_part)
                    if lane_match:
                        info['lane_number'] = int(lane_match.group(1))
                
                # 提取轴数-重量-速度信息
                vehicle_info = parts[4]
                vehicle_match = re.search(r'(\d+)轴-([0-9.]+)t-([0-9.]+)kmh', vehicle_info)
                if vehicle_match:
                    info['axle_type'] = int(vehicle_match.group(1))
                    info['load_tons'] = float(vehicle_match.group(2))
                    info['speed_kmh'] = float(vehicle_match.group(3))
        
        except Exception as e:
            self.processing_log.append(f"解析文件名失败 {filename}: {str(e)}")
        
        return info
    
    def extract_comprehensive_features(self, df: pd.DataFrame, file_path: str, file_info: Dict) -> Dict:
        """提取综合特征"""
        try:
            # 获取传感器列（假设前20列是传感器数据）
            sensor_columns = []
            for col in df.columns:
                if col.lower().startswith('sensor') or col.isdigit() or 'acc' in col.lower():
                    sensor_columns.append(col)
            
            # 如果没有明确的传感器列，使用数值列
            if not sensor_columns:
                sensor_columns = df.select_dtypes(include=[np.number]).columns.tolist()[:20]
            
            if not sensor_columns:
                return None
            
            # 基础信息
            features = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'sensor_count': len(sensor_columns),
                'data_points': len(df)
            }
            
            # 添加文件信息
            features.update(file_info)
            
            # 为每个传感器提取特征
            for i, col in enumerate(sensor_columns[:20]):  # 限制最多20个传感器
                try:
                    signal = df[col].dropna().values
                    if len(signal) < 100:  # 确保有足够的数据点
                        continue
                    
                    sensor_name = f'sensor_{i+1:02d}'
                    
                    # 时域特征
                    features[f'{sensor_name}_mean'] = np.mean(signal)
                    features[f'{sensor_name}_std'] = np.std(signal)
                    features[f'{sensor_name}_var'] = np.var(signal)
                    features[f'{sensor_name}_rms'] = np.sqrt(np.mean(signal**2))
                    features[f'{sensor_name}_peak'] = np.max(np.abs(signal))
                    features[f'{sensor_name}_peak_to_peak'] = np.max(signal) - np.min(signal)
                    
                    # 避免除零错误
                    rms_val = features[f'{sensor_name}_rms']
                    features[f'{sensor_name}_crest_factor'] = features[f'{sensor_name}_peak'] / rms_val if rms_val > 0 else 0
                    
                    # 统计特征
                    features[f'{sensor_name}_skewness'] = self.calculate_skewness(signal)
                    features[f'{sensor_name}_kurtosis'] = self.calculate_kurtosis(signal)
                    features[f'{sensor_name}_energy'] = np.sum(signal**2)
                    
                    # 频域特征
                    try:
                        fft_signal = np.fft.fft(signal)
                        fft_magnitude = np.abs(fft_signal[:len(fft_signal)//2])
                        
                        if len(fft_magnitude) > 0 and np.sum(fft_magnitude) > 0:
                            features[f'{sensor_name}_dominant_freq'] = np.argmax(fft_magnitude)
                            features[f'{sensor_name}_spectral_energy'] = np.sum(fft_magnitude**2)
                            features[f'{sensor_name}_spectral_centroid'] = np.sum(np.arange(len(fft_magnitude)) * fft_magnitude) / np.sum(fft_magnitude)
                        else:
                            features[f'{sensor_name}_dominant_freq'] = 0
                            features[f'{sensor_name}_spectral_energy'] = 0
                            features[f'{sensor_name}_spectral_centroid'] = 0
                    except:
                        features[f'{sensor_name}_dominant_freq'] = 0
                        features[f'{sensor_name}_spectral_energy'] = 0
                        features[f'{sensor_name}_spectral_centroid'] = 0
                        
                except Exception:
                    continue
            
            return features
            
        except Exception as e:
            return None
    
    def calculate_skewness(self, data):
        """计算偏度"""
        try:
            mean = np.mean(data)
            std = np.std(data)
            if std == 0:
                return 0
            return np.mean(((data - mean) / std) ** 3)
        except:
            return 0
    
    def calculate_kurtosis(self, data):
        """计算峰度"""
        try:
            mean = np.mean(data)
            std = np.std(data)
            if std == 0:
                return 0
            return np.mean(((data - mean) / std) ** 4) - 3
        except:
            return 0
    
    def merge_datasets(self, existing_df: pd.DataFrame, new_df: pd.DataFrame) -> pd.DataFrame:
        """合并数据集"""
        print("\n🔗 步骤3: 合并数据集")
        print("-" * 50)
        
        if existing_df.empty and new_df.empty:
            print("   ❌ 没有数据可以合并")
            return pd.DataFrame()
        elif existing_df.empty:
            print(f"   📊 只有新数据: {new_df.shape[0]} 个样本")
            return new_df
        elif new_df.empty:
            print(f"   📊 只有现有数据: {existing_df.shape[0]} 个样本")
            return existing_df
        
        # 对齐列
        all_columns = set(existing_df.columns) | set(new_df.columns)
        
        # 为缺失的列添加NaN值
        for col in all_columns:
            if col not in existing_df.columns:
                existing_df[col] = np.nan
            if col not in new_df.columns:
                new_df[col] = np.nan
        
        # 重新排序列
        column_order = sorted(all_columns)
        existing_df = existing_df[column_order]
        new_df = new_df[column_order]
        
        # 合并数据
        merged_df = pd.concat([existing_df, new_df], ignore_index=True)
        
        print(f"   ✅ 合并完成:")
        print(f"      现有数据: {existing_df.shape[0]} 个样本")
        print(f"      新数据: {new_df.shape[0]} 个样本")
        print(f"      合并后: {merged_df.shape[0]} 个样本, {merged_df.shape[1]} 个特征")
        
        return merged_df
    
    def validate_data_quality(self, df: pd.DataFrame) -> Dict:
        """验证数据质量"""
        print("\n✅ 步骤4: 数据质量验证")
        print("-" * 50)
        
        if df.empty:
            return {"status": "failed", "reason": "数据集为空"}
        
        quality_report = {
            "total_samples": int(len(df)),
            "total_features": int(len(df.columns)),
            "missing_values": {},
            "target_variables": {},
            "data_distribution": {},
            "quality_score": 0
        }
        
        # 缺失值分析
        missing_stats = df.isnull().sum()
        missing_percent = (missing_stats / len(df)) * 100
        quality_report["missing_values"] = {
            "columns_with_missing": int((missing_stats > 0).sum()),
            "max_missing_rate": float(missing_percent.max()),
            "avg_missing_rate": float(missing_percent.mean())
        }
        
        print(f"   📊 数据概览:")
        print(f"      总样本数: {quality_report['total_samples']}")
        print(f"      总特征数: {quality_report['total_features']}")
        print(f"      最大缺失率: {quality_report['missing_values']['max_missing_rate']:.1f}%")
        
        # 目标变量分析
        target_vars = ['speed_kmh', 'load_tons', 'axle_type']
        for var in target_vars:
            if var in df.columns:
                # 确保数据类型正确
                df[var] = pd.to_numeric(df[var], errors='coerce')

                non_null_count = df[var].notna().sum()
                coverage = (non_null_count / len(df)) * 100

                quality_report["target_variables"][var] = {
                    "valid_samples": int(non_null_count),
                    "coverage": float(coverage),
                    "min_value": float(df[var].min()) if non_null_count > 0 else None,
                    "max_value": float(df[var].max()) if non_null_count > 0 else None,
                    "mean_value": float(df[var].mean()) if non_null_count > 0 else None
                }

                print(f"      {var}: {non_null_count} 个有效值 ({coverage:.1f}%)")
        
        # 计算质量评分
        quality_report["quality_score"] = self.calculate_quality_score(quality_report)
        print(f"   🎯 数据质量评分: {quality_report['quality_score']:.1f}/100")
        
        return quality_report
    
    def calculate_quality_score(self, report: Dict) -> float:
        """计算数据质量评分"""
        score = 0
        
        # 数据完整性 (30分)
        missing_rate = report["missing_values"]["avg_missing_rate"]
        completeness_score = max(0, 30 * (1 - missing_rate / 100))
        score += completeness_score
        
        # 目标变量覆盖 (25分)
        target_coverage = 0
        for var_info in report["target_variables"].values():
            if var_info["coverage"] > 80:
                target_coverage += 1
        target_score = (target_coverage / 3) * 25
        score += target_score
        
        # 样本数量充足性 (25分)
        sample_count = report["total_samples"]
        sample_score = min(25, (sample_count / 3000) * 25)
        score += sample_score
        
        # 特征数量充足性 (20分)
        feature_count = report["total_features"]
        feature_score = min(20, (feature_count / 100) * 20)
        score += feature_score
        
        return score
    
    def save_expanded_dataset(self, df: pd.DataFrame):
        """保存扩展后的数据集"""
        print("\n💾 步骤5: 保存扩展后的数据集")
        print("-" * 50)
        
        if df.empty:
            print("   ❌ 数据集为空，无法保存")
            return
        
        # 保存主文件
        df.to_csv(self.output_file, index=False)
        print(f"   ✅ 已保存扩展数据集: {self.output_file}")
        print(f"      数据形状: {df.shape}")
        
        # 备份原文件
        if os.path.exists(self.existing_features_file):
            backup_file = f"{self.existing_features_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(self.existing_features_file, backup_file)
            print(f"   📁 已备份原文件: {backup_file}")
        
        # 更新主特征文件
        df.to_csv(self.existing_features_file, index=False)
        print(f"   🔄 已更新主特征文件: {self.existing_features_file}")
    
    def generate_expansion_report(self, existing_df: pd.DataFrame, expanded_df: pd.DataFrame, quality_report: Dict) -> Dict:
        """生成扩展报告"""
        print("\n📋 步骤6: 生成扩展报告")
        print("-" * 50)
        
        report = {
            "expansion_summary": {
                "original_samples": len(existing_df) if not existing_df.empty else 0,
                "new_samples": len(expanded_df) - len(existing_df) if not existing_df.empty else len(expanded_df),
                "total_samples": len(expanded_df),
                "expansion_rate": ((len(expanded_df) - len(existing_df)) / len(existing_df) * 100) if not existing_df.empty and len(existing_df) > 0 else 0,
                "target_achieved": len(expanded_df) >= 3000
            },
            "quality_assessment": quality_report,
            "processing_log": self.processing_log,
            "timestamp": datetime.now().isoformat()
        }
        
        # 保存报告
        with open("data_expansion_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"   📊 扩展摘要:")
        print(f"      原始样本数: {report['expansion_summary']['original_samples']}")
        print(f"      新增样本数: {report['expansion_summary']['new_samples']}")
        print(f"      总样本数: {report['expansion_summary']['total_samples']}")
        print(f"      扩展率: {report['expansion_summary']['expansion_rate']:.1f}%")
        print(f"      目标达成: {'✅' if report['expansion_summary']['target_achieved'] else '❌'} (目标: 3000+)")
        print(f"   📋 报告已保存: data_expansion_report.json")
        
        return report

def main():
    """主函数"""
    processor = DataExpansionProcessor()
    report = processor.expand_dataset()
    
    print(f"\n🎉 数据集扩展完成！")
    print(f"   最终样本数: {report['expansion_summary']['total_samples']}")
    print(f"   数据质量评分: {report['quality_assessment']['quality_score']:.1f}/100")
    
    return report

if __name__ == "__main__":
    main()
