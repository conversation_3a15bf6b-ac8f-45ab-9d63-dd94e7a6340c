{"speed_prediction": {"found": true, "performance": {"speed_prediction": {"implementation_status": "implemented", "model_found": false, "training_data_analysis": {"feature_file_found": true, "speed_column": "speed_kmh", "speed_distribution": {"min": 40.0, "max": 100.0, "mean": 55.46137339055794, "std": 13.544312442648664}, "data_quality": {"missing_values": 0, "missing_ratio": 0.0}, "sample_count": 1398}, "performance_metrics": {"model_results_found": false, "r2_score": null, "mae": null, "rmse": null, "target_achieved": false}, "prediction_range": {"expected_range": [40, 100], "actual_range": null, "coverage_adequate": false}, "output_verification": {"prediction_files_found": false, "output_format_correct": false, "error_analysis_present": false, "visualization_found": false}, "issues": []}, "weight_prediction": {"implementation_status": "implemented", "model_found": false, "training_data_analysis": {"feature_file_found": true, "weight_column": "axle_load_tons", "weight_distribution": {"min": 2.0, "max": 55.62, "mean": 34.623161659513585, "std": 15.769684834807764}, "weight_categories": {"25.0": 347, "2.0": 160, "34.98": 347, "45.39": 292, "55.62": 252}, "data_quality": {"missing_values": 0, "unique_weights": 5, "missing_ratio": 0.0}, "sample_count": 1398}, "performance_metrics": {"model_results_found": false, "r2_score": null, "mae": null, "rmse": null, "target_achieved": false}, "weight_range": {"expected_categories": ["轻型车辆(<5吨)", "中型车辆(5-20吨)", "重型车辆(20-50吨)", "超重车辆(>50吨)"], "actual_range": null, "category_coverage": {}}, "output_verification": {"prediction_files_found": false, "output_format_correct": false, "unit_verification": false, "precision_check": false}, "issues": []}, "best_parameters": {"BP Neural Network": {"hidden_layers": 2, "neurons_layer1": 100, "neurons_layer2": 75, "neurons_layer3": 32, "dropout_rate": 0.21649165607921678, "learning_rate": 0.0016738085788752138, "batch_size": 128, "activation": "relu", "batch_norm": true}, "CNN-LSTM": {"timesteps": 44, "cnn_filters1": 52, "cnn_filters2": 24, "cnn_kernel_size": 3, "lstm_units1": 61, "lstm_units2": 41, "dense_units": 37, "dropout_rate": 0.21649165607921678, "learning_rate": 0.0016738085788752138, "batch_size": 64}, "TCN": {"timesteps": 24, "nb_filters": 124, "kernel_size": 6, "nb_stacks": 2, "dilations": [1, 2, 4], "dropout_rate": 0.3598528437324806, "learning_rate": 0.0015930522616241021, "batch_size": 64}}, "optimization_results": {"BP Neural Network": {"best_score": 0.9015591018843261, "best_params": {"hidden_layers": 2, "neurons_layer1": 100, "neurons_layer2": 75, "neurons_layer3": 32, "dropout_rate": 0.21649165607921678, "learning_rate": 0.0016738085788752138, "batch_size": 128, "activation": "relu", "batch_norm": true}, "n_trials": 5}, "CNN-LSTM": {"best_score": -0.02844488555217506, "best_params": {"timesteps": 44, "cnn_filters1": 52, "cnn_filters2": 24, "cnn_kernel_size": 3, "lstm_units1": 61, "lstm_units2": 41, "dense_units": 37, "dropout_rate": 0.21649165607921678, "learning_rate": 0.0016738085788752138, "batch_size": 64}, "n_trials": 5}, "TCN": {"best_score": 0.8916530513181108, "best_params": {"timesteps": 24, "nb_filters": 124, "kernel_size": 6, "nb_stacks": 2, "dilations": [1, 2, 4], "dropout_rate": 0.3598528437324806, "learning_rate": 0.0015930522616241021, "batch_size": 64}, "n_trials": 5}}}}, "weight_prediction": {"found": true, "performance": {"speed_prediction": {"implementation_status": "implemented", "model_found": false, "training_data_analysis": {"feature_file_found": true, "speed_column": "speed_kmh", "speed_distribution": {"min": 40.0, "max": 100.0, "mean": 55.46137339055794, "std": 13.544312442648664}, "data_quality": {"missing_values": 0, "missing_ratio": 0.0}, "sample_count": 1398}, "performance_metrics": {"model_results_found": false, "r2_score": null, "mae": null, "rmse": null, "target_achieved": false}, "prediction_range": {"expected_range": [40, 100], "actual_range": null, "coverage_adequate": false}, "output_verification": {"prediction_files_found": false, "output_format_correct": false, "error_analysis_present": false, "visualization_found": false}, "issues": []}, "weight_prediction": {"implementation_status": "implemented", "model_found": false, "training_data_analysis": {"feature_file_found": true, "weight_column": "axle_load_tons", "weight_distribution": {"min": 2.0, "max": 55.62, "mean": 34.623161659513585, "std": 15.769684834807764}, "weight_categories": {"25.0": 347, "2.0": 160, "34.98": 347, "45.39": 292, "55.62": 252}, "data_quality": {"missing_values": 0, "unique_weights": 5, "missing_ratio": 0.0}, "sample_count": 1398}, "performance_metrics": {"model_results_found": false, "r2_score": null, "mae": null, "rmse": null, "target_achieved": false}, "weight_range": {"expected_categories": ["轻型车辆(<5吨)", "中型车辆(5-20吨)", "重型车辆(20-50吨)", "超重车辆(>50吨)"], "actual_range": null, "category_coverage": {}}, "output_verification": {"prediction_files_found": false, "output_format_correct": false, "unit_verification": false, "precision_check": false}, "issues": []}}}, "axle_classification": {"found": true, "performance": {"axle_classification": {"implementation_status": "implemented", "model_found": false, "training_data_analysis": {"feature_file_found": true, "axle_type_column": "axle_load_tons", "class_distribution": {"25.0": 347, "34.98": 347, "45.39": 292, "55.62": 252, "2.0": 160}, "data_quality": {"missing_values": 0, "unique_classes": 5, "missing_ratio": 0.0}, "sample_count": 1398}, "performance_metrics": {"model_results_found": false, "accuracy": null, "classification_report": null, "confusion_matrix": null, "target_achieved": false}, "class_coverage": {"expected_classes": ["单轴", "双轴", "三轴", "四轴", "多轴"], "found_classes": [], "missing_classes": [], "coverage_ratio": 0.0}, "output_verification": {"prediction_files_found": false, "output_format_correct": false, "prediction_range_valid": false, "confidence_scores_present": false}, "issues": []}}}, "files_found": ["ml_functionality_verification_results.json", "verification_results.json", "dl_optimization_speed_prediction.json", "traditional_optimization_speed_prediction.json", "dl_optimization_speed_prediction.json", "traditional_optimization_speed_prediction.json"]}