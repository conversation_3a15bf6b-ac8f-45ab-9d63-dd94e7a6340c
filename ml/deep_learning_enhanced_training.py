#!/usr/bin/env python3
"""
深度学习增强训练脚本
集成BP神经网络和TCN到振动信号分析系统
"""

import numpy as np
import pandas as pd
import logging
import time
import os
from typing import Dict, Any, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# 导入传统机器学习模块
from model_optimization import BayesianOptimizer
from gpu_accelerated_training import get_gpu_trainer

# 导入深度学习模块
try:
    from bp_neural_network import BPNeuralNetworkOptimizer, BPNeuralNetworkTrainer, create_optimized_bp_model
    from temporal_convolutional_network import TCNOptimizer, TCNTrainer, create_optimized_tcn_model
    import torch
    DEEP_LEARNING_AVAILABLE = True
    print("✅ 深度学习模块加载成功")
except ImportError as e:
    DEEP_LEARNING_AVAILABLE = False
    print(f"❌ 深度学习模块加载失败: {e}")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeepLearningEnhancedTrainer:
    """深度学习增强训练器"""
    
    def __init__(self, n_trials: int = 50, cv_folds: int = 5, random_state: int = 42):
        """
        初始化训练器
        
        Args:
            n_trials: 贝叶斯优化试验次数
            cv_folds: 交叉验证折数
            random_state: 随机种子
        """
        self.n_trials = n_trials
        self.cv_folds = cv_folds
        self.random_state = random_state
        self.results = {}
        
        # 检查GPU可用性
        if DEEP_LEARNING_AVAILABLE:
            self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
            logger.info(f"深度学习计算设备: {self.device}")
            if self.device == 'cuda':
                logger.info(f"GPU设备: {torch.cuda.get_device_name(0)}")
        else:
            self.device = 'cpu'
            logger.warning("深度学习模块不可用，将只使用传统机器学习算法")
    
    def load_dataset(self, filepath: str) -> Tuple[pd.DataFrame, str]:
        """加载数据集"""
        logger.info(f"加载数据集: {filepath}")
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"数据集文件不存在: {filepath}")
        
        df = pd.read_csv(filepath)
        logger.info(f"数据集形状: {df.shape}")
        
        # 确定任务类型
        if 'classification' in filepath.lower() or 'type' in filepath.lower():
            task_type = 'classification'
        else:
            task_type = 'regression'
        
        logger.info(f"任务类型: {task_type}")
        return df, task_type
    
    def prepare_features_and_target(self, df: pd.DataFrame, task_type: str) -> Tuple[np.ndarray, np.ndarray]:
        """准备特征和目标变量"""
        # 移除非数值列
        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
        df_numeric = df[numeric_columns]
        
        # 确定目标列
        if task_type == 'classification':
            # 寻找可能的目标列
            target_candidates = ['label', 'target', 'class', 'type', 'category']
            target_col = None
            for col in target_candidates:
                if col in df_numeric.columns:
                    target_col = col
                    break
            
            if target_col is None:
                # 使用最后一列作为目标
                target_col = df_numeric.columns[-1]
        else:
            # 回归任务，寻找目标列
            target_candidates = ['speed', 'load', 'weight', 'target', 'y']
            target_col = None
            for col in target_candidates:
                if any(candidate in col.lower() for candidate in target_candidates):
                    target_col = col
                    break
            
            if target_col is None:
                target_col = df_numeric.columns[-1]
        
        logger.info(f"目标列: {target_col}")
        
        # 分离特征和目标
        X = df_numeric.drop(columns=[target_col]).values
        y = df_numeric[target_col].values
        
        logger.info(f"特征形状: {X.shape}")
        logger.info(f"目标变量形状: {y.shape}")
        
        if task_type == 'regression':
            logger.info(f"目标变量范围: [{y.min():.2f}, {y.max():.2f}]")
        else:
            logger.info(f"类别数量: {len(np.unique(y))}")
            logger.info(f"类别分布: {np.bincount(y.astype(int))}")
        
        return X, y
    
    def train_traditional_models(self, X: np.ndarray, y: np.ndarray, task_type: str) -> Dict[str, Any]:
        """训练传统机器学习模型"""
        logger.info("🔍 开始训练传统机器学习模型...")
        
        # 使用GPU加速训练器
        gpu_trainer = get_gpu_trainer()
        logger.info("GPU训练器状态:")
        print(gpu_trainer.get_optimization_summary())
        
        # 创建贝叶斯优化器
        optimizer = BayesianOptimizer(
            task_type=task_type,
            cv_folds=self.cv_folds,
            n_trials=self.n_trials,
            random_state=self.random_state
        )
        
        # 执行优化
        start_time = time.time()
        results = optimizer.optimize_all_models(X, y, include_deep_learning=False)
        end_time = time.time()
        
        logger.info(f"传统模型优化完成，耗时: {end_time - start_time:.2f}秒")
        
        # 显示结果
        best_scores = results['best_scores']
        logger.info("传统模型最佳分数:")
        for model_name, score in best_scores.items():
            logger.info(f"  {model_name}: {score:.4f}")
        
        return results
    
    def train_deep_learning_models(self, X: np.ndarray, y: np.ndarray, task_type: str) -> Dict[str, Any]:
        """训练深度学习模型"""
        if not DEEP_LEARNING_AVAILABLE:
            logger.warning("深度学习模块不可用，跳过深度学习模型训练")
            return {}
        
        logger.info("🧠 开始训练深度学习模型...")
        
        results = {}
        
        # 训练BP神经网络
        logger.info("训练BP神经网络...")
        try:
            bp_optimizer = BPNeuralNetworkOptimizer(
                task_type=task_type,
                n_trials=min(self.n_trials, 30),  # 限制试验次数
                cv_folds=self.cv_folds
            )
            
            start_time = time.time()
            bp_results = bp_optimizer.optimize(X, y)
            end_time = time.time()
            
            results['BPNeuralNetwork'] = {
                'best_params': bp_results['best_params'],
                'best_score': bp_results['best_score'],
                'training_time': end_time - start_time
            }
            
            logger.info(f"BP神经网络最佳分数: {bp_results['best_score']:.4f}")
            logger.info(f"BP神经网络训练时间: {end_time - start_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"BP神经网络训练失败: {str(e)}")
        
        # 训练TCN
        logger.info("训练时间卷积网络...")
        try:
            tcn_optimizer = TCNOptimizer(
                task_type=task_type,
                n_trials=min(self.n_trials, 20),  # TCN训练较慢
                cv_folds=self.cv_folds
            )
            
            start_time = time.time()
            tcn_results = tcn_optimizer.optimize(X, y)
            end_time = time.time()
            
            results['TCN'] = {
                'best_params': tcn_results['best_params'],
                'best_score': tcn_results['best_score'],
                'training_time': end_time - start_time
            }
            
            logger.info(f"TCN最佳分数: {tcn_results['best_score']:.4f}")
            logger.info(f"TCN训练时间: {end_time - start_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"TCN训练失败: {str(e)}")
        
        return results
    
    def train_complete_pipeline(self, datasets: List[Tuple[str, str]]) -> Dict[str, Dict[str, Any]]:
        """训练完整的深度学习增强管道"""
        logger.info("🚀 开始深度学习增强训练管道...")
        
        all_results = {}
        
        for dataset_path, dataset_name in datasets:
            logger.info(f"\n{'='*60}")
            logger.info(f"处理数据集: {dataset_name}")
            logger.info(f"{'='*60}")
            
            try:
                # 加载数据
                df, task_type = self.load_dataset(dataset_path)
                X, y = self.prepare_features_and_target(df, task_type)
                
                # 训练传统模型
                traditional_results = self.train_traditional_models(X, y, task_type)
                
                # 训练深度学习模型
                deep_learning_results = self.train_deep_learning_models(X, y, task_type)
                
                # 合并结果
                dataset_results = {
                    'task_type': task_type,
                    'data_shape': X.shape,
                    'traditional_models': traditional_results,
                    'deep_learning_models': deep_learning_results
                }
                
                all_results[dataset_name] = dataset_results
                
                # 显示最佳结果
                self.display_best_results(dataset_name, traditional_results, deep_learning_results)
                
            except Exception as e:
                logger.error(f"处理数据集 {dataset_name} 时出错: {str(e)}")
                continue
        
        return all_results
    
    def display_best_results(self, dataset_name: str, traditional_results: Dict[str, Any], 
                           deep_learning_results: Dict[str, Any]):
        """显示最佳结果"""
        logger.info(f"\n📊 {dataset_name} 最佳结果总结:")
        logger.info("-" * 50)
        
        all_scores = {}
        
        # 传统模型分数
        if 'best_scores' in traditional_results:
            for model_name, score in traditional_results['best_scores'].items():
                all_scores[f"{model_name} (传统)"] = score
        
        # 深度学习模型分数
        for model_name, results in deep_learning_results.items():
            if 'best_score' in results:
                all_scores[f"{model_name} (深度学习)"] = results['best_score']
        
        # 排序并显示
        sorted_scores = sorted(all_scores.items(), key=lambda x: x[1], reverse=True)
        
        for i, (model_name, score) in enumerate(sorted_scores):
            status = "🏆" if i == 0 else "✅" if score > 0.75 else "📈"
            logger.info(f"  {status} {model_name}: {score:.4f}")
        
        if sorted_scores:
            best_model, best_score = sorted_scores[0]
            logger.info(f"\n🎯 最佳模型: {best_model} (分数: {best_score:.4f})")
            
            # 检查是否达到目标
            if best_score > 0.75:
                logger.info("🎉 已达到目标性能 (R² > 0.75)!")
            else:
                logger.info(f"📈 距离目标还差: {0.75 - best_score:.4f}")

def main():
    """主函数"""
    print("🚀 深度学习增强振动信号分析系统")
    print("=" * 60)
    
    # 检查深度学习可用性
    if DEEP_LEARNING_AVAILABLE:
        print("✅ 深度学习模块已加载")
        if torch.cuda.is_available():
            print(f"✅ GPU加速可用: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  使用CPU训练深度学习模型")
    else:
        print("❌ 深度学习模块不可用，将只使用传统机器学习算法")
    
    # 定义数据集
    datasets = [
        ('./training_datasets/speed_regression.csv', '速度预测'),
        ('./training_datasets/load_regression.csv', '轴重预测'),
        ('./ml/combined_features_clean.csv', '轴型分类')
    ]
    
    # 创建训练器
    trainer = DeepLearningEnhancedTrainer(
        n_trials=30,  # 减少试验次数以节省时间
        cv_folds=5,
        random_state=42
    )
    
    # 执行训练
    start_time = time.time()
    results = trainer.train_complete_pipeline(datasets)
    end_time = time.time()
    
    print(f"\n🎉 深度学习增强训练完成!")
    print(f"总耗时: {end_time - start_time:.2f}秒")
    
    # 保存结果
    import json
    with open('deep_learning_enhanced_results.json', 'w', encoding='utf-8') as f:
        # 转换不可序列化的对象
        serializable_results = {}
        for dataset_name, dataset_results in results.items():
            serializable_results[dataset_name] = {
                'task_type': dataset_results['task_type'],
                'data_shape': list(dataset_results['data_shape']),
                'traditional_best_scores': dataset_results['traditional_models'].get('best_scores', {}),
                'deep_learning_scores': {
                    model_name: model_results.get('best_score', 0.0)
                    for model_name, model_results in dataset_results['deep_learning_models'].items()
                }
            }
        
        json.dump(serializable_results, f, indent=2, ensure_ascii=False)
    
    print("📁 结果已保存到 deep_learning_enhanced_results.json")

if __name__ == "__main__":
    main()
