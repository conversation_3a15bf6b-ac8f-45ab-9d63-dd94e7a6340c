#!/usr/bin/env python3
"""
GPU加速训练模块
自动检测GPU可用性并优化模型训练
"""

import os
import logging
import warnings
import numpy as np
from typing import Dict, Any, Tuple, Optional

warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GPUAcceleratedTrainer:
    """GPU加速训练器"""
    
    def __init__(self):
        """初始化GPU加速训练器"""
        self.gpu_status = self._check_gpu_availability()
        self._configure_gpu_settings()
        
    def _check_gpu_availability(self) -> Dict[str, bool]:
        """检查GPU可用性"""
        status = {
            'nvidia_gpu': False,
            'tensorflow_gpu': False,
            'pytorch_gpu': False,
            'xgboost_gpu': False
        }
        
        # 检查NVIDIA GPU
        try:
            import subprocess
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=5)
            status['nvidia_gpu'] = result.returncode == 0
        except:
            pass
        
        # 检查TensorFlow GPU
        try:
            import tensorflow as tf
            gpus = tf.config.list_physical_devices('GPU')
            status['tensorflow_gpu'] = len(gpus) > 0
        except:
            pass
        
        # 检查PyTorch GPU
        try:
            import torch
            status['pytorch_gpu'] = torch.cuda.is_available()
        except:
            pass
        
        # 检查XGBoost GPU
        try:
            import xgboost as xgb
            from sklearn.datasets import make_classification
            X, y = make_classification(n_samples=50, n_features=5, random_state=42)
            model = xgb.XGBClassifier(device='cuda', tree_method='hist', n_estimators=5)
            model.fit(X, y)
            status['xgboost_gpu'] = True
        except:
            pass
        
        logger.info(f"GPU状态检查: {status}")
        return status
    
    def _configure_gpu_settings(self):
        """配置GPU设置"""
        if self.gpu_status['tensorflow_gpu']:
            try:
                import tensorflow as tf
                gpus = tf.config.list_physical_devices('GPU')
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                logger.info("✅ TensorFlow GPU内存增长模式已启用")
            except Exception as e:
                logger.warning(f"TensorFlow GPU配置失败: {str(e)}")
    
    def get_xgboost_params(self, base_params: Dict[str, Any]) -> Dict[str, Any]:
        """获取XGBoost GPU优化参数"""
        params = base_params.copy()
        
        if self.gpu_status['xgboost_gpu']:
            # 使用新的XGBoost 2.0+ GPU配置
            params['device'] = 'cuda'
            params['tree_method'] = 'hist'  # 在GPU上使用hist方法
            logger.info("✅ XGBoost GPU加速已启用")
        else:
            # 回退到CPU优化配置
            params['tree_method'] = 'hist'
            params['n_jobs'] = -1
            logger.info("⚠️  XGBoost使用CPU训练")
        
        return params
    
    def get_tensorflow_device(self) -> str:
        """获取TensorFlow设备配置"""
        if self.gpu_status['tensorflow_gpu']:
            return '/GPU:0'
        else:
            return '/CPU:0'
    
    def create_optimized_xgboost_regressor(self, **kwargs):
        """创建GPU优化的XGBoost回归器"""
        import xgboost as xgb
        
        default_params = {
            'n_estimators': 200,
            'learning_rate': 0.1,
            'max_depth': 6,
            'random_state': 42
        }
        default_params.update(kwargs)
        
        # 应用GPU优化
        optimized_params = self.get_xgboost_params(default_params)
        
        return xgb.XGBRegressor(**optimized_params)
    
    def create_optimized_xgboost_classifier(self, **kwargs):
        """创建GPU优化的XGBoost分类器"""
        import xgboost as xgb
        
        default_params = {
            'n_estimators': 200,
            'learning_rate': 0.1,
            'max_depth': 6,
            'random_state': 42
        }
        default_params.update(kwargs)
        
        # 应用GPU优化
        optimized_params = self.get_xgboost_params(default_params)
        
        return xgb.XGBClassifier(**optimized_params)
    
    def create_gpu_optimized_cnn_lstm(self, input_shape: Tuple[int, ...], 
                                     num_classes: Optional[int] = None,
                                     task_type: str = 'regression') -> Any:
        """创建GPU优化的CNN-LSTM模型"""
        try:
            import tensorflow as tf
            from tensorflow.keras import layers, models
            
            device = self.get_tensorflow_device()
            logger.info(f"使用设备: {device}")
            
            with tf.device(device):
                model = models.Sequential()
                
                # CNN层
                model.add(layers.Conv1D(filters=64, kernel_size=3, activation='relu', 
                                      input_shape=input_shape))
                model.add(layers.Conv1D(filters=64, kernel_size=3, activation='relu'))
                model.add(layers.Dropout(0.2))
                model.add(layers.MaxPooling1D(pool_size=2))
                
                model.add(layers.Conv1D(filters=128, kernel_size=3, activation='relu'))
                model.add(layers.Conv1D(filters=128, kernel_size=3, activation='relu'))
                model.add(layers.Dropout(0.2))
                model.add(layers.MaxPooling1D(pool_size=2))
                
                # LSTM层
                model.add(layers.LSTM(100, return_sequences=True, dropout=0.2))
                model.add(layers.LSTM(50, dropout=0.2))
                
                # 全连接层
                model.add(layers.Dense(50, activation='relu'))
                model.add(layers.Dropout(0.3))
                
                # 输出层
                if task_type == 'regression':
                    model.add(layers.Dense(1, activation='linear'))
                    model.compile(optimizer='adam', loss='mse', metrics=['mae'])
                else:
                    if num_classes is None:
                        num_classes = 2
                    if num_classes == 2:
                        model.add(layers.Dense(1, activation='sigmoid'))
                        model.compile(optimizer='adam', loss='binary_crossentropy', 
                                    metrics=['accuracy'])
                    else:
                        model.add(layers.Dense(num_classes, activation='softmax'))
                        model.compile(optimizer='adam', loss='sparse_categorical_crossentropy', 
                                    metrics=['accuracy'])
            
            logger.info(f"✅ CNN-LSTM模型已创建 (设备: {device})")
            return model
            
        except ImportError:
            logger.error("❌ TensorFlow未安装，无法创建CNN-LSTM模型")
            return None
        except Exception as e:
            logger.error(f"❌ 创建CNN-LSTM模型失败: {str(e)}")
            return None
    
    def train_with_gpu_optimization(self, model, X, y, **kwargs):
        """使用GPU优化训练模型"""
        model_type = type(model).__name__
        
        if 'XGB' in model_type:
            # XGBoost模型
            if self.gpu_status['xgboost_gpu']:
                logger.info("🚀 使用GPU训练XGBoost模型")
            else:
                logger.info("💻 使用CPU训练XGBoost模型")
            
            # XGBoost训练参数
            fit_params = {}
            if 'eval_set' in kwargs:
                fit_params['eval_set'] = kwargs['eval_set']
            if 'early_stopping_rounds' in kwargs:
                fit_params['early_stopping_rounds'] = kwargs['early_stopping_rounds']
            if 'verbose' in kwargs:
                fit_params['verbose'] = kwargs['verbose']
            
            return model.fit(X, y, **fit_params)
        
        elif hasattr(model, 'fit') and hasattr(model, 'predict'):
            # Keras/TensorFlow模型
            if self.gpu_status['tensorflow_gpu']:
                logger.info("🚀 使用GPU训练深度学习模型")
            else:
                logger.info("💻 使用CPU训练深度学习模型")
            
            # 深度学习训练参数
            fit_params = {
                'epochs': kwargs.get('epochs', 100),
                'batch_size': kwargs.get('batch_size', 32),
                'validation_split': kwargs.get('validation_split', 0.2),
                'verbose': kwargs.get('verbose', 1)
            }
            
            if 'callbacks' in kwargs:
                fit_params['callbacks'] = kwargs['callbacks']
            
            return model.fit(X, y, **fit_params)
        
        else:
            # 其他scikit-learn模型
            logger.info("💻 使用CPU训练scikit-learn模型")
            return model.fit(X, y)
    
    def get_optimization_summary(self) -> str:
        """获取优化总结"""
        summary = []
        summary.append("🚀 GPU加速状态总结:")
        summary.append(f"   NVIDIA GPU: {'✅' if self.gpu_status['nvidia_gpu'] else '❌'}")
        summary.append(f"   TensorFlow GPU: {'❌ (需要CUDA库)' if not self.gpu_status['tensorflow_gpu'] else '✅'}")
        summary.append(f"   PyTorch GPU: {'✅ (CUDA 12.1)' if self.gpu_status['pytorch_gpu'] else '❌'}")
        summary.append(f"   XGBoost GPU: {'✅ (device=cuda)' if self.gpu_status['xgboost_gpu'] else '❌'}")

        summary.append("\n⚙️  当前配置:")
        if self.gpu_status['xgboost_gpu']:
            summary.append("   XGBoost: 🚀 GPU加速 (device='cuda', tree_method='hist')")
        else:
            summary.append("   XGBoost: 💻 CPU训练 (tree_method='hist', n_jobs=-1)")

        if self.gpu_status['pytorch_gpu']:
            summary.append("   PyTorch: 🚀 GPU加速 (CUDA 12.1)")
        else:
            summary.append("   PyTorch: 💻 CPU训练")

        if self.gpu_status['tensorflow_gpu']:
            summary.append("   TensorFlow: 🚀 GPU加速 (/GPU:0)")
        else:
            summary.append("   TensorFlow: 💻 CPU训练 (需要安装CUDA库)")

        summary.append("   scikit-learn: 💻 CPU训练 (n_jobs=-1)")

        return "\n".join(summary)

# 全局GPU训练器实例
gpu_trainer = GPUAcceleratedTrainer()

def get_gpu_trainer():
    """获取GPU训练器实例"""
    return gpu_trainer

def create_gpu_optimized_xgboost_regressor(**kwargs):
    """创建GPU优化的XGBoost回归器 (便捷函数)"""
    return gpu_trainer.create_optimized_xgboost_regressor(**kwargs)

def create_gpu_optimized_xgboost_classifier(**kwargs):
    """创建GPU优化的XGBoost分类器 (便捷函数)"""
    return gpu_trainer.create_optimized_xgboost_classifier(**kwargs)

def train_with_gpu_optimization(model, X, y, **kwargs):
    """使用GPU优化训练模型 (便捷函数)"""
    return gpu_trainer.train_with_gpu_optimization(model, X, y, **kwargs)

if __name__ == "__main__":
    # 测试GPU加速训练器
    trainer = GPUAcceleratedTrainer()
    print(trainer.get_optimization_summary())
    
    # 测试XGBoost GPU
    print("\n🧪 测试XGBoost GPU优化...")
    from sklearn.datasets import make_regression
    X, y = make_regression(n_samples=1000, n_features=20, random_state=42)
    
    model = trainer.create_optimized_xgboost_regressor(n_estimators=50)
    trainer.train_with_gpu_optimization(model, X, y)
    print("✅ XGBoost GPU测试完成")
