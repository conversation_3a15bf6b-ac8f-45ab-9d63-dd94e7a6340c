#!/usr/bin/env python3
"""
轴型分类模型优化脚本
目标：准确率 > 0.92 (当前基线已达0.9853)
"""

import os
import pandas as pd
import numpy as np
import time
import logging
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import xgboost as xgb
import optuna
import warnings
warnings.filterwarnings('ignore')

# 导入GPU加速模块
from gpu_accelerated_training import get_gpu_trainer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 初始化GPU训练器
gpu_trainer = get_gpu_trainer()

def load_axle_type_data():
    """加载轴型分类数据"""
    filepath = './training_datasets_clean/axle_type_classification.csv'

    df = pd.read_csv(filepath)
    logger.info(f"原始数据形状: {df.shape}")

    # 分离特征和目标
    target_col = 'axle_type'
    feature_cols = [col for col in df.columns
                  if col not in [target_col, 'experiment_id', 'sensor_id', 'timestamp']]

    X = df[feature_cols].select_dtypes(include=[np.number])
    y = df[target_col]

    # 数据清理
    X = X.fillna(X.median())

    # 标签编码 (为XGBoost准备)
    from sklearn.preprocessing import LabelEncoder
    label_encoder = LabelEncoder()
    y_encoded = label_encoder.fit_transform(y)

    logger.info(f"清理后数据形状: {X.shape}")
    logger.info(f"类别分布: {y.value_counts().to_dict()}")
    logger.info(f"编码映射: {dict(zip(label_encoder.classes_, label_encoder.transform(label_encoder.classes_)))}")

    return X, y, y_encoded, label_encoder

def optimize_random_forest_classification(X, y, n_trials=15):
    """优化Random Forest分类器"""
    logger.info("优化Random Forest分类器...")
    
    def objective(trial):
        params = {
            'n_estimators': trial.suggest_int('n_estimators', 200, 500),
            'max_depth': trial.suggest_int('max_depth', 10, 25),
            'min_samples_split': trial.suggest_int('min_samples_split', 2, 10),
            'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 5),
            'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', 0.3, 0.5]),
            'bootstrap': trial.suggest_categorical('bootstrap', [True, False]),
            'class_weight': trial.suggest_categorical('class_weight', [None, 'balanced']),
            'random_state': 42,
            'n_jobs': -1
        }
        
        model = RandomForestClassifier(**params)
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
        return scores.mean()
    
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials)
    
    logger.info(f"Random Forest最佳参数: {study.best_params}")
    logger.info(f"Random Forest最佳准确率: {study.best_value:.4f}")
    
    return study.best_params, study.best_value

def optimize_gradient_boosting_classification(X, y, n_trials=15):
    """优化Gradient Boosting分类器"""
    logger.info("优化Gradient Boosting分类器...")
    
    def objective(trial):
        params = {
            'n_estimators': trial.suggest_int('n_estimators', 200, 400),
            'learning_rate': trial.suggest_float('learning_rate', 0.05, 0.2),
            'max_depth': trial.suggest_int('max_depth', 4, 10),
            'min_samples_split': trial.suggest_int('min_samples_split', 2, 10),
            'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 8),
            'subsample': trial.suggest_float('subsample', 0.8, 1.0),
            'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', 0.3, 0.5]),
            'random_state': 42
        }
        
        model = GradientBoostingClassifier(**params)
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
        return scores.mean()
    
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials)
    
    logger.info(f"Gradient Boosting最佳参数: {study.best_params}")
    logger.info(f"Gradient Boosting最佳准确率: {study.best_value:.4f}")
    
    return study.best_params, study.best_value

def optimize_xgboost_classification(X, y_encoded, n_trials=15):
    """优化XGBoost分类器 (GPU加速版)"""
    logger.info("优化XGBoost分类器 (GPU加速)...")

    def objective(trial):
        base_params = {
            'n_estimators': trial.suggest_int('n_estimators', 200, 400),
            'learning_rate': trial.suggest_float('learning_rate', 0.05, 0.2),
            'max_depth': trial.suggest_int('max_depth', 4, 10),
            'min_child_weight': trial.suggest_int('min_child_weight', 1, 8),
            'subsample': trial.suggest_float('subsample', 0.8, 1.0),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.8, 1.0),
            'reg_alpha': trial.suggest_float('reg_alpha', 0, 0.5),
            'reg_lambda': trial.suggest_float('reg_lambda', 0, 0.5),
            'random_state': 42,
            'eval_metric': 'logloss'
        }

        # 应用GPU优化
        params = gpu_trainer.get_xgboost_params(base_params)
        model = xgb.XGBClassifier(**params)

        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        scores = cross_val_score(model, X, y_encoded, cv=cv, scoring='accuracy')
        return scores.mean()

    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials)

    logger.info(f"XGBoost最佳参数: {study.best_params}")
    logger.info(f"XGBoost最佳准确率: {study.best_value:.4f}")

    return study.best_params, study.best_value

def train_and_evaluate_models(X, y, best_params):
    """训练和评估最佳模型"""
    logger.info("训练和评估最佳模型...")
    
    # 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    results = {}
    
    # 1. 优化后的Random Forest
    if 'rf' in best_params:
        rf_params = best_params['rf'][0]
        rf_params['random_state'] = 42
        rf_params['n_jobs'] = -1
        
        rf_model = RandomForestClassifier(**rf_params)
        rf_model.fit(X_train, y_train)
        rf_pred = rf_model.predict(X_test)
        
        rf_accuracy = accuracy_score(y_test, rf_pred)
        
        results['Optimized_RandomForest'] = {
            'accuracy': rf_accuracy,
            'cv_score': best_params['rf'][1],
            'predictions': rf_pred,
            'model': rf_model
        }
        logger.info(f"优化Random Forest - 准确率: {rf_accuracy:.4f}")
    
    # 2. 优化后的Gradient Boosting
    if 'gb' in best_params:
        gb_params = best_params['gb'][0]
        gb_params['random_state'] = 42
        
        gb_model = GradientBoostingClassifier(**gb_params)
        gb_model.fit(X_train, y_train)
        gb_pred = gb_model.predict(X_test)
        
        gb_accuracy = accuracy_score(y_test, gb_pred)
        
        results['Optimized_GradientBoosting'] = {
            'accuracy': gb_accuracy,
            'cv_score': best_params['gb'][1],
            'predictions': gb_pred,
            'model': gb_model
        }
        logger.info(f"优化Gradient Boosting - 准确率: {gb_accuracy:.4f}")
    
    # 3. 优化后的XGBoost
    if 'xgb' in best_params:
        xgb_params = best_params['xgb'][0]
        xgb_params['random_state'] = 42
        
        xgb_model = xgb.XGBClassifier(**xgb_params)
        xgb_model.fit(X_train, y_train)
        xgb_pred = xgb_model.predict(X_test)
        
        xgb_accuracy = accuracy_score(y_test, xgb_pred)
        
        results['Optimized_XGBoost'] = {
            'accuracy': xgb_accuracy,
            'cv_score': best_params['xgb'][1],
            'predictions': xgb_pred,
            'model': xgb_model
        }
        logger.info(f"优化XGBoost - 准确率: {xgb_accuracy:.4f}")
    
    # 4. 基线Random Forest
    baseline_rf = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
    baseline_rf.fit(X_train, y_train)
    baseline_pred = baseline_rf.predict(X_test)
    
    baseline_accuracy = accuracy_score(y_test, baseline_pred)
    
    results['Baseline_RandomForest'] = {
        'accuracy': baseline_accuracy,
        'cv_score': None,
        'predictions': baseline_pred,
        'model': baseline_rf
    }
    logger.info(f"基线Random Forest - 准确率: {baseline_accuracy:.4f}")
    
    return results, (X_test, y_test)

def main():
    """主函数"""
    print("🚀 轴型分类模型优化 (GPU加速版)")
    print("=" * 60)

    # 显示GPU状态
    print(gpu_trainer.get_optimization_summary())
    print()

    # 加载数据
    X, y, y_encoded, label_encoder = load_axle_type_data()
    
    # 优化模型
    best_params = {}
    
    print("\n📊 开始模型优化...")
    start_time = time.time()
    
    # 优化Random Forest
    rf_params, rf_score = optimize_random_forest_classification(X, y, n_trials=12)
    best_params['rf'] = (rf_params, rf_score)
    
    # 优化Gradient Boosting
    gb_params, gb_score = optimize_gradient_boosting_classification(X, y, n_trials=12)
    best_params['gb'] = (gb_params, gb_score)
    
    # 优化XGBoost (使用编码后的标签)
    xgb_params, xgb_score = optimize_xgboost_classification(X, y_encoded, n_trials=12)
    best_params['xgb'] = (xgb_params, xgb_score)
    
    optimization_time = time.time() - start_time
    print(f"\n⏱️  优化完成，耗时: {optimization_time:.2f}秒")
    
    # 训练和评估
    print("\n🎯 训练和评估最佳模型...")
    results, (X_test, y_test) = train_and_evaluate_models(X, y, best_params)
    
    # 显示结果
    print(f"\n" + "="*50)
    print("🎉 轴型分类优化结果")
    print("="*50)
    
    # 找到最佳模型
    best_model = max(results.items(), key=lambda x: x[1]['accuracy'])
    
    print(f"\n📈 性能对比:")
    for model_name, metrics in results.items():
        accuracy = metrics['accuracy']
        cv_score = metrics.get('cv_score', 'N/A')
        
        status = "🏆" if model_name == best_model[0] else "  "
        print(f"{status} {model_name}:")
        print(f"     准确率 = {accuracy:.4f}")
        if cv_score != 'N/A' and cv_score is not None:
            print(f"     交叉验证准确率 = {cv_score:.4f}")
    
    # 检查是否达到目标
    best_accuracy = best_model[1]['accuracy']
    target_accuracy = 0.92
    baseline_accuracy = 0.9853  # 原始基线
    
    print(f"\n🎯 目标检查:")
    print(f"   原始基线准确率: {baseline_accuracy}")
    print(f"   目标准确率: {target_accuracy}")
    print(f"   最佳准确率: {best_accuracy:.4f}")
    
    if best_accuracy >= target_accuracy:
        print(f"   ✅ 已达到目标！")
        if best_accuracy >= baseline_accuracy:
            print(f"   🎉 超越原始基线！")
        else:
            decline = ((baseline_accuracy - best_accuracy) / baseline_accuracy * 100)
            print(f"   ⚠️  相比原始基线下降了 {decline:.1f}%")
    else:
        needed_improvement = ((target_accuracy - best_accuracy) / best_accuracy * 100)
        print(f"   ⚠️  还需提升 {needed_improvement:.1f}% 才能达到目标")
    
    # 详细分类报告
    print(f"\n📊 最佳模型详细报告:")
    best_model_name = best_model[0]
    best_predictions = best_model[1]['predictions']
    
    print(f"\n{best_model_name} 分类报告:")
    print(classification_report(y_test, best_predictions))
    
    # 保存结果
    import json
    with open('axle_type_optimization_results.json', 'w', encoding='utf-8') as f:
        # 移除不能序列化的对象
        save_results = {}
        for k, v in results.items():
            save_results[k] = {
                'accuracy': v['accuracy'],
                'cv_score': v.get('cv_score', None)
            }
        
        json.dump({
            'best_params': best_params,
            'results': save_results,
            'optimization_time': optimization_time,
            'best_model': best_model_name,
            'target_achieved': best_accuracy >= target_accuracy
        }, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n💾 结果已保存到: axle_type_optimization_results.json")
    
    return results

if __name__ == "__main__":
    main()
