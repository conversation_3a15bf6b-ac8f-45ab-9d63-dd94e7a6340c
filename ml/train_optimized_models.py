#!/usr/bin/env python3
"""
优化版机器学习训练脚本
集成贝叶斯优化、模型替换和性能比较
"""

import os
import sys
import pandas as pd
import numpy as np
import time
import logging
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, accuracy_score
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from model_optimization import BayesianOptimizer
from model_replacer import ModelReplacer
from performance_comparison import PerformanceComparator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('optimization.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OptimizedMLTrainer:
    """优化版机器学习训练器"""
    
    def __init__(self, n_trials: int = 50, cv_folds: int = 5, random_state: int = 42):
        """
        初始化训练器
        
        Args:
            n_trials: 贝叶斯优化试验次数
            cv_folds: 交叉验证折数
            random_state: 随机种子
        """
        self.n_trials = n_trials
        self.cv_folds = cv_folds
        self.random_state = random_state
        self.results = {}
        self.optimization_results = {}
        self.comparison_results = {}
        
        logger.info(f"初始化优化训练器: n_trials={n_trials}, cv_folds={cv_folds}")
    
    def load_and_prepare_data(self, filepath: str, task_type: str):
        """加载和准备数据"""
        logger.info(f"加载数据集: {filepath}")
        
        try:
            df = pd.read_csv(filepath)
            logger.info(f"原始数据形状: {df.shape}")
            
            # 确定目标列
            if task_type == 'regression':
                if 'axle_load_tons' in df.columns:
                    target_col = 'axle_load_tons'
                elif 'speed_kmh' in df.columns:
                    target_col = 'speed_kmh'
                else:
                    raise ValueError("未找到回归目标列")
            else:  # classification
                target_col = 'axle_type'
            
            # 分离特征和目标
            feature_cols = [col for col in df.columns 
                          if col not in [target_col, 'experiment_id', 'sensor_id', 'timestamp']]
            
            X = df[feature_cols].select_dtypes(include=[np.number])
            y = df[target_col]
            
            # 数据清理
            X = X.fillna(X.median())
            
            # 移除异常值
            if task_type == 'regression':
                Q1 = y.quantile(0.25)
                Q3 = y.quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                valid_mask = (y >= lower_bound) & (y <= upper_bound)
                X = X[valid_mask]
                y = y[valid_mask]
            
            logger.info(f"清理后数据形状: {X.shape}")
            logger.info(f"特征数量: {X.shape[1]}")
            logger.info(f"目标列: {target_col}")
            
            if task_type == 'classification':
                logger.info(f"类别分布: {y.value_counts().to_dict()}")
            else:
                logger.info(f"目标值范围: [{y.min():.2f}, {y.max():.2f}]")
            
            return X, y, target_col
            
        except Exception as e:
            logger.error(f"数据加载失败: {str(e)}")
            return None, None, None
    
    def optimize_models(self, X: np.ndarray, y: np.ndarray, task_type: str, task_name: str):
        """使用贝叶斯优化优化模型参数"""
        logger.info(f"开始{task_name}的贝叶斯优化...")
        
        # 创建优化器
        optimizer = BayesianOptimizer(
            task_type=task_type,
            cv_folds=self.cv_folds,
            n_trials=self.n_trials,
            random_state=self.random_state
        )
        
        # 执行优化
        start_time = time.time()
        optimization_results = optimizer.optimize_all_models(X, y)
        end_time = time.time()
        
        logger.info(f"{task_name}优化完成，耗时: {end_time - start_time:.2f}秒")
        
        # 保存优化结果
        results_file = f'optimization_results_{task_name.lower().replace(" ", "_")}.json'
        optimizer.save_optimization_results(results_file)
        
        self.optimization_results[task_name] = optimization_results
        return optimization_results
    
    def replace_linear_models(self, X: np.ndarray, y: np.ndarray, task_type: str, task_name: str, 
                            optimized_params: dict = None):
        """替换线性模型并比较性能"""
        logger.info(f"开始{task_name}的模型替换...")
        
        # 创建模型替换器
        replacer = ModelReplacer(task_type=task_type, random_state=self.random_state)
        
        # 比较模型性能
        comparison_results = replacer.compare_models(X, y, cv_folds=self.cv_folds)
        
        # 保存比较结果
        results_file = f'model_comparison_{task_name.lower().replace(" ", "_")}.json'
        replacer.save_comparison_results(results_file)
        
        self.comparison_results[task_name] = comparison_results
        return comparison_results
    
    def train_and_evaluate_best_models(self, X: np.ndarray, y: np.ndarray, task_type: str, 
                                     optimization_results: dict, comparison_results: dict):
        """使用最佳参数训练和评估模型"""
        logger.info("训练最佳模型...")
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=self.random_state,
            stratify=y if task_type == 'classification' else None
        )
        
        # 获取最佳参数
        best_params = optimization_results.get('best_params', {})
        best_scores = optimization_results.get('best_scores', {})
        
        # 训练最佳模型
        results = {}
        
        # 1. 训练优化后的Random Forest
        if 'RandomForest' in best_params:
            from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
            
            rf_params = best_params['RandomForest']
            rf_params['random_state'] = self.random_state
            rf_params['n_jobs'] = -1
            
            if task_type == 'regression':
                model = RandomForestRegressor(**rf_params)
            else:
                model = RandomForestClassifier(**rf_params)
            
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            
            if task_type == 'regression':
                r2 = r2_score(y_test, y_pred)
                rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                results['Optimized_RandomForest'] = {
                    'model': model,
                    'r2': r2,
                    'rmse': rmse,
                    'predictions': y_pred
                }
                logger.info(f"优化Random Forest - R²: {r2:.4f}, RMSE: {rmse:.4f}")
            else:
                accuracy = accuracy_score(y_test, y_pred)
                results['Optimized_RandomForest'] = {
                    'model': model,
                    'accuracy': accuracy,
                    'predictions': y_pred
                }
                logger.info(f"优化Random Forest - 准确率: {accuracy:.4f}")
        
        # 2. 训练最佳替换模型
        enhanced_results = comparison_results.get('performance_comparison', {}).get('enhanced', {})
        if enhanced_results:
            # 找到最佳增强模型
            best_enhanced = max(enhanced_results.items(), 
                              key=lambda x: x[1].get('mean_score', 0) if 'error' not in x[1] else 0)
            
            logger.info(f"最佳增强模型: {best_enhanced[0]} (分数: {best_enhanced[1].get('mean_score', 0):.4f})")
        
        return results, (X_test, y_test)
    
    def generate_reports_and_visualizations(self, task_name: str):
        """生成报告和可视化"""
        logger.info(f"生成{task_name}的报告和可视化...")
        
        # 创建性能比较器
        comparator = PerformanceComparator(output_dir='./')
        
        # 获取结果
        optimization_results = self.optimization_results.get(task_name, {})
        comparison_results = self.comparison_results.get(task_name, {})
        
        # 生成性能报告
        if optimization_results and comparison_results:
            report_file = comparator.generate_performance_report(
                optimization_results, comparison_results, task_name
            )
            logger.info(f"性能报告已生成: {report_file}")
        
        # 生成优化收敛图
        if 'optimization_history' in optimization_results:
            convergence_file = comparator.create_optimization_convergence_chart(
                optimization_results['optimization_history'], task_name
            )
            logger.info(f"收敛图已生成: {convergence_file}")
    
    def train_single_task(self, filepath: str, task_type: str, task_name: str):
        """训练单个任务"""
        logger.info(f"开始训练任务: {task_name}")
        
        # 1. 加载数据
        X, y, target_col = self.load_and_prepare_data(filepath, task_type)
        if X is None:
            return None
        
        # 2. 贝叶斯优化
        optimization_results = self.optimize_models(X, y, task_type, task_name)
        
        # 3. 模型替换
        comparison_results = self.replace_linear_models(
            X, y, task_type, task_name, 
            optimization_results.get('best_params', {})
        )
        
        # 4. 训练最佳模型
        best_results, test_data = self.train_and_evaluate_best_models(
            X, y, task_type, optimization_results, comparison_results
        )
        
        # 5. 生成报告
        self.generate_reports_and_visualizations(task_name)
        
        return {
            'task_name': task_name,
            'task_type': task_type,
            'target_col': target_col,
            'optimization_results': optimization_results,
            'comparison_results': comparison_results,
            'best_results': best_results,
            'test_data': test_data
        }

def main():
    """主函数"""
    print("🚀 优化版机器学习训练系统")
    print("=" * 60)
    print("集成贝叶斯优化、模型替换和性能比较")
    
    # 检查数据集
    datasets = [
        ('./training_datasets_clean/speed_regression.csv', 'regression', '速度预测'),
        ('./training_datasets_clean/axle_load_regression.csv', 'regression', '轴重预测'),
        ('./training_datasets_clean/axle_type_classification.csv', 'classification', '轴型分类')
    ]
    
    available_datasets = []
    for filepath, task_type, task_name in datasets:
        if os.path.exists(filepath):
            available_datasets.append((filepath, task_type, task_name))
            print(f"✅ {task_name}: {filepath}")
        else:
            print(f"❌ {task_name}: {filepath} (文件不存在)")
    
    if not available_datasets:
        print("\n❌ 没有可用的数据集文件")
        return False
    
    # 创建训练器
    trainer = OptimizedMLTrainer(n_trials=50, cv_folds=5)
    
    # 训练所有任务
    all_results = {}
    
    for filepath, task_type, task_name in available_datasets:
        try:
            result = trainer.train_single_task(filepath, task_type, task_name)
            if result:
                all_results[task_name] = result
                logger.info(f"✅ {task_name} 训练完成")
            else:
                logger.error(f"❌ {task_name} 训练失败")
        except Exception as e:
            logger.error(f"❌ {task_name} 训练异常: {str(e)}")
            continue
    
    # 生成总结
    if all_results:
        print(f"\n" + "="*60)
        print("🎉 优化训练总结")
        print("="*60)
        
        for task_name, result in all_results.items():
            print(f"\n{task_name}:")
            
            # 显示优化结果
            opt_results = result.get('optimization_results', {})
            if 'best_scores' in opt_results:
                best_model = max(opt_results['best_scores'].items(), key=lambda x: x[1])
                print(f"  最佳优化模型: {best_model[0]} (分数: {best_model[1]:.4f})")
            
            # 显示最终结果
            best_results = result.get('best_results', {})
            for model_name, model_result in best_results.items():
                if result['task_type'] == 'regression':
                    print(f"  {model_name}: R² = {model_result['r2']:.4f}, RMSE = {model_result['rmse']:.4f}")
                else:
                    print(f"  {model_name}: 准确率 = {model_result['accuracy']:.4f}")
        
        print(f"\n🎉 所有优化任务完成！")
        print(f"📊 查看生成的报告和图表了解详细结果")
        return True
    else:
        print(f"\n❌ 所有任务都失败了")
        return False

if __name__ == "__main__":
    main()
