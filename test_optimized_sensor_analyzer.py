#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script for Optimized Sensor Data Analyzer
Tests the modified sensor analyzer that uses processed features instead of raw data

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import sys
import numpy as np
import pandas as pd
from pathlib import Path
import time

def create_test_features_data():
    """Create synthetic processed features data for testing"""
    print("🔧 Creating synthetic processed features data...")
    
    try:
        # Create synthetic features data similar to combined_features.csv
        np.random.seed(42)
        
        # Generate features for multiple sensors
        sensors = [f'sensor_{i:02d}' for i in range(1, 21)]
        n_records = 100  # 100 records per sensor
        
        data = []
        
        for sensor_id in sensors:
            for record_id in range(n_records):
                # Generate realistic vibration signal features
                base_freq = np.random.uniform(10, 100)  # Dominant frequency
                
                record = {
                    'sensor_id': sensor_id,
                    'record_id': record_id,
                    'mean': np.random.normal(0, 0.5),
                    'std': np.random.uniform(0.5, 2.0),
                    'rms': np.random.uniform(0.8, 2.5),
                    'peak': np.random.uniform(2.0, 8.0),
                    'crest_factor': np.random.uniform(2.0, 6.0),
                    'skewness': np.random.normal(0, 0.5),
                    'kurtosis': np.random.uniform(2.5, 5.0),
                    'energy': np.random.uniform(100, 1000),
                    'dominant_frequency': base_freq,
                    'spectral_centroid': base_freq * np.random.uniform(0.8, 1.2),
                    'spectral_rolloff': base_freq * np.random.uniform(1.5, 3.0),
                    'spectral_bandwidth': np.random.uniform(10, 50),
                    'zero_crossing_rate': np.random.uniform(0.1, 0.3),
                    'mfcc_1': np.random.normal(0, 1),
                    'mfcc_2': np.random.normal(0, 1),
                    'mfcc_3': np.random.normal(0, 1),
                    'wavelet_energy_1': np.random.uniform(0.1, 0.4),
                    'wavelet_energy_2': np.random.uniform(0.1, 0.4),
                    'wavelet_energy_3': np.random.uniform(0.1, 0.4),
                    'speed': np.random.uniform(40, 100),
                    'axle_weight': np.random.uniform(5, 55),
                    'axle_type': np.random.choice([2, 3, 4, 5, 6])
                }
                data.append(record)
        
        # Create DataFrame and save
        df = pd.DataFrame(data)
        test_features_file = 'test_combined_features.csv'
        df.to_csv(test_features_file, index=False)
        
        print(f"   ✅ Test features data created: {test_features_file}")
        print(f"   📊 Data shape: {df.shape}")
        print(f"   🔢 Sensors: {len(sensors)}")
        print(f"   📋 Features per sensor: {n_records}")
        print(f"   📈 Total features: {len(df.columns)}")
        
        return test_features_file, df
        
    except Exception as e:
        print(f"   ❌ Error creating test features data: {str(e)}")
        return None, None

def test_optimized_sensor_analyzer():
    """Test the optimized sensor analyzer with processed features"""
    print("\n🧪 Testing optimized sensor analyzer...")
    
    try:
        from sensor_data_analyzer import SensorDataAnalyzer
        
        # Create test features data
        test_file, test_df = create_test_features_data()
        if test_file is None:
            return False
        
        # Initialize analyzer
        analyzer = SensorDataAnalyzer(
            output_dir='test_optimized_charts',
            file_prefix='optimized_test_'
        )
        
        # Test loading processed features
        print("   📂 Testing processed features loading...")
        success = analyzer.load_processed_features(test_file, 'sensor_01')
        if not success:
            print("   ❌ Features loading failed")
            return False
        print("   ✅ Features loading successful")
        
        # Test visualization data extraction
        print("   🔧 Testing visualization data extraction...")
        if analyzer.features_df is None or analyzer.sensor_data is None:
            print("   ❌ Visualization data extraction failed")
            return False
        print("   ✅ Visualization data extraction successful")
        
        # Test time domain analysis
        print("   📈 Testing time domain analysis...")
        success = analyzer.generate_time_domain_analysis()
        if not success:
            print("   ❌ Time domain analysis failed")
            return False
        print("   ✅ Time domain analysis successful")
        
        # Test frequency domain analysis
        print("   🔊 Testing frequency domain analysis...")
        success = analyzer.generate_frequency_domain_analysis()
        if not success:
            print("   ❌ Frequency domain analysis failed")
            return False
        print("   ✅ Frequency domain analysis successful")
        
        # Test time-frequency analysis
        print("   🌊 Testing time-frequency analysis...")
        success = analyzer.generate_time_frequency_analysis()
        if not success:
            print("   ❌ Time-frequency analysis failed")
            return False
        print("   ✅ Time-frequency analysis successful")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Optimized analyzer test failed: {str(e)}")
        return False

def test_complete_workflow():
    """Test complete optimized workflow"""
    print("\n🧪 Testing complete optimized workflow...")
    
    try:
        from sensor_data_analyzer import SensorDataAnalyzer
        
        # Create test features data
        test_file, test_df = create_test_features_data()
        if test_file is None:
            return False
        
        # Initialize analyzer
        analyzer = SensorDataAnalyzer(
            output_dir='test_optimized_charts',
            file_prefix='workflow_test_'
        )
        
        # Test complete workflow
        print("   🚀 Running complete optimized workflow...")
        start_time = time.time()
        
        success = analyzer.analyze_sensor(
            features_file=test_file,
            sensor_id='sensor_05',
            apply_preprocessing=True
        )
        
        end_time = time.time()
        
        if success:
            print(f"   ✅ Complete optimized workflow successful ({end_time - start_time:.1f}s)")
            return True
        else:
            print("   ❌ Complete optimized workflow failed")
            return False
        
    except Exception as e:
        print(f"   ❌ Complete workflow test failed: {str(e)}")
        return False

def test_performance_comparison():
    """Test performance improvement compared to original approach"""
    print("\n🧪 Testing performance improvement...")
    
    try:
        from sensor_data_analyzer import SensorDataAnalyzer
        
        # Create test features data
        test_file, test_df = create_test_features_data()
        if test_file is None:
            return False
        
        # Test multiple sensors to measure performance
        test_sensors = ['sensor_01', 'sensor_05', 'sensor_10']
        
        total_start_time = time.time()
        success_count = 0
        
        for sensor_id in test_sensors:
            print(f"   🔍 Testing {sensor_id}...")
            
            analyzer = SensorDataAnalyzer(
                output_dir='test_optimized_charts',
                file_prefix=f'perf_{sensor_id}_'
            )
            
            sensor_start_time = time.time()
            success = analyzer.analyze_sensor(
                features_file=test_file,
                sensor_id=sensor_id,
                apply_preprocessing=True
            )
            sensor_end_time = time.time()
            
            if success:
                print(f"      ✅ {sensor_id} completed in {sensor_end_time - sensor_start_time:.1f}s")
                success_count += 1
            else:
                print(f"      ❌ {sensor_id} failed")
        
        total_end_time = time.time()
        total_time = total_end_time - total_start_time
        
        print(f"   📊 Performance test: {success_count}/{len(test_sensors)} successful")
        print(f"   ⏱️ Total time: {total_time:.1f}s")
        print(f"   ⚡ Average time per sensor: {total_time/len(test_sensors):.1f}s")
        print(f"   🚀 Performance improvement: No raw data processing required!")
        
        return success_count == len(test_sensors)
        
    except Exception as e:
        print(f"   ❌ Performance test failed: {str(e)}")
        return False

def test_output_quality():
    """Test output chart quality and naming"""
    print("\n🧪 Testing output chart quality...")
    
    try:
        output_dir = Path('test_optimized_charts')
        
        if not output_dir.exists():
            print("   ❌ Output directory not found")
            return False
        
        # Check generated files
        chart_files = list(output_dir.glob('*.png'))
        
        if not chart_files:
            print("   ❌ No chart files found")
            return False
        
        print(f"   📊 Found {len(chart_files)} chart files")
        
        # Check naming convention and quality
        naming_issues = 0
        quality_issues = 0
        
        for chart_file in chart_files:
            # Check naming convention
            if not (chart_file.name.startswith('optimized_test_') or 
                   chart_file.name.startswith('workflow_test_') or
                   chart_file.name.startswith('perf_')):
                naming_issues += 1
                print(f"      ⚠️ Naming issue: {chart_file.name}")
            
            # Check file size
            file_size = chart_file.stat().st_size
            if file_size < 30 * 1024:  # Less than 30KB might be too small
                quality_issues += 1
                print(f"      ⚠️ Small file size: {chart_file.name} ({file_size/1024:.1f}KB)")
        
        print(f"   📋 Naming convention: {len(chart_files) - naming_issues}/{len(chart_files)} correct")
        print(f"   📏 File quality: {len(chart_files) - quality_issues}/{len(chart_files)} good size")
        
        # List example files
        print("   📁 Example generated files:")
        for i, chart_file in enumerate(chart_files[:6]):
            print(f"      {i+1}. {chart_file.name}")
        if len(chart_files) > 6:
            print(f"      ... and {len(chart_files) - 6} more files")
        
        success_rate = (len(chart_files) - naming_issues - quality_issues) / len(chart_files)
        return success_rate >= 0.8  # 80% success rate
        
    except Exception as e:
        print(f"   ❌ Output quality test failed: {str(e)}")
        return False

def cleanup_test_files():
    """Clean up test files"""
    print("\n🧹 Cleaning up test files...")
    
    try:
        # Remove test features file
        test_file = 'test_combined_features.csv'
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"   ✅ Removed: {test_file}")
        
        # Remove test chart directory
        import shutil
        test_dir = Path('test_optimized_charts')
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"   ✅ Removed: {test_dir}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Cleanup failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Optimized Sensor Data Analyzer Tests")
    print("=" * 80)
    
    # Run all tests
    tests = [
        ("Optimized Functionality", test_optimized_sensor_analyzer),
        ("Complete Workflow", test_complete_workflow),
        ("Performance Comparison", test_performance_comparison),
        ("Output Quality", test_output_quality)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 Optimized Sensor Data Analyzer Test Results")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Optimized Sensor Data Analyzer is working correctly.")
        print("⚡ Performance improvement: No duplicate data processing!")
    elif passed >= total * 0.75:
        print("⚠️  Most tests passed. Optimization is functional with minor issues.")
    else:
        print("❌ Multiple test failures. Please check the optimization implementation.")
    
    # Cleanup
    cleanup_test_files()
    
    return passed >= total * 0.75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
