#!/usr/bin/env python3
"""
文档生成器
为振动信号分析系统的可视化图表生成完整的说明文档
"""

import os
import json
from datetime import datetime

class DocumentationGenerator:
    """文档生成器"""
    
    def __init__(self, output_dir='process_visualization'):
        """初始化文档生成器"""
        self.output_dir = output_dir
        self.chart_info = self._initialize_chart_info()
    
    def _initialize_chart_info(self):
        """初始化图表信息"""
        return {
            'chinese': {
                '原始信号_流程图_中文.png': {
                    'title': '原始振动信号时域波形图',
                    'description': '展示路面埋设加速度传感器采集的原始振动信号，包含多个传感器的时域波形数据。图中显示了车辆通过时产生的振动特征。',
                    'data_source': '路面埋设加速度传感器实时采集数据',
                    'analysis': '通过时域波形可以观察到车辆通过事件的明显特征，为后续的事件检测和特征提取提供基础数据。',
                    'latex_ref': '\\ref{fig:raw_signal_cn}'
                },
                '事件检测_流程图_中文.png': {
                    'title': '车辆通过事件检测与数据截取',
                    'description': '展示车辆通过事件的自动检测过程，通过寻找信号最大值定位车辆通过时刻，并以此为中心截取1秒钟的有效数据段。',
                    'data_source': '融合后的传感器信号数据',
                    'analysis': '事件检测算法能够准确识别车辆通过时刻，截取的1秒数据段包含完整的车辆通过信息，为特征提取提供高质量的数据基础。',
                    'latex_ref': '\\ref{fig:event_detection_cn}'
                },
                '特征提取_流程图_中文.png': {
                    'title': '特征提取结果展示图',
                    'description': '展示从截取的振动信号中提取的时域和频域特征，包括统计特征、频谱分析和功率谱密度分析。',
                    'data_source': '截取的1秒车辆通过数据段',
                    'analysis': '提取的特征能够有效表征车辆的重量、轴型和速度信息，为机器学习模型提供有效的输入特征。',
                    'latex_ref': '\\ref{fig:feature_extraction_cn}'
                },
                '数据预处理_流程图_中文.png': {
                    'title': '数据预处理效果对比图',
                    'description': '展示特征数据标准化前后的分布对比，验证数据预处理的效果和必要性。',
                    'data_source': '提取的原始特征数据',
                    'analysis': '标准化处理消除了不同特征间的量纲差异，使得机器学习算法能够更好地学习特征间的关系。',
                    'latex_ref': '\\ref{fig:preprocessing_cn}'
                },
                '模型训练_流程图_中文.png': {
                    'title': '模型训练过程与收敛分析',
                    'description': '展示不同机器学习算法的训练过程，包括传统机器学习和深度学习模型的收敛曲线。',
                    'data_source': '预处理后的特征数据',
                    'analysis': '训练曲线显示模型收敛良好，验证集性能稳定，表明模型具有良好的泛化能力。',
                    'latex_ref': '\\ref{fig:training_process_cn}'
                },
                '预测结果_流程图_中文.png': {
                    'title': '最终预测结果对比分析',
                    'description': '展示不同算法在速度预测任务上的最终性能，通过预测值与实际值的散点图评估模型精度。',
                    'data_source': '测试集数据和模型预测结果',
                    'analysis': '集成模型表现最佳，R²达到0.92以上，满足实际应用的精度要求。',
                    'latex_ref': '\\ref{fig:prediction_results_cn}'
                },
                '时频分析_流程图_中文.png': {
                    'title': '时频分析：小波变换与短时傅里叶变换',
                    'description': '展示振动信号的时频特性分析，包括STFT、小波变换和瞬时频率分析。',
                    'data_source': '车辆通过事件的振动信号',
                    'analysis': '时频分析揭示了振动信号的频率成分随时间的变化规律，为深入理解车辆-路面相互作用提供了重要信息。',
                    'latex_ref': '\\ref{fig:time_frequency_cn}'
                },
                '传感器融合_流程图_中文.png': {
                    'title': '多传感器数据融合过程',
                    'description': '展示多个传感器数据的融合过程和不同融合策略的效果对比。',
                    'data_source': '多个路面埋设传感器的同步数据',
                    'analysis': 'RMS融合方法在保持信号特征的同时有效降低了噪声，提高了系统的可靠性。',
                    'latex_ref': '\\ref{fig:sensor_fusion_cn}'
                },
                '算法性能雷达图_流程图_中文.png': {
                    'title': '算法性能对比雷达图',
                    'description': '从准确率、速度、鲁棒性、可解释性和内存使用等多个维度对比不同算法的性能。',
                    'data_source': '算法性能评估结果',
                    'analysis': '集成模型在准确率和鲁棒性方面表现优异，随机森林在可解释性方面具有优势，为实际应用中的算法选择提供了参考。',
                    'latex_ref': '\\ref{fig:algorithm_radar_cn}'
                }
            },
            'english': {
                'raw_signal_process_diagram_english.png': {
                    'title': 'Raw Vibration Signal Time-Domain Waveforms',
                    'description': 'Shows the original vibration signals collected by road-embedded accelerometer sensors, including time-domain waveform data from multiple sensors with vehicle passing characteristics.',
                    'data_source': 'Real-time data collected from road-embedded accelerometer sensors',
                    'analysis': 'The time-domain waveforms clearly show the characteristic features of vehicle passing events, providing fundamental data for subsequent event detection and feature extraction.',
                    'latex_ref': '\\ref{fig:raw_signal_en}'
                },
                'event_detection_process_diagram_english.png': {
                    'title': 'Vehicle Passing Event Detection and Data Segmentation',
                    'description': 'Demonstrates the automatic detection process of vehicle passing events by locating signal maximum values to identify vehicle passing moments and extracting 1-second effective data segments centered on these moments.',
                    'data_source': 'Fused sensor signal data',
                    'analysis': 'The event detection algorithm accurately identifies vehicle passing moments, and the extracted 1-second data segments contain complete vehicle passing information, providing high-quality data foundation for feature extraction.',
                    'latex_ref': '\\ref{fig:event_detection_en}'
                },
                'feature_extraction_process_diagram_english.png': {
                    'title': 'Feature Extraction Results Visualization',
                    'description': 'Shows time-domain and frequency-domain features extracted from segmented vibration signals, including statistical features, spectral analysis, and power spectral density analysis.',
                    'data_source': 'Extracted 1-second vehicle passing data segments',
                    'analysis': 'The extracted features effectively characterize vehicle weight, axle type, and speed information, providing effective input features for machine learning models.',
                    'latex_ref': '\\ref{fig:feature_extraction_en}'
                },
                'data_preprocessing_process_diagram_english.png': {
                    'title': 'Data Preprocessing Effects Comparison',
                    'description': 'Shows the distribution comparison of feature data before and after standardization, validating the effectiveness and necessity of data preprocessing.',
                    'data_source': 'Raw extracted feature data',
                    'analysis': 'Standardization processing eliminates dimensional differences between different features, enabling machine learning algorithms to better learn relationships between features.',
                    'latex_ref': '\\ref{fig:preprocessing_en}'
                },
                'model_training_process_diagram_english.png': {
                    'title': 'Model Training Process and Convergence Analysis',
                    'description': 'Shows the training process of different machine learning algorithms, including convergence curves for traditional machine learning and deep learning models.',
                    'data_source': 'Preprocessed feature data',
                    'analysis': 'Training curves show good model convergence with stable validation performance, indicating good generalization capability of the models.',
                    'latex_ref': '\\ref{fig:training_process_en}'
                },
                'prediction_results_process_diagram_english.png': {
                    'title': 'Final Prediction Results Comparison Analysis',
                    'description': 'Shows the final performance of different algorithms on speed prediction tasks, evaluating model accuracy through scatter plots of predicted vs. actual values.',
                    'data_source': 'Test set data and model prediction results',
                    'analysis': 'The ensemble model performs best with R² exceeding 0.92, meeting the accuracy requirements for practical applications.',
                    'latex_ref': '\\ref{fig:prediction_results_en}'
                },
                'time_frequency_analysis_process_diagram_english.png': {
                    'title': 'Time-Frequency Analysis: Wavelet Transform and STFT',
                    'description': 'Shows time-frequency characteristics analysis of vibration signals, including STFT, wavelet transform, and instantaneous frequency analysis.',
                    'data_source': 'Vibration signals from vehicle passing events',
                    'analysis': 'Time-frequency analysis reveals the temporal variation patterns of frequency components in vibration signals, providing important information for understanding vehicle-pavement interactions.',
                    'latex_ref': '\\ref{fig:time_frequency_en}'
                },
                'sensor_fusion_process_diagram_english.png': {
                    'title': 'Multi-Sensor Data Fusion Process',
                    'description': 'Shows the fusion process of multiple sensor data and comparison of different fusion strategy effects.',
                    'data_source': 'Synchronized data from multiple road-embedded sensors',
                    'analysis': 'RMS fusion method effectively reduces noise while maintaining signal characteristics, improving system reliability.',
                    'latex_ref': '\\ref{fig:sensor_fusion_en}'
                },
                'algorithm_performance_radar_process_diagram_english.png': {
                    'title': 'Algorithm Performance Comparison Radar Chart',
                    'description': 'Compares the performance of different algorithms from multiple dimensions including accuracy, speed, robustness, interpretability, and memory usage.',
                    'data_source': 'Algorithm performance evaluation results',
                    'analysis': 'The ensemble model excels in accuracy and robustness, while Random Forest has advantages in interpretability, providing reference for algorithm selection in practical applications.',
                    'latex_ref': '\\ref{fig:algorithm_radar_en}'
                }
            }
        }
    
    def generate_chart_index(self):
        """生成图表索引文档"""
        print("📋 生成图表索引文档...")
        
        for language in ['chinese', 'english']:
            content = []
            
            if language == 'chinese':
                content.append("# 振动信号分析系统 - 数据处理流程可视化图表索引")
                content.append("=" * 80)
                content.append("")
                content.append(f"**生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
                content.append("")
                content.append("## 图表概览")
                content.append("")
                content.append("本文档包含振动信号分析系统完整数据处理流程的可视化图表，")
                content.append("所有图表均采用IEEE/Elsevier期刊标准格式，300 DPI高分辨率输出，")
                content.append("适用于学术论文和技术报告。")
                content.append("")
                content.append("## 图表列表")
                content.append("")
            else:
                content.append("# Vibration Signal Analysis System - Data Processing Flow Visualization Chart Index")
                content.append("=" * 80)
                content.append("")
                content.append(f"**Generated Time**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                content.append("")
                content.append("## Chart Overview")
                content.append("")
                content.append("This document contains visualization charts for the complete data processing flow ")
                content.append("of the vibration signal analysis system. All charts follow IEEE/Elsevier journal ")
                content.append("standards with 300 DPI high-resolution output, suitable for academic papers and ")
                content.append("technical reports.")
                content.append("")
                content.append("## Chart List")
                content.append("")
            
            # 添加图表信息
            chart_info = self.chart_info[language]
            for i, (filename, info) in enumerate(chart_info.items(), 1):
                content.append(f"### {i}. {info['title']}")
                content.append("")
                content.append(f"**文件名**: `{filename}`" if language == 'chinese' else f"**Filename**: `{filename}`")
                content.append("")
                content.append(f"**描述**: {info['description']}")
                content.append("")
                content.append(f"**数据来源**: {info['data_source']}" if language == 'chinese' else f"**Data Source**: {info['data_source']}")
                content.append("")
                content.append(f"**分析结论**: {info['analysis']}" if language == 'chinese' else f"**Analysis**: {info['analysis']}")
                content.append("")
                content.append(f"**LaTeX引用**: `{info['latex_ref']}`" if language == 'chinese' else f"**LaTeX Reference**: `{info['latex_ref']}`")
                content.append("")
                content.append("---")
                content.append("")
            
            # 技术规格
            if language == 'chinese':
                content.append("## 技术规格")
                content.append("")
                content.append("- **分辨率**: 300 DPI")
                content.append("- **图片格式**: PNG")
                content.append("- **颜色模式**: RGB")
                content.append("- **字体**: 中文 - SimHei, 英文 - Times New Roman")
                content.append("- **图表尺寸**: 单栏图 8.5cm, 双栏图 17.8cm")
                content.append("- **网格透明度**: 30%")
                content.append("- **符合标准**: IEEE/Elsevier期刊格式")
            else:
                content.append("## Technical Specifications")
                content.append("")
                content.append("- **Resolution**: 300 DPI")
                content.append("- **Image Format**: PNG")
                content.append("- **Color Mode**: RGB")
                content.append("- **Font**: Times New Roman")
                content.append("- **Chart Size**: Single column 8.5cm, Double column 17.8cm")
                content.append("- **Grid Transparency**: 30%")
                content.append("- **Standards**: IEEE/Elsevier journal format")
            
            # 保存文档
            filename = 'chart_index_chinese.md' if language == 'chinese' else 'chart_index_english.md'
            filepath = os.path.join(self.output_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            print(f"✅ {language.upper()}版图表索引已保存: {filepath}")
    
    def generate_latex_references(self):
        """生成LaTeX引用代码"""
        print("📋 生成LaTeX引用代码...")
        
        for language in ['chinese', 'english']:
            content = []
            
            if language == 'chinese':
                content.append("% 振动信号分析系统图表LaTeX引用代码")
                content.append("% 中文版本")
                content.append("")
            else:
                content.append("% Vibration Signal Analysis System Chart LaTeX Reference Code")
                content.append("% English Version")
                content.append("")
            
            chart_info = self.chart_info[language]
            for filename, info in chart_info.items():
                # 生成figure环境
                ref_name = info['latex_ref'].replace('\\ref{fig:', '').replace('}', '')
                
                content.append(f"\\begin{{figure}}[htbp]")
                content.append(f"    \\centering")
                content.append(f"    \\includegraphics[width=\\textwidth]{{{filename}}}")
                content.append(f"    \\caption{{{info['title']}}}")
                content.append(f"    \\label{{fig:{ref_name}}}")
                content.append(f"\\end{{figure}}")
                content.append("")
            
            # 保存LaTeX代码
            filename = 'latex_references_chinese.tex' if language == 'chinese' else 'latex_references_english.tex'
            filepath = os.path.join(self.output_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            print(f"✅ {language.upper()}版LaTeX引用代码已保存: {filepath}")
    
    def generate_quality_checklist(self):
        """生成图表质量检查清单"""
        print("📋 生成图表质量检查清单...")
        
        checklist_items = {
            'chinese': [
                "图表分辨率是否为300 DPI",
                "字体大小是否符合标准（标题16pt，轴标签14pt，刻度12pt）",
                "中文字体是否为SimHei",
                "图表是否包含适当的网格线（透明度30%）",
                "颜色是否在黑白打印时仍清晰可读",
                "图例是否清晰且位置合适",
                "轴标签是否完整且单位正确",
                "图表标题是否准确描述内容",
                "数据标签是否清晰可读",
                "图表布局是否紧凑合理",
                "文件命名是否符合规范",
                "图表尺寸是否符合期刊要求"
            ],
            'english': [
                "Chart resolution is 300 DPI",
                "Font sizes meet standards (title 16pt, axis labels 14pt, ticks 12pt)",
                "English font is Times New Roman",
                "Charts include appropriate grid lines (30% transparency)",
                "Colors remain clear when printed in black and white",
                "Legends are clear and properly positioned",
                "Axis labels are complete with correct units",
                "Chart titles accurately describe content",
                "Data labels are clearly readable",
                "Chart layout is compact and reasonable",
                "File naming follows conventions",
                "Chart dimensions meet journal requirements"
            ]
        }
        
        for language in ['chinese', 'english']:
            content = []
            
            if language == 'chinese':
                content.append("# 图表质量检查清单")
                content.append("=" * 40)
                content.append("")
                content.append("请使用此清单检查每个生成的图表是否符合学术期刊标准：")
                content.append("")
            else:
                content.append("# Chart Quality Checklist")
                content.append("=" * 40)
                content.append("")
                content.append("Use this checklist to verify that each generated chart meets academic journal standards:")
                content.append("")
            
            for i, item in enumerate(checklist_items[language], 1):
                content.append(f"- [ ] {i}. {item}")
            
            content.append("")
            if language == 'chinese':
                content.append("## 检查说明")
                content.append("")
                content.append("- 每个图表都应该通过所有检查项")
                content.append("- 如有不符合项，请重新生成相应图表")
                content.append("- 建议在提交论文前进行最终检查")
            else:
                content.append("## Checking Instructions")
                content.append("")
                content.append("- Each chart should pass all checklist items")
                content.append("- If any item fails, regenerate the corresponding chart")
                content.append("- Final check is recommended before paper submission")
            
            # 保存检查清单
            filename = 'quality_checklist_chinese.md' if language == 'chinese' else 'quality_checklist_english.md'
            filepath = os.path.join(self.output_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            print(f"✅ {language.upper()}版质量检查清单已保存: {filepath}")
    
    def generate_all_documentation(self):
        """生成所有文档"""
        print("📚 生成完整文档集...")
        
        self.generate_chart_index()
        self.generate_latex_references()
        self.generate_quality_checklist()
        
        print("✅ 所有文档生成完成!")

def main():
    """测试函数"""
    print("🧪 测试文档生成器...")
    
    # 初始化文档生成器
    doc_gen = DocumentationGenerator()
    
    # 生成所有文档
    doc_gen.generate_all_documentation()
    
    print("✅ 测试完成!")

if __name__ == "__main__":
    main()
