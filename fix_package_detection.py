#!/usr/bin/env python3
"""
包检测问题快速修复脚本
专门解决scikit-learn等包的检测问题
"""

import sys
import subprocess
import importlib
from typing import List, Dict

def test_package_import(package_name: str, import_name: str) -> bool:
    """测试包导入"""
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} ({import_name}) - 导入成功")
        return True
    except ImportError as e:
        print(f"❌ {package_name} ({import_name}) - 导入失败: {str(e)}")
        return False

def install_missing_package(package_name: str) -> bool:
    """安装缺失的包"""
    print(f"📦 正在安装 {package_name}...")
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', package_name
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ {package_name} 安装异常: {str(e)}")
        return False

def fix_scikit_learn_specifically():
    """专门修复scikit-learn问题"""
    print("\n🔧 专门修复scikit-learn问题...")
    
    # 1. 检查当前状态
    print("1. 检查当前安装状态:")
    
    # 检查pip中是否有scikit-learn
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'show', 'scikit-learn'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ scikit-learn 在pip中已安装")
            print("版本信息:")
            for line in result.stdout.split('\n'):
                if line.startswith(('Name:', 'Version:', 'Location:')):
                    print(f"   {line}")
        else:
            print("❌ scikit-learn 在pip中未找到")
            
    except Exception as e:
        print(f"❌ 检查pip状态失败: {str(e)}")
    
    # 2. 测试导入
    print("\n2. 测试导入:")
    sklearn_works = test_package_import('scikit-learn', 'sklearn')
    
    # 3. 如果导入失败，尝试重新安装
    if not sklearn_works:
        print("\n3. 尝试重新安装scikit-learn...")
        
        # 先卸载
        print("卸载现有版本...")
        subprocess.run([
            sys.executable, '-m', 'pip', 'uninstall', 'scikit-learn', '-y'
        ], capture_output=True)
        
        # 重新安装
        if install_missing_package('scikit-learn'):
            # 再次测试
            print("\n4. 重新测试导入:")
            sklearn_works = test_package_import('scikit-learn', 'sklearn')
    
    return sklearn_works

def run_complete_fix():
    """运行完整修复流程"""
    print("🔧 深度学习增强振动信号分析系统 - 包检测问题修复")
    print("=" * 70)
    
    # 关键包映射
    critical_packages = {
        'numpy': 'numpy',
        'pandas': 'pandas',
        'scikit-learn': 'sklearn',
        'chardet': 'chardet'
    }
    
    print("🔍 检查关键包状态...")
    
    failed_packages = []
    
    for package_name, import_name in critical_packages.items():
        if not test_package_import(package_name, import_name):
            failed_packages.append(package_name)
    
    if not failed_packages:
        print("\n🎉 所有关键包都正常!")
        return True
    
    print(f"\n❌ 发现问题包: {failed_packages}")
    
    # 特殊处理scikit-learn
    if 'scikit-learn' in failed_packages:
        print("\n🎯 特殊处理scikit-learn...")
        if fix_scikit_learn_specifically():
            failed_packages.remove('scikit-learn')
    
    # 处理其他包
    for package in failed_packages:
        if package != 'scikit-learn':
            print(f"\n🔧 修复 {package}...")
            if install_missing_package(package):
                # 测试安装结果
                import_name = critical_packages[package]
                test_package_import(package, import_name)
    
    # 最终验证
    print(f"\n🔍 最终验证...")
    all_good = True
    
    for package_name, import_name in critical_packages.items():
        if not test_package_import(package_name, import_name):
            all_good = False
    
    if all_good:
        print(f"\n🎉 所有包修复完成!")
        print(f"✅ 现在可以运行: python setup_real_data_training.py")
    else:
        print(f"\n⚠️  仍有包存在问题，请手动检查")
    
    return all_good

def main():
    """主函数"""
    try:
        success = run_complete_fix()
        
        if success:
            print(f"\n🚀 测试修复效果...")
            
            # 测试setup_real_data_training.py的包检测逻辑
            print("测试setup_real_data_training.py的包检测逻辑...")
            
            required_packages = {
                'pandas': 'pandas',
                'numpy': 'numpy', 
                'scikit-learn': 'sklearn',
                'chardet': 'chardet'
            }
            
            all_detected = True
            for package_name, import_name in required_packages.items():
                try:
                    __import__(import_name)
                    print(f"✅ {package_name}: 检测成功")
                except ImportError:
                    print(f"❌ {package_name}: 检测失败")
                    all_detected = False
            
            if all_detected:
                print(f"\n🎉 包检测逻辑测试通过!")
                print(f"✅ setup_real_data_training.py 应该可以正常运行了")
            else:
                print(f"\n❌ 包检测逻辑仍有问题")
        
    except Exception as e:
        print(f"❌ 修复过程出错: {str(e)}")

if __name__ == "__main__":
    main()
