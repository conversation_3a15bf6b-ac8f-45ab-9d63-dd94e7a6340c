#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复数据集成问题
直接使用已处理的扩展数据文件来更新主特征文件

作者: AI Assistant
日期: 2024-12-22
"""

import os
import pandas as pd
import numpy as np

def quick_fix_data_integration():
    """快速修复数据集成问题"""
    print("🛠️  快速修复数据集成问题")
    print("="*80)
    print("🎯 目标：直接使用扩展数据文件更新主特征文件")
    print("-"*80)
    
    try:
        # 检查扩展文件是否存在
        expanded_file = 'combined_features_expanded.csv'
        if not os.path.exists(expanded_file):
            print(f"❌ 扩展文件 {expanded_file} 不存在")
            return False
        
        # 读取扩展文件
        print(f"📁 读取扩展文件: {expanded_file}")
        expanded_df = pd.read_csv(expanded_file)
        print(f"   数据形状: {expanded_df.shape}")
        
        # 检查数据源分布
        if 'data_source' in expanded_df.columns:
            source_counts = expanded_df['data_source'].value_counts()
            print(f"   📊 原始数据源分布:")
            for source, count in source_counts.items():
                print(f"      {source}: {count} 样本")
            
            # 修复数据源标识
            print(f"\n🔧 修复数据源标识...")
            
            # 将 'existing' 重新标记为 'legacy_format'
            expanded_df.loc[expanded_df['data_source'] == 'existing', 'data_source'] = 'legacy_format'
            
            # 验证修复后的分布
            source_counts_fixed = expanded_df['data_source'].value_counts()
            print(f"   📊 修复后数据源分布:")
            for source, count in source_counts_fixed.items():
                print(f"      {source}: {count} 样本")
            
            # 检查是否包含两种格式
            has_new_format = 'new_format' in source_counts_fixed
            has_legacy_format = 'legacy_format' in source_counts_fixed
            
            if has_new_format and has_legacy_format:
                print(f"✅ 数据包含两种格式")
                
                # 备份原主文件
                main_file = 'combined_features.csv'
                if os.path.exists(main_file):
                    backup_file = f'{main_file}.backup_before_fix'
                    os.rename(main_file, backup_file)
                    print(f"   📁 已备份原文件: {backup_file}")
                
                # 保存修复后的数据为主文件
                expanded_df.to_csv(main_file, index=False)
                print(f"   ✅ 已更新主文件: {main_file}")
                
                # 验证保存结果
                verification_df = pd.read_csv(main_file)
                print(f"\n✅ 验证结果:")
                print(f"   主文件数据形状: {verification_df.shape}")
                
                if 'data_source' in verification_df.columns:
                    final_source_counts = verification_df['data_source'].value_counts()
                    print(f"   📊 最终数据源分布:")
                    for source, count in final_source_counts.items():
                        print(f"      {source}: {count} 样本")
                    
                    # 计算统计信息
                    total_samples = len(verification_df)
                    new_format_samples = final_source_counts.get('new_format', 0)
                    legacy_format_samples = final_source_counts.get('legacy_format', 0)
                    
                    print(f"\n📈 修复效果总结:")
                    print(f"   总样本数: {total_samples}")
                    print(f"   新格式样本: {new_format_samples} ({new_format_samples/total_samples*100:.1f}%)")
                    print(f"   传统格式样本: {legacy_format_samples} ({legacy_format_samples/total_samples*100:.1f}%)")
                    print(f"   总特征数: {verification_df.shape[1]}")
                    
                    # 检查关键目标变量
                    target_vars = ['speed_kmh', 'load_tons', 'axle_type']
                    print(f"\n🎯 目标变量验证:")
                    for var in target_vars:
                        if var in verification_df.columns:
                            valid_count = verification_df[var].notna().sum()
                            print(f"   {var}: {valid_count}/{total_samples} 有效值 ({valid_count/total_samples*100:.1f}%)")
                        else:
                            print(f"   {var}: ❌ 缺失")
                    
                    if total_samples >= 3000:
                        print(f"\n🎉 修复成功！")
                        print(f"   ✅ 样本数量达到目标（{total_samples} ≥ 3000）")
                        print(f"   ✅ 支持双格式数据处理")
                        print(f"   ✅ 数据源标识正确")
                        return True
                    else:
                        print(f"\n⚠️  修复部分成功")
                        print(f"   样本数量: {total_samples} < 3000")
                        return False
                else:
                    print(f"❌ 验证失败：缺少data_source列")
                    return False
            else:
                print(f"❌ 数据不包含两种格式")
                return False
        else:
            print(f"❌ 扩展文件缺少data_source列")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False

def test_fixed_system():
    """测试修复后的系统"""
    print(f"\n🧪 测试修复后的系统")
    print("-"*50)
    
    try:
        # 检查主文件
        main_file = 'combined_features.csv'
        if os.path.exists(main_file):
            df = pd.read_csv(main_file)
            print(f"✅ 主文件读取成功: {df.shape}")
            
            # 检查数据源
            if 'data_source' in df.columns:
                source_counts = df['data_source'].value_counts()
                print(f"   数据源分布: {dict(source_counts)}")
                
                # 检查是否支持双格式
                has_both_formats = len(source_counts) >= 2
                has_sufficient_samples = len(df) >= 3000
                
                print(f"   双格式支持: {'✅' if has_both_formats else '❌'}")
                print(f"   样本数量充足: {'✅' if has_sufficient_samples else '❌'}")
                
                if has_both_formats and has_sufficient_samples:
                    print(f"🎉 系统修复成功，可以进行完整分析！")
                    return True
                else:
                    print(f"⚠️  系统部分修复，建议进一步优化")
                    return False
            else:
                print(f"❌ 缺少数据源标识")
                return False
        else:
            print(f"❌ 主文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 启动快速数据集成修复")
    
    # 执行修复
    fix_success = quick_fix_data_integration()
    
    if fix_success:
        # 测试修复效果
        test_success = test_fixed_system()
        
        if test_success:
            print(f"\n💡 下一步建议:")
            print(f"   1. 运行 python unified_vibration_analysis.py 进行完整分析")
            print(f"   2. 验证模型性能是否达到目标")
            print(f"   3. 检查预测准确率提升效果")
            return True
        else:
            print(f"\n⚠️  修复完成但测试未完全通过")
            return False
    else:
        print(f"\n❌ 修复失败，请检查扩展数据文件")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
