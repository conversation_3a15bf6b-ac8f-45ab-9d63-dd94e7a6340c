#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
振动信号分析系统数据处理状态验证程序
验证数据格式识别、处理完整性和特征数据集构成

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import chinese_font_config  # 中文字体配置
import pandas as pd
import numpy as np
import os
import sys
import glob
import json
from pathlib import Path
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class DataProcessingVerifier:
    """数据处理状态验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.data_dir = "data"
        self.results = {
            'new_format_files': [],
            'old_format_structure': {},
            'feature_dataset_analysis': {},
            'processing_completeness': {},
            'recommendations': []
        }
        
        print("🔍 数据处理状态验证器已初始化")
        print(f"   数据目录: {self.data_dir}")
    
    def scan_data_directory(self):
        """扫描数据目录结构"""
        print("\n📂 步骤1: 扫描数据目录结构")
        print("=" * 60)
        
        if not os.path.exists(self.data_dir):
            print(f"❌ 数据目录不存在: {self.data_dir}")
            return False
        
        # 扫描新格式数据文件
        self._scan_new_format_files()
        
        # 扫描旧格式数据结构
        self._scan_old_format_structure()
        
        return True
    
    def _scan_new_format_files(self):
        """扫描新格式数据文件"""
        print("\n🔍 扫描新格式数据文件...")
        
        # 查找所有CSV文件
        csv_files = glob.glob(os.path.join(self.data_dir, "**/*.csv"), recursive=True)
        
        new_format_files = []
        
        for file_path in csv_files:
            try:
                # 检查是否是新格式文件
                if self._is_new_format_file(file_path):
                    file_info = self._analyze_new_format_file(file_path)
                    if file_info:
                        new_format_files.append(file_info)
                        
            except Exception as e:
                print(f"   ⚠️  文件分析失败 {file_path}: {str(e)}")
                continue
        
        self.results['new_format_files'] = new_format_files
        
        print(f"   ✅ 找到 {len(new_format_files)} 个新格式数据文件")
        
        if new_format_files:
            print("   📋 新格式文件列表:")
            for i, file_info in enumerate(new_format_files[:5]):  # 显示前5个
                print(f"      {i+1}. {file_info['relative_path']}")
                print(f"         形状: {file_info['shape']}, 传感器: {file_info['sensor_count']}")
            
            if len(new_format_files) > 5:
                print(f"      ... 还有 {len(new_format_files) - 5} 个文件")
    
    def _is_new_format_file(self, file_path):
        """判断是否是新格式文件"""
        try:
            # 检查文件路径特征
            path_parts = file_path.replace('\\', '/').split('/')
            
            # 新格式文件通常直接在data目录下或子目录中
            # 不在三级嵌套结构中（轴重/轴型/速度）
            
            # 检查是否在旧格式的三级结构中
            has_weight_dir = any('吨' in part for part in path_parts)
            has_axle_dir = any('轴' in part for part in path_parts)
            has_speed_dir = any('km' in part for part in path_parts)
            
            # 如果在三级结构中，可能是旧格式
            if has_weight_dir and has_axle_dir and has_speed_dir:
                return False
            
            # 尝试读取文件检查列结构
            df = self._safe_read_csv(file_path)
            if df is None:
                return False
            
            # 新格式特征：22列（count + 无用列 + 20个传感器列）
            if len(df.columns) == 22:
                # 检查是否有count列
                if 'count' in df.columns:
                    # 检查传感器列数量
                    sensor_cols = [col for col in df.columns if 'sensor' in col.lower()]
                    if len(sensor_cols) == 20:
                        return True
            
            return False
            
        except Exception:
            return False
    
    def _analyze_new_format_file(self, file_path):
        """分析新格式文件"""
        try:
            df = self._safe_read_csv(file_path)
            if df is None:
                return None
            
            # 提取文件信息
            relative_path = os.path.relpath(file_path, self.data_dir)
            
            # 分析传感器列
            sensor_cols = [col for col in df.columns if 'sensor' in col.lower()]
            
            # 从文件名提取元数据
            metadata = self._extract_metadata_from_filename(file_path)
            
            file_info = {
                'file_path': file_path,
                'relative_path': relative_path,
                'shape': df.shape,
                'columns': list(df.columns),
                'sensor_columns': sensor_cols,
                'sensor_count': len(sensor_cols),
                'metadata': metadata,
                'file_size_mb': os.path.getsize(file_path) / (1024 * 1024)
            }
            
            return file_info
            
        except Exception as e:
            print(f"   ❌ 新格式文件分析失败 {file_path}: {str(e)}")
            return None
    
    def _scan_old_format_structure(self):
        """扫描旧格式数据结构"""
        print("\n🔍 扫描旧格式数据结构...")
        
        old_format_structure = {}
        
        # 查找三级目录结构
        for root, dirs, files in os.walk(self.data_dir):
            path_parts = os.path.relpath(root, self.data_dir).split(os.sep)
            
            # 检查是否是三级结构：轴重/轴型/速度
            if len(path_parts) == 3:
                weight_dir, axle_dir, speed_dir = path_parts
                
                # 验证目录命名模式
                if ('吨' in weight_dir and '轴' in axle_dir and 
                    ('km' in speed_dir or 'h' in speed_dir)):
                    
                    # 查找CSV文件
                    csv_files = [f for f in files if f.endswith('.csv')]
                    
                    if csv_files:
                        experiment_key = f"{weight_dir}_{axle_dir}_{speed_dir}"
                        
                        file_analysis = []
                        for csv_file in csv_files:
                            file_path = os.path.join(root, csv_file)
                            file_info = self._analyze_old_format_file(file_path)
                            if file_info:
                                file_analysis.append(file_info)
                        
                        if file_analysis:
                            old_format_structure[experiment_key] = {
                                'weight': weight_dir,
                                'axle_type': axle_dir,
                                'speed': speed_dir,
                                'directory': root,
                                'csv_files': file_analysis,
                                'file_count': len(file_analysis)
                            }
        
        self.results['old_format_structure'] = old_format_structure
        
        print(f"   ✅ 找到 {len(old_format_structure)} 个旧格式实验配置")
        
        if old_format_structure:
            print("   📋 旧格式实验配置:")
            for exp_key, exp_info in list(old_format_structure.items())[:5]:
                print(f"      {exp_key}: {exp_info['file_count']} 个CSV文件")
            
            if len(old_format_structure) > 5:
                print(f"      ... 还有 {len(old_format_structure) - 5} 个配置")
    
    def _analyze_old_format_file(self, file_path):
        """分析旧格式文件"""
        try:
            df = self._safe_read_csv(file_path)
            if df is None:
                return None
            
            # 检查是否是旧格式（21列：count + 20个传感器）
            if len(df.columns) == 21:
                sensor_cols = [col for col in df.columns if col != 'count']
                
                file_info = {
                    'file_name': os.path.basename(file_path),
                    'file_path': file_path,
                    'shape': df.shape,
                    'sensor_count': len(sensor_cols),
                    'file_size_mb': os.path.getsize(file_path) / (1024 * 1024)
                }
                
                return file_info
            
            return None
            
        except Exception as e:
            print(f"   ❌ 旧格式文件分析失败 {file_path}: {str(e)}")
            return None
    
    def _extract_metadata_from_filename(self, file_path):
        """从文件名提取元数据"""
        filename = os.path.basename(file_path)
        metadata = {}
        
        # 尝试从文件名提取信息
        # 格式：MonitorPoint_DateTime_AcceData_Lane_AxleType_Weight_Speed
        parts = filename.replace('.csv', '').split('_')
        
        if len(parts) >= 7:
            try:
                metadata['monitor_point'] = parts[0]
                metadata['datetime'] = parts[1]
                metadata['data_type'] = parts[2]
                metadata['lane'] = parts[3]
                metadata['axle_type'] = parts[4]
                metadata['weight'] = parts[5]
                metadata['speed'] = parts[6]
            except:
                pass
        
        return metadata
    
    def _safe_read_csv(self, file_path):
        """安全读取CSV文件"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
        
        for encoding in encodings:
            try:
                return pd.read_csv(file_path, encoding=encoding)
            except UnicodeDecodeError:
                continue
            except Exception:
                break
        return None
    
    def analyze_current_feature_dataset(self):
        """分析当前特征数据集"""
        print("\n📊 步骤2: 分析当前特征数据集")
        print("=" * 60)
        
        feature_files = [
            'combined_features.csv',
            './ml/combined_features_clean.csv',
            './analysis_results/combined_features.csv'
        ]
        
        current_features_df = None
        used_file = None
        
        # 查找当前使用的特征文件
        for file_path in feature_files:
            if os.path.exists(file_path):
                try:
                    current_features_df = pd.read_csv(file_path)
                    used_file = file_path
                    print(f"   ✅ 找到特征文件: {file_path}")
                    break
                except Exception as e:
                    print(f"   ⚠️  文件读取失败 {file_path}: {str(e)}")
                    continue
        
        if current_features_df is None:
            print("   ❌ 未找到任何特征文件")
            return False
        
        # 分析特征数据集
        analysis = self._analyze_feature_dataset_composition(current_features_df, used_file)
        self.results['feature_dataset_analysis'] = analysis
        
        return True
    
    def _analyze_feature_dataset_composition(self, df, file_path):
        """分析特征数据集构成"""
        print(f"\n📋 特征数据集分析: {file_path}")
        print(f"   数据形状: {df.shape}")
        
        analysis = {
            'file_path': file_path,
            'shape': df.shape,
            'columns': list(df.columns),
            'sample_composition': {},
            'data_source_analysis': {}
        }
        
        # 分析样本构成
        if 'experiment_id' in df.columns:
            exp_counts = df['experiment_id'].value_counts()
            print(f"   实验数量: {len(exp_counts)}")
            print(f"   样本分布:")
            
            for exp_id, count in exp_counts.head(10).items():
                print(f"      {exp_id}: {count} 个样本")
            
            if len(exp_counts) > 10:
                print(f"      ... 还有 {len(exp_counts) - 10} 个实验")
            
            analysis['sample_composition']['experiment_counts'] = exp_counts.to_dict()
            analysis['sample_composition']['total_experiments'] = len(exp_counts)
        
        # 分析数据来源
        source_analysis = self._identify_data_sources(df)
        analysis['data_source_analysis'] = source_analysis
        
        # 分析特征完整性
        feature_analysis = self._analyze_feature_completeness(df)
        analysis['feature_completeness'] = feature_analysis
        
        return analysis
    
    def _identify_data_sources(self, df):
        """识别数据来源"""
        print(f"\n🔍 识别数据来源...")
        
        source_analysis = {
            'new_format_samples': 0,
            'old_format_samples': 0,
            'unknown_samples': 0,
            'source_indicators': {}
        }
        
        # 通过experiment_id模式识别来源
        if 'experiment_id' in df.columns:
            for exp_id in df['experiment_id'].unique():
                exp_samples = len(df[df['experiment_id'] == exp_id])
                
                # 旧格式通常有特定的命名模式（包含吨、轴、km等）
                if ('吨' in str(exp_id) and '轴' in str(exp_id) and 'km' in str(exp_id)):
                    source_analysis['old_format_samples'] += exp_samples
                    source_analysis['source_indicators'][exp_id] = 'old_format'
                # 新格式通常有不同的命名模式
                elif ('MonitorPoint' in str(exp_id) or '_AcceData_' in str(exp_id)):
                    source_analysis['new_format_samples'] += exp_samples
                    source_analysis['source_indicators'][exp_id] = 'new_format'
                else:
                    source_analysis['unknown_samples'] += exp_samples
                    source_analysis['source_indicators'][exp_id] = 'unknown'
        
        print(f"   旧格式样本: {source_analysis['old_format_samples']}")
        print(f"   新格式样本: {source_analysis['new_format_samples']}")
        print(f"   未知来源样本: {source_analysis['unknown_samples']}")
        
        return source_analysis
    
    def _analyze_feature_completeness(self, df):
        """分析特征完整性"""
        print(f"\n🔍 分析特征完整性...")
        
        # 预期的30个核心特征
        expected_features = [
            # 时域特征 (11个)
            'mean', 'std', 'var', 'rms', 'peak', 'peak_to_peak', 
            'crest_factor', 'skewness', 'kurtosis', 'energy', 'zero_crossing_rate',
            # 频域特征 (10个)
            'dominant_frequency', 'mean_frequency', 'frequency_std',
            'spectral_centroid', 'spectral_rolloff', 'spectral_flux',
            'total_power', 'low_freq_power', 'mid_freq_power', 'high_freq_power',
            # 时频域特征 (9个)
            'spectrogram_mean', 'spectrogram_std', 'spectrogram_max',
            'time_bandwidth_product', 'wavelet_energy_d1', 'wavelet_energy_d2',
            'wavelet_energy_d3', 'wavelet_energy_d4', 'wavelet_energy_a4'
        ]
        
        # 检查特征存在性
        found_features = [feat for feat in expected_features if feat in df.columns]
        missing_features = [feat for feat in expected_features if feat not in df.columns]
        
        # 分析其他列
        metadata_cols = ['experiment_id', 'sensor_id', 'passage_idx', 'segment_idx']
        other_cols = [col for col in df.columns if col not in expected_features and col not in metadata_cols]
        
        feature_analysis = {
            'expected_features': expected_features,
            'found_features': found_features,
            'missing_features': missing_features,
            'feature_completeness_ratio': len(found_features) / len(expected_features),
            'other_columns': other_cols,
            'total_feature_columns': len([col for col in df.columns if col not in metadata_cols])
        }
        
        print(f"   核心特征完整性: {len(found_features)}/{len(expected_features)} ({feature_analysis['feature_completeness_ratio']*100:.1f}%)")
        
        if missing_features:
            print(f"   缺失特征: {missing_features[:5]}{'...' if len(missing_features) > 5 else ''}")
        
        if other_cols:
            print(f"   其他特征列: {len(other_cols)} 个")
        
        return feature_analysis

    def check_processing_completeness(self):
        """检查数据处理完整性"""
        print("\n🔍 步骤3: 检查数据处理完整性")
        print("=" * 60)

        completeness_analysis = {
            'new_format_processing': {},
            'old_format_processing': {},
            'missing_data': [],
            'processing_gaps': []
        }

        # 检查新格式数据处理情况
        new_format_analysis = self._check_new_format_processing()
        completeness_analysis['new_format_processing'] = new_format_analysis

        # 检查旧格式数据处理情况
        old_format_analysis = self._check_old_format_processing()
        completeness_analysis['old_format_processing'] = old_format_analysis

        # 识别处理缺口
        processing_gaps = self._identify_processing_gaps()
        completeness_analysis['processing_gaps'] = processing_gaps

        self.results['processing_completeness'] = completeness_analysis

        return True

    def _check_new_format_processing(self):
        """检查新格式数据处理情况"""
        print("\n📋 检查新格式数据处理情况...")

        new_format_files = self.results.get('new_format_files', [])
        feature_analysis = self.results.get('feature_dataset_analysis', {})

        analysis = {
            'total_files': len(new_format_files),
            'processed_files': 0,
            'unprocessed_files': [],
            'processing_rate': 0.0
        }

        if not new_format_files:
            print("   ℹ️  未发现新格式数据文件")
            return analysis

        # 检查哪些新格式文件已被处理
        source_indicators = feature_analysis.get('data_source_analysis', {}).get('source_indicators', {})

        processed_experiments = [exp_id for exp_id, source in source_indicators.items()
                               if source == 'new_format']

        # 尝试匹配文件和已处理的实验
        for file_info in new_format_files:
            file_metadata = file_info.get('metadata', {})
            file_path = file_info['relative_path']

            # 检查是否在已处理的实验中
            is_processed = False
            for exp_id in processed_experiments:
                if self._match_file_to_experiment(file_info, exp_id):
                    is_processed = True
                    break

            if is_processed:
                analysis['processed_files'] += 1
            else:
                analysis['unprocessed_files'].append({
                    'file_path': file_path,
                    'metadata': file_metadata,
                    'reason': 'Not found in feature dataset'
                })

        analysis['processing_rate'] = analysis['processed_files'] / analysis['total_files'] if analysis['total_files'] > 0 else 0

        print(f"   新格式文件总数: {analysis['total_files']}")
        print(f"   已处理文件: {analysis['processed_files']}")
        print(f"   未处理文件: {len(analysis['unprocessed_files'])}")
        print(f"   处理率: {analysis['processing_rate']*100:.1f}%")

        if analysis['unprocessed_files']:
            print("   未处理的文件:")
            for unprocessed in analysis['unprocessed_files'][:3]:
                print(f"      - {unprocessed['file_path']}")
            if len(analysis['unprocessed_files']) > 3:
                print(f"      ... 还有 {len(analysis['unprocessed_files']) - 3} 个文件")

        return analysis

    def _check_old_format_processing(self):
        """检查旧格式数据处理情况"""
        print("\n📋 检查旧格式数据处理情况...")

        old_format_structure = self.results.get('old_format_structure', {})
        feature_analysis = self.results.get('feature_dataset_analysis', {})

        analysis = {
            'total_experiments': len(old_format_structure),
            'processed_experiments': 0,
            'unprocessed_experiments': [],
            'processing_rate': 0.0
        }

        if not old_format_structure:
            print("   ℹ️  未发现旧格式数据结构")
            return analysis

        # 检查哪些旧格式实验已被处理
        source_indicators = feature_analysis.get('data_source_analysis', {}).get('source_indicators', {})

        processed_experiments = [exp_id for exp_id, source in source_indicators.items()
                               if source == 'old_format']

        # 检查每个旧格式实验配置
        for exp_key, exp_info in old_format_structure.items():
            # 尝试匹配实验配置和已处理的实验
            is_processed = False
            for processed_exp in processed_experiments:
                if self._match_old_experiment(exp_info, processed_exp):
                    is_processed = True
                    break

            if is_processed:
                analysis['processed_experiments'] += 1
            else:
                analysis['unprocessed_experiments'].append({
                    'experiment_key': exp_key,
                    'experiment_info': exp_info,
                    'reason': 'Not found in feature dataset'
                })

        analysis['processing_rate'] = analysis['processed_experiments'] / analysis['total_experiments'] if analysis['total_experiments'] > 0 else 0

        print(f"   旧格式实验总数: {analysis['total_experiments']}")
        print(f"   已处理实验: {analysis['processed_experiments']}")
        print(f"   未处理实验: {len(analysis['unprocessed_experiments'])}")
        print(f"   处理率: {analysis['processing_rate']*100:.1f}%")

        if analysis['unprocessed_experiments']:
            print("   未处理的实验:")
            for unprocessed in analysis['unprocessed_experiments'][:3]:
                print(f"      - {unprocessed['experiment_key']}")
            if len(analysis['unprocessed_experiments']) > 3:
                print(f"      ... 还有 {len(analysis['unprocessed_experiments']) - 3} 个实验")

        return analysis

    def _match_file_to_experiment(self, file_info, exp_id):
        """匹配文件到实验"""
        # 简单的匹配逻辑，可以根据实际情况调整
        file_path = file_info['relative_path']
        metadata = file_info.get('metadata', {})

        # 检查文件路径或元数据是否与实验ID匹配
        if str(exp_id) in file_path:
            return True

        # 检查元数据匹配
        for key, value in metadata.items():
            if str(value) in str(exp_id):
                return True

        return False

    def _match_old_experiment(self, exp_info, processed_exp):
        """匹配旧格式实验"""
        # 检查实验信息是否与已处理的实验匹配
        weight = exp_info['weight']
        axle_type = exp_info['axle_type']
        speed = exp_info['speed']

        # 构建可能的实验ID模式
        possible_patterns = [
            f"{weight}_{axle_type}_{speed}",
            f"{weight}{axle_type}{speed}",
            weight + axle_type + speed
        ]

        for pattern in possible_patterns:
            if pattern in str(processed_exp) or str(processed_exp) in pattern:
                return True

        return False

    def _identify_processing_gaps(self):
        """识别处理缺口"""
        print("\n🔍 识别处理缺口...")

        gaps = []

        # 检查新格式数据缺口
        new_format_analysis = self.results.get('processing_completeness', {}).get('new_format_processing', {})
        if new_format_analysis.get('unprocessed_files'):
            gaps.append({
                'type': 'new_format_missing',
                'description': f"{len(new_format_analysis['unprocessed_files'])} 个新格式文件未被处理",
                'files': new_format_analysis['unprocessed_files']
            })

        # 检查旧格式数据缺口
        old_format_analysis = self.results.get('processing_completeness', {}).get('old_format_processing', {})
        if old_format_analysis.get('unprocessed_experiments'):
            gaps.append({
                'type': 'old_format_missing',
                'description': f"{len(old_format_analysis['unprocessed_experiments'])} 个旧格式实验未被处理",
                'experiments': old_format_analysis['unprocessed_experiments']
            })

        # 检查特征完整性缺口
        feature_analysis = self.results.get('feature_dataset_analysis', {})
        feature_completeness = feature_analysis.get('feature_completeness', {})

        if feature_completeness.get('feature_completeness_ratio', 1.0) < 0.9:
            gaps.append({
                'type': 'feature_incomplete',
                'description': f"特征完整性不足: {feature_completeness.get('feature_completeness_ratio', 0)*100:.1f}%",
                'missing_features': feature_completeness.get('missing_features', [])
            })

        if gaps:
            print(f"   发现 {len(gaps)} 个处理缺口:")
            for i, gap in enumerate(gaps):
                print(f"      {i+1}. {gap['description']}")
        else:
            print("   ✅ 未发现明显的处理缺口")

        return gaps

    def generate_recommendations(self):
        """生成改进建议"""
        print("\n💡 步骤4: 生成改进建议")
        print("=" * 60)

        recommendations = []

        # 基于处理缺口生成建议
        processing_gaps = self.results['processing_completeness']['processing_gaps']

        for gap in processing_gaps:
            if gap['type'] == 'new_format_missing':
                recommendations.append({
                    'priority': 'high',
                    'category': 'data_processing',
                    'title': '处理遗漏的新格式数据文件',
                    'description': f"发现 {len(gap['files'])} 个新格式数据文件未被处理到特征数据集中",
                    'action': '重新运行特征提取流程，确保包含所有新格式数据文件',
                    'files': [f['file_path'] for f in gap['files']]
                })

            elif gap['type'] == 'old_format_missing':
                recommendations.append({
                    'priority': 'high',
                    'category': 'data_processing',
                    'title': '处理遗漏的旧格式实验数据',
                    'description': f"发现 {len(gap['experiments'])} 个旧格式实验配置未被处理",
                    'action': '检查旧格式数据处理逻辑，确保所有实验配置都被正确识别和处理',
                    'experiments': [exp['experiment_key'] for exp in gap['experiments']]
                })

            elif gap['type'] == 'feature_incomplete':
                recommendations.append({
                    'priority': 'medium',
                    'category': 'feature_extraction',
                    'title': '完善特征提取',
                    'description': f"特征完整性不足，缺少 {len(gap['missing_features'])} 个核心特征",
                    'action': '检查特征提取器实现，确保所有30个核心特征都能正确提取',
                    'missing_features': gap['missing_features']
                })

        # 基于数据分析生成建议
        feature_analysis = self.results.get('feature_dataset_analysis', {})
        source_analysis = feature_analysis.get('data_source_analysis', {})

        if source_analysis.get('new_format_samples', 0) == 0:
            recommendations.append({
                'priority': 'high',
                'category': 'data_integration',
                'title': '集成新格式数据',
                'description': '当前特征数据集中没有新格式数据样本',
                'action': '确保新格式数据处理流程正常工作，并将新格式数据纳入特征提取'
            })

        if source_analysis.get('unknown_samples', 0) > 0:
            recommendations.append({
                'priority': 'medium',
                'category': 'data_quality',
                'title': '识别未知来源数据',
                'description': f'发现 {source_analysis["unknown_samples"]} 个未知来源的样本',
                'action': '分析这些样本的来源，确保数据溯源的完整性'
            })

        self.results['recommendations'] = recommendations

        print(f"   生成了 {len(recommendations)} 条改进建议")

        for i, rec in enumerate(recommendations):
            print(f"   {i+1}. [{rec['priority'].upper()}] {rec['title']}")
            print(f"      {rec['description']}")

        return recommendations

    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        print("\n📝 生成综合分析报告...")

        report_lines = []
        report_lines.append("# 振动信号分析系统数据处理状态验证报告")
        report_lines.append(f"生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")

        # 执行摘要
        report_lines.append("## 执行摘要")

        new_format_count = len(self.results.get('new_format_files', []))
        old_format_count = len(self.results.get('old_format_structure', {}))
        feature_shape = self.results.get('feature_dataset_analysis', {}).get('shape', (0, 0))

        report_lines.append(f"- **数据发现**: 新格式文件 {new_format_count} 个，旧格式实验 {old_format_count} 个")
        report_lines.append(f"- **当前特征数据集**: {feature_shape[0]} 个样本，{feature_shape[1]} 个特征")

        # 数据来源分析
        source_analysis = self.results.get('feature_dataset_analysis', {}).get('data_source_analysis', {})
        new_samples = source_analysis.get('new_format_samples', 0)
        old_samples = source_analysis.get('old_format_samples', 0)
        unknown_samples = source_analysis.get('unknown_samples', 0)

        report_lines.append(f"- **样本来源**: 新格式 {new_samples} 个，旧格式 {old_samples} 个，未知 {unknown_samples} 个")

        # 处理完整性
        processing_completeness = self.results.get('processing_completeness', {})
        new_processing_rate = processing_completeness.get('new_format_processing', {}).get('processing_rate', 0) * 100
        old_processing_rate = processing_completeness.get('old_format_processing', {}).get('processing_rate', 0) * 100

        report_lines.append(f"- **处理完整性**: 新格式 {new_processing_rate:.1f}%，旧格式 {old_processing_rate:.1f}%")

        # 改进建议数量
        recommendations_count = len(self.results.get('recommendations', []))
        high_priority_count = len([r for r in self.results.get('recommendations', []) if r['priority'] == 'high'])

        report_lines.append(f"- **改进建议**: {recommendations_count} 条建议，其中 {high_priority_count} 条高优先级")
        report_lines.append("")

        # 详细分析
        report_lines.append("## 数据格式识别结果")

        # 新格式数据
        report_lines.append("### 新格式数据文件")
        if new_format_count > 0:
            report_lines.append(f"发现 {new_format_count} 个新格式数据文件（22列：count + 无用列 + 20个传感器列）")
            report_lines.append("")
            report_lines.append("| 文件路径 | 数据形状 | 传感器数 | 文件大小(MB) |")
            report_lines.append("|---------|---------|---------|-------------|")

            for file_info in self.results.get('new_format_files', [])[:10]:
                path = file_info['relative_path']
                shape = f"{file_info['shape'][0]}×{file_info['shape'][1]}"
                sensors = file_info['sensor_count']
                size = f"{file_info['file_size_mb']:.2f}"
                report_lines.append(f"| {path} | {shape} | {sensors} | {size} |")

            if new_format_count > 10:
                report_lines.append(f"| ... | ... | ... | ... |")
                report_lines.append(f"| 共 {new_format_count} 个文件 | | | |")
        else:
            report_lines.append("未发现新格式数据文件")

        report_lines.append("")

        # 旧格式数据
        report_lines.append("### 旧格式数据结构")
        if old_format_count > 0:
            report_lines.append(f"发现 {old_format_count} 个旧格式实验配置（三级目录结构：轴重/轴型/速度）")
            report_lines.append("")
            report_lines.append("| 实验配置 | CSV文件数 | 目录路径 |")
            report_lines.append("|---------|----------|---------|")

            for exp_key, exp_info in list(self.results.get('old_format_structure', {}).items())[:10]:
                file_count = exp_info['file_count']
                directory = exp_info['directory']
                report_lines.append(f"| {exp_key} | {file_count} | {directory} |")

            if old_format_count > 10:
                report_lines.append(f"| ... | ... | ... |")
                report_lines.append(f"| 共 {old_format_count} 个配置 | | |")
        else:
            report_lines.append("未发现旧格式数据结构")

        report_lines.append("")

        # 当前特征数据集分析
        report_lines.append("## 当前特征数据集分析")

        feature_analysis = self.results.get('feature_dataset_analysis', {})
        if feature_analysis:
            file_path = feature_analysis.get('file_path', 'Unknown')
            shape = feature_analysis.get('shape', (0, 0))

            report_lines.append(f"**使用的特征文件**: {file_path}")
            report_lines.append(f"**数据形状**: {shape[0]} 个样本 × {shape[1]} 个特征")
            report_lines.append("")

            # 样本构成
            sample_composition = feature_analysis.get('sample_composition', {})
            if 'experiment_counts' in sample_composition:
                exp_counts = sample_composition['experiment_counts']
                total_experiments = sample_composition.get('total_experiments', 0)

                report_lines.append(f"**实验数量**: {total_experiments} 个")
                report_lines.append("**样本分布** (前10个实验):")

                for i, (exp_id, count) in enumerate(list(exp_counts.items())[:10]):
                    report_lines.append(f"- {exp_id}: {count} 个样本")

                if total_experiments > 10:
                    report_lines.append(f"- ... 还有 {total_experiments - 10} 个实验")

                report_lines.append("")

            # 数据来源分析
            source_analysis = feature_analysis.get('data_source_analysis', {})
            if source_analysis:
                report_lines.append("**数据来源分析**:")
                report_lines.append(f"- 新格式数据样本: {source_analysis.get('new_format_samples', 0)} 个")
                report_lines.append(f"- 旧格式数据样本: {source_analysis.get('old_format_samples', 0)} 个")
                report_lines.append(f"- 未知来源样本: {source_analysis.get('unknown_samples', 0)} 个")
                report_lines.append("")

            # 特征完整性
            feature_completeness = feature_analysis.get('feature_completeness', {})
            if feature_completeness:
                completeness_ratio = feature_completeness.get('feature_completeness_ratio', 0) * 100
                found_count = len(feature_completeness.get('found_features', []))
                missing_count = len(feature_completeness.get('missing_features', []))

                report_lines.append("**特征完整性分析**:")
                report_lines.append(f"- 核心特征完整性: {completeness_ratio:.1f}% ({found_count}/30)")

                if missing_count > 0:
                    missing_features = feature_completeness.get('missing_features', [])
                    report_lines.append(f"- 缺失特征 ({missing_count} 个): {', '.join(missing_features[:5])}")
                    if missing_count > 5:
                        report_lines.append(f"  ... 还有 {missing_count - 5} 个")

                other_cols = feature_completeness.get('other_columns', [])
                if other_cols:
                    report_lines.append(f"- 其他特征列: {len(other_cols)} 个")

                report_lines.append("")

        # 处理完整性分析
        report_lines.append("## 数据处理完整性分析")

        processing_completeness = self.results.get('processing_completeness', {})

        # 新格式处理情况
        new_format_processing = processing_completeness.get('new_format_processing', {})
        if new_format_processing:
            total_files = new_format_processing.get('total_files', 0)
            processed_files = new_format_processing.get('processed_files', 0)
            processing_rate = new_format_processing.get('processing_rate', 0) * 100

            report_lines.append("### 新格式数据处理情况")
            report_lines.append(f"- 总文件数: {total_files}")
            report_lines.append(f"- 已处理文件: {processed_files}")
            report_lines.append(f"- 处理率: {processing_rate:.1f}%")

            unprocessed_files = new_format_processing.get('unprocessed_files', [])
            if unprocessed_files:
                report_lines.append(f"- 未处理文件 ({len(unprocessed_files)} 个):")
                for unprocessed in unprocessed_files[:5]:
                    report_lines.append(f"  - {unprocessed['file_path']}")
                if len(unprocessed_files) > 5:
                    report_lines.append(f"  - ... 还有 {len(unprocessed_files) - 5} 个文件")

            report_lines.append("")

        # 旧格式处理情况
        old_format_processing = processing_completeness.get('old_format_processing', {})
        if old_format_processing:
            total_experiments = old_format_processing.get('total_experiments', 0)
            processed_experiments = old_format_processing.get('processed_experiments', 0)
            processing_rate = old_format_processing.get('processing_rate', 0) * 100

            report_lines.append("### 旧格式数据处理情况")
            report_lines.append(f"- 总实验数: {total_experiments}")
            report_lines.append(f"- 已处理实验: {processed_experiments}")
            report_lines.append(f"- 处理率: {processing_rate:.1f}%")

            unprocessed_experiments = old_format_processing.get('unprocessed_experiments', [])
            if unprocessed_experiments:
                report_lines.append(f"- 未处理实验 ({len(unprocessed_experiments)} 个):")
                for unprocessed in unprocessed_experiments[:5]:
                    report_lines.append(f"  - {unprocessed['experiment_key']}")
                if len(unprocessed_experiments) > 5:
                    report_lines.append(f"  - ... 还有 {len(unprocessed_experiments) - 5} 个实验")

            report_lines.append("")

        # 改进建议
        report_lines.append("## 改进建议")

        recommendations = self.results.get('recommendations', [])
        if recommendations:
            high_priority = [r for r in recommendations if r['priority'] == 'high']
            medium_priority = [r for r in recommendations if r['priority'] == 'medium']

            if high_priority:
                report_lines.append("### 高优先级建议")
                for i, rec in enumerate(high_priority):
                    report_lines.append(f"{i+1}. **{rec['title']}**")
                    report_lines.append(f"   - 问题: {rec['description']}")
                    report_lines.append(f"   - 建议: {rec['action']}")
                    report_lines.append("")

            if medium_priority:
                report_lines.append("### 中优先级建议")
                for i, rec in enumerate(medium_priority):
                    report_lines.append(f"{i+1}. **{rec['title']}**")
                    report_lines.append(f"   - 问题: {rec['description']}")
                    report_lines.append(f"   - 建议: {rec['action']}")
                    report_lines.append("")
        else:
            report_lines.append("✅ 未发现需要改进的问题")

        # 保存报告
        report_content = "\n".join(report_lines)
        report_path = "data_processing_verification_report.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"   ✅ 综合分析报告已保存: {report_path}")

        return report_path

    def run_complete_verification(self):
        """运行完整验证流程"""
        print("🚀 开始数据处理状态验证")
        print("=" * 80)

        try:
            # 步骤1: 扫描数据目录
            if not self.scan_data_directory():
                return False

            # 步骤2: 分析当前特征数据集
            if not self.analyze_current_feature_dataset():
                return False

            # 步骤3: 检查处理完整性
            if not self.check_processing_completeness():
                return False

            # 步骤4: 生成改进建议
            self.generate_recommendations()

            # 步骤5: 生成综合报告
            report_path = self.generate_comprehensive_report()

            # 保存验证结果
            results_path = "verification_results.json"
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2, default=str)

            print("\n" + "=" * 80)
            print("✅ 数据处理状态验证完成！")
            print(f"📄 详细报告: {report_path}")
            print(f"📊 验证结果: {results_path}")

            return True

        except Exception as e:
            print(f"❌ 验证过程失败: {str(e)}")
            return False

def main():
    """主函数"""
    verifier = DataProcessingVerifier()
    success = verifier.run_complete_verification()

    if success:
        print("\n🎯 验证摘要:")

        # 显示关键发现
        new_format_count = len(verifier.results.get('new_format_files', []))
        old_format_count = len(verifier.results.get('old_format_structure', {}))

        feature_analysis = verifier.results.get('feature_dataset_analysis', {})
        feature_shape = feature_analysis.get('shape', (0, 0))
        source_analysis = feature_analysis.get('data_source_analysis', {})

        print(f"   📁 发现数据: 新格式 {new_format_count} 个文件，旧格式 {old_format_count} 个实验")
        print(f"   📊 当前特征数据集: {feature_shape[0]} 个样本 × {feature_shape[1]} 个特征")
        print(f"   🔍 样本来源: 新格式 {source_analysis.get('new_format_samples', 0)} 个，旧格式 {source_analysis.get('old_format_samples', 0)} 个")

        recommendations = verifier.results.get('recommendations', [])
        high_priority_count = len([r for r in recommendations if r['priority'] == 'high'])

        if high_priority_count > 0:
            print(f"   ⚠️  发现 {high_priority_count} 个高优先级问题需要处理")
        else:
            print("   ✅ 数据处理状态良好，无高优先级问题")

        return True
    else:
        print("❌ 验证失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
