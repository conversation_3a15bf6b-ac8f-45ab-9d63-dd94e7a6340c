# -*- coding: utf-8 -*-
"""
振动信号分析框架 - 交通信息逆向解析系统
基于水泥混凝土路面振动信号的车辆类型识别、车速估计和载重分析

作者: AI Assistant
日期: 2024
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import signal
from scipy.fft import fft, fftfreq
from scipy.signal import spectrogram, welch
import pywt
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder, MinMaxScaler
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.svm import SVC, SVR
from sklearn.neural_network import MLPClassifier, MLPRegressor
from sklearn.metrics import classification_report, confusion_matrix, mean_squared_error, r2_score
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class VibrationSignalProcessor:
    """振动信号处理器"""
    
    def __init__(self, fs=1000):
        """
        初始化信号处理器
        
        参数:
        fs: 采样频率 (Hz)
        """
        self.fs = fs
        
    def denoise_signal(self, acc_data):
        """
        对加速度信号进行去噪处理
        
        参数:
        acc_data: 原始加速度数据
        
        返回:
        denoised_data: 去噪后的数据
        """
        # 1. 带通滤波 (保留5-200Hz的信号，过滤环境噪声和高频干扰)
        b, a = signal.butter(4, [5 / (self.fs / 2), 200 / (self.fs / 2)], 'bandpass')
        filtered_data = signal.filtfilt(b, a, acc_data)
        
        # 2. 小波去噪 (适用于非平稳信号)
        coeffs = pywt.wavedec(filtered_data, 'db4', level=4)
        # 阈值处理
        for i in range(1, len(coeffs)):
            coeffs[i] = pywt.threshold(coeffs[i], np.std(coeffs[i]) * 0.5, mode='soft')
        denoised_data = pywt.waverec(coeffs, 'db4')
        
        return denoised_data
    
    def detect_outliers(self, data, method='iqr', threshold=3):
        """
        异常值检测
        
        参数:
        data: 输入数据
        method: 检测方法 ('iqr', 'zscore')
        threshold: 阈值
        
        返回:
        outlier_mask: 异常值掩码
        """
        if method == 'iqr':
            Q1 = np.percentile(data, 25)
            Q3 = np.percentile(data, 75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            outlier_mask = (data < lower_bound) | (data > upper_bound)
        elif method == 'zscore':
            z_scores = np.abs((data - np.mean(data)) / np.std(data))
            outlier_mask = z_scores > threshold
        
        return outlier_mask
    
    def segment_signal(self, data, window_size=1000, overlap=0.5):
        """
        信号分割
        
        参数:
        data: 输入信号
        window_size: 窗口大小
        overlap: 重叠比例
        
        返回:
        segments: 分割后的信号段列表
        """
        step_size = int(window_size * (1 - overlap))
        segments = []
        
        for i in range(0, len(data) - window_size + 1, step_size):
            segment = data[i:i + window_size]
            segments.append(segment)
        
        return np.array(segments)

class FeatureExtractor:
    """特征提取器"""
    
    def __init__(self, fs=1000):
        self.fs = fs
    
    def extract_time_domain_features(self, signal):
        """
        提取时域特征
        
        参数:
        signal: 输入信号
        
        返回:
        features: 时域特征字典
        """
        features = {
            'mean': np.mean(signal),
            'std': np.std(signal),
            'var': np.var(signal),
            'rms': np.sqrt(np.mean(signal**2)),
            'peak': np.max(np.abs(signal)),
            'peak_to_peak': np.max(signal) - np.min(signal),
            'crest_factor': np.max(np.abs(signal)) / np.sqrt(np.mean(signal**2)),
            'skewness': self._skewness(signal),
            'kurtosis': self._kurtosis(signal),
            'energy': np.sum(signal**2),
            'zero_crossing_rate': self._zero_crossing_rate(signal)
        }
        return features
    
    def extract_frequency_domain_features(self, signal):
        """
        提取频域特征
        
        参数:
        signal: 输入信号
        
        返回:
        features: 频域特征字典
        """
        # FFT变换
        fft_signal = fft(signal)
        freqs = fftfreq(len(signal), 1/self.fs)
        magnitude = np.abs(fft_signal)
        
        # 只取正频率部分
        positive_freqs = freqs[:len(freqs)//2]
        positive_magnitude = magnitude[:len(magnitude)//2]
        
        # 功率谱密度
        f_psd, psd = welch(signal, self.fs, nperseg=min(256, len(signal)//4))
        
        # 先计算基础值
        mean_frequency = np.sum(positive_freqs * positive_magnitude) / np.sum(positive_magnitude)

        features = {
            'dominant_frequency': positive_freqs[np.argmax(positive_magnitude)],
            'mean_frequency': mean_frequency,
            'frequency_std': np.sqrt(np.sum(((positive_freqs - mean_frequency)**2) * positive_magnitude) / np.sum(positive_magnitude)),
            'spectral_centroid': np.sum(positive_freqs * positive_magnitude) / np.sum(positive_magnitude),
            'spectral_rolloff': self._spectral_rolloff(positive_freqs, positive_magnitude),
            'spectral_flux': np.sum(np.diff(positive_magnitude)**2),
            'total_power': np.sum(psd),
            'low_freq_power': np.sum(psd[f_psd <= 50]),
            'mid_freq_power': np.sum(psd[(f_psd > 50) & (f_psd <= 150)]),
            'high_freq_power': np.sum(psd[f_psd > 150])
        }
        return features
    
    def extract_time_frequency_features(self, signal):
        """
        提取时频域特征
        
        参数:
        signal: 输入信号
        
        返回:
        features: 时频域特征字典
        """
        # 短时傅里叶变换
        f, t, Sxx = spectrogram(signal, self.fs, nperseg=min(256, len(signal)//4))
        
        # 小波变换特征
        coeffs = pywt.wavedec(signal, 'db4', level=4)
        
        features = {
            'spectrogram_mean': np.mean(Sxx),
            'spectrogram_std': np.std(Sxx),
            'spectrogram_max': np.max(Sxx),
            'time_bandwidth_product': len(signal) * (self.fs / 2),
            'wavelet_energy_d1': np.sum(coeffs[1]**2),
            'wavelet_energy_d2': np.sum(coeffs[2]**2),
            'wavelet_energy_d3': np.sum(coeffs[3]**2),
            'wavelet_energy_d4': np.sum(coeffs[4]**2),
            'wavelet_energy_a4': np.sum(coeffs[0]**2)
        }
        return features
    
    def extract_all_features(self, signal):
        """
        提取所有特征，包含完整的异常处理

        参数:
        signal: 输入信号

        返回:
        all_features: 所有特征的字典
        """
        try:
            # 输入验证
            if signal is None:
                print("      ⚠️  输入信号为None")
                return {}

            if len(signal) == 0:
                print("      ⚠️  输入信号为空")
                return {}

            # 转换为numpy数组
            signal = np.array(signal)

            # 检查信号有效性
            if np.all(np.isnan(signal)):
                print("      ⚠️  输入信号全为NaN")
                return {}

            if np.all(signal == 0):
                print("      ⚠️  输入信号全为零")
                return {}

            # 处理NaN值
            if np.any(np.isnan(signal)):
                print("      🔧 检测到NaN值，使用插值修复")
                # 使用线性插值填补NaN值
                valid_indices = ~np.isnan(signal)
                if np.sum(valid_indices) > 1:
                    signal = np.interp(np.arange(len(signal)),
                                     np.where(valid_indices)[0],
                                     signal[valid_indices])
                else:
                    print("      ❌ 有效数据点太少，无法插值")
                    return {}

            # 处理无穷大值
            if np.any(np.isinf(signal)):
                print("      🔧 检测到无穷大值，进行截断处理")
                signal = np.clip(signal, -1e10, 1e10)

            print(f"      📊 信号统计: 长度={len(signal)}, 范围=[{signal.min():.4f}, {signal.max():.4f}], 均值={signal.mean():.4f}")

            all_features = {}

            # 时域特征
            try:
                time_features = self.extract_time_domain_features(signal)
                if time_features:
                    all_features.update(time_features)
                    print(f"      ✅ 时域特征提取成功: {len(time_features)} 个")
                else:
                    print(f"      ⚠️  时域特征提取返回空结果")
            except Exception as e:
                print(f"      ❌ 时域特征提取失败: {str(e)}")

            # 频域特征
            try:
                freq_features = self.extract_frequency_domain_features(signal)
                if freq_features:
                    all_features.update(freq_features)
                    print(f"      ✅ 频域特征提取成功: {len(freq_features)} 个")
                else:
                    print(f"      ⚠️  频域特征提取返回空结果")
            except Exception as e:
                print(f"      ❌ 频域特征提取失败: {str(e)}")

            # 时频域特征
            try:
                time_freq_features = self.extract_time_frequency_features(signal)
                if time_freq_features:
                    all_features.update(time_freq_features)
                    print(f"      ✅ 时频域特征提取成功: {len(time_freq_features)} 个")
                else:
                    print(f"      ⚠️  时频域特征提取返回空结果")
            except Exception as e:
                print(f"      ❌ 时频域特征提取失败: {str(e)}")

            if len(all_features) == 0:
                print(f"      ❌ 所有特征提取都失败")
                return {}

            print(f"      ✅ 特征提取完成，总共 {len(all_features)} 个特征")
            return all_features

        except Exception as e:
            print(f"      ❌ 特征提取过程出错: {str(e)}")
            return {}
    
    def _skewness(self, data):
        """计算偏度"""
        mean = np.mean(data)
        std = np.std(data)
        return np.mean(((data - mean) / std) ** 3)
    
    def _kurtosis(self, data):
        """计算峰度"""
        mean = np.mean(data)
        std = np.std(data)
        return np.mean(((data - mean) / std) ** 4) - 3
    
    def _zero_crossing_rate(self, signal):
        """计算过零率"""
        return np.sum(np.diff(np.sign(signal)) != 0) / len(signal)
    
    def _spectral_rolloff(self, freqs, magnitude, rolloff_percent=0.85):
        """计算频谱滚降点"""
        total_energy = np.sum(magnitude)
        cumulative_energy = np.cumsum(magnitude)
        rolloff_index = np.where(cumulative_energy >= rolloff_percent * total_energy)[0]
        if len(rolloff_index) > 0:
            return freqs[rolloff_index[0]]
        else:
            return freqs[-1]

class DataProcessor:
    """数据处理器 - 批量处理CSV文件"""

    def __init__(self, signal_processor, feature_extractor):
        self.signal_processor = signal_processor
        self.feature_extractor = feature_extractor

        # 传感器元数据配置
        self.sensor_metadata = self._initialize_sensor_metadata()

    def _initialize_sensor_metadata(self):
        """初始化传感器元数据，支持多种命名格式"""
        metadata = {}

        # 为每个传感器创建两种格式的元数据
        for i in range(1, 21):
            # 确定传感器组和属性
            if i <= 5:
                group = 1
                depth_cm = 5.0
                lane_type = 'main'
                near_joint = False
            elif i <= 10:
                group = 2
                depth_cm = 5.0
                lane_type = 'overtaking' if i == 6 else 'main'
                near_joint = (i == 6)  # sensor_06跨越切缝
            elif i <= 15:
                group = 3
                depth_cm = 3.5
                lane_type = 'main'
                near_joint = False
            else:  # 16-20
                group = 4
                depth_cm = 3.5
                lane_type = 'overtaking' if i == 16 else 'main'
                near_joint = (i == 16)  # sensor_16跨越切缝

            # 位置估算
            position_x = 0.67 if i <= 2 or (6 <= i <= 7) or (11 <= i <= 12) or (16 <= i <= 17) else 2.5
            position_y = 1.0 if i in [1, 3, 5, 6, 8, 10, 11, 13, 15, 16, 18, 20] else 0.7

            # 传感器属性
            sensor_attrs = {
                'group': group,
                'depth_cm': depth_cm,
                'lane_type': lane_type,
                'position_x': position_x,
                'position_y': position_y,
                'near_joint': near_joint
            }

            # 支持两种命名格式
            sensor_id_old = f"sensor_{i:02d}"  # sensor_01, sensor_02, ...
            sensor_id_new = f"acce{i:02d}"     # acce01, acce02, ...

            metadata[sensor_id_old] = sensor_attrs.copy()
            metadata[sensor_id_new] = sensor_attrs.copy()

        return metadata

    def batch_process_multi_sensor_csv_files(self, input_dir, output_dir=None, file_pattern="*.csv",
                                           time_column='time', processing_mode='all_sensors',
                                           add_condition_label=True):
        """
        批量处理包含20个传感器数据的CSV文件

        参数:
        input_dir: 输入CSV文件所在目录
        output_dir: 处理后数据的保存目录
        file_pattern: 文件匹配模式
        time_column: 时间列名
        processing_mode: 处理模式 ('all_sensors', 'by_group', 'individual')
        add_condition_label: 是否添加工况标签

        返回:
        processed_data: 处理后的数据DataFrame
        """
        import glob

        # 创建输出目录
        if output_dir is None:
            output_dir = os.path.join(input_dir, "processed")

        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 获取所有匹配的CSV文件
        file_paths = glob.glob(os.path.join(input_dir, file_pattern))

        if not file_paths:
            print(f"未在 {input_dir} 中找到匹配 {file_pattern} 的文件")
            return None

        print(f"找到 {len(file_paths)} 个CSV文件")
        print(f"处理模式: {processing_mode}")

        all_processed_data = []

        # 处理每个文件
        for file_path in file_paths:
            file_name = os.path.basename(file_path)
            print(f"处理文件: {file_name}")

            try:
                # 读取CSV文件
                df = pd.read_csv(file_path)

                # 验证数据格式
                if not self._validate_multi_sensor_format(df, time_column):
                    print(f"跳过文件 {file_name}: 数据格式不符合要求")
                    continue

                # 提取工况标签
                condition_label = os.path.splitext(file_name)[0]

                # 根据处理模式处理数据
                if processing_mode == 'all_sensors':
                    processed_data = self._process_all_sensors(df, file_name, condition_label, time_column, add_condition_label)
                elif processing_mode == 'by_group':
                    processed_data = self._process_by_sensor_group(df, file_name, condition_label, time_column, add_condition_label)
                elif processing_mode == 'individual':
                    processed_data = self._process_individual_sensors(df, file_name, condition_label, time_column, add_condition_label)
                else:
                    print(f"未知的处理模式: {processing_mode}")
                    continue

                if processed_data:
                    all_processed_data.extend(processed_data)

                # 保存处理后的原始数据
                output_path = os.path.join(output_dir, f"processed_{file_name}")
                df.to_csv(output_path, index=False)
                print(f"  已保存处理结果到: {output_path}")

            except Exception as e:
                print(f"处理文件 {file_name} 时出错: {str(e)}")

        # 合并所有特征数据
        if all_processed_data:
            combined_features = pd.concat(all_processed_data, ignore_index=True)
            features_output_path = os.path.join(output_dir, "extracted_features.csv")
            combined_features.to_csv(features_output_path, index=False)
            print(f"已将所有提取的特征保存到: {features_output_path}")
            return combined_features
        else:
            print("没有成功处理任何文件")
            return None

    def batch_process_csv_files(self, input_dir, output_dir=None, file_pattern="*.csv",
                               signal_column=None, add_condition_label=True):
        """
        批量处理目录中的CSV文件
        
        参数:
        input_dir: 输入CSV文件所在目录
        output_dir: 处理后数据的保存目录
        file_pattern: 文件匹配模式
        signal_column: 包含振动信号的列名
        add_condition_label: 是否添加工况标签
        
        返回:
        processed_data: 处理后的数据DataFrame
        """
        import glob
        
        # 创建输出目录
        if output_dir is None:
            output_dir = os.path.join(input_dir, "processed")
        
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 获取所有匹配的CSV文件
        file_paths = glob.glob(os.path.join(input_dir, file_pattern))
        
        if not file_paths:
            print(f"未在 {input_dir} 中找到匹配 {file_pattern} 的文件")
            return None
        
        print(f"找到 {len(file_paths)} 个CSV文件")
        
        all_processed_data = []
        
        # 处理每个文件
        for file_path in file_paths:
            file_name = os.path.basename(file_path)
            print(f"处理文件: {file_name}")
            
            try:
                # 读取CSV文件
                df = pd.read_csv(file_path)
                
                # 提取工况标签
                condition_label = os.path.splitext(file_name)[0]
                
                # 确定要处理的列
                if signal_column is not None:
                    if signal_column in df.columns:
                        columns_to_process = [signal_column]
                    else:
                        print(f"警告: 列 '{signal_column}' 不存在于文件 {file_name} 中")
                        continue
                else:
                    # 自动检测数值列
                    columns_to_process = df.select_dtypes(include=[np.number]).columns.tolist()
                
                # 处理每一列
                processed_df = df.copy()
                for col in columns_to_process:
                    print(f"  处理列: {col}")
                    
                    # 应用降噪处理
                    denoised_signal = self.signal_processor.denoise_signal(df[col].values)
                    processed_df[f"{col}_denoised"] = denoised_signal
                    
                    # 异常值检测
                    outlier_mask = self.signal_processor.detect_outliers(denoised_signal)
                    processed_df[f"{col}_outliers"] = outlier_mask
                    
                    # 信号分割和特征提取
                    segments = self.signal_processor.segment_signal(denoised_signal)
                    
                    # 为每个段提取特征
                    segment_features = []
                    for i, segment in enumerate(segments):
                        features = self.feature_extractor.extract_all_features(segment)
                        features['segment_id'] = i
                        features['file_name'] = file_name
                        if add_condition_label:
                            features['condition'] = condition_label
                        segment_features.append(features)
                    
                    # 将特征添加到处理结果中
                    if segment_features:
                        features_df = pd.DataFrame(segment_features)
                        all_processed_data.append(features_df)
                
                # 保存处理后的原始数据
                output_path = os.path.join(output_dir, f"processed_{file_name}")
                processed_df.to_csv(output_path, index=False)
                print(f"  已保存处理结果到: {output_path}")
                
            except Exception as e:
                print(f"处理文件 {file_name} 时出错: {str(e)}")
        
        # 合并所有特征数据
        if all_processed_data:
            combined_features = pd.concat(all_processed_data, ignore_index=True)
            features_output_path = os.path.join(output_dir, "extracted_features.csv")
            combined_features.to_csv(features_output_path, index=False)
            print(f"已将所有提取的特征保存到: {features_output_path}")
            return combined_features
        else:
            print("没有成功处理任何文件")
            return None

    def _validate_multi_sensor_format(self, df, time_column):
        """验证多传感器数据格式"""
        # 检查时间列
        if time_column not in df.columns:
            print(f"错误: 未找到时间列 '{time_column}'")
            return False

        # 检查传感器列
        expected_sensors = [f"sensor_{i:02d}" for i in range(1, 21)]
        missing_sensors = [sensor for sensor in expected_sensors if sensor not in df.columns]

        if missing_sensors:
            print(f"警告: 缺少传感器列: {missing_sensors}")
            # 如果缺少的传感器少于5个，仍然可以处理
            if len(missing_sensors) > 5:
                return False

        return True

    def _process_all_sensors(self, df, file_name, condition_label, time_column, add_condition_label):
        """处理所有传感器数据（融合模式）"""
        processed_data = []

        # 获取所有传感器列
        sensor_columns = [col for col in df.columns if col.startswith('sensor_')]

        if not sensor_columns:
            print(f"  警告: 文件 {file_name} 中未找到传感器列")
            return []

        print(f"  找到 {len(sensor_columns)} 个传感器列")

        # 对每个传感器进行处理
        for sensor_col in sensor_columns:
            sensor_data = df[sensor_col].values

            # 应用信号处理
            denoised_signal = self.signal_processor.denoise_signal(sensor_data)

            # 信号分割
            segments = self.signal_processor.segment_signal(denoised_signal)

            # 获取传感器元数据
            sensor_metadata = self.sensor_metadata.get(sensor_col, {})

            # 为每个段提取特征
            for i, segment in enumerate(segments):
                features = self.feature_extractor.extract_all_features(segment)

                # 添加元数据
                features['segment_id'] = i
                features['file_name'] = file_name
                features['sensor_id'] = sensor_col
                features['sensor_group'] = sensor_metadata.get('group', 0)
                features['depth_cm'] = sensor_metadata.get('depth_cm', 0)
                features['lane_type'] = sensor_metadata.get('lane_type', 'unknown')
                features['near_joint'] = sensor_metadata.get('near_joint', False)

                if add_condition_label:
                    features['condition'] = condition_label

                processed_data.append(pd.DataFrame([features]))

        return processed_data

    def _process_by_sensor_group(self, df, file_name, condition_label, time_column, add_condition_label):
        """按传感器组处理数据"""
        processed_data = []

        # 按组分类传感器
        groups = {1: [], 2: [], 3: [], 4: []}

        for sensor_col in df.columns:
            if sensor_col.startswith('sensor_'):
                sensor_metadata = self.sensor_metadata.get(sensor_col, {})
                group = sensor_metadata.get('group', 0)
                if group in groups:
                    groups[group].append(sensor_col)

        # 处理每个组
        for group_id, sensor_list in groups.items():
            if not sensor_list:
                continue

            print(f"  处理第{group_id}组传感器: {sensor_list}")

            # 对组内传感器进行融合处理
            group_signals = []
            for sensor_col in sensor_list:
                if sensor_col in df.columns:
                    sensor_data = df[sensor_col].values
                    denoised_signal = self.signal_processor.denoise_signal(sensor_data)
                    group_signals.append(denoised_signal)

            if group_signals:
                # 计算组内信号的平均值作为融合信号
                fused_signal = np.mean(group_signals, axis=0)

                # 信号分割
                segments = self.signal_processor.segment_signal(fused_signal)

                # 获取组元数据
                group_metadata = self.sensor_metadata.get(sensor_list[0], {})

                # 为每个段提取特征
                for i, segment in enumerate(segments):
                    features = self.feature_extractor.extract_all_features(segment)

                    # 添加元数据
                    features['segment_id'] = i
                    features['file_name'] = file_name
                    features['sensor_group'] = group_id
                    features['depth_cm'] = group_metadata.get('depth_cm', 0)
                    features['lane_type'] = group_metadata.get('lane_type', 'unknown')
                    features['sensor_count'] = len(sensor_list)

                    if add_condition_label:
                        features['condition'] = condition_label

                    processed_data.append(pd.DataFrame([features]))

        return processed_data

    def _process_individual_sensors(self, df, file_name, condition_label, time_column, add_condition_label):
        """单独处理每个传感器"""
        processed_data = []

        # 获取所有传感器列
        sensor_columns = [col for col in df.columns if col.startswith('sensor_')]

        for sensor_col in sensor_columns:
            print(f"  处理传感器: {sensor_col}")

            sensor_data = df[sensor_col].values

            # 应用信号处理
            denoised_signal = self.signal_processor.denoise_signal(sensor_data)

            # 信号分割
            segments = self.signal_processor.segment_signal(denoised_signal)

            # 获取传感器元数据
            sensor_metadata = self.sensor_metadata.get(sensor_col, {})

            # 为每个段提取特征
            segment_features = []
            for i, segment in enumerate(segments):
                features = self.feature_extractor.extract_all_features(segment)

                # 添加元数据
                features['segment_id'] = i
                features['file_name'] = file_name
                features['sensor_id'] = sensor_col
                features['sensor_group'] = sensor_metadata.get('group', 0)
                features['depth_cm'] = sensor_metadata.get('depth_cm', 0)
                features['lane_type'] = sensor_metadata.get('lane_type', 'unknown')
                features['near_joint'] = sensor_metadata.get('near_joint', False)

                if add_condition_label:
                    features['condition'] = condition_label

                segment_features.append(features)

            if segment_features:
                features_df = pd.DataFrame(segment_features)
                processed_data.append(features_df)

        return processed_data

class ModelTrainer:
    """模型训练器 - 支持多种机器学习算法"""

    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.label_encoders = {}
        self.results = {}

    def prepare_data(self, features_df, target_columns, test_size=0.2, random_state=42):
        """
        准备训练数据

        参数:
        features_df: 特征数据DataFrame
        target_columns: 目标列字典 {'vehicle_type': 'condition', 'speed': 'speed_col', 'weight': 'weight_col'}
        test_size: 测试集比例
        random_state: 随机种子

        返回:
        data_splits: 数据分割结果字典
        """
        # 分离特征和标签
        feature_columns = [col for col in features_df.columns
                          if col not in ['segment_id', 'file_name', 'condition'] + list(target_columns.values())]

        X = features_df[feature_columns]

        # 处理缺失值
        X = X.fillna(X.mean())

        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        X_scaled_df = pd.DataFrame(X_scaled, columns=feature_columns)

        self.scalers['feature_scaler'] = scaler

        data_splits = {}

        # 为每个任务准备数据
        for task_name, target_col in target_columns.items():
            if target_col in features_df.columns:
                y = features_df[target_col]

                # 根据任务类型处理标签
                if task_name == 'vehicle_type':
                    # 分类任务 - 编码标签
                    le = LabelEncoder()
                    y_encoded = le.fit_transform(y)
                    self.label_encoders[task_name] = le
                    y_final = y_encoded
                else:
                    # 回归任务 - 直接使用数值
                    y_final = y

                # 数据分割
                X_train, X_test, y_train, y_test = train_test_split(
                    X_scaled_df, y_final, test_size=test_size, random_state=random_state,
                    stratify=y_encoded if task_name == 'vehicle_type' else None
                )

                data_splits[task_name] = {
                    'X_train': X_train,
                    'X_test': X_test,
                    'y_train': y_train,
                    'y_test': y_test,
                    'feature_names': feature_columns
                }

        return data_splits

    def train_random_forest(self, X_train, y_train, task_type='classification', **kwargs):
        """训练随机森林模型"""
        if task_type == 'classification':
            model = RandomForestClassifier(
                n_estimators=kwargs.get('n_estimators', 100),
                max_depth=kwargs.get('max_depth', None),
                min_samples_split=kwargs.get('min_samples_split', 2),
                min_samples_leaf=kwargs.get('min_samples_leaf', 1),
                random_state=42
            )
        else:
            model = RandomForestRegressor(
                n_estimators=kwargs.get('n_estimators', 100),
                max_depth=kwargs.get('max_depth', None),
                min_samples_split=kwargs.get('min_samples_split', 2),
                min_samples_leaf=kwargs.get('min_samples_leaf', 1),
                random_state=42
            )

        model.fit(X_train, y_train)
        return model

    def train_svm(self, X_train, y_train, task_type='classification', **kwargs):
        """训练支持向量机模型"""
        if task_type == 'classification':
            model = SVC(
                C=kwargs.get('C', 1.0),
                kernel=kwargs.get('kernel', 'rbf'),
                gamma=kwargs.get('gamma', 'scale'),
                random_state=42
            )
        else:
            model = SVR(
                C=kwargs.get('C', 1.0),
                kernel=kwargs.get('kernel', 'rbf'),
                gamma=kwargs.get('gamma', 'scale')
            )

        model.fit(X_train, y_train)
        return model

    def train_xgboost(self, X_train, y_train, task_type='classification', **kwargs):
        """训练XGBoost模型"""
        if task_type == 'classification':
            model = xgb.XGBClassifier(
                n_estimators=kwargs.get('n_estimators', 100),
                max_depth=kwargs.get('max_depth', 6),
                learning_rate=kwargs.get('learning_rate', 0.1),
                random_state=42
            )
        else:
            model = xgb.XGBRegressor(
                n_estimators=kwargs.get('n_estimators', 100),
                max_depth=kwargs.get('max_depth', 6),
                learning_rate=kwargs.get('learning_rate', 0.1),
                random_state=42
            )

        model.fit(X_train, y_train)
        return model

    def train_neural_network(self, X_train, y_train, task_type='classification', **kwargs):
        """训练神经网络模型"""
        if task_type == 'classification':
            model = MLPClassifier(
                hidden_layer_sizes=kwargs.get('hidden_layer_sizes', (100, 50)),
                activation=kwargs.get('activation', 'relu'),
                solver=kwargs.get('solver', 'adam'),
                max_iter=kwargs.get('max_iter', 500),
                random_state=42
            )
        else:
            model = MLPRegressor(
                hidden_layer_sizes=kwargs.get('hidden_layer_sizes', (100, 50)),
                activation=kwargs.get('activation', 'relu'),
                solver=kwargs.get('solver', 'adam'),
                max_iter=kwargs.get('max_iter', 500),
                random_state=42
            )

        model.fit(X_train, y_train)
        return model
