# -*- coding: utf-8 -*-
"""
实验数据处理器
处理基于文件夹结构的实验数据，包括数据合并、车辆通过检测、分段提取和特征提取

作者: AI Assistant
日期: 2024
"""

import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import signal
from vibration_signal_analysis_framework import (VibrationSignalProcessor, 
                                               FeatureExtractor, 
                                               DataProcessor, 
                                               ModelTrainer)

class ExperimentalDataProcessor:
    """实验数据处理器"""
    
    def __init__(self, fs=1000):
        """
        初始化实验数据处理器
        
        参数:
        fs: 采样频率 (Hz)
        """
        self.fs = fs
        self.signal_processor = VibrationSignalProcessor(fs=fs)
        self.feature_extractor = FeatureExtractor(fs=fs)
        self.data_processor = DataProcessor(self.signal_processor, self.feature_extractor)
        
        # 车辆通过检测参数
        self.detection_params = {
            'threshold_factor': 3.0,      # 阈值因子（相对于背景噪声）
            'min_duration': 0.5,          # 最小持续时间（秒）
            'max_duration': 10.0,         # 最大持续时间（秒）
            'buffer_time': 0.2,           # 前后缓冲时间（秒）
            'smoothing_window': 0.1       # 平滑窗口大小（秒）
        }
        
        # 分段参数
        self.segmentation_params = {
            'segment_duration': 1.0,      # 固定段长度（秒）
            'overlap_ratio': 0.05,         # 重叠比例
            'min_segments': 1             # 每个实验最少段数
        }
    
    def scan_nested_directory_structure(self, root_dir):
        """
        扫描嵌套目录结构，自动识别轴重/轴型/速度组合，支持重复实验工况

        参数:
        root_dir: 原始数据根目录

        返回:
        experiment_configs: 实验配置列表
        """
        print(f"扫描目录结构: {root_dir}")
        print("=" * 40)

        experiment_configs = []

        if not os.path.exists(root_dir):
            print(f"❌ 根目录不存在: {root_dir}")
            return experiment_configs

        # 用于跟踪重复实验
        experiment_counter = {}

        # 遍历轴重目录
        for axle_load_dir in os.listdir(root_dir):
            axle_load_path = os.path.join(root_dir, axle_load_dir)

            if not os.path.isdir(axle_load_path):
                continue

            print(f"发现轴重目录: {axle_load_dir}")

            # 遍历轴型目录
            for axle_type_dir in os.listdir(axle_load_path):
                axle_type_path = os.path.join(axle_load_path, axle_type_dir)

                if not os.path.isdir(axle_type_path):
                    continue

                print(f"  发现轴型目录: {axle_type_dir}")

                # 收集所有速度目录，用于处理重复实验
                speed_dirs = []
                for item in os.listdir(axle_type_path):
                    item_path = os.path.join(axle_type_path, item)
                    if os.path.isdir(item_path):
                        # 检查是否包含CSV文件
                        csv_files = [f for f in os.listdir(item_path) if f.endswith('.csv')]
                        if len(csv_files) >= 1:
                            speed_dirs.append((item, item_path, csv_files))

                # 处理重复实验工况
                processed_configs = self._process_duplicate_experiments(
                    speed_dirs, axle_load_dir, axle_type_dir, experiment_counter
                )

                experiment_configs.extend(processed_configs)

        print(f"\n✅ 总共发现 {len(experiment_configs)} 个实验配置")

        # 显示重复实验统计
        duplicate_count = sum(1 for config in experiment_configs if config.get('experiment_repeat', 1) > 1)
        if duplicate_count > 0:
            print(f"📊 其中包含 {duplicate_count} 个重复实验工况")

        return experiment_configs

    def _process_duplicate_experiments(self, speed_dirs, axle_load_dir, axle_type_dir, experiment_counter):
        """
        处理重复实验工况，为每个重复实验生成唯一标识

        参数:
        speed_dirs: 速度目录列表 [(dir_name, dir_path, csv_files), ...]
        axle_load_dir: 轴重目录名
        axle_type_dir: 轴型目录名
        experiment_counter: 实验计数器

        返回:
        configs: 处理后的实验配置列表
        """
        configs = []

        # 按速度分组
        speed_groups = {}
        for speed_dir, speed_path, csv_files in speed_dirs:
            # 解析基础速度（去除可能的重复标识）
            base_speed = self._extract_base_speed(speed_dir)

            if base_speed not in speed_groups:
                speed_groups[base_speed] = []

            speed_groups[base_speed].append((speed_dir, speed_path, csv_files))

        # 处理每个速度组
        for base_speed, speed_experiments in speed_groups.items():
            if len(speed_experiments) == 1:
                # 单个实验，正常处理
                speed_dir, speed_path, csv_files = speed_experiments[0]
                config = self._create_experiment_config(
                    speed_dir, speed_path, csv_files,
                    axle_load_dir, axle_type_dir,
                    experiment_repeat=1, repeat_index=1
                )
                configs.append(config)
                print(f"    发现速度目录: {speed_dir} ({len(csv_files)} 个CSV文件)")

            else:
                # 多个重复实验
                print(f"    发现重复速度实验: {base_speed} ({len(speed_experiments)} 次重复)")

                for repeat_idx, (speed_dir, speed_path, csv_files) in enumerate(speed_experiments, 1):
                    config = self._create_experiment_config(
                        speed_dir, speed_path, csv_files,
                        axle_load_dir, axle_type_dir,
                        experiment_repeat=len(speed_experiments), repeat_index=repeat_idx
                    )
                    configs.append(config)
                    print(f"      重复实验 {repeat_idx}: {speed_dir} ({len(csv_files)} 个CSV文件)")

        return configs

    def _extract_base_speed(self, speed_dir):
        """
        提取基础速度值，去除重复标识

        参数:
        speed_dir: 速度目录名

        返回:
        base_speed: 基础速度值
        """
        import re

        # 提取数字部分作为基础速度
        numbers = re.findall(r'\d+(?:\.\d+)?', speed_dir)

        if numbers:
            return float(numbers[0])
        else:
            # 如果无法提取数字，使用目录名
            return speed_dir

    def _create_experiment_config(self, speed_dir, speed_path, csv_files,
                                 axle_load_dir, axle_type_dir,
                                 experiment_repeat=1, repeat_index=1):
        """
        创建实验配置

        参数:
        speed_dir: 速度目录名
        speed_path: 速度目录路径
        csv_files: CSV文件列表
        axle_load_dir: 轴重目录名
        axle_type_dir: 轴型目录名
        experiment_repeat: 重复实验总数
        repeat_index: 当前重复实验索引

        返回:
        experiment_config: 实验配置字典
        """
        # 解析元数据
        axle_load_tons = self._parse_axle_load(axle_load_dir)
        axle_type = self._parse_axle_type(axle_type_dir)
        speed_kmh = self._parse_speed(speed_dir)

        # 生成实验ID
        if experiment_repeat > 1:
            experiment_id = f"{axle_load_dir}_{axle_type_dir}_{speed_kmh}kmh_实验{repeat_index}"
        else:
            experiment_id = f"{axle_load_dir}_{axle_type_dir}_{speed_dir}"

        # 创建实验配置
        experiment_config = {
            'experiment_path': speed_path,
            'experiment_id': experiment_id,
            'axle_load_tons': axle_load_tons,
            'axle_type': axle_type,
            'speed_kmh': speed_kmh,
            'csv_count': len(csv_files),
            'relative_path': os.path.join(axle_load_dir, axle_type_dir, speed_dir),
            'experiment_repeat': experiment_repeat,
            'repeat_index': repeat_index,
            'original_speed_dir': speed_dir
        }

        return experiment_config

    def _parse_axle_load(self, axle_load_dir):
        """从目录名解析轴重信息"""
        import re

        # 尝试提取数字
        numbers = re.findall(r'\d+(?:\.\d+)?', axle_load_dir)

        if numbers:
            return float(numbers[0])
        else:
            # 如果无法解析，返回目录名
            return axle_load_dir

    def _parse_axle_type(self, axle_type_dir):
        """从目录名解析轴型信息"""
        # 直接返回目录名作为轴型
        return axle_type_dir

    def _parse_speed(self, speed_dir):
        """从目录名解析速度信息"""
        import re

        # 尝试提取数字
        numbers = re.findall(r'\d+(?:\.\d+)?', speed_dir)

        if numbers:
            return float(numbers[0])
        else:
            # 如果无法解析，返回目录名
            return speed_dir

    def create_metadata_template(self, output_path='experiment_metadata.csv'):
        """
        创建元数据模板文件（保留原有功能以兼容旧版本）

        参数:
        output_path: 输出文件路径
        """
        template_data = {
            'experiment_folder': [
                'experiment_001',
                'experiment_002',
                'experiment_003',
                'speed_60_load_15',
                'speed_80_load_25'
            ],
            'vehicle_type': [
                'small_car',
                'medium_car',
                'truck',
                'small_car',
                'truck'
            ],
            'speed_kmh': [50, 70, 45, 60, 80],
            'axle_load_tons': [1.5, 2.2, 25.0, 1.5, 25.0],
            'test_date': [
                '2024-01-15',
                '2024-01-16',
                '2024-01-17',
                '2024-01-18',
                '2024-01-19'
            ],
            'weather_condition': [
                'sunny',
                'cloudy',
                'rainy',
                'sunny',
                'cloudy'
            ],
            'notes': [
                '正常测试',
                '轻微振动',
                '重载测试',
                '标准小车',
                '满载卡车'
            ]
        }

        df = pd.DataFrame(template_data)
        df.to_csv(output_path, index=False, encoding='utf-8-sig')

        print(f"✅ 元数据模板已创建: {output_path}")
        print("请根据您的实际实验情况填写此文件")

        return output_path
    
    def load_metadata(self, metadata_path):
        """
        加载实验元数据
        
        参数:
        metadata_path: 元数据文件路径
        
        返回:
        metadata_dict: 元数据字典
        """
        try:
            df = pd.read_csv(metadata_path, encoding='utf-8-sig')
            
            # 验证必要列
            required_columns = ['experiment_folder', 'vehicle_type', 'speed_kmh', 'axle_load_tons']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                raise ValueError(f"元数据文件缺少必要列: {missing_columns}")
            
            # 转换为字典格式
            metadata_dict = {}
            for _, row in df.iterrows():
                folder_name = row['experiment_folder']
                metadata_dict[folder_name] = {
                    'vehicle_type': row['vehicle_type'],
                    'speed_kmh': row['speed_kmh'],
                    'axle_load_tons': row['axle_load_tons'],
                    'test_date': row.get('test_date', ''),
                    'weather_condition': row.get('weather_condition', ''),
                    'notes': row.get('notes', '')
                }
            
            print(f"✅ 成功加载 {len(metadata_dict)} 个实验的元数据")
            return metadata_dict
            
        except Exception as e:
            print(f"❌ 加载元数据失败: {str(e)}")
            return None
    
    def merge_csv_files(self, folder_path, time_column='time'):
        """
        合并文件夹内的CSV文件，支持count列到time列的自动转换

        参数:
        folder_path: 实验文件夹路径
        time_column: 时间列名（如果不存在会尝试从count列转换）

        返回:
        merged_df: 合并后的数据
        """
        csv_files = [f for f in os.listdir(folder_path) if f.endswith('.csv')]

        if len(csv_files) == 0:
            print(f"警告: 文件夹 {folder_path} 不包含CSV文件")
            return None

        if len(csv_files) != 3:
            print(f"警告: 文件夹 {folder_path} 包含 {len(csv_files)} 个CSV文件，期望3个")

        print(f"  合并文件: {csv_files}")

        merged_data = []

        for csv_file in sorted(csv_files):
            file_path = os.path.join(folder_path, csv_file)
            try:
                df = pd.read_csv(file_path)

                # 处理时间列：如果不存在time列，尝试从count列转换
                df = self._process_time_column(df, time_column, csv_file)

                if df is None:
                    continue

                # 识别传感器列（支持多种命名格式）
                sensor_columns, sensor_format = self._identify_sensor_columns(df)

                if not sensor_columns:
                    print(f"    ❌ 文件 {csv_file} 未找到有效的传感器列")
                    continue

                # 记录传感器格式信息
                if not hasattr(self, 'sensor_format_info'):
                    self.sensor_format_info = {}
                self.sensor_format_info[csv_file] = {
                    'format': sensor_format,
                    'columns': sensor_columns,
                    'count': len(sensor_columns)
                }

                # 检测和处理缺失数据
                df, missing_info = self._handle_missing_sensor_data(df, csv_file, sensor_columns)

                # 记录缺失数据信息
                if not hasattr(self, 'missing_data_summary'):
                    self.missing_data_summary = {}
                self.missing_data_summary[csv_file] = missing_info

                # 添加文件标识
                df['source_file'] = csv_file
                merged_data.append(df)

            except Exception as e:
                print(f"    错误: 读取文件 {csv_file} 失败: {str(e)}")

        if not merged_data:
            return None

        # 合并数据
        merged_df = pd.concat(merged_data, ignore_index=True)

        # 按时间排序
        merged_df = merged_df.sort_values(time_column).reset_index(drop=True)

        print(f"  ✅ 合并完成，数据形状: {merged_df.shape}")

        return merged_df

    def _process_time_column(self, df, time_column, csv_file):
        """
        处理时间列：如果不存在time列，从count列转换

        参数:
        df: 数据DataFrame
        time_column: 期望的时间列名
        csv_file: 文件名（用于日志）

        返回:
        df: 处理后的DataFrame，如果失败返回None
        """
        # 如果已经存在时间列，直接返回
        if time_column in df.columns:
            print(f"    ✅ 文件 {csv_file} 已包含时间列 '{time_column}'")
            return df

        # 尝试识别count列
        count_column = self._identify_count_column(df)

        if count_column is None:
            print(f"    ❌ 文件 {csv_file} 无法识别count列或时间列")
            return None

        print(f"    🔄 文件 {csv_file} 从 '{count_column}' 列转换为时间列")

        try:
            # 将count转换为时间
            df[time_column] = df[count_column] / self.fs

            # 验证转换结果
            if df[time_column].isnull().any():
                print(f"    ⚠️  时间转换后存在空值")

            print(f"    ✅ 时间转换完成: {df[time_column].min():.3f}s - {df[time_column].max():.3f}s")

            return df

        except Exception as e:
            print(f"    ❌ 时间转换失败: {str(e)}")
            return None

    def _identify_count_column(self, df):
        """
        识别count列

        参数:
        df: 数据DataFrame

        返回:
        count_column: count列名，如果未找到返回None
        """
        # 可能的count列名
        possible_count_names = ['count', 'sample', 'index', 'n', 'num', 'number']

        # 首先检查明确的count列名
        for col_name in possible_count_names:
            if col_name.lower() in [col.lower() for col in df.columns]:
                # 找到匹配的列名（保持原始大小写）
                actual_col = next(col for col in df.columns if col.lower() == col_name.lower())
                print(f"    🔍 识别到count列: '{actual_col}'")
                return actual_col

        # 如果没找到明确的count列名，检查第一列是否为整数序列
        first_col = df.columns[0]

        try:
            # 检查第一列是否为数值类型
            if pd.api.types.is_numeric_dtype(df[first_col]):
                # 检查是否为递增序列（允许一定的容错）
                values = df[first_col].values

                # 检查是否大致为递增序列
                if len(values) > 1:
                    diff = np.diff(values)
                    # 如果大部分差值为正且相对稳定，认为是count列
                    if np.mean(diff > 0) > 0.8:  # 80%的差值为正
                        print(f"    🔍 第一列 '{first_col}' 识别为count列（递增数值序列）")
                        return first_col

        except Exception as e:
            print(f"    ⚠️  检查第一列时出错: {str(e)}")

        print(f"    ❌ 无法识别count列，可用列: {list(df.columns)}")
        return None

    def _identify_sensor_columns(self, df):
        """
        识别传感器列，支持多种命名格式

        参数:
        df: 数据DataFrame

        返回:
        sensor_columns: 传感器列名列表
        sensor_format: 传感器命名格式
        """
        all_columns = list(df.columns)

        # 支持的传感器列命名格式
        sensor_patterns = [
            {
                'pattern': 'acce',
                'description': 'acce前缀格式 (acce01, acce02, ...)',
                'regex': r'^acce\d+$'
            },
            {
                'pattern': 'sensor_',
                'description': 'sensor_前缀格式 (sensor_01, sensor_02, ...)',
                'regex': r'^sensor_\d+$'
            }
        ]

        import re

        for pattern_info in sensor_patterns:
            pattern = pattern_info['pattern']
            regex = pattern_info['regex']
            description = pattern_info['description']

            # 查找匹配该模式的列
            matching_columns = [col for col in all_columns if re.match(regex, col)]

            if matching_columns:
                # 按列名排序确保顺序正确
                matching_columns.sort()

                print(f"    🔍 识别到传感器列格式: {description}")
                print(f"    📊 找到 {len(matching_columns)} 个传感器列")

                if len(matching_columns) != 20:
                    print(f"    ⚠️  传感器列数为 {len(matching_columns)}，期望20个")

                # 显示前几个和后几个列名作为示例
                if len(matching_columns) > 6:
                    sample_cols = matching_columns[:3] + ['...'] + matching_columns[-3:]
                else:
                    sample_cols = matching_columns
                print(f"    📝 传感器列示例: {sample_cols}")

                return matching_columns, pattern

        # 如果没有找到匹配的模式
        print(f"    ❌ 未识别到标准传感器列格式")
        print(f"    💡 支持的格式:")
        for pattern_info in sensor_patterns:
            print(f"       - {pattern_info['description']}")
        print(f"    📋 当前列名: {all_columns}")

        return [], 'unknown'

    def _handle_missing_sensor_data(self, df, csv_file, sensor_columns=None):
        """
        处理传感器数据缺失的情况

        参数:
        df: 数据DataFrame
        csv_file: 文件名（用于日志）
        sensor_columns: 传感器列名列表（如果为None则自动识别）

        返回:
        df: 处理后的DataFrame
        missing_info: 缺失数据信息
        """
        print(f"    🔍 检查传感器数据完整性...")

        # 获取传感器列
        if sensor_columns is None:
            sensor_columns, _ = self._identify_sensor_columns(df)

        if not sensor_columns:
            print(f"    ❌ 未找到有效的传感器列")
            return None, {}

        missing_info = {
            'total_sensors': len(sensor_columns),
            'completely_missing': [],
            'partially_missing': {},
            'valid_sensors': [],
            'interpolated_sensors': []
        }

        # 检查每个传感器列的缺失情况
        for sensor_col in sensor_columns:
            sensor_data = df[sensor_col]

            # 检查完全缺失（全为NaN或空字符串）
            if sensor_data.isna().all() or (sensor_data == '').all():
                missing_info['completely_missing'].append(sensor_col)
                print(f"      ⚠️  传感器 {sensor_col} 完全缺失，将从分析中排除")
                continue

            # 检查部分缺失
            missing_count = sensor_data.isna().sum()
            if missing_count > 0:
                missing_ratio = missing_count / len(sensor_data)
                missing_info['partially_missing'][sensor_col] = {
                    'missing_count': int(missing_count),
                    'missing_ratio': float(missing_ratio),
                    'total_points': len(sensor_data)
                }

                print(f"      🔧 传感器 {sensor_col} 部分缺失: {missing_count}/{len(sensor_data)} ({missing_ratio:.1%})")

                # 使用线性插值填补缺失值
                if missing_ratio < 0.5:  # 如果缺失比例小于50%，进行插值
                    df[sensor_col] = sensor_data.interpolate(method='linear', limit_direction='both')

                    # 如果首尾仍有NaN，用前向/后向填充
                    df[sensor_col] = df[sensor_col].fillna(method='ffill').fillna(method='bfill')

                    missing_info['interpolated_sensors'].append(sensor_col)
                    print(f"        ✅ 已使用线性插值填补缺失值")
                else:
                    # 缺失比例过高，标记为完全缺失
                    missing_info['completely_missing'].append(sensor_col)
                    print(f"        ❌ 缺失比例过高({missing_ratio:.1%})，排除该传感器")
                    continue

            # 记录有效传感器
            missing_info['valid_sensors'].append(sensor_col)

        # 统计有效传感器数量
        valid_sensor_count = len(missing_info['valid_sensors'])
        missing_info['valid_sensor_count'] = valid_sensor_count

        print(f"    📊 传感器状态统计:")
        print(f"      总传感器数: {missing_info['total_sensors']}")
        print(f"      有效传感器数: {valid_sensor_count}")
        print(f"      完全缺失: {len(missing_info['completely_missing'])}")
        print(f"      插值修复: {len(missing_info['interpolated_sensors'])}")

        # 检查有效传感器数量
        if valid_sensor_count < 10:
            print(f"      ⚠️  警告: 有效传感器数量({valid_sensor_count})少于推荐值(10)，可能影响检测精度")
        elif valid_sensor_count < 5:
            print(f"      ❌ 错误: 有效传感器数量({valid_sensor_count})过少，无法进行可靠分析")
            return None, missing_info
        else:
            print(f"      ✅ 有效传感器数量充足")

        return df, missing_info
    
    def detect_vehicle_passages(self, merged_df, sensor_columns=None, method='global_max'):
        """
        检测车辆通过的有效数据段

        参数:
        merged_df: 合并后的数据
        sensor_columns: 传感器列名列表
        method: 检测方法 ('global_max', 'threshold', 'energy_peak', 'sliding_max', 'multiple_peaks')

        返回:
        passages: 检测到的车辆通过段列表
        """
        if sensor_columns is None:
            # 自动识别传感器列（支持多种格式）
            sensor_columns, sensor_format = self._identify_sensor_columns(merged_df)
            print(f"  🔍 自动识别传感器格式: {sensor_format}")

        if not sensor_columns:
            print(f"  ❌ 未找到有效的传感器列")
            return []

        print(f"  使用 {len(sensor_columns)} 个传感器进行车辆通过检测")
        print(f"  检测方法: {method}")

        # 计算多传感器融合信号，处理缺失数据
        fusion_signal, valid_sensor_count = self._calculate_fusion_signal(merged_df, sensor_columns)

        if fusion_signal is None:
            print(f"  ❌ 融合信号计算失败")
            return []

        # 记录有效传感器数量
        if not hasattr(self, 'detection_sensor_info'):
            self.detection_sensor_info = {}
        self.detection_sensor_info['valid_sensor_count'] = valid_sensor_count

        # 根据选择的方法进行检测
        if method == 'global_max':
            passages = self._detect_by_global_max(merged_df, fusion_signal)
        elif method == 'threshold':
            passages = self._detect_by_threshold(merged_df, fusion_signal)
        elif method == 'energy_peak':
            passages = self._detect_by_energy_peak(merged_df, fusion_signal)
        elif method == 'sliding_max':
            passages = self._detect_by_sliding_max(merged_df, fusion_signal)
        elif method == 'multiple_peaks':
            passages = self._detect_by_multiple_peaks(merged_df, fusion_signal)
        else:
            print(f"  ❌ 未知的检测方法: {method}")
            return []

        print(f"  ✅ 检测到 {len(passages)} 个有效车辆通过段")

        return passages

    def _calculate_fusion_signal(self, sensor_data, valid_sensor_columns=None):
        """
        计算多传感器融合信号，支持缺失传感器处理

        参数:
        sensor_data: 传感器数据矩阵或DataFrame
        valid_sensor_columns: 有效传感器列名列表

        返回:
        fusion_signal: 融合信号
        valid_sensor_count: 有效传感器数量
        """
        if isinstance(sensor_data, pd.DataFrame):
            # 如果输入是DataFrame，获取传感器列
            if valid_sensor_columns is None:
                # 自动识别传感器列（支持多种格式）
                all_sensor_columns, sensor_format = self._identify_sensor_columns(sensor_data)
                print(f"    🔍 自动识别传感器格式: {sensor_format}")

                # 自动检测有效传感器（非全NaN列）
                valid_sensor_columns = []
                for col in all_sensor_columns:
                    if not sensor_data[col].isna().all():
                        valid_sensor_columns.append(col)
            else:
                # 使用提供的传感器列
                all_sensor_columns = valid_sensor_columns

            # 只使用有效传感器
            if valid_sensor_columns:
                sensor_data_matrix = sensor_data[valid_sensor_columns].values
            else:
                print("    ❌ 没有有效的传感器数据")
                return None, 0
        else:
            # 如果输入是矩阵，直接使用
            sensor_data_matrix = sensor_data
            valid_sensor_columns = [f"sensor_{i:02d}" for i in range(1, sensor_data_matrix.shape[1] + 1)]

        valid_sensor_count = len(valid_sensor_columns)

        print(f"    📊 融合信号计算: 使用 {valid_sensor_count} 个有效传感器")

        # 检查数据中是否还有NaN值
        if np.isnan(sensor_data_matrix).any():
            print(f"    ⚠️  检测到残留的NaN值，使用均值填充")
            # 对每列分别处理NaN
            for i in range(sensor_data_matrix.shape[1]):
                col_data = sensor_data_matrix[:, i]
                if np.isnan(col_data).any():
                    # 使用该列的均值填充NaN
                    col_mean = np.nanmean(col_data)
                    if not np.isnan(col_mean):
                        sensor_data_matrix[:, i] = np.where(np.isnan(col_data), col_mean, col_data)
                    else:
                        # 如果均值也是NaN，用0填充
                        sensor_data_matrix[:, i] = np.where(np.isnan(col_data), 0, col_data)

        # 使用RMS作为默认融合方法
        fusion_signal = np.sqrt(np.mean(sensor_data_matrix**2, axis=1))

        # 可选：使用其他融合方法
        # fusion_signal = np.mean(np.abs(sensor_data_matrix), axis=1)  # 平均绝对值
        # fusion_signal = np.max(np.abs(sensor_data_matrix), axis=1)   # 最大绝对值

        print(f"    ✅ 融合信号范围: {fusion_signal.min():.4f} - {fusion_signal.max():.4f}")

        return fusion_signal, valid_sensor_count

    def _detect_by_global_max(self, merged_df, fusion_signal):
        """
        方法1: 基于全局最大值的检测（您要求的方法）

        参数:
        merged_df: 合并后的数据
        fusion_signal: 融合信号

        返回:
        passages: 检测到的车辆通过段列表
        """
        print(f"    使用全局最大值方法")

        # 找到全局最大值位置
        max_idx = np.argmax(fusion_signal)
        max_value = fusion_signal[max_idx]
        max_time = merged_df.iloc[max_idx]['time']

        print(f"    全局最大值: {max_value:.4f} at 时间 {max_time:.3f}s")

        # 以最大值为中心，前后各截取0.5秒（总共1秒）
        half_duration = 0.5  # 秒
        half_samples = int(half_duration * self.fs)

        start_idx = max(0, max_idx - half_samples)
        end_idx = min(len(fusion_signal), max_idx + half_samples)

        # 确保段长度合理
        actual_duration = (end_idx - start_idx) / self.fs

        if actual_duration < 0.8:  # 如果实际长度小于0.8秒，尝试扩展
            print(f"    ⚠️  实际段长度 {actual_duration:.2f}s 较短，尝试扩展")
            # 尝试扩展到1秒
            target_samples = int(1.0 * self.fs)
            start_idx = max(0, max_idx - target_samples // 2)
            end_idx = min(len(fusion_signal), max_idx + target_samples // 2)
            actual_duration = (end_idx - start_idx) / self.fs

        passages = [{
            'start_idx': start_idx,
            'end_idx': end_idx,
            'duration': actual_duration,
            'max_amplitude': max_value,
            'start_time': merged_df.iloc[start_idx]['time'],
            'end_time': merged_df.iloc[end_idx-1]['time'],
            'method': 'global_max',
            'valid_sensor_count': getattr(self, 'detection_sensor_info', {}).get('valid_sensor_count', 20)
        }]

        print(f"    截取段: {passages[0]['start_time']:.3f}s - {passages[0]['end_time']:.3f}s (长度: {actual_duration:.2f}s)")

        return passages

    def _detect_by_threshold(self, merged_df, fusion_signal):
        """
        方法2: 基于阈值的检测（原有方法，保留兼容性）
        """
        print(f"    使用阈值方法")

        # 信号平滑
        window_size = int(self.detection_params.get('smoothing_window', 0.1) * self.fs)
        if window_size > 1:
            from scipy import signal as scipy_signal
            fusion_signal = scipy_signal.savgol_filter(fusion_signal,
                                                      window_length=min(window_size, len(fusion_signal)//2*2-1),
                                                      polyorder=2)

        # 计算背景噪声水平
        background_level = np.percentile(fusion_signal, 10)
        threshold = background_level * self.detection_params.get('threshold_factor', 3.0)

        print(f"    背景噪声水平: {background_level:.4f}")
        print(f"    检测阈值: {threshold:.4f}")

        # 检测超过阈值的区域
        above_threshold = fusion_signal > threshold

        # 找到连续区域
        passages = []
        in_passage = False
        start_idx = 0

        for i, is_above in enumerate(above_threshold):
            if is_above and not in_passage:
                start_idx = i
                in_passage = True
            elif not is_above and in_passage:
                end_idx = i
                duration = (end_idx - start_idx) / self.fs

                min_duration = self.detection_params.get('min_duration', 0.5)
                max_duration = self.detection_params.get('max_duration', 10.0)

                if min_duration <= duration <= max_duration:
                    buffer_samples = int(self.detection_params.get('buffer_time', 0.2) * self.fs)
                    start_with_buffer = max(0, start_idx - buffer_samples)
                    end_with_buffer = min(len(fusion_signal), end_idx + buffer_samples)

                    passages.append({
                        'start_idx': start_with_buffer,
                        'end_idx': end_with_buffer,
                        'duration': (end_with_buffer - start_with_buffer) / self.fs,
                        'max_amplitude': np.max(fusion_signal[start_idx:end_idx]),
                        'start_time': merged_df.iloc[start_with_buffer]['time'],
                        'end_time': merged_df.iloc[end_with_buffer-1]['time'],
                        'method': 'threshold'
                    })

                in_passage = False

        return passages

    def _detect_by_energy_peak(self, merged_df, fusion_signal):
        """
        方法3: 基于能量峰值的检测
        计算信号能量，找到能量峰值作为车辆通过时刻
        """
        print(f"    使用能量峰值方法")

        # 计算滑动窗口能量
        window_size = int(0.2 * self.fs)  # 0.2秒窗口
        energy_signal = np.zeros(len(fusion_signal))

        for i in range(len(fusion_signal)):
            start = max(0, i - window_size // 2)
            end = min(len(fusion_signal), i + window_size // 2)
            energy_signal[i] = np.sum(fusion_signal[start:end] ** 2)

        # 找到能量峰值
        max_energy_idx = np.argmax(energy_signal)
        max_energy_time = merged_df.iloc[max_energy_idx]['time']

        print(f"    最大能量位置: 时间 {max_energy_time:.3f}s")

        # 以能量峰值为中心截取1.5秒
        half_duration = 0.75  # 秒
        half_samples = int(half_duration * self.fs)

        start_idx = max(0, max_energy_idx - half_samples)
        end_idx = min(len(fusion_signal), max_energy_idx + half_samples)
        actual_duration = (end_idx - start_idx) / self.fs

        passages = [{
            'start_idx': start_idx,
            'end_idx': end_idx,
            'duration': actual_duration,
            'max_amplitude': np.max(fusion_signal[start_idx:end_idx]),
            'start_time': merged_df.iloc[start_idx]['time'],
            'end_time': merged_df.iloc[end_idx-1]['time'],
            'method': 'energy_peak'
        }]

        return passages

    def _detect_by_sliding_max(self, merged_df, fusion_signal):
        """
        方法4: 基于滑动窗口最大值的检测
        在滑动窗口中找到局部最大值，选择全局最优
        """
        print(f"    使用滑动窗口最大值方法")

        # 滑动窗口参数
        window_size = int(1.0 * self.fs)  # 1秒窗口
        step_size = int(0.1 * self.fs)    # 0.1秒步长

        max_values = []
        max_positions = []

        # 滑动窗口计算最大值
        for i in range(0, len(fusion_signal) - window_size, step_size):
            window_signal = fusion_signal[i:i + window_size]
            local_max_idx = np.argmax(window_signal)
            global_max_idx = i + local_max_idx

            max_values.append(fusion_signal[global_max_idx])
            max_positions.append(global_max_idx)

        if not max_values:
            return []

        # 选择全局最大值
        best_idx = np.argmax(max_values)
        best_position = max_positions[best_idx]

        print(f"    最佳位置: 时间 {merged_df.iloc[best_position]['time']:.3f}s")

        # 以最佳位置为中心截取1.2秒
        half_duration = 0.6  # 秒
        half_samples = int(half_duration * self.fs)

        start_idx = max(0, best_position - half_samples)
        end_idx = min(len(fusion_signal), best_position + half_samples)
        actual_duration = (end_idx - start_idx) / self.fs

        passages = [{
            'start_idx': start_idx,
            'end_idx': end_idx,
            'duration': actual_duration,
            'max_amplitude': np.max(fusion_signal[start_idx:end_idx]),
            'start_time': merged_df.iloc[start_idx]['time'],
            'end_time': merged_df.iloc[end_idx-1]['time'],
            'method': 'sliding_max'
        }]

        return passages

    def _detect_by_multiple_peaks(self, merged_df, fusion_signal):
        """
        方法5: 基于多峰值检测
        找到多个峰值，选择最显著的作为车辆通过时刻
        """
        print(f"    使用多峰值检测方法")

        # 信号平滑
        from scipy import signal as scipy_signal
        smoothed_signal = scipy_signal.savgol_filter(fusion_signal,
                                                    window_length=min(51, len(fusion_signal)//4*2-1),
                                                    polyorder=3)

        # 找到峰值
        # 设置峰值检测参数
        height = np.percentile(smoothed_signal, 75)  # 75%分位数作为最小高度
        distance = int(0.5 * self.fs)  # 峰值间最小距离0.5秒

        peaks, properties = scipy_signal.find_peaks(smoothed_signal,
                                                   height=height,
                                                   distance=distance)

        if len(peaks) == 0:
            # 如果没找到峰值，降低要求
            height = np.percentile(smoothed_signal, 60)
            peaks, properties = scipy_signal.find_peaks(smoothed_signal,
                                                       height=height,
                                                       distance=distance//2)

        if len(peaks) == 0:
            # 仍然没找到，使用全局最大值
            peaks = [np.argmax(smoothed_signal)]

        print(f"    找到 {len(peaks)} 个峰值")

        # 选择最高的峰值
        peak_heights = smoothed_signal[peaks]
        best_peak_idx = peaks[np.argmax(peak_heights)]

        print(f"    最佳峰值位置: 时间 {merged_df.iloc[best_peak_idx]['time']:.3f}s")

        # 以最佳峰值为中心截取1.5秒
        half_duration = 0.75  # 秒
        half_samples = int(half_duration * self.fs)

        start_idx = max(0, best_peak_idx - half_samples)
        end_idx = min(len(fusion_signal), best_peak_idx + half_samples)
        actual_duration = (end_idx - start_idx) / self.fs

        passages = [{
            'start_idx': start_idx,
            'end_idx': end_idx,
            'duration': actual_duration,
            'max_amplitude': np.max(fusion_signal[start_idx:end_idx]),
            'start_time': merged_df.iloc[start_idx]['time'],
            'end_time': merged_df.iloc[end_idx-1]['time'],
            'method': 'multiple_peaks',
            'num_peaks': len(peaks)
        }]

        return passages

    def extract_fixed_segments(self, merged_df, passages):
        """
        从有效通过段中提取固定长度的时间段
        
        参数:
        merged_df: 合并后的数据
        passages: 检测到的车辆通过段
        
        返回:
        segments: 提取的固定长度段列表
        """
        segment_duration = self.segmentation_params['segment_duration']
        overlap_ratio = self.segmentation_params['overlap_ratio']
        
        segment_samples = int(segment_duration * self.fs)
        step_samples = int(segment_samples * (1 - overlap_ratio))
        
        segments = []
        
        for passage_idx, passage in enumerate(passages):
            start_idx = passage['start_idx']
            end_idx = passage['end_idx']
            passage_length = end_idx - start_idx
            
            print(f"  处理通过段 {passage_idx+1}: 长度 {passage['duration']:.2f}秒")
            
            # 在通过段内提取固定长度段
            current_start = start_idx
            segment_count = 0
            
            while current_start + segment_samples <= end_idx:
                current_end = current_start + segment_samples
                
                segment_data = merged_df.iloc[current_start:current_end].copy()
                
                segments.append({
                    'data': segment_data,
                    'passage_idx': passage_idx,
                    'segment_idx': segment_count,
                    'start_time': segment_data.iloc[0]['time'],
                    'end_time': segment_data.iloc[-1]['time'],
                    'duration': segment_duration
                })
                
                current_start += step_samples
                segment_count += 1
            
            print(f"    提取了 {segment_count} 个固定长度段")
        
        print(f"  ✅ 总共提取了 {len(segments)} 个固定长度段")

        return segments

    def extract_features_from_segments(self, segments, experiment_metadata):
        """
        从固定长度段中提取特征

        参数:
        segments: 固定长度段列表
        experiment_metadata: 实验元数据

        返回:
        features_df: 特征数据DataFrame
        """
        print(f"  开始从 {len(segments)} 个段中提取特征...")

        all_features = []

        try:
            for segment_idx, segment_info in enumerate(segments):
                try:
                    segment_data = segment_info['data']

                    # 获取传感器列并检查有效性（支持多种格式）
                    all_sensor_columns, sensor_format = self._identify_sensor_columns(segment_data)

                    if not all_sensor_columns:
                        print(f"    ⚠️  段 {segment_idx+1} 中未找到传感器列，跳过该段")
                        continue

                    # 筛选有效传感器（非全NaN列）
                    valid_sensor_columns = []
                    for col in all_sensor_columns:
                        if not segment_data[col].isna().all():
                            valid_sensor_columns.append(col)

                    if not valid_sensor_columns:
                        print(f"    ⚠️  段 {segment_idx+1} 中所有传感器列都为空，跳过该段")
                        continue

                    valid_sensor_count = len(valid_sensor_columns)
                    print(f"    📊 段 {segment_idx+1}: 使用 {valid_sensor_count}/{len(all_sensor_columns)} 个有效传感器")

                    # 为每个有效传感器提取特征
                    for sensor_col in valid_sensor_columns:
                        try:
                            sensor_signal = segment_data[sensor_col].values

                            # 检查信号有效性
                            if len(sensor_signal) == 0:
                                print(f"      ⚠️  传感器 {sensor_col} 信号为空，跳过")
                                continue

                            if np.all(np.isnan(sensor_signal)):
                                print(f"      ⚠️  传感器 {sensor_col} 信号全为NaN，跳过")
                                continue

                            # 应用信号处理
                            denoised_signal = self.signal_processor.denoise_signal(sensor_signal)

                            # 提取特征
                            features = self.feature_extractor.extract_all_features(denoised_signal)

                            # 验证特征提取结果
                            if not features or not isinstance(features, dict):
                                print(f"      ⚠️  传感器 {sensor_col} 特征提取失败，跳过")
                                continue

                            # 添加段信息元数据
                            features.update({
                                'passage_idx': segment_info.get('passage_idx', segment_idx),
                                'segment_idx': segment_info.get('segment_idx', segment_idx),
                                'sensor_id': sensor_col,
                                'start_time': segment_info.get('start_time', 0.0),
                                'end_time': segment_info.get('end_time', 0.0),
                                'duration': segment_info.get('duration', 0.0)
                            })

                            # 添加实验元数据（包括重复实验信息）
                            features.update(experiment_metadata)

                            # 添加传感器元数据
                            sensor_metadata = self.data_processor.sensor_metadata.get(sensor_col, {})
                            features.update({
                                'sensor_group': sensor_metadata.get('group', 0),
                                'depth_cm': sensor_metadata.get('depth_cm', 0),
                                'lane_type': sensor_metadata.get('lane_type', 'unknown'),
                                'near_joint': sensor_metadata.get('near_joint', False),
                                'valid_sensor_count': valid_sensor_count  # 添加有效传感器数量
                            })

                            all_features.append(features)

                        except Exception as e:
                            print(f"      ❌ 传感器 {sensor_col} 特征提取出错: {str(e)}")
                            continue

                except Exception as e:
                    print(f"    ❌ 段 {segment_idx+1} 处理出错: {str(e)}")
                    continue

            # 检查是否有成功提取的特征
            if not all_features:
                print(f"  ⚠️  所有段的特征提取都失败，返回空DataFrame")
                # 创建空的DataFrame但包含必要的列
                empty_features = pd.DataFrame(columns=[
                    'passage_idx', 'segment_idx', 'sensor_id', 'start_time', 'end_time', 'duration',
                    'valid_sensor_count', 'experiment_id'
                ])
                return empty_features

            # 转换为DataFrame
            features_df = pd.DataFrame(all_features)

            print(f"  ✅ 特征提取完成，数据形状: {features_df.shape}")
            print(f"  📊 成功提取 {len(all_features)} 个特征样本")

            return features_df

        except Exception as e:
            print(f"  ❌ 特征提取过程出错: {str(e)}")
            # 返回空DataFrame而不是None
            empty_features = pd.DataFrame(columns=[
                'passage_idx', 'segment_idx', 'sensor_id', 'start_time', 'end_time', 'duration',
                'valid_sensor_count', 'experiment_id'
            ])
            return empty_features

    def process_single_nested_experiment(self, experiment_path, experiment_metadata, output_dir=None):
        """
        处理单个嵌套实验（轴重/轴型/速度目录下的CSV文件）

        参数:
        experiment_path: 实验路径（包含CSV文件的目录）
        experiment_metadata: 实验元数据
        output_dir: 输出目录

        返回:
        features_df: 提取的特征数据
        """
        experiment_id = experiment_metadata['experiment_id']
        print(f"\n处理嵌套实验: {experiment_id}")
        print("-" * 40)

        print(f"实验信息: 轴重{experiment_metadata['axle_load_tons']}吨, "
              f"轴型{experiment_metadata['axle_type']}, "
              f"速度{experiment_metadata['speed_kmh']}km/h")

        # 1. 合并CSV文件
        print("\n步骤1: 合并CSV文件")
        merged_df = self.merge_csv_files(experiment_path)

        if merged_df is None:
            print("❌ 文件合并失败")
            return None

        # 2. 检测车辆通过段
        print("\n步骤2: 检测车辆通过段")
        # 使用全局最大值方法（默认）
        detection_method = experiment_metadata.get('detection_method', 'global_max')
        passages = self.detect_vehicle_passages(merged_df, method=detection_method)

        if not passages:
            print("❌ 未检测到有效车辆通过段")
            return None

        # 3. 提取固定长度段
        print("\n步骤3: 提取固定长度段")
        segments = self.extract_fixed_segments(merged_df, passages)

        if len(segments) < self.segmentation_params['min_segments']:
            print(f"❌ 提取的段数 ({len(segments)}) 少于最小要求 ({self.segmentation_params['min_segments']})")
            return None

        # 4. 特征提取
        print("\n步骤4: 特征提取")
        try:
            features_df = self.extract_features_from_segments(segments, experiment_metadata)

            # 检查特征提取结果
            if features_df is None or features_df.empty:
                print("❌ 特征提取失败或结果为空")
                return None

            print(f"✅ 特征提取成功，共 {len(features_df)} 个特征样本")

        except Exception as e:
            print(f"❌ 特征提取过程出错: {str(e)}")
            return None

        # 5. 保存结果
        if output_dir:
            try:
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e:
                print(f"⚠️  创建输出目录失败: {str(e)}")
                # 继续处理，不保存文件

            # 保存合并后的原始数据
            merged_data_path = os.path.join(output_dir, f"{experiment_id}_merged_data.csv")
            merged_df.to_csv(merged_data_path, index=False)

            # 保存检测结果
            detection_results = {
                'experiment_id': experiment_id,
                'experiment_metadata': experiment_metadata,
                'detection_method': 'global_max',  # 记录使用的检测方法
                'total_passages': len(passages),
                'total_segments': len(segments),
                'passages': passages,
                'missing_data_summary': getattr(self, 'missing_data_summary', {}),  # 添加缺失数据信息
                'sensor_info': getattr(self, 'detection_sensor_info', {})
            }

            detection_path = os.path.join(output_dir, f"{experiment_id}_detection_results.json")
            with open(detection_path, 'w', encoding='utf-8') as f:
                json.dump(detection_results, f, indent=2, ensure_ascii=False, default=str)

            # 保存特征数据
            features_path = os.path.join(output_dir, f"{experiment_id}_features.csv")
            features_df.to_csv(features_path, index=False)

            print(f"  ✅ 结果已保存到: {output_dir}")

        return features_df

    def process_single_experiment(self, experiment_folder, metadata_dict, output_dir=None):
        """
        处理单个实验文件夹

        参数:
        experiment_folder: 实验文件夹路径
        metadata_dict: 元数据字典
        output_dir: 输出目录

        返回:
        features_df: 提取的特征数据
        """
        folder_name = os.path.basename(experiment_folder)
        print(f"\n处理实验: {folder_name}")
        print("-" * 40)

        # 获取实验元数据
        if folder_name not in metadata_dict:
            print(f"❌ 未找到实验 {folder_name} 的元数据")
            return None

        experiment_metadata = metadata_dict[folder_name].copy()
        experiment_metadata['experiment_folder'] = folder_name

        print(f"实验信息: {experiment_metadata['vehicle_type']}, "
              f"{experiment_metadata['speed_kmh']}km/h, "
              f"{experiment_metadata['axle_load_tons']}吨")

        # 1. 合并CSV文件
        print("\n步骤1: 合并CSV文件")
        merged_df = self.merge_csv_files(experiment_folder)

        if merged_df is None:
            print("❌ 文件合并失败")
            return None

        # 2. 检测车辆通过段
        print("\n步骤2: 检测车辆通过段")
        # 强制使用全局最大值方法
        detection_method = 'global_max'
        print(f"  使用检测方法: {detection_method}")
        passages = self.detect_vehicle_passages(merged_df, method=detection_method)

        if not passages:
            print("❌ 未检测到有效车辆通过段")
            return None

        # 3. 提取固定长度段
        print("\n步骤3: 提取固定长度段")
        segments = self.extract_fixed_segments(merged_df, passages)

        if len(segments) < self.segmentation_params['min_segments']:
            print(f"❌ 提取的段数 ({len(segments)}) 少于最小要求 ({self.segmentation_params['min_segments']})")
            return None

        # 4. 特征提取
        print("\n步骤4: 特征提取")
        try:
            features_df = self.extract_features_from_segments(segments, experiment_metadata)

            # 检查特征提取结果
            if features_df is None or features_df.empty:
                print("❌ 特征提取失败或结果为空")
                return None

            print(f"✅ 特征提取成功，共 {len(features_df)} 个特征样本")

        except Exception as e:
            print(f"❌ 特征提取过程出错: {str(e)}")
            return None

        # 5. 保存结果
        if output_dir:
            try:
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e:
                print(f"⚠️  创建输出目录失败: {str(e)}")
                # 继续处理，不保存文件

            # 保存合并后的原始数据
            merged_data_path = os.path.join(output_dir, f"{folder_name}_merged_data.csv")
            merged_df.to_csv(merged_data_path, index=False)

            # 保存检测结果
            detection_results = {
                'experiment': folder_name,
                'detection_method': 'global_max',  # 记录使用的检测方法
                'total_passages': len(passages),
                'total_segments': len(segments),
                'passages': passages
            }

            detection_path = os.path.join(output_dir, f"{folder_name}_detection_results.json")
            with open(detection_path, 'w', encoding='utf-8') as f:
                json.dump(detection_results, f, indent=2, ensure_ascii=False, default=str)

            # 保存特征数据
            features_path = os.path.join(output_dir, f"{folder_name}_features.csv")
            features_df.to_csv(features_path, index=False)

            print(f"  ✅ 结果已保存到: {output_dir}")

        return features_df

    def process_nested_experiments(self, root_dir, output_dir='./nested_experiment_results'):
        """
        处理嵌套目录结构的实验数据（轴重/轴型/速度/CSV文件）

        参数:
        root_dir: 原始数据根目录
        output_dir: 输出目录

        返回:
        combined_features_df: 合并的特征数据
        """
        print("开始批量处理嵌套目录结构的实验数据")
        print("=" * 60)
        print("🎯 车辆通过检测方法: global_max (全局最大值方法)")
        print("   - 自动找到信号最强时刻")
        print("   - 以最大值为中心截取1秒数据段")
        print("   - 无需参数调整，完全自动化")
        print("-" * 60)

        # 扫描目录结构
        experiment_configs = self.scan_nested_directory_structure(root_dir)

        if not experiment_configs:
            print("❌ 未找到任何实验配置")
            return None

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        all_features = []
        processing_summary = {
            'total_experiments': len(experiment_configs),
            'successful_experiments': 0,
            'failed_experiments': [],
            'total_segments': 0,
            'axle_loads': set(),
            'axle_types': set(),
            'speeds': set(),
            'processing_params': {
                'detection_params': self.detection_params,
                'segmentation_params': self.segmentation_params
            }
        }

        # 处理每个实验配置
        for config in experiment_configs:
            experiment_id = config['experiment_id']
            experiment_path = config['experiment_path']

            print(f"\n处理实验: {experiment_id}")
            print(f"路径: {config['relative_path']}")
            print(f"轴重: {config['axle_load_tons']}吨, 轴型: {config['axle_type']}, 速度: {config['speed_kmh']}km/h")

            try:
                # 创建实验元数据（默认使用global_max检测方法）
                experiment_metadata = {
                    'experiment_id': experiment_id,
                    'axle_load_tons': config['axle_load_tons'],
                    'axle_type': config['axle_type'],
                    'speed_kmh': config['speed_kmh'],
                    'relative_path': config['relative_path'],
                    'csv_count': config['csv_count'],
                    'detection_method': 'global_max'  # 默认使用全局最大值方法
                }

                # 添加重复实验信息（如果存在）
                if 'experiment_repeat' in config:
                    experiment_metadata.update({
                        'experiment_repeat': config['experiment_repeat'],
                        'repeat_index': config['repeat_index'],
                        'original_speed_dir': config['original_speed_dir']
                    })

                print(f"  📂 CSV文件数量: {config['csv_count']}")
                print(f"  🔍 检测方法: global_max")

                # 处理单个实验
                features_df = self.process_single_nested_experiment(
                    experiment_path,
                    experiment_metadata,
                    os.path.join(output_dir, experiment_id)
                )

                if features_df is not None and not features_df.empty:
                    all_features.append(features_df)
                    processing_summary['successful_experiments'] += 1
                    processing_summary['total_segments'] += len(features_df)
                    processing_summary['axle_loads'].add(config['axle_load_tons'])
                    processing_summary['axle_types'].add(config['axle_type'])
                    processing_summary['speeds'].add(config['speed_kmh'])
                    print(f"✅ 实验 {experiment_id} 处理成功 (特征样本: {len(features_df)})")
                else:
                    processing_summary['failed_experiments'].append({
                        'experiment_id': experiment_id,
                        'error_type': 'empty_result',
                        'error_message': '特征提取结果为空'
                    })
                    print(f"❌ 实验 {experiment_id} 处理失败: 特征提取结果为空")

            except Exception as e:
                import traceback
                error_details = {
                    'experiment_id': experiment_id,
                    'error_type': type(e).__name__,
                    'error_message': str(e),
                    'traceback': traceback.format_exc()
                }
                processing_summary['failed_experiments'].append(error_details)

                print(f"❌ 实验 {experiment_id} 处理出错: {str(e)}")
                print(f"   错误类型: {type(e).__name__}")

                # 如果是关键错误，提供更多信息
                if "cannot access local variable" in str(e):
                    print(f"   💡 这是一个变量作用域错误，通常由特征提取过程中的异常导致")
                elif "KeyError" in str(e):
                    print(f"   💡 这是一个键值错误，可能是数据格式或列名问题")
                elif "FileNotFoundError" in str(e):
                    print(f"   💡 这是一个文件未找到错误，请检查数据路径")

                # 继续处理下一个实验，不中断整个流程
                print(f"   ⏭️  跳过该实验，继续处理下一个...")

        # 转换集合为列表以便JSON序列化
        processing_summary['axle_loads'] = sorted(list(processing_summary['axle_loads']))
        processing_summary['axle_types'] = sorted(list(processing_summary['axle_types']))
        processing_summary['speeds'] = sorted(list(processing_summary['speeds']))

        # 处理结果总结
        print(f"\n" + "=" * 60)
        print(f"批量处理完成总结:")
        print(f"=" * 60)
        print(f"总实验数: {processing_summary['total_experiments']}")
        print(f"成功处理: {processing_summary['successful_experiments']}")
        print(f"失败数量: {len(processing_summary['failed_experiments'])}")

        if processing_summary['failed_experiments']:
            print(f"\n失败实验详情:")
            for i, failed_exp in enumerate(processing_summary['failed_experiments'], 1):
                if isinstance(failed_exp, dict):
                    print(f"  {i}. {failed_exp['experiment_id']}: {failed_exp['error_message']}")
                else:
                    print(f"  {i}. {failed_exp}")

        # 合并所有特征数据
        if all_features:
            try:
                combined_features_df = pd.concat(all_features, ignore_index=True)

                # 保存合并的特征数据
                combined_features_path = os.path.join(output_dir, 'combined_features.csv')
                combined_features_df.to_csv(combined_features_path, index=False)

                print(f"\n✅ 特征数据已合并保存到: {combined_features_path}")
                print(f"合并数据形状: {combined_features_df.shape}")
                print(f"包含实验: {combined_features_df['experiment_id'].nunique()} 个")

                # 显示数据统计
                self._display_nested_processing_summary(combined_features_df, processing_summary)

                # 保存处理摘要
                summary_path = os.path.join(output_dir, 'processing_summary.json')
                with open(summary_path, 'w', encoding='utf-8') as f:
                    json.dump(processing_summary, f, indent=2, ensure_ascii=False, default=str)

                print(f"✅ 处理摘要已保存到: {summary_path}")

                # 创建机器学习数据集
                try:
                    dataset_info = self.create_training_dataset(
                        combined_features_df,
                        output_dir=os.path.join(output_dir, 'training_datasets')
                    )
                    print(f"✅ 机器学习数据集已生成")
                except Exception as e:
                    print(f"⚠️  机器学习数据集生成失败: {str(e)}")

                return combined_features_df

            except Exception as e:
                print(f"❌ 特征数据合并失败: {str(e)}")

                # 即使合并失败，也保存处理摘要
                summary_path = os.path.join(output_dir, 'processing_summary.json')
                with open(summary_path, 'w', encoding='utf-8') as f:
                    json.dump(processing_summary, f, indent=2, ensure_ascii=False, default=str)

                return None
        else:
            print("❌ 没有成功处理任何实验")
            print("💡 可能的原因:")
            print("   - 数据格式不正确（检查传感器列命名）")
            print("   - 采样频率设置错误")
            print("   - CSV文件损坏或为空")
            print("   - 目录结构不符合要求")

            # 保存失败摘要
            summary_path = os.path.join(output_dir, 'processing_summary.json')
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(processing_summary, f, indent=2, ensure_ascii=False, default=str)

            print(f"📄 失败详情已保存到: {summary_path}")

            return None

    def process_all_experiments(self, experiments_root_dir, metadata_path, output_dir='./experiment_results'):
        """
        处理所有实验文件夹（保留原有功能以兼容旧版本）

        参数:
        experiments_root_dir: 实验根目录
        metadata_path: 元数据文件路径
        output_dir: 输出目录

        返回:
        combined_features_df: 合并的特征数据
        """
        print("开始批量处理实验数据")
        print("=" * 60)

        # 加载元数据
        metadata_dict = self.load_metadata(metadata_path)
        if metadata_dict is None:
            return None

        # 获取实验文件夹列表
        experiment_folders = [d for d in os.listdir(experiments_root_dir)
                            if os.path.isdir(os.path.join(experiments_root_dir, d))]

        print(f"找到 {len(experiment_folders)} 个实验文件夹")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        all_features = []
        processing_summary = {
            'total_experiments': len(experiment_folders),
            'successful_experiments': 0,
            'failed_experiments': [],
            'total_segments': 0,
            'processing_params': {
                'detection_params': self.detection_params,
                'segmentation_params': self.segmentation_params
            }
        }

        # 处理每个实验
        for folder_name in experiment_folders:
            experiment_folder = os.path.join(experiments_root_dir, folder_name)

            try:
                features_df = self.process_single_experiment(
                    experiment_folder,
                    metadata_dict,
                    os.path.join(output_dir, folder_name)
                )

                if features_df is not None:
                    all_features.append(features_df)
                    processing_summary['successful_experiments'] += 1
                    processing_summary['total_segments'] += len(features_df)
                    print(f"✅ 实验 {folder_name} 处理成功")
                else:
                    processing_summary['failed_experiments'].append(folder_name)
                    print(f"❌ 实验 {folder_name} 处理失败")

            except Exception as e:
                processing_summary['failed_experiments'].append(folder_name)
                print(f"❌ 实验 {folder_name} 处理出错: {str(e)}")

        # 合并所有特征数据
        if all_features:
            combined_features_df = pd.concat(all_features, ignore_index=True)

            # 保存合并的特征数据
            combined_features_path = os.path.join(output_dir, 'combined_features.csv')
            combined_features_df.to_csv(combined_features_path, index=False)

            print(f"\n✅ 所有特征数据已合并保存到: {combined_features_path}")
            print(f"合并数据形状: {combined_features_df.shape}")

            # 显示数据统计
            self._display_processing_summary(combined_features_df, processing_summary)

            # 保存处理摘要
            summary_path = os.path.join(output_dir, 'processing_summary.json')
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(processing_summary, f, indent=2, ensure_ascii=False, default=str)

            return combined_features_df
        else:
            print("❌ 没有成功处理任何实验")
            return None

    def _display_processing_summary(self, combined_features_df, processing_summary):
        """显示处理摘要统计"""
        print("\n处理摘要统计:")
        print("=" * 40)

        print(f"成功处理实验: {processing_summary['successful_experiments']}/{processing_summary['total_experiments']}")
        print(f"总提取段数: {processing_summary['total_segments']}")

        if processing_summary['failed_experiments']:
            print(f"失败实验: {processing_summary['failed_experiments']}")

        # 按车辆类型统计
        if 'vehicle_type' in combined_features_df.columns:
            print("\n按车辆类型统计:")
            vehicle_stats = combined_features_df.groupby('vehicle_type').agg({
                'experiment_folder': 'nunique',
                'passage_idx': 'count'
            }).rename(columns={'experiment_folder': '实验数', 'passage_idx': '段数'})
            print(vehicle_stats.to_string())

        # 按速度统计
        if 'speed_kmh' in combined_features_df.columns:
            print("\n按速度统计:")
            speed_stats = combined_features_df.groupby('speed_kmh').agg({
                'experiment_folder': 'nunique',
                'passage_idx': 'count'
            }).rename(columns={'experiment_folder': '实验数', 'passage_idx': '段数'})
            print(speed_stats.to_string())

        # 按载重统计
        if 'axle_load_tons' in combined_features_df.columns:
            print("\n按载重统计:")
            load_stats = combined_features_df.groupby('axle_load_tons').agg({
                'experiment_folder': 'nunique',
                'passage_idx': 'count'
            }).rename(columns={'experiment_folder': '实验数', 'passage_idx': '段数'})
            print(load_stats.to_string())

    def _display_nested_processing_summary(self, combined_features_df, processing_summary):
        """显示嵌套处理摘要统计"""
        print("\n嵌套实验处理摘要统计:")
        print("=" * 40)

        print(f"成功处理实验: {processing_summary['successful_experiments']}/{processing_summary['total_experiments']}")
        print(f"总提取段数: {processing_summary['total_segments']}")

        if processing_summary['failed_experiments']:
            print(f"失败实验: {processing_summary['failed_experiments']}")

        # 重复实验统计
        if 'experiment_repeat' in combined_features_df.columns:
            repeat_stats = combined_features_df.groupby('experiment_repeat').agg({
                'experiment_id': 'nunique'
            }).rename(columns={'experiment_id': '实验数'})
            print(f"\n重复实验统计:")
            for repeat_count, stats in repeat_stats.iterrows():
                if repeat_count > 1:
                    print(f"  {repeat_count}次重复: {stats['实验数']} 个工况")
                else:
                    print(f"  单次实验: {stats['实验数']} 个工况")

        # 按轴重统计
        if 'axle_load_tons' in combined_features_df.columns:
            print("\n按轴重统计:")
            axle_load_stats = combined_features_df.groupby('axle_load_tons').agg({
                'experiment_id': 'nunique',
                'passage_idx': 'count'
            }).rename(columns={'experiment_id': '实验数', 'passage_idx': '段数'})
            print(axle_load_stats.to_string())

        # 按轴型统计
        if 'axle_type' in combined_features_df.columns:
            print("\n按轴型统计:")
            axle_type_stats = combined_features_df.groupby('axle_type').agg({
                'experiment_id': 'nunique',
                'passage_idx': 'count'
            }).rename(columns={'experiment_id': '实验数', 'passage_idx': '段数'})
            print(axle_type_stats.to_string())

        # 按速度统计
        if 'speed_kmh' in combined_features_df.columns:
            print("\n按速度统计:")
            speed_stats = combined_features_df.groupby('speed_kmh').agg({
                'experiment_id': 'nunique',
                'passage_idx': 'count'
            }).rename(columns={'experiment_id': '实验数', 'passage_idx': '段数'})
            print(speed_stats.to_string())

        # 按轴重和轴型组合统计
        if 'axle_load_tons' in combined_features_df.columns and 'axle_type' in combined_features_df.columns:
            print("\n按轴重-轴型组合统计:")
            combo_stats = combined_features_df.groupby(['axle_load_tons', 'axle_type']).agg({
                'experiment_id': 'nunique',
                'passage_idx': 'count'
            }).rename(columns={'experiment_id': '实验数', 'passage_idx': '段数'})
            print(combo_stats.to_string())

        # 重复实验详细统计
        if 'experiment_repeat' in combined_features_df.columns and 'repeat_index' in combined_features_df.columns:
            duplicate_experiments = combined_features_df[combined_features_df['experiment_repeat'] > 1]
            if not duplicate_experiments.empty:
                print(f"\n重复实验详细信息:")
                duplicate_summary = duplicate_experiments.groupby(['axle_load_tons', 'axle_type', 'speed_kmh']).agg({
                    'experiment_repeat': 'first',
                    'experiment_id': 'nunique',
                    'passage_idx': 'count'
                }).rename(columns={'experiment_repeat': '重复次数', 'experiment_id': '实验数', 'passage_idx': '段数'})
                print(duplicate_summary.to_string())

        # 传感器格式和数据质量统计
        if hasattr(self, 'sensor_format_info'):
            print(f"\n传感器列格式统计:")
            format_counts = {}
            for file_info in self.sensor_format_info.values():
                format_type = file_info['format']
                format_counts[format_type] = format_counts.get(format_type, 0) + 1

            for format_type, count in format_counts.items():
                if format_type == 'acce':
                    print(f"  acce格式 (acce01, acce02, ...): {count} 个文件")
                elif format_type == 'sensor_':
                    print(f"  sensor_格式 (sensor_01, sensor_02, ...): {count} 个文件")
                else:
                    print(f"  {format_type}格式: {count} 个文件")

        if 'valid_sensor_count' in combined_features_df.columns:
            print(f"\n传感器数据质量统计:")
            sensor_count_stats = combined_features_df['valid_sensor_count'].describe()
            print(f"有效传感器数量统计:")
            print(f"  平均值: {sensor_count_stats['mean']:.1f}")
            print(f"  最小值: {sensor_count_stats['min']:.0f}")
            print(f"  最大值: {sensor_count_stats['max']:.0f}")

            # 按有效传感器数量分组统计
            sensor_distribution = combined_features_df['valid_sensor_count'].value_counts().sort_index()
            print(f"\n有效传感器数量分布:")
            for count, samples in sensor_distribution.items():
                print(f"  {count:2.0f}个传感器: {samples:4d} 个样本")

            # 警告低质量数据
            low_quality_samples = combined_features_df[combined_features_df['valid_sensor_count'] < 15]
            if not low_quality_samples.empty:
                print(f"\n⚠️  传感器数据质量警告:")
                print(f"  有 {len(low_quality_samples)} 个样本的有效传感器数量少于15个")

                low_quality_experiments = low_quality_samples['experiment_id'].unique()
                print(f"  涉及实验: {list(low_quality_experiments)}")
            else:
                print(f"\n✅ 所有样本的传感器数据质量良好")

    def visualize_detection_results(self, experiment_folder, metadata_dict, output_dir=None):
        """
        可视化车辆通过检测结果

        参数:
        experiment_folder: 实验文件夹路径
        metadata_dict: 元数据字典
        output_dir: 输出目录
        """
        folder_name = os.path.basename(experiment_folder)

        # 合并数据
        merged_df = self.merge_csv_files(experiment_folder)
        if merged_df is None:
            return

        # 检测车辆通过段
        passages = self.detect_vehicle_passages(merged_df)

        # 计算融合信号
        sensor_columns = [col for col in merged_df.columns if col.startswith('sensor_')]
        sensor_data = merged_df[sensor_columns].values
        fusion_signal = np.sqrt(np.mean(sensor_data**2, axis=1))

        # 创建图形
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))

        time_data = merged_df['time'].values

        # 子图1: 融合信号和检测结果
        axes[0].plot(time_data, fusion_signal, 'b-', linewidth=1, label='融合信号')

        # 标记检测到的通过段
        for i, passage in enumerate(passages):
            start_time = merged_df.iloc[passage['start_idx']]['time']
            end_time = merged_df.iloc[passage['end_idx']]['time']
            axes[0].axvspan(start_time, end_time, alpha=0.3, color='red',
                          label='检测到的通过段' if i == 0 else '')

        axes[0].set_ylabel('振幅')
        axes[0].set_title(f'车辆通过检测结果 - {folder_name}')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)

        # 子图2: 多传感器信号
        for i, sensor_col in enumerate(sensor_columns[:5]):  # 只显示前5个传感器
            axes[1].plot(time_data, merged_df[sensor_col],
                        linewidth=0.8, label=sensor_col, alpha=0.7)

        axes[1].set_ylabel('加速度')
        axes[1].set_title('多传感器信号')
        axes[1].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        axes[1].grid(True, alpha=0.3)

        # 子图3: 频谱分析
        if len(fusion_signal) > 1024:
            f, Pxx = signal.welch(fusion_signal, self.fs, nperseg=1024)
            axes[2].semilogy(f, Pxx)
            axes[2].set_xlabel('频率 (Hz)')
            axes[2].set_ylabel('功率谱密度')
            axes[2].set_title('融合信号频谱')
            axes[2].grid(True, alpha=0.3)
            axes[2].set_xlim(0, 100)  # 只显示0-100Hz

        plt.tight_layout()

        # 保存图形
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            plot_path = os.path.join(output_dir, f'{folder_name}_detection_visualization.png')
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            print(f"  可视化结果已保存到: {plot_path}")

        plt.show()

    def create_training_dataset(self, combined_features_df, output_dir='./training_dataset'):
        """
        创建机器学习训练数据集

        参数:
        combined_features_df: 合并的特征数据
        output_dir: 输出目录

        返回:
        dataset_info: 数据集信息
        """
        print("\n创建机器学习训练数据集")
        print("=" * 40)

        os.makedirs(output_dir, exist_ok=True)

        # 分离特征和标签
        feature_columns = [col for col in combined_features_df.columns
                          if col not in ['passage_idx', 'segment_idx', 'sensor_id',
                                       'start_time', 'end_time', 'duration',
                                       'experiment_folder', 'vehicle_type',
                                       'speed_kmh', 'axle_load_tons', 'test_date',
                                       'weather_condition', 'notes', 'sensor_group',
                                       'depth_cm', 'lane_type', 'near_joint']]

        # 创建不同的数据集
        datasets = {}

        # 1. 车辆类型分类数据集
        if 'vehicle_type' in combined_features_df.columns:
            vehicle_dataset = combined_features_df[feature_columns + ['vehicle_type']].copy()
            datasets['vehicle_classification'] = {
                'data': vehicle_dataset,
                'features': feature_columns,
                'target': 'vehicle_type',
                'task_type': 'classification'
            }

        # 2. 速度回归数据集
        if 'speed_kmh' in combined_features_df.columns:
            speed_dataset = combined_features_df[feature_columns + ['speed_kmh']].copy()
            datasets['speed_regression'] = {
                'data': speed_dataset,
                'features': feature_columns,
                'target': 'speed_kmh',
                'task_type': 'regression'
            }

        # 3. 载重回归数据集
        if 'axle_load_tons' in combined_features_df.columns:
            load_dataset = combined_features_df[feature_columns + ['axle_load_tons']].copy()
            datasets['load_regression'] = {
                'data': load_dataset,
                'features': feature_columns,
                'target': 'axle_load_tons',
                'task_type': 'regression'
            }

        # 保存数据集
        dataset_info = {
            'creation_time': pd.Timestamp.now().isoformat(),
            'total_samples': len(combined_features_df),
            'feature_count': len(feature_columns),
            'datasets': {}
        }

        for dataset_name, dataset_config in datasets.items():
            # 保存数据
            dataset_path = os.path.join(output_dir, f'{dataset_name}.csv')
            dataset_config['data'].to_csv(dataset_path, index=False)

            # 统计信息
            dataset_info['datasets'][dataset_name] = {
                'file_path': dataset_path,
                'task_type': dataset_config['task_type'],
                'target_column': dataset_config['target'],
                'sample_count': len(dataset_config['data']),
                'feature_count': len(dataset_config['features'])
            }

            if dataset_config['task_type'] == 'classification':
                unique_classes = dataset_config['data'][dataset_config['target']].unique()
                dataset_info['datasets'][dataset_name]['classes'] = unique_classes.tolist()
                dataset_info['datasets'][dataset_name]['class_distribution'] = \
                    dataset_config['data'][dataset_config['target']].value_counts().to_dict()

            print(f"✅ 已创建 {dataset_name} 数据集: {dataset_path}")

        # 保存数据集信息
        info_path = os.path.join(output_dir, 'dataset_info.json')
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(dataset_info, f, indent=2, ensure_ascii=False, default=str)

        # 保存特征列表
        features_path = os.path.join(output_dir, 'feature_list.txt')
        with open(features_path, 'w', encoding='utf-8') as f:
            f.write("特征列表:\n")
            f.write("=" * 30 + "\n")
            for i, feature in enumerate(feature_columns, 1):
                f.write(f"{i:3d}. {feature}\n")

        print(f"✅ 数据集信息已保存到: {info_path}")
        print(f"✅ 特征列表已保存到: {features_path}")

        return dataset_info
