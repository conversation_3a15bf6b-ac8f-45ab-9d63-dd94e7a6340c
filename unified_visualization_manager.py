import chinese_font_config  # 中文字体配置
#!/usr/bin/env python3
"""
统一可视化管理器
整合所有可视化功能，提供统一的接口
"""

import numpy as np
import pandas as pd
import os
from datetime import datetime
from regression_visualizer import RegressionVisualizer
from classification_visualizer import ClassificationVisualizer
from model_analysis_visualizer import ModelAnalysisVisualizer
from improved_visualization_base import ImprovedVisualizationBase
import warnings
warnings.filterwarnings('ignore')

class UnifiedVisualizationManager(ImprovedVisualizationBase):
    """统一可视化管理器"""
    
    def __init__(self, output_dir='unified_visualizations'):
        """初始化统一可视化管理器"""
        super().__init__(output_dir)
        
        # 初始化各个专用可视化器
        self.regression_viz = RegressionVisualizer(os.path.join(output_dir, 'regression'))
        self.classification_viz = ClassificationVisualizer(os.path.join(output_dir, 'classification'))
        self.model_analysis_viz = ModelAnalysisVisualizer(os.path.join(output_dir, 'model_analysis'))
        
        # 可视化配置
        self.visualization_config = {
            'generate_chinese': True,
            'generate_english': True,
            'generate_regression': True,
            'generate_classification': True,
            'generate_model_analysis': True,
            'generate_sensor_analysis': True
        }
        
        # 存储生成的图表元数据
        self.chart_metadata = []
        
        print("🎯 统一可视化管理器初始化完成")
    
    def generate_comprehensive_visualizations(self, analysis_results, model_results=None):
        """生成全面的可视化图表"""
        print("\n🎨 开始生成全面的可视化图表...")
        print("=" * 80)
        
        try:
            # 1. 回归任务可视化
            if self.visualization_config['generate_regression']:
                print("\n📊 第一阶段：回归任务可视化")
                self._generate_regression_visualizations(analysis_results, model_results)
            
            # 2. 分类任务可视化
            if self.visualization_config['generate_classification']:
                print("\n📊 第二阶段：分类任务可视化")
                self._generate_classification_visualizations(analysis_results, model_results)
            
            # 3. 模型分析可视化
            if self.visualization_config['generate_model_analysis']:
                print("\n📊 第三阶段：模型分析可视化")
                self._generate_model_analysis_visualizations(analysis_results, model_results)
            
            # 4. 传感器分析可视化
            if self.visualization_config['generate_sensor_analysis']:
                print("\n📊 第四阶段：传感器分析可视化")
                self._generate_sensor_analysis_visualizations(analysis_results)
            
            # 5. 生成可视化索引
            print("\n📋 第五阶段：生成可视化索引")
            self._generate_visualization_index()
            
            print(f"\n✅ 全面可视化生成完成!")
            self._print_visualization_summary()
            
        except Exception as e:
            print(f"❌ 可视化生成失败: {str(e)}")
            print("⚠️  跳过可视化生成")
    
    def _generate_regression_visualizations(self, analysis_results, model_results):
        """生成回归任务可视化"""
        print("   🎯 生成回归任务可视化图表...")
        
        # 检查是否有回归任务结果
        regression_tasks = ['speed_prediction', 'axle_weight_prediction']
        
        for task in regression_tasks:
            if task in analysis_results:
                task_results = analysis_results[task]
                
                # 获取模型结果
                if model_results and task in model_results:
                    models_data = model_results[task]
                    
                    for model_name, model_data in models_data.items():
                        if 'y_true' in model_data and 'y_pred' in model_data:
                            y_true = model_data['y_true']
                            y_pred = model_data['y_pred']
                            
                            # 生成中英文版本
                            for language in ['chinese', 'english']:
                                if (language == 'chinese' and self.visualization_config['generate_chinese']) or \
                                   (language == 'english' and self.visualization_config['generate_english']):
                                    
                                    # 预测精度分析图
                                    metadata = self.regression_viz.create_prediction_accuracy_plot(
                                        y_true, y_pred, model_name, 
                                        '速度预测' if task == 'speed_prediction' else '轴重预测', 
                                        language
                                    )
                                    self.chart_metadata.append(metadata)
                                    
                                    # 残差分析图
                                    metadata = self.regression_viz.create_residual_analysis_plot(
                                        y_true, y_pred, model_name,
                                        '速度预测' if task == 'speed_prediction' else '轴重预测',
                                        language
                                    )
                                    self.chart_metadata.append(metadata)
                
                # 模型性能对比图
                if model_results and task in model_results:
                    for language in ['chinese', 'english']:
                        if (language == 'chinese' and self.visualization_config['generate_chinese']) or \
                           (language == 'english' and self.visualization_config['generate_english']):
                            
                            metadata = self.regression_viz.create_model_performance_comparison(
                                model_results[task],
                                '速度预测' if task == 'speed_prediction' else '轴重预测',
                                language
                            )
                            self.chart_metadata.append(metadata)
        
        print("      ✅ 回归任务可视化完成")
    
    def _generate_classification_visualizations(self, analysis_results, model_results):
        """生成分类任务可视化"""
        print("   🎯 生成分类任务可视化图表...")
        
        # 检查是否有分类任务结果
        classification_tasks = ['axle_type_classification']
        
        for task in classification_tasks:
            if task in analysis_results:
                task_results = analysis_results[task]
                
                # 获取模型结果
                if model_results and task in model_results:
                    models_data = model_results[task]
                    
                    for model_name, model_data in models_data.items():
                        if 'y_true' in model_data and 'y_pred' in model_data:
                            y_true = model_data['y_true']
                            y_pred = model_data['y_pred']
                            y_scores = model_data.get('y_scores', np.random.rand(len(y_true)))
                            
                            class_names = ['2轴车', '3轴车', '4轴车']
                            
                            # 生成中英文版本
                            for language in ['chinese', 'english']:
                                if (language == 'chinese' and self.visualization_config['generate_chinese']) or \
                                   (language == 'english' and self.visualization_config['generate_english']):
                                    
                                    # 混淆矩阵
                                    metadata = self.classification_viz.create_confusion_matrix_plot(
                                        y_true, y_pred, class_names, model_name, '轴型分类', language
                                    )
                                    self.chart_metadata.append(metadata)
                                    
                                    # 性能雷达图
                                    metadata = self.classification_viz.create_classification_performance_radar(
                                        y_true, y_pred, model_name, '轴型分类', language
                                    )
                                    self.chart_metadata.append(metadata)
                                    
                                    # 分类报告
                                    metadata = self.classification_viz.create_classification_report_plot(
                                        y_true, y_pred, class_names, model_name, '轴型分类', language
                                    )
                                    self.chart_metadata.append(metadata)
                                    
                                    # ROC/PR曲线（仅二分类）
                                    if len(np.unique(y_true)) == 2:
                                        self.classification_viz.create_roc_pr_curves_plot(
                                            y_true, y_scores, model_name, '轴型分类', class_names, language
                                        )
        
        print("      ✅ 分类任务可视化完成")
    
    def _generate_model_analysis_visualizations(self, analysis_results, model_results):
        """生成模型分析可视化"""
        print("   🎯 生成模型分析可视化图表...")
        
        # 1. 学习曲线
        if model_results:
            for task_name, task_results in model_results.items():
                for model_name, model_data in task_results.items():
                    if 'learning_curve' in model_data:
                        learning_data = model_data['learning_curve']
                        
                        for language in ['chinese', 'english']:
                            if (language == 'chinese' and self.visualization_config['generate_chinese']) or \
                               (language == 'english' and self.visualization_config['generate_english']):
                                
                                metadata = self.model_analysis_viz.create_learning_curves_plot(
                                    learning_data['train_sizes'],
                                    learning_data['train_scores'],
                                    learning_data['val_scores'],
                                    model_name, task_name, language
                                )
                                self.chart_metadata.append(metadata)
        
        # 2. 特征重要性
        if 'feature_importance' in analysis_results:
            feature_importance_data = analysis_results['feature_importance']
            feature_names = analysis_results.get('feature_names', None)
            
            for language in ['chinese', 'english']:
                if (language == 'chinese' and self.visualization_config['generate_chinese']) or \
                   (language == 'english' and self.visualization_config['generate_english']):
                    
                    metadata = self.model_analysis_viz.create_feature_importance_plot(
                        feature_importance_data, feature_names, language=language
                    )
                    self.chart_metadata.append(metadata)
        
        # 3. 超参数优化历史
        if model_results:
            for task_name, task_results in model_results.items():
                for model_name, model_data in task_results.items():
                    if 'optimization_history' in model_data:
                        optimization_history = model_data['optimization_history']
                        
                        for language in ['chinese', 'english']:
                            if (language == 'chinese' and self.visualization_config['generate_chinese']) or \
                               (language == 'english' and self.visualization_config['generate_english']):
                                
                                metadata = self.model_analysis_viz.create_hyperparameter_optimization_plot(
                                    optimization_history, model_name, language
                                )
                                self.chart_metadata.append(metadata)
        
        print("      ✅ 模型分析可视化完成")
    
    def _generate_sensor_analysis_visualizations(self, analysis_results):
        """生成传感器分析可视化"""
        print("   🎯 生成传感器分析可视化图表...")
        
        # 这里可以集成传感器优化可视化功能
        # 如果有传感器分析结果，生成相应的可视化
        if 'sensor_analysis' in analysis_results:
            sensor_data = analysis_results['sensor_analysis']
            # 生成传感器布局图、数据质量对比图等
            # 这部分可以调用之前创建的传感器可视化功能
        
        print("      ✅ 传感器分析可视化完成")
    
    def _generate_visualization_index(self):
        """生成可视化索引文档"""
        print("   📋 生成可视化索引文档...")
        
        # 生成中文索引
        if self.visualization_config['generate_chinese']:
            self._create_index_document('chinese')
        
        # 生成英文索引
        if self.visualization_config['generate_english']:
            self._create_index_document('english')
        
        print("      ✅ 可视化索引文档生成完成")
    
    def _create_index_document(self, language):
        """创建索引文档"""
        index_content = []
        
        if language == 'chinese':
            index_content.append("# 振动信号分析系统 - 可视化图表索引")
            index_content.append("")
            index_content.append(f"**生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
            index_content.append(f"**图表总数**: {len(self.chart_metadata)}")
            index_content.append(f"**分辨率**: {self.chart_config['dpi']} DPI")
            index_content.append(f"**字体**: {self.chinese_font}")
            index_content.append("")
        else:
            index_content.append("# Vibration Signal Analysis System - Visualization Chart Index")
            index_content.append("")
            index_content.append(f"**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            index_content.append(f"**Total Charts**: {len(self.chart_metadata)}")
            index_content.append(f"**Resolution**: {self.chart_config['dpi']} DPI")
            index_content.append(f"**Font**: {self.chinese_font}")
            index_content.append("")
        
        # 按类型分组图表
        chart_types = {}
        for metadata in self.chart_metadata:
            if metadata['language'] == language:
                chart_type = metadata['chart_type']
                if chart_type not in chart_types:
                    chart_types[chart_type] = []
                chart_types[chart_type].append(metadata)
        
        # 生成各类型的图表列表
        for chart_type, charts in chart_types.items():
            if language == 'chinese':
                type_names = {
                    'prediction_accuracy': '预测精度分析',
                    'residual_analysis': '残差分析',
                    'model_performance_comparison': '模型性能对比',
                    'confusion_matrix': '混淆矩阵',
                    'performance_radar': '性能雷达图',
                    'classification_report': '分类报告',
                    'learning_curves': '学习曲线',
                    'feature_importance': '特征重要性',
                    'hyperparameter_optimization': '超参数优化'
                }
                section_title = f"## {type_names.get(chart_type, chart_type)}"
            else:
                section_title = f"## {chart_type.replace('_', ' ').title()}"
            
            index_content.append(section_title)
            index_content.append("")
            
            for chart in charts:
                description = chart.get('description', 'No description')
                model = chart.get('model', 'Unknown')
                index_content.append(f"- **{model}**: {description}")
            
            index_content.append("")
        
        # 保存索引文档
        filename = f'visualization_index_{language}.md'
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write('\n'.join(index_content))
        
        print(f"      ✅ {language}索引文档已保存: {filepath}")
    
    def _print_visualization_summary(self):
        """打印可视化总结"""
        print("\n" + "=" * 80)
        print("🎉 可视化生成总结")
        print("=" * 80)
        
        # 统计图表数量
        total_charts = len(self.chart_metadata)
        chinese_charts = len([m for m in self.chart_metadata if m['language'] == 'chinese'])
        english_charts = len([m for m in self.chart_metadata if m['language'] == 'english'])
        
        print(f"📊 **图表统计**:")
        print(f"   总计: {total_charts} 个图表")
        print(f"   中文版: {chinese_charts} 个")
        print(f"   英文版: {english_charts} 个")
        
        # 按类型统计
        chart_types = {}
        for metadata in self.chart_metadata:
            chart_type = metadata['chart_type']
            chart_types[chart_type] = chart_types.get(chart_type, 0) + 1
        
        print(f"\n📈 **图表类型分布**:")
        for chart_type, count in chart_types.items():
            print(f"   {chart_type}: {count} 个")
        
        print(f"\n🎨 **技术规格**:")
        print(f"   分辨率: {self.chart_config['dpi']} DPI")
        print(f"   中文字体: {self.chinese_font}")
        print(f"   输出目录: {self.output_dir}")
        
        print(f"\n📁 **输出文件**:")
        print(f"   可视化图表: {self.output_dir}/")
        print(f"   索引文档: visualization_index_chinese.md / visualization_index_english.md")

def main():
    """测试函数"""
    print("🧪 测试统一可视化管理器...")
    
    # 初始化管理器
    viz_manager = UnifiedVisualizationManager()
    
    # 创建模拟分析结果
    np.random.seed(42)
    n_samples = 200
    
    # 模拟回归任务结果
    y_true_reg = np.random.uniform(20, 80, n_samples)
    
    analysis_results = {
        'speed_prediction': {'task_type': 'regression'},
        'axle_type_classification': {'task_type': 'classification'}
    }
    
    model_results = {
        'speed_prediction': {
            'Random Forest': {
                'y_true': y_true_reg,
                'y_pred': y_true_reg + np.random.normal(0, 5, n_samples)
            },
            'XGBoost': {
                'y_true': y_true_reg,
                'y_pred': y_true_reg + np.random.normal(0, 3, n_samples)
            }
        },
        'axle_type_classification': {
            'Random Forest': {
                'y_true': np.random.randint(0, 3, n_samples),
                'y_pred': np.random.randint(0, 3, n_samples),
                'y_scores': np.random.rand(n_samples)
            }
        }
    }
    
    # 生成全面可视化
    viz_manager.generate_comprehensive_visualizations(analysis_results, model_results)
    
    print("✅ 统一可视化管理器测试完成!")

if __name__ == "__main__":
    main()
