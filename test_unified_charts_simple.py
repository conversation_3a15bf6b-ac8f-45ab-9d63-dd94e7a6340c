#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试统一图表目录配置
快速验证所有可视化图表都保存到unified_charts目录中

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import sys
from pathlib import Path

def test_enhanced_academic_visualizations():
    """测试增强版学术可视化（统一目录）"""
    print("🧪 测试增强版学术可视化（统一目录）...")
    
    try:
        from visualization_generator_enhanced import EnhancedVisualizationGenerator
        
        # 初始化生成器（统一目录）
        generator = EnhancedVisualizationGenerator(
            output_dir='unified_charts',
            file_prefix='academic_'
        )
        
        # 生成所有图表
        generator.generate_all_visualizations()
        
        # 检查输出文件
        output_dir = Path('unified_charts')
        expected_files = [
            'academic_data_expansion_comparison.png',
            'academic_model_performance_comparison.png',
            'academic_optimization_results.png',
            'academic_data_distribution_analysis.png',
            'academic_feature_importance_analysis.png',
            'academic_confusion_matrix_analysis.png',
            'academic_roc_curves_multiclass.png',
            'academic_precision_recall_curves.png',
            'academic_precision_recall_summary.png'
        ]
        
        success_count = 0
        for file_name in expected_files:
            file_path = output_dir / file_name
            if file_path.exists():
                print(f"   ✅ {file_name}")
                success_count += 1
            else:
                print(f"   ❌ {file_name}")
        
        print(f"   📊 学术图表: {success_count}/{len(expected_files)} 成功")
        return success_count == len(expected_files)
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        return False

def test_technical_workflow_visualizations():
    """测试技术工作流可视化（统一目录）"""
    print("\n🧪 测试技术工作流可视化（统一目录）...")
    
    try:
        from technical_workflow_visualizer import TechnicalWorkflowVisualizer
        
        # 初始化生成器（统一目录）
        generator = TechnicalWorkflowVisualizer(
            output_base_dir='unified_charts',
            file_prefix='technical_'
        )
        
        # 生成所有图表
        generator.generate_all_technical_visualizations()
        
        # 检查输出文件
        output_dir = Path('unified_charts')
        expected_files = [
            'technical_system_overview_diagram.png',
            'technical_data_processing_pipeline.png',
            'technical_comprehensive_workflow.png',
            'technical_sample_vibration_signals.png',
            'technical_multi_sensor_comparison.png',
            'technical_time_domain_features.png',
            'technical_frequency_domain_features.png',
            'technical_time_frequency_features.png',
            'technical_signal_preprocessing_demo.png',
            'technical_filtering_comparison.png'
        ]
        
        success_count = 0
        for file_name in expected_files:
            file_path = output_dir / file_name
            if file_path.exists():
                print(f"   ✅ {file_name}")
                success_count += 1
            else:
                print(f"   ❌ {file_name}")
        
        print(f"   📊 技术图表: {success_count}/{len(expected_files)} 成功")
        return success_count == len(expected_files)
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        return False

def check_unified_directory():
    """检查统一目录结构"""
    print("\n🧪 检查统一目录结构...")
    
    output_dir = Path('unified_charts')
    
    if not output_dir.exists():
        print(f"   ❌ 统一目录不存在: {output_dir}")
        return False
    
    # 检查所有文件
    all_files = list(output_dir.glob('*.png'))
    academic_files = [f for f in all_files if f.name.startswith('academic_')]
    technical_files = [f for f in all_files if f.name.startswith('technical_')]
    
    print(f"   📊 统一目录统计:")
    print(f"      - 总文件数: {len(all_files)}")
    print(f"      - 学术图表: {len(academic_files)} 个")
    print(f"      - 技术图表: {len(technical_files)} 个")
    
    # 检查文件名冲突
    file_names = [f.name for f in all_files]
    unique_names = set(file_names)
    
    if len(file_names) == len(unique_names):
        print(f"   ✅ 无文件名冲突")
    else:
        print(f"   ❌ 发现文件名冲突: {len(file_names) - len(unique_names)} 个")
        return False
    
    # 显示前缀分类
    prefix_stats = {}
    for file_name in file_names:
        if '_' in file_name:
            prefix = file_name.split('_')[0]
            prefix_stats[prefix] = prefix_stats.get(prefix, 0) + 1
    
    print(f"   📊 前缀分类:")
    for prefix, count in prefix_stats.items():
        print(f"      - {prefix}_: {count} 个文件")
    
    expected_total = 19  # 9 academic + 10 technical
    if len(all_files) >= expected_total:
        print(f"   ✅ 文件数量达标: {len(all_files)} >= {expected_total}")
        return True
    else:
        print(f"   ❌ 文件数量不足: {len(all_files)} < {expected_total}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始统一图表目录简化测试")
    print("=" * 60)
    
    # 运行测试
    test1 = test_enhanced_academic_visualizations()
    test2 = test_technical_workflow_visualizations()
    test3 = check_unified_directory()
    
    # 总结结果
    print("\n" + "=" * 60)
    print("🎯 测试结果总结")
    print("=" * 60)
    
    results = [
        ("学术可视化", test1),
        ("技术可视化", test2),
        ("统一目录", test3)
    ]
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} - {test_name}")
    
    print(f"\n📊 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！统一图表目录配置工作正常。")
        print("📁 所有图表现在都保存在unified_charts目录中。")
    else:
        print("⚠️  部分测试失败，请检查配置。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
