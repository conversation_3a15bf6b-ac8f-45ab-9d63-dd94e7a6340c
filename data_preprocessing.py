#!/usr/bin/env python3
"""
振动信号数据预处理脚本
从原始数据生成训练数据集
"""

import pandas as pd
import numpy as np
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from sklearn.preprocessing import StandardScaler, LabelEncoder
from scipy import signal
from scipy.stats import skew, kurtosis
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VibrationDataProcessor:
    """振动数据处理器"""

    def __init__(self, raw_data_dir: str = './raw_data', output_dir: str = './training_datasets'):
        """
        初始化数据处理器

        Args:
            raw_data_dir: 原始数据目录
            output_dir: 输出目录
        """
        self.raw_data_dir = raw_data_dir
        self.output_dir = output_dir
        self.scaler = StandardScaler()

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

    def safe_read_csv(self, file_path: str) -> Optional[pd.DataFrame]:
        """安全读取CSV文件，自动处理编码问题"""
        import chardet

        # 尝试检测编码
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)
                result = chardet.detect(raw_data)
                detected_encoding = result['encoding']
        except:
            detected_encoding = 'gbk'

        # 尝试多种编码
        encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
        if detected_encoding and detected_encoding not in encodings_to_try:
            encodings_to_try.insert(0, detected_encoding)

        for encoding in encodings_to_try:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                logger.info(f"✅ 成功使用编码 {encoding} 读取文件: {os.path.basename(file_path)}")
                return df
            except UnicodeDecodeError:
                continue
            except Exception as e:
                logger.warning(f"读取文件失败 (编码 {encoding}): {str(e)}")
                continue

        logger.error(f"❌ 无法读取文件: {file_path}")
        return None
        
    def extract_time_domain_features(self, signal_data: np.ndarray) -> Dict[str, float]:
        """提取时域特征"""
        features = {}
        
        # 基本统计特征
        features['mean'] = np.mean(signal_data)
        features['std'] = np.std(signal_data)
        features['var'] = np.var(signal_data)
        features['rms'] = np.sqrt(np.mean(signal_data**2))
        features['peak'] = np.max(np.abs(signal_data))
        features['peak_to_peak'] = np.max(signal_data) - np.min(signal_data)
        features['crest_factor'] = features['peak'] / features['rms'] if features['rms'] > 0 else 0
        features['shape_factor'] = features['rms'] / np.mean(np.abs(signal_data)) if np.mean(np.abs(signal_data)) > 0 else 0
        features['impulse_factor'] = features['peak'] / np.mean(np.abs(signal_data)) if np.mean(np.abs(signal_data)) > 0 else 0
        features['clearance_factor'] = features['peak'] / (np.mean(np.sqrt(np.abs(signal_data))))**2 if np.mean(np.sqrt(np.abs(signal_data))) > 0 else 0
        
        # 高阶统计特征
        features['skewness'] = skew(signal_data)
        features['kurtosis'] = kurtosis(signal_data)
        
        # 能量特征
        features['energy'] = np.sum(signal_data**2)
        features['power'] = features['energy'] / len(signal_data)
        
        return features
    
    def extract_frequency_domain_features(self, signal_data: np.ndarray, sampling_rate: float = 1000) -> Dict[str, float]:
        """提取频域特征"""
        features = {}
        
        # FFT变换
        fft_data = np.fft.fft(signal_data)
        fft_magnitude = np.abs(fft_data[:len(fft_data)//2])
        frequencies = np.fft.fftfreq(len(signal_data), 1/sampling_rate)[:len(fft_data)//2]
        
        # 频域统计特征
        features['freq_mean'] = np.mean(fft_magnitude)
        features['freq_std'] = np.std(fft_magnitude)
        features['freq_var'] = np.var(fft_magnitude)
        features['freq_peak'] = np.max(fft_magnitude)
        features['freq_rms'] = np.sqrt(np.mean(fft_magnitude**2))
        
        # 频率重心
        features['spectral_centroid'] = np.sum(frequencies * fft_magnitude) / np.sum(fft_magnitude) if np.sum(fft_magnitude) > 0 else 0
        
        # 频带能量
        freq_bands = [(0, 50), (50, 100), (100, 200), (200, 400)]
        for i, (low, high) in enumerate(freq_bands):
            band_mask = (frequencies >= low) & (frequencies < high)
            band_energy = np.sum(fft_magnitude[band_mask]**2)
            features[f'band_{i+1}_energy'] = band_energy
        
        # 主频率
        peak_freq_idx = np.argmax(fft_magnitude)
        features['dominant_frequency'] = frequencies[peak_freq_idx] if peak_freq_idx < len(frequencies) else 0
        
        return features
    
    def extract_wavelet_features(self, signal_data: np.ndarray) -> Dict[str, float]:
        """提取小波特征"""
        features = {}
        
        try:
            # 使用scipy的连续小波变换近似
            # 这里使用简化的小波分解
            from scipy.signal import cwt, ricker
            
            # 使用不同尺度的Ricker小波
            scales = np.arange(1, 32)
            coefficients = cwt(signal_data, ricker, scales)
            
            # 小波能量特征
            for i, scale in enumerate([1, 4, 8, 16, 31]):
                if i < len(coefficients):
                    coeff = coefficients[i]
                    features[f'wavelet_energy_scale_{scale}'] = np.sum(coeff**2)
                    features[f'wavelet_std_scale_{scale}'] = np.std(coeff)
                    features[f'wavelet_mean_scale_{scale}'] = np.mean(np.abs(coeff))
        
        except Exception as e:
            logger.warning(f"小波特征提取失败: {str(e)}")
            # 如果小波变换失败，使用简单的分段统计特征
            n_segments = 4
            segment_length = len(signal_data) // n_segments
            for i in range(n_segments):
                start_idx = i * segment_length
                end_idx = (i + 1) * segment_length if i < n_segments - 1 else len(signal_data)
                segment = signal_data[start_idx:end_idx]
                features[f'segment_{i+1}_energy'] = np.sum(segment**2)
                features[f'segment_{i+1}_std'] = np.std(segment)
                features[f'segment_{i+1}_mean'] = np.mean(np.abs(segment))
        
        return features
    
    def process_vibration_signal(self, signal_data: pd.DataFrame, sampling_rate: float = 1000) -> Dict[str, float]:
        """处理单个振动信号，提取所有特征"""
        all_features = {}
        
        # 获取传感器列
        sensor_columns = [col for col in signal_data.columns if 'sensor' in col.lower()]
        
        for col in sensor_columns:
            if col in signal_data.columns:
                signal_values = signal_data[col].dropna().values
                
                if len(signal_values) > 0:
                    # 时域特征
                    time_features = self.extract_time_domain_features(signal_values)
                    for key, value in time_features.items():
                        all_features[f'{col}_{key}'] = value
                    
                    # 频域特征
                    freq_features = self.extract_frequency_domain_features(signal_values, sampling_rate)
                    for key, value in freq_features.items():
                        all_features[f'{col}_{key}'] = value
                    
                    # 小波特征
                    wavelet_features = self.extract_wavelet_features(signal_values)
                    for key, value in wavelet_features.items():
                        all_features[f'{col}_{key}'] = value
        
        return all_features
    
    def load_and_merge_labels(self, timestamp: pd.Timestamp, time_window: int = 30) -> Dict:
        """加载并合并标签数据"""
        labels = {}
        
        # 时间窗口
        start_time = timestamp - timedelta(seconds=time_window)
        end_time = timestamp + timedelta(seconds=time_window)
        
        # 加载速度标签
        speed_file = os.path.join(self.raw_data_dir, 'speed_labels', 'speed_labels.csv')
        if os.path.exists(speed_file):
            try:
                speed_df = self.safe_read_csv(speed_file)
                if speed_df is not None:
                    speed_df['timestamp'] = pd.to_datetime(speed_df['timestamp'])
                
                    # 查找时间窗口内的标签
                    mask = (speed_df['timestamp'] >= start_time) & (speed_df['timestamp'] <= end_time)
                    matching_speeds = speed_df[mask]

                    if not matching_speeds.empty:
                        labels['speed_kmh'] = matching_speeds['speed_kmh'].mean()
                        if 'confidence' in matching_speeds.columns:
                            labels['speed_confidence'] = matching_speeds['confidence'].mean()
            except Exception as e:
                logger.warning(f"速度标签加载失败: {str(e)}")

        # 加载轴重标签
        load_file = os.path.join(self.raw_data_dir, 'load_labels', 'load_labels.csv')
        if os.path.exists(load_file):
            try:
                load_df = self.safe_read_csv(load_file)
                if load_df is not None:
                    load_df['timestamp'] = pd.to_datetime(load_df['timestamp'])
                
                mask = (load_df['timestamp'] >= start_time) & (load_df['timestamp'] <= end_time)
                matching_loads = load_df[mask]
                
                if not matching_loads.empty:
                    if 'total_load' in matching_loads.columns:
                        labels['total_load'] = matching_loads['total_load'].mean()
                    # 添加各轴载荷
                    for col in matching_loads.columns:
                        if 'axle' in col and 'load' in col:
                            labels[col] = matching_loads[col].mean()
            except Exception as e:
                logger.warning(f"轴重标签加载失败: {str(e)}")
        
        # 加载轴型标签
        type_file = os.path.join(self.raw_data_dir, 'type_labels', 'type_labels.csv')
        if os.path.exists(type_file):
            try:
                type_df = self.safe_read_csv(type_file)
                if type_df is not None:
                    type_df['timestamp'] = pd.to_datetime(type_df['timestamp'])
                
                mask = (type_df['timestamp'] >= start_time) & (type_df['timestamp'] <= end_time)
                matching_types = type_df[mask]
                
                if not matching_types.empty:
                    # 使用最常见的轴型
                    if 'axle_type' in matching_types.columns:
                        labels['axle_type'] = matching_types['axle_type'].mode().iloc[0] if not matching_types['axle_type'].mode().empty else None
                    if 'axle_count' in matching_types.columns:
                        labels['axle_count'] = matching_types['axle_count'].mode().iloc[0] if not matching_types['axle_count'].mode().empty else None
                    if 'vehicle_class' in matching_types.columns:
                        labels['vehicle_class'] = matching_types['vehicle_class'].mode().iloc[0] if not matching_types['vehicle_class'].mode().empty else None
            except Exception as e:
                logger.warning(f"轴型标签加载失败: {str(e)}")
        
        return labels
    
    def process_all_data(self, window_size: int = 1000, overlap: float = 0.5) -> Dict[str, pd.DataFrame]:
        """处理所有数据"""
        logger.info("🚀 开始数据预处理...")
        
        all_datasets = {
            'speed_regression': [],
            'load_regression': [],
            'type_classification': []
        }
        
        # 处理振动信号文件
        vibration_dir = os.path.join(self.raw_data_dir, 'vibration_signals')
        if not os.path.exists(vibration_dir):
            logger.error(f"振动信号目录不存在: {vibration_dir}")
            return {}
        
        vibration_files = [f for f in os.listdir(vibration_dir) if f.endswith('.csv')]
        logger.info(f"发现 {len(vibration_files)} 个振动信号文件")
        
        for file_name in vibration_files:
            logger.info(f"处理文件: {file_name}")
            file_path = os.path.join(vibration_dir, file_name)
            
            try:
                # 安全读取振动信号
                df = self.safe_read_csv(file_path)
                if df is None:
                    logger.error(f"跳过文件 {file_name}：读取失败")
                    continue

                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.sort_values('timestamp')
                
                # 滑动窗口处理
                step_size = int(window_size * (1 - overlap))
                
                for start_idx in range(0, len(df) - window_size + 1, step_size):
                    end_idx = start_idx + window_size
                    window_data = df.iloc[start_idx:end_idx]
                    
                    # 提取特征
                    features = self.process_vibration_signal(window_data)
                    
                    # 获取窗口中心时间戳
                    center_timestamp = window_data['timestamp'].iloc[len(window_data)//2]
                    
                    # 加载标签
                    labels = self.load_and_merge_labels(center_timestamp)
                    
                    # 合并特征和标签
                    record = {**features, **labels, 'timestamp': center_timestamp}
                    
                    # 分配到不同数据集
                    if 'speed_kmh' in labels:
                        all_datasets['speed_regression'].append(record)
                    
                    if 'total_load' in labels:
                        all_datasets['load_regression'].append(record)
                    
                    if 'axle_type' in labels:
                        all_datasets['type_classification'].append(record)
                
            except Exception as e:
                logger.error(f"处理文件 {file_name} 时出错: {str(e)}")
                continue
        
        # 转换为DataFrame
        processed_datasets = {}
        for dataset_name, records in all_datasets.items():
            if records:
                df = pd.DataFrame(records)
                processed_datasets[dataset_name] = df
                logger.info(f"{dataset_name}: {len(df)} 条记录")
            else:
                logger.warning(f"{dataset_name}: 无数据")
        
        return processed_datasets
    
    def clean_and_save_datasets(self, datasets: Dict[str, pd.DataFrame]):
        """清洗并保存数据集"""
        logger.info("🧹 清洗和保存数据集...")
        
        for dataset_name, df in datasets.items():
            logger.info(f"处理数据集: {dataset_name}")
            
            # 数据清洗
            # 1. 移除缺失值过多的列
            missing_threshold = 0.5
            df = df.loc[:, df.isnull().mean() < missing_threshold]
            
            # 2. 移除缺失值过多的行
            df = df.dropna(thresh=len(df.columns) * 0.7)
            
            # 3. 处理无穷大值
            df = df.replace([np.inf, -np.inf], np.nan)
            df = df.fillna(df.median())
            
            # 4. 移除异常值（使用IQR方法）
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                if col not in ['timestamp', 'axle_type', 'axle_count']:
                    Q1 = df[col].quantile(0.25)
                    Q3 = df[col].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    df = df[(df[col] >= lower_bound) & (df[col] <= upper_bound)]
            
            # 保存数据集
            output_file = os.path.join(self.output_dir, f'{dataset_name}.csv')
            df.to_csv(output_file, index=False)
            logger.info(f"✅ 保存数据集: {output_file} ({len(df)} 条记录)")
        
        logger.info("✅ 数据预处理完成")

    def generate_feature_importance_report(self, datasets: Dict[str, pd.DataFrame]):
        """生成特征重要性报告"""
        logger.info("📊 生成特征重要性报告...")

        from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
        from sklearn.feature_selection import mutual_info_regression, mutual_info_classif

        report_file = os.path.join(self.output_dir, 'feature_importance_report.txt')

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("特征重要性分析报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().isoformat()}\n\n")

            for dataset_name, df in datasets.items():
                f.write(f"\n📊 数据集: {dataset_name}\n")
                f.write("-" * 40 + "\n")
                f.write(f"样本数量: {len(df)}\n")
                f.write(f"特征数量: {len(df.columns) - 2}\n")  # 减去timestamp和target

                # 准备数据
                feature_columns = [col for col in df.columns if col not in ['timestamp', 'speed_kmh', 'total_load', 'axle_type', 'axle_count', 'vehicle_class']]

                if len(feature_columns) > 0:
                    X = df[feature_columns].fillna(0)

                    # 确定目标变量
                    if 'speed_kmh' in df.columns:
                        y = df['speed_kmh']
                        task_type = 'regression'
                    elif 'total_load' in df.columns:
                        y = df['total_load']
                        task_type = 'regression'
                    elif 'axle_type' in df.columns:
                        y = df['axle_type']
                        task_type = 'classification'
                    else:
                        continue

                    try:
                        if task_type == 'regression':
                            # 随机森林特征重要性
                            rf = RandomForestRegressor(n_estimators=100, random_state=42)
                            rf.fit(X, y)
                            rf_importance = rf.feature_importances_

                            # 互信息
                            mi_scores = mutual_info_regression(X, y, random_state=42)

                        else:
                            # 分类任务
                            le = LabelEncoder()
                            y_encoded = le.fit_transform(y.astype(str))

                            rf = RandomForestClassifier(n_estimators=100, random_state=42)
                            rf.fit(X, y_encoded)
                            rf_importance = rf.feature_importances_

                            mi_scores = mutual_info_classif(X, y_encoded, random_state=42)

                        # 排序特征
                        feature_importance_df = pd.DataFrame({
                            'feature': feature_columns,
                            'rf_importance': rf_importance,
                            'mutual_info': mi_scores
                        })
                        feature_importance_df['combined_score'] = (
                            feature_importance_df['rf_importance'] + feature_importance_df['mutual_info']
                        ) / 2
                        feature_importance_df = feature_importance_df.sort_values('combined_score', ascending=False)

                        # 写入报告
                        f.write(f"\n前20个重要特征:\n")
                        for i, row in feature_importance_df.head(20).iterrows():
                            f.write(f"  {row['feature']}: {row['combined_score']:.4f}\n")

                    except Exception as e:
                        f.write(f"特征重要性分析失败: {str(e)}\n")

        logger.info(f"✅ 特征重要性报告已保存: {report_file}")

def main():
    """主函数"""
    print("🔄 振动信号数据预处理")
    print("=" * 50)
    
    # 创建处理器
    processor = VibrationDataProcessor()
    
    # 处理数据
    datasets = processor.process_all_data()
    
    if datasets:
        # 清洗和保存
        processor.clean_and_save_datasets(datasets)
        
        print(f"\n📊 预处理完成:")
        for name, df in datasets.items():
            print(f"   {name}: {len(df)} 条记录")
        print(f"\n✅ 训练数据集已保存到: ./training_datasets/")
    else:
        print("❌ 数据预处理失败，请检查原始数据")

if __name__ == "__main__":
    main()
