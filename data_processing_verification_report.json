{"filter_check": {"has_csv_filter": true, "has_legacy_keywords": true, "has_new_keywords": true, "has_or_logic": true}, "parsing_check": {"new_format_count": 20, "legacy_format_count": 0, "parsing_results": {"new_format": {"lane": 1, "axle_type": "2轴", "load_tons": 3.0, "speed_kmh": 72.0}}, "column_check": {"new_format": {"columns": 22, "has_acce_columns": true, "sample_columns": ["Unnamed: 0", "count", "acce1", "acce2", "acce3"]}}}, "integration_check": {"calls_expansion_processor": true, "has_import_statement": true, "has_fallback_logic": true, "processor_exists": true, "handles_new_format": true, "has_filename_parsing": true}, "coverage_check": {"features_file_exists": true, "total_samples": 1398, "has_data_source_column": false, "target_variables": {"speed_kmh": {"exists": true, "valid_count": "1398", "coverage_rate": 1.0}, "load_tons": {"exists": false}, "axle_type": {"exists": true, "valid_count": "1398", "coverage_rate": 1.0}}}, "overall_report": {"total_score": 12, "max_score": 16, "percentage": 75.0, "sections": {"filter": 4, "parsing": 3, "integration": 4, "coverage": 1}}}