#!/usr/bin/env python3
"""
超参数优化模块
使用Optuna进行贝叶斯优化，针对路面埋设加速度传感器数据优化
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import cross_val_score, StratifiedKFold, KFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.ensemble import GradientBoostingRegressor, GradientBoostingClassifier
from sklearn.ensemble import AdaBoostRegressor, AdaBoostClassifier
from sklearn.ensemble import ExtraTreesRegressor, ExtraTreesClassifier
from sklearn.svm import SVR, SVC
import optuna
import joblib
import warnings
warnings.filterwarnings('ignore')

class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(self, n_trials=100, cv_folds=5, random_state=42):
        """
        初始化优化器
        
        Args:
            n_trials: 优化试验次数
            cv_folds: 交叉验证折数
            random_state: 随机种子
        """
        self.n_trials = n_trials
        self.cv_folds = cv_folds
        self.random_state = random_state
        self.best_params = {}
        self.optimization_results = {}
        
    def optimize_random_forest(self, X, y, task_type='regression'):
        """优化随机森林参数"""
        print(f"    🔧 优化Random Forest ({task_type})...")
        
        def objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 50, 300),
                'max_depth': trial.suggest_int('max_depth', 3, 20),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', None]),
                'bootstrap': trial.suggest_categorical('bootstrap', [True, False]),
                'random_state': self.random_state,
                'n_jobs': -1
            }
            
            if task_type == 'regression':
                model = RandomForestRegressor(**params)
                cv = KFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                scores = cross_val_score(model, X, y, cv=cv, scoring='r2')
            else:
                model = RandomForestClassifier(**params)
                cv = StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
            
            return scores.mean()
        
        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
        study.optimize(objective, n_trials=self.n_trials, show_progress_bar=False)
        
        self.best_params['Random Forest'] = study.best_params
        self.optimization_results['Random Forest'] = {
            'best_score': study.best_value,
            'best_params': study.best_params,
            'n_trials': len(study.trials)
        }
        
        print(f"      最佳分数: {study.best_value:.4f}")
        return study.best_params
    
    def optimize_xgboost(self, X, y, task_type='regression'):
        """优化XGBoost参数"""
        print(f"    🔧 优化XGBoost ({task_type})...")
        
        try:
            import xgboost as xgb
            
            def objective(trial):
                params = {
                    'n_estimators': trial.suggest_int('n_estimators', 50, 300),
                    'max_depth': trial.suggest_int('max_depth', 3, 10),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                    'reg_alpha': trial.suggest_float('reg_alpha', 0, 10),
                    'reg_lambda': trial.suggest_float('reg_lambda', 0, 10),
                    'random_state': self.random_state,
                    'tree_method': 'hist',
                    'n_jobs': -1
                }
                
                if task_type == 'regression':
                    model = xgb.XGBRegressor(**params)
                    cv = KFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                    scores = cross_val_score(model, X, y, cv=cv, scoring='r2')
                else:
                    model = xgb.XGBClassifier(**params)
                    cv = StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                    scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
                
                return scores.mean()
            
            study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
            study.optimize(objective, n_trials=self.n_trials, show_progress_bar=False)
            
            self.best_params['XGBoost'] = study.best_params
            self.optimization_results['XGBoost'] = {
                'best_score': study.best_value,
                'best_params': study.best_params,
                'n_trials': len(study.trials)
            }
            
            print(f"      最佳分数: {study.best_value:.4f}")
            return study.best_params
            
        except ImportError:
            print("      ⚠️  XGBoost未安装，跳过优化")
            return {}
    
    def optimize_gradient_boosting(self, X, y, task_type='regression'):
        """优化梯度提升参数"""
        print(f"    🔧 优化Gradient Boosting ({task_type})...")
        
        def objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 50, 300),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', None]),
                'random_state': self.random_state
            }
            
            if task_type == 'regression':
                model = GradientBoostingRegressor(**params)
                cv = KFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                scores = cross_val_score(model, X, y, cv=cv, scoring='r2')
            else:
                model = GradientBoostingClassifier(**params)
                cv = StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
            
            return scores.mean()
        
        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
        study.optimize(objective, n_trials=self.n_trials, show_progress_bar=False)
        
        self.best_params['Gradient Boosting'] = study.best_params
        self.optimization_results['Gradient Boosting'] = {
            'best_score': study.best_value,
            'best_params': study.best_params,
            'n_trials': len(study.trials)
        }
        
        print(f"      最佳分数: {study.best_value:.4f}")
        return study.best_params
    
    def optimize_extra_trees(self, X, y, task_type='regression'):
        """优化Extra Trees参数"""
        print(f"    🔧 优化Extra Trees ({task_type})...")
        
        def objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 50, 300),
                'max_depth': trial.suggest_int('max_depth', 3, 20),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', None]),
                'bootstrap': trial.suggest_categorical('bootstrap', [True, False]),
                'random_state': self.random_state,
                'n_jobs': -1
            }
            
            if task_type == 'regression':
                model = ExtraTreesRegressor(**params)
                cv = KFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                scores = cross_val_score(model, X, y, cv=cv, scoring='r2')
            else:
                model = ExtraTreesClassifier(**params)
                cv = StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
            
            return scores.mean()
        
        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
        study.optimize(objective, n_trials=self.n_trials, show_progress_bar=False)
        
        self.best_params['Extra Trees'] = study.best_params
        self.optimization_results['Extra Trees'] = {
            'best_score': study.best_value,
            'best_params': study.best_params,
            'n_trials': len(study.trials)
        }
        
        print(f"      最佳分数: {study.best_value:.4f}")
        return study.best_params
    
    def optimize_svm(self, X, y, task_type='regression'):
        """优化SVM参数"""
        print(f"    🔧 优化SVM ({task_type})...")
        
        def objective(trial):
            # 修复gamma参数冲突问题
            gamma_type = trial.suggest_categorical('gamma_type', ['fixed', 'auto'])
            if gamma_type == 'auto':
                gamma_value = trial.suggest_categorical('gamma_auto', ['scale', 'auto'])
            else:
                gamma_value = trial.suggest_float('gamma_fixed', 1e-6, 1e-1, log=True)

            params = {
                'C': trial.suggest_float('C', 0.1, 100, log=True),
                'gamma': gamma_value,
                'kernel': trial.suggest_categorical('kernel', ['rbf', 'poly', 'sigmoid']),
                'epsilon': trial.suggest_float('epsilon', 0.01, 1.0) if task_type == 'regression' else None
            }
            
            # 清理None值
            params = {k: v for k, v in params.items() if v is not None}
            
            if task_type == 'regression':
                model = SVR(**params)
                cv = KFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                scores = cross_val_score(model, X, y, cv=cv, scoring='r2')
            else:
                params['probability'] = True
                params['random_state'] = self.random_state
                model = SVC(**params)
                cv = StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
            
            return scores.mean()
        
        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
        study.optimize(objective, n_trials=min(50, self.n_trials), show_progress_bar=False)  # SVM较慢，减少试验次数
        
        self.best_params['SVM'] = study.best_params
        self.optimization_results['SVM'] = {
            'best_score': study.best_value,
            'best_params': study.best_params,
            'n_trials': len(study.trials)
        }
        
        print(f"      最佳分数: {study.best_value:.4f}")
        return study.best_params, study.best_value
    
    def optimize_adaboost(self, X, y, task_type='regression'):
        """优化AdaBoost参数"""
        print(f"    🔧 优化AdaBoost ({task_type})...")
        
        def objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 50, 300),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 2.0),
                'random_state': self.random_state
            }
            
            if task_type == 'regression':
                params['loss'] = trial.suggest_categorical('loss', ['linear', 'square', 'exponential'])
                model = AdaBoostRegressor(**params)
                cv = KFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                scores = cross_val_score(model, X, y, cv=cv, scoring='r2')
            else:
                params['algorithm'] = trial.suggest_categorical('algorithm', ['SAMME', 'SAMME.R'])
                model = AdaBoostClassifier(**params)
                cv = StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
            
            return scores.mean()
        
        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
        study.optimize(objective, n_trials=self.n_trials, show_progress_bar=False)
        
        self.best_params['AdaBoost'] = study.best_params
        self.optimization_results['AdaBoost'] = {
            'best_score': study.best_value,
            'best_params': study.best_params,
            'n_trials': len(study.trials)
        }
        
        print(f"      最佳分数: {study.best_value:.4f}")
        return study.best_params
    
    def optimize_all_traditional_models(self, X, y, task_type='regression'):
        """优化所有传统机器学习模型"""
        print(f"🔧 开始传统机器学习模型超参数优化 ({task_type})...")
        
        optimizers = [
            ('Random Forest', self.optimize_random_forest),
            ('Extra Trees', self.optimize_extra_trees),
            ('Gradient Boosting', self.optimize_gradient_boosting),
            ('XGBoost', self.optimize_xgboost),
            ('AdaBoost', self.optimize_adaboost),
            ('SVM', self.optimize_svm)
        ]
        
        for model_name, optimizer_func in optimizers:
            try:
                optimizer_func(X, y, task_type)
            except Exception as e:
                print(f"    ❌ {model_name} 优化失败: {str(e)}")
                continue
        
        print(f"✅ 传统机器学习模型优化完成")
        return self.best_params
    
    def save_optimization_results(self, filename='hyperparameter_optimization_results.json'):
        """保存优化结果"""
        import json
        
        results = {
            'optimization_settings': {
                'n_trials': self.n_trials,
                'cv_folds': self.cv_folds,
                'random_state': self.random_state
            },
            'best_parameters': self.best_params,
            'optimization_results': self.optimization_results
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 优化结果已保存: {filename}")

def main():
    """测试函数"""
    from sklearn.datasets import make_regression, make_classification
    
    print("🧪 测试超参数优化器...")
    
    # 创建测试数据
    X_reg, y_reg = make_regression(n_samples=1000, n_features=20, noise=0.1, random_state=42)
    
    # 初始化优化器
    optimizer = HyperparameterOptimizer(n_trials=20, cv_folds=3)  # 测试用较少试验次数
    
    # 优化回归模型
    optimizer.optimize_all_traditional_models(X_reg, y_reg, 'regression')
    
    # 保存结果
    optimizer.save_optimization_results('test_optimization_results.json')
    
    print("✅ 测试完成!")

if __name__ == "__main__":
    main()
