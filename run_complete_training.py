#!/usr/bin/env python3
"""
完整的端到端训练执行脚本
从数据验证到模型训练的完整流程
"""

import os
import sys
import time
import logging
import subprocess
from datetime import datetime
from typing import Dict, List, Any
import json

# 设置Windows编码
if sys.platform.startswith('win'):
    import locale
    try:
        # 设置控制台编码为UTF-8
        os.system('chcp 65001 > nul')
        # 设置Python默认编码
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
    except:
        pass

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteTrainingPipeline:
    """完整训练管道"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化训练管道
        
        Args:
            config: 配置参数
        """
        self.config = config or self.get_default_config()
        self.results = {}
        self.start_time = time.time()
        
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'raw_data_dir': './raw_data',
            'training_data_dir': './training_datasets',
            'models_dir': './models',
            'results_dir': './results',
            'enable_gpu': True,
            'enable_deep_learning': True,
            'n_trials': 30,
            'cv_folds': 5,
            'validation_threshold': 0.8,
            'target_r2_regression': 0.75,
            'target_accuracy_classification': 0.85
        }
    
    def setup_directories(self):
        """设置目录结构"""
        logger.info("📁 设置目录结构...")
        
        directories = [
            self.config['training_data_dir'],
            self.config['models_dir'],
            self.config['results_dir'],
            os.path.join(self.config['results_dir'], 'logs'),
            os.path.join(self.config['results_dir'], 'reports'),
            os.path.join(self.config['results_dir'], 'models')
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"✅ 目录已创建: {directory}")
    
    def step_1_data_validation(self) -> bool:
        """步骤1: 数据验证"""
        logger.info("🔍 步骤1: 数据验证")
        print("=" * 60)
        print("🔍 步骤1: 数据验证")
        print("=" * 60)
        
        try:
            # 运行数据验证脚本
            result = subprocess.run([
                sys.executable, 'data_validation.py'
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("✅ 数据验证完成")
                print("✅ 数据验证通过")
                
                # 检查验证报告
                if os.path.exists('data_validation_report.txt'):
                    with open('data_validation_report.txt', 'r', encoding='utf-8') as f:
                        report_content = f.read()
                        # 简单解析验证成功率
                        if '验证成功率:' in report_content:
                            success_rate_line = [line for line in report_content.split('\n') if '验证成功率:' in line][0]
                            success_rate = float(success_rate_line.split(':')[1].strip().replace('%', '')) / 100
                            
                            if success_rate >= self.config['validation_threshold']:
                                self.results['data_validation'] = {'success': True, 'success_rate': success_rate}
                                return True
                            else:
                                logger.warning(f"数据验证成功率过低: {success_rate:.1%}")
                                self.results['data_validation'] = {'success': False, 'success_rate': success_rate}
                                return False
                
                self.results['data_validation'] = {'success': True, 'success_rate': 1.0}
                return True
            else:
                logger.error(f"数据验证失败: {result.stderr}")
                print(f"❌ 数据验证失败: {result.stderr}")
                self.results['data_validation'] = {'success': False, 'error': result.stderr}
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("数据验证超时")
            print("❌ 数据验证超时")
            self.results['data_validation'] = {'success': False, 'error': 'timeout'}
            return False
        except Exception as e:
            logger.error(f"数据验证异常: {str(e)}")
            print(f"❌ 数据验证异常: {str(e)}")
            self.results['data_validation'] = {'success': False, 'error': str(e)}
            return False
    
    def step_2_data_preprocessing(self) -> bool:
        """步骤2: 数据预处理"""
        logger.info("🔄 步骤2: 数据预处理")
        print("\n" + "=" * 60)
        print("🔄 步骤2: 数据预处理")
        print("=" * 60)
        
        try:
            # 运行数据预处理脚本
            result = subprocess.run([
                sys.executable, 'data_preprocessing.py'
            ], capture_output=True, text=True, timeout=1800)  # 30分钟超时
            
            if result.returncode == 0:
                logger.info("✅ 数据预处理完成")
                print("✅ 数据预处理完成")
                
                # 检查生成的数据集
                datasets_found = []
                expected_datasets = ['speed_regression.csv', 'load_regression.csv', 'type_classification.csv']
                
                for dataset in expected_datasets:
                    dataset_path = os.path.join(self.config['training_data_dir'], dataset)
                    if os.path.exists(dataset_path):
                        datasets_found.append(dataset)
                        logger.info(f"✅ 数据集已生成: {dataset}")
                        print(f"✅ 数据集已生成: {dataset}")
                
                if datasets_found:
                    self.results['data_preprocessing'] = {
                        'success': True, 
                        'datasets_generated': datasets_found
                    }
                    return True
                else:
                    logger.error("未生成任何训练数据集")
                    print("❌ 未生成任何训练数据集")
                    self.results['data_preprocessing'] = {'success': False, 'error': 'no_datasets_generated'}
                    return False
            else:
                logger.error(f"数据预处理失败: {result.stderr}")
                print(f"❌ 数据预处理失败: {result.stderr}")
                self.results['data_preprocessing'] = {'success': False, 'error': result.stderr}
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("数据预处理超时")
            print("❌ 数据预处理超时")
            self.results['data_preprocessing'] = {'success': False, 'error': 'timeout'}
            return False
        except Exception as e:
            logger.error(f"数据预处理异常: {str(e)}")
            print(f"❌ 数据预处理异常: {str(e)}")
            self.results['data_preprocessing'] = {'success': False, 'error': str(e)}
            return False
    
    def step_3_traditional_ml_training(self) -> bool:
        """步骤3: 传统机器学习训练"""
        logger.info("🤖 步骤3: 传统机器学习训练")
        print("\n" + "=" * 60)
        print("🤖 步骤3: 传统机器学习训练")
        print("=" * 60)
        
        try:
            # 运行GPU优化训练脚本
            result = subprocess.run([
                sys.executable, 'ml/gpu_optimized_training.py'
            ], capture_output=True, text=True, timeout=3600)  # 1小时超时
            
            if result.returncode == 0:
                logger.info("✅ 传统机器学习训练完成")
                print("✅ 传统机器学习训练完成")
                
                # 解析输出中的最佳分数
                output_lines = result.stdout.split('\n')
                best_scores = {}
                
                for line in output_lines:
                    if 'R²' in line and '=' in line:
                        # 解析类似 "Random Forest: R² = 0.7086" 的行
                        parts = line.split(':')
                        if len(parts) >= 2:
                            model_name = parts[0].strip()
                            score_part = parts[1].strip()
                            if 'R²' in score_part and '=' in score_part:
                                try:
                                    score = float(score_part.split('=')[1].strip())
                                    best_scores[model_name] = score
                                except:
                                    pass
                
                self.results['traditional_ml'] = {
                    'success': True,
                    'best_scores': best_scores
                }
                
                # 显示结果
                if best_scores:
                    print("\n📊 传统机器学习最佳结果:")
                    for model, score in best_scores.items():
                        status = "🎉" if score > self.config['target_r2_regression'] else "📈"
                        print(f"   {status} {model}: R² = {score:.4f}")
                
                return True
            else:
                logger.error(f"传统机器学习训练失败: {result.stderr}")
                print(f"❌ 传统机器学习训练失败")
                self.results['traditional_ml'] = {'success': False, 'error': result.stderr}
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("传统机器学习训练超时")
            print("❌ 传统机器学习训练超时")
            self.results['traditional_ml'] = {'success': False, 'error': 'timeout'}
            return False
        except Exception as e:
            logger.error(f"传统机器学习训练异常: {str(e)}")
            print(f"❌ 传统机器学习训练异常: {str(e)}")
            self.results['traditional_ml'] = {'success': False, 'error': str(e)}
            return False
    
    def step_4_deep_learning_training(self) -> bool:
        """步骤4: 深度学习训练"""
        if not self.config['enable_deep_learning']:
            logger.info("⏭️  跳过深度学习训练（已禁用）")
            print("⏭️  跳过深度学习训练（已禁用）")
            return True
        
        logger.info("🧠 步骤4: 深度学习训练")
        print("\n" + "=" * 60)
        print("🧠 步骤4: 深度学习训练")
        print("=" * 60)
        
        try:
            # 运行深度学习优化脚本
            result = subprocess.run([
                sys.executable, 'run_deep_learning_optimization.py'
            ], capture_output=True, text=True, timeout=7200)  # 2小时超时
            
            if result.returncode == 0:
                logger.info("✅ 深度学习训练完成")
                print("✅ 深度学习训练完成")
                
                # 检查结果文件
                results_file = 'deep_learning_optimization_results.json'
                if os.path.exists(results_file):
                    with open(results_file, 'r', encoding='utf-8') as f:
                        dl_results = json.load(f)
                    
                    self.results['deep_learning'] = {
                        'success': True,
                        'results': dl_results
                    }
                    
                    # 显示结果
                    print("\n📊 深度学习最佳结果:")
                    for dataset_name, dataset_results in dl_results.items():
                        print(f"\n   📊 {dataset_name}:")
                        bp_score = dataset_results.get('bp_score', 0.0)
                        tcn_score = dataset_results.get('tcn_score', 0.0)
                        
                        target = self.config['target_r2_regression']
                        bp_status = "🎉" if bp_score > target else "📈"
                        tcn_status = "🎉" if tcn_score > target else "📈"
                        
                        print(f"      {bp_status} BP神经网络: {bp_score:.4f}")
                        print(f"      {tcn_status} TCN: {tcn_score:.4f}")
                else:
                    self.results['deep_learning'] = {'success': True, 'results': {}}
                
                return True
            else:
                logger.error(f"深度学习训练失败: {result.stderr}")
                print(f"❌ 深度学习训练失败")
                self.results['deep_learning'] = {'success': False, 'error': result.stderr}
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("深度学习训练超时")
            print("❌ 深度学习训练超时")
            self.results['deep_learning'] = {'success': False, 'error': 'timeout'}
            return False
        except Exception as e:
            logger.error(f"深度学习训练异常: {str(e)}")
            print(f"❌ 深度学习训练异常: {str(e)}")
            self.results['deep_learning'] = {'success': False, 'error': str(e)}
            return False
    
    def step_5_generate_final_report(self):
        """步骤5: 生成最终报告"""
        logger.info("📊 步骤5: 生成最终报告")
        print("\n" + "=" * 60)
        print("📊 步骤5: 生成最终报告")
        print("=" * 60)
        
        end_time = time.time()
        total_time = end_time - self.start_time
        
        # 生成详细报告
        report_file = os.path.join(self.config['results_dir'], 'reports', 'complete_training_report.txt')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("深度学习增强振动信号分析系统 - 完整训练报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"训练开始时间: {datetime.fromtimestamp(self.start_time).isoformat()}\n")
            f.write(f"训练结束时间: {datetime.fromtimestamp(end_time).isoformat()}\n")
            f.write(f"总训练时间: {total_time/3600:.2f} 小时\n\n")
            
            # 各步骤结果
            f.write("📋 训练步骤结果\n")
            f.write("-" * 50 + "\n")
            
            steps = [
                ('data_validation', '数据验证'),
                ('data_preprocessing', '数据预处理'),
                ('traditional_ml', '传统机器学习'),
                ('deep_learning', '深度学习')
            ]
            
            for step_key, step_name in steps:
                if step_key in self.results:
                    result = self.results[step_key]
                    status = "✅ 成功" if result.get('success', False) else "❌ 失败"
                    f.write(f"{step_name}: {status}\n")
                    
                    if not result.get('success', False) and 'error' in result:
                        f.write(f"   错误: {result['error']}\n")
                else:
                    f.write(f"{step_name}: ⏭️ 跳过\n")
            
            # 最佳模型结果
            f.write(f"\n🏆 最佳模型结果\n")
            f.write("-" * 50 + "\n")
            
            all_scores = {}
            
            # 传统机器学习结果
            if 'traditional_ml' in self.results and 'best_scores' in self.results['traditional_ml']:
                for model, score in self.results['traditional_ml']['best_scores'].items():
                    all_scores[f"{model} (传统ML)"] = score
            
            # 深度学习结果
            if 'deep_learning' in self.results and 'results' in self.results['deep_learning']:
                for dataset_name, dataset_results in self.results['deep_learning']['results'].items():
                    bp_score = dataset_results.get('bp_score', 0.0)
                    tcn_score = dataset_results.get('tcn_score', 0.0)
                    if bp_score > 0:
                        all_scores[f"BP神经网络 ({dataset_name})"] = bp_score
                    if tcn_score > 0:
                        all_scores[f"TCN ({dataset_name})"] = tcn_score
            
            # 排序并显示
            if all_scores:
                sorted_scores = sorted(all_scores.items(), key=lambda x: x[1], reverse=True)
                for model_name, score in sorted_scores:
                    target = self.config['target_r2_regression']
                    status = "🎉 达标" if score > target else "📈 待优化"
                    f.write(f"{model_name}: {score:.4f} {status}\n")
                
                # 最佳模型
                best_model, best_score = sorted_scores[0]
                f.write(f"\n🏆 最佳模型: {best_model}\n")
                f.write(f"🎯 最佳分数: {best_score:.4f}\n")
                
                if best_score > self.config['target_r2_regression']:
                    f.write("🎉 已达到目标性能!\n")
                else:
                    f.write(f"📈 距离目标还差: {self.config['target_r2_regression'] - best_score:.4f}\n")
            
            # 建议和下一步
            f.write(f"\n💡 建议和下一步\n")
            f.write("-" * 50 + "\n")
            
            if all_scores:
                best_score = max(all_scores.values())
                if best_score > self.config['target_r2_regression']:
                    f.write("✅ 模型性能已达标，可以部署到生产环境\n")
                    f.write("💡 建议进行模型集成以进一步提升性能\n")
                else:
                    f.write("📈 模型性能需要进一步优化\n")
                    f.write("💡 建议调整超参数或增加训练数据\n")
            else:
                f.write("❌ 训练未完成，请检查错误日志\n")
        
        # 保存结果JSON
        results_json_file = os.path.join(self.config['results_dir'], 'complete_training_results.json')
        with open(results_json_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"✅ 最终报告已保存: {report_file}")
        print(f"✅ 最终报告已保存: {report_file}")
        print(f"✅ 结果数据已保存: {results_json_file}")
        
        # 显示总结
        print(f"\n🎊 训练完成总结:")
        print(f"   总耗时: {total_time/3600:.2f} 小时")
        
        if all_scores:
            best_score = max(all_scores.values())
            print(f"   最佳分数: {best_score:.4f}")
            if best_score > self.config['target_r2_regression']:
                print("   🎉 已达到目标性能!")
            else:
                print(f"   📈 距离目标: {self.config['target_r2_regression'] - best_score:.4f}")
    
    def run_complete_pipeline(self):
        """运行完整训练管道"""
        print("🚀 深度学习增强振动信号分析系统 - 完整训练管道")
        print("=" * 80)
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 设置目录
        self.setup_directories()
        
        # 执行训练步骤
        steps = [
            ("数据验证", self.step_1_data_validation),
            ("数据预处理", self.step_2_data_preprocessing),
            ("传统机器学习训练", self.step_3_traditional_ml_training),
            ("深度学习训练", self.step_4_deep_learning_training)
        ]
        
        for step_name, step_func in steps:
            try:
                success = step_func()
                if not success:
                    logger.error(f"步骤失败: {step_name}")
                    print(f"\n❌ 管道在 {step_name} 步骤失败")
                    break
            except Exception as e:
                logger.error(f"步骤异常: {step_name} - {str(e)}")
                print(f"\n❌ 管道在 {step_name} 步骤异常: {str(e)}")
                break
        
        # 生成最终报告
        self.step_5_generate_final_report()

def main():
    """主函数"""
    # 创建配置
    config = {
        'raw_data_dir': './raw_data',
        'training_data_dir': './training_datasets',
        'models_dir': './models',
        'results_dir': './results',
        'enable_gpu': True,
        'enable_deep_learning': True,
        'n_trials': 25,  # 减少试验次数以节省时间
        'cv_folds': 5,
        'validation_threshold': 0.7,  # 降低验证阈值
        'target_r2_regression': 0.75,
        'target_accuracy_classification': 0.85
    }
    
    # 创建并运行管道
    pipeline = CompleteTrainingPipeline(config)
    pipeline.run_complete_pipeline()

if __name__ == "__main__":
    main()
