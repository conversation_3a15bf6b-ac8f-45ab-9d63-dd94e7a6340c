#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单直接的中文字体修复方案
解决matplotlib中文字体显示为方框的问题

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
import os
import numpy as np

def fix_chinese_font():
    """修复中文字体显示问题"""
    print("🔧 修复中文字体显示问题...")
    
    # 根据操作系统设置字体
    system = platform.system()
    
    if system == "Windows":
        # Windows系统字体配置
        fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong']
        print("   🖥️  检测到Windows系统")
    elif system == "Darwin":  # macOS
        # macOS系统字体配置
        fonts = ['PingFang SC', 'Hiragino Sans GB', 'Heiti SC', 'STHeiti', 'STSong']
        print("   🍎 检测到macOS系统")
    else:  # Linux
        # Linux系统字体配置
        fonts = ['Noto Sans CJK SC', 'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Source Han Sans SC']
        print("   🐧 检测到Linux系统")
    
    # 添加通用备用字体
    fonts.extend(['DejaVu Sans', 'Arial Unicode MS', 'sans-serif'])
    
    # 应用字体配置
    plt.rcParams['font.sans-serif'] = fonts
    plt.rcParams['axes.unicode_minus'] = False
    
    # 设置字体大小
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.titlesize'] = 16
    plt.rcParams['axes.labelsize'] = 14
    plt.rcParams['xtick.labelsize'] = 12
    plt.rcParams['ytick.labelsize'] = 12
    plt.rcParams['legend.fontsize'] = 12
    
    print(f"   ✅ 字体配置已应用: {fonts[0]}")
    return fonts[0]

def test_chinese_display():
    """测试中文字体显示效果"""
    print("🧪 测试中文字体显示效果...")
    
    try:
        # 创建测试图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
        
        # 生成测试数据
        x = np.linspace(0, 10, 100)
        
        # 子图1：正弦波
        y1 = np.sin(x)
        ax1.plot(x, y1, 'b-', linewidth=2, label='正弦波')
        ax1.set_title('振动信号分析', fontsize=14, fontweight='bold')
        ax1.set_xlabel('时间 (秒)', fontsize=12)
        ax1.set_ylabel('幅值', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 子图2：余弦波
        y2 = np.cos(x)
        ax2.plot(x, y2, 'r-', linewidth=2, label='余弦波')
        ax2.set_title('车辆轴重检测', fontsize=14, fontweight='bold')
        ax2.set_xlabel('时间 (秒)', fontsize=12)
        ax2.set_ylabel('幅值', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 子图3：衰减振荡
        y3 = np.sin(x) * np.exp(-x/5)
        ax3.plot(x, y3, 'g-', linewidth=2, label='衰减振荡')
        ax3.set_title('机器学习模型', fontsize=14, fontweight='bold')
        ax3.set_xlabel('时间 (秒)', fontsize=12)
        ax3.set_ylabel('幅值', fontsize=12)
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 子图4：随机信号
        y4 = np.random.normal(0, 0.5, len(x)) + np.sin(x/2)
        ax4.plot(x, y4, 'm-', linewidth=1, alpha=0.7, label='噪声信号')
        ax4.set_title('数据可视化', fontsize=14, fontweight='bold')
        ax4.set_xlabel('时间 (秒)', fontsize=12)
        ax4.set_ylabel('幅值', fontsize=12)
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # 设置总标题
        plt.suptitle('中文字体显示测试 - 振动信号分析系统', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # 保存测试图表
        save_path = 'chinese_font_test_result.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"   ✅ 测试图表已保存: {save_path}")
        
        # 检查文件是否成功生成
        if os.path.exists(save_path):
            file_size = os.path.getsize(save_path) / 1024  # KB
            print(f"   📁 文件大小: {file_size:.1f} KB")
            
            if file_size > 50:
                print("   ✅ 中文字体显示测试通过")
                return True
            else:
                print("   ⚠️  文件大小异常，可能存在显示问题")
                return False
        else:
            print("   ❌ 测试图表生成失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def update_main_system():
    """更新主系统的字体配置"""
    print("🔄 更新主系统字体配置...")
    
    # 字体配置代码
    font_config_code = '''
# 中文字体配置 - 自动添加
import matplotlib.pyplot as plt
import platform

def setup_chinese_fonts():
    """设置中文字体"""
    system = platform.system()
    
    if system == "Windows":
        fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong']
    elif system == "Darwin":  # macOS
        fonts = ['PingFang SC', 'Hiragino Sans GB', 'Heiti SC', 'STHeiti', 'STSong']
    else:  # Linux
        fonts = ['Noto Sans CJK SC', 'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Source Han Sans SC']
    
    fonts.extend(['DejaVu Sans', 'Arial Unicode MS', 'sans-serif'])
    
    plt.rcParams['font.sans-serif'] = fonts
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.titlesize'] = 16
    plt.rcParams['axes.labelsize'] = 14
    plt.rcParams['xtick.labelsize'] = 12
    plt.rcParams['ytick.labelsize'] = 12
    plt.rcParams['legend.fontsize'] = 12

# 自动执行字体配置
setup_chinese_fonts()
'''
    
    # 创建字体配置文件
    config_file = 'chinese_font_config.py'
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(font_config_code)
        print(f"   ✅ 创建字体配置文件: {config_file}")
    except Exception as e:
        print(f"   ❌ 创建配置文件失败: {e}")
        return False
    
    # 更新主程序文件
    main_files = [
        'unified_vibration_analysis.py',
        'advanced_visualization.py',
        'process_visualization.py',
        'unified_visualization_manager.py'
    ]
    
    updated_count = 0
    
    for file_name in main_files:
        if os.path.exists(file_name):
            try:
                # 读取文件内容
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否已经有字体配置
                if 'chinese_font_config' not in content and 'font.sans-serif' not in content[:1000]:
                    # 在文件开头添加字体配置导入
                    lines = content.split('\n')
                    
                    # 找到合适的插入位置
                    insert_pos = 0
                    for i, line in enumerate(lines):
                        if line.strip().startswith('import ') or line.strip().startswith('from '):
                            insert_pos = i + 1
                        elif line.strip() and not line.strip().startswith('#'):
                            break
                    
                    # 插入字体配置导入
                    lines.insert(insert_pos, 'import chinese_font_config  # 中文字体配置')
                    
                    # 写回文件
                    with open(file_name, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(lines))
                    
                    print(f"   ✅ 已更新: {file_name}")
                    updated_count += 1
                else:
                    print(f"   ✓ 已配置: {file_name}")
                    
            except Exception as e:
                print(f"   ❌ 更新失败 {file_name}: {e}")
    
    print(f"   📊 更新了 {updated_count} 个文件")
    return updated_count >= 0

def main():
    """主函数"""
    print("🚀 简单中文字体修复工具")
    print("=" * 40)
    
    # 修复字体配置
    selected_font = fix_chinese_font()
    
    # 测试字体显示
    if test_chinese_display():
        print("✅ 字体显示测试通过")
    else:
        print("⚠️  字体显示可能存在问题")
    
    # 更新主系统
    if update_main_system():
        print("✅ 主系统字体配置已更新")
    else:
        print("⚠️  主系统更新可能存在问题")
    
    print("\n" + "=" * 40)
    print("✅ 字体修复完成！")
    print(f"📝 使用字体: {selected_font}")
    print("💡 请查看生成的测试图表验证效果")
    print("🔄 如果问题仍然存在，请重启Python环境")
    
    return True

if __name__ == "__main__":
    main()
