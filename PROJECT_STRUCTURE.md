# 🚀 振动信号分析系统 - 清理后的项目结构

## 📁 核心文件结构

```
振动信号分析系统/
├── 🚀 unified_vibration_analysis.py    # 主程序 (替代原quick_start.py)
├── 🤖 unified_ml_models.py             # 统一模型模块 (XGBoost+CNN-LSTM+BP)
├── 📦 install_requirements.py          # 依赖安装脚本
├── 📖 README_UNIFIED.md                # 使用说明文档
├── 📋 requirements.txt                 # 依赖包清单
├── 📊 combined_features.csv            # 提取的特征数据
├── 📁 core/                            # 核心特征提取模块
│   ├── experimental_data_processor.py  # 实验数据处理器
│   └── vibration_signal_analysis_framework.py
├── 📁 data/                            # 原始振动信号数据
│   ├── 2吨/双轴/40km_h/
│   ├── 25吨/三轴/60km_h/
│   └── ...
├── 📁 training_datasets/               # 训练数据集
│   ├── speed_regression.csv
│   ├── load_regression.csv
│   └── dataset_info.json
├── 📁 ml/                              # 机器学习相关文件
│   └── combined_features_clean.csv
├── 📁 models/                          # 训练好的模型存储
├── 📁 venv/                            # Python虚拟环境
└── 📁 实验数据目录/                     # 处理后的实验数据
    ├── 2吨_双轴_40.0kmh_实验1/
    ├── 25吨_三轴_50.0kmh_实验2/
    └── ...
```

## 🎯 使用方法

### 1. 安装依赖
```bash
python install_requirements.py
```

### 2. 运行分析
```bash
python unified_vibration_analysis.py
```

## 🔧 核心功能

### 主程序 (`unified_vibration_analysis.py`)
- ✅ 环境检查和配置
- ✅ 自动数据发现和加载
- ✅ 特征提取或加载现有特征
- ✅ 数据预处理和清洗
- ✅ 多模型训练 (XGBoost + CNN-LSTM + BP)
- ✅ 结果分析和报告生成

### 模型模块 (`unified_ml_models.py`)
- 🤖 **XGBoost**: 梯度提升决策树，支持GPU加速
- 🧠 **CNN-LSTM**: 卷积神经网络+长短期记忆网络
- 🔗 **BP神经网络**: 多层感知机，批量归一化+Dropout

### 特征提取 (`core/experimental_data_processor.py`)
- 📊 时域特征提取
- 📈 频域特征提取
- 🔄 自动数据处理和合并

## 📊 支持的任务

1. **速度预测** (回归)
   - 目标: R² > 0.75
   - 输入: 振动信号特征
   - 输出: 车辆速度 (km/h)

2. **轴重预测** (回归)
   - 目标: R² > 0.75
   - 输入: 振动信号特征
   - 输出: 轴重 (吨)

3. **轴型分类** (分类)
   - 目标: 准确率 > 0.85
   - 输入: 振动信号特征
   - 输出: 轴型类别

## 🗑️ 已删除的冗余文件

### 旧版本主程序
- ❌ quick_start.py (已被unified_vibration_analysis.py替代)
- ❌ quick_start_demo.py
- ❌ run_complete_training.py
- ❌ run_deep_learning_optimization.py
- ❌ setup_real_data_training.py

### 功能重复的脚本
- ❌ data_validation.py (功能已集成)
- ❌ data_preprocessing.py (功能已集成)
- ❌ migrate_real_data.py (一次性使用)
- ❌ deploy_models.py (功能可集成)

### 测试和诊断脚本
- ❌ test_*.py 系列文件
- ❌ check_*.py 系列文件
- ❌ diagnose_*.py 系列文件
- ❌ fix_*.py 系列文件
- ❌ verify_*.py 系列文件

### 临时文件和报告
- ❌ 各种 .txt 报告文件
- ❌ 各种 .log 文件
- ❌ 各种 .json 结果文件

### 冗余目录
- ❌ archive/ (归档目录)
- ❌ examples/ (示例目录)
- ❌ tools/ (工具目录)
- ❌ results/ (临时结果)
- ❌ docs/ (文档目录)

## 📈 清理统计

- ✅ **删除了 72 个文件**
- ✅ **删除了 9 个目录**
- ✅ **保留了所有核心功能**
- ✅ **简化了项目结构**

## 🎉 清理效果

### 清理前
- 📁 100+ 文件，结构混乱
- 🔄 多个版本的相似功能
- 📝 大量测试和临时文件
- 🤔 使用方法不明确

### 清理后
- 📁 精简到核心文件
- 🎯 单一入口点 (unified_vibration_analysis.py)
- 🔧 统一的模型框架
- 📖 清晰的使用指南

## 🚀 下一步使用

1. **确保环境准备**:
   ```bash
   python install_requirements.py
   ```

2. **运行统一分析系统**:
   ```bash
   python unified_vibration_analysis.py
   ```

3. **查看结果**:
   - `combined_features.csv` - 特征数据
   - `analysis_report.md` - 分析报告
   - `model_*.pkl` / `model_*.h5` - 训练好的模型

## 💡 优势

- 🎯 **简化操作**: 一个命令完成所有分析
- 🔧 **统一架构**: 所有算法集成在统一框架
- 📊 **自动化**: 自动检测环境、数据、GPU等
- 🚀 **高性能**: 支持GPU加速和并行计算
- 📖 **易维护**: 清晰的代码结构和文档

现在您的振动信号分析系统已经完全重构和清理完成！
