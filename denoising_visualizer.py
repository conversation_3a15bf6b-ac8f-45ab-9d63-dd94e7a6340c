#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
降噪效果可视化模块
生成学术质量的降噪对比图表

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import signal
import os
import warnings
warnings.filterwarnings('ignore')

class DenoisingVisualizer:
    """降噪效果可视化器"""
    
    def __init__(self, output_dir='denoising_visualizations', dpi=330):
        """
        初始化可视化器
        
        参数:
        output_dir: 输出目录
        dpi: 图像分辨率
        """
        self.output_dir = output_dir
        self.dpi = dpi
        self.setup_style()
        self.ensure_output_dir()
        
    def setup_style(self):
        """设置绘图样式"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置学术风格
        plt.style.use('seaborn-v0_8-whitegrid')
        plt.rcParams.update({
            'figure.dpi': self.dpi,
            'savefig.dpi': self.dpi,
            'font.size': 12,
            'axes.titlesize': 16,
            'axes.labelsize': 14,
            'xtick.labelsize': 12,
            'ytick.labelsize': 12,
            'legend.fontsize': 12,
            'figure.titlesize': 18,
            'lines.linewidth': 1.5,
            'grid.alpha': 0.3
        })
        
    def ensure_output_dir(self):
        """确保输出目录存在"""
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'chinese'), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'english'), exist_ok=True)
        
    def plot_signal_comparison(self, original_signal, denoised_signals, fs=1000, 
                             signal_name="signal", time_range=None, language='chinese'):
        """
        绘制信号对比图
        
        参数:
        original_signal: 原始信号
        denoised_signals: 降噪信号字典
        fs: 采样频率
        signal_name: 信号名称
        time_range: 时间范围 [start, end] (秒)
        language: 语言 ('chinese', 'english')
        """
        # 设置语言
        if language == 'chinese':
            title = f"{signal_name} - 降噪效果对比"
            xlabel = "时间 (秒)"
            ylabel = "幅值"
            original_label = "原始信号"
        else:
            title = f"{signal_name} - Denoising Comparison"
            xlabel = "Time (s)"
            ylabel = "Amplitude"
            original_label = "Original Signal"
        
        # 创建时间轴
        time_axis = np.arange(len(original_signal)) / fs
        
        # 应用时间范围
        if time_range:
            start_idx = int(time_range[0] * fs)
            end_idx = int(time_range[1] * fs)
            time_axis = time_axis[start_idx:end_idx]
            original_signal = original_signal[start_idx:end_idx]
            denoised_signals = {k: v[start_idx:end_idx] for k, v in denoised_signals.items()}
        
        # 选择最佳的几种方法进行对比
        best_methods = list(denoised_signals.keys())[:4]  # 最多显示4种方法
        
        fig, axes = plt.subplots(len(best_methods) + 1, 1, figsize=(12, 2.5 * (len(best_methods) + 1)))
        if len(best_methods) == 0:
            axes = [axes]
        
        # 绘制原始信号
        axes[0].plot(time_axis, original_signal, 'b-', alpha=0.8, label=original_label)
        axes[0].set_title(original_label, fontweight='bold')
        axes[0].set_ylabel(ylabel)
        axes[0].grid(True, alpha=0.3)
        axes[0].legend()
        
        # 绘制降噪信号
        colors = ['red', 'green', 'orange', 'purple']
        for i, (method_name, denoised_signal) in enumerate(zip(best_methods, 
                                                              [denoised_signals[m] for m in best_methods])):
            axes[i+1].plot(time_axis, denoised_signal, color=colors[i % len(colors)], 
                          alpha=0.8, label=method_name)
            axes[i+1].set_title(method_name, fontweight='bold')
            axes[i+1].set_ylabel(ylabel)
            axes[i+1].grid(True, alpha=0.3)
            axes[i+1].legend()
        
        # 设置最后一个子图的x轴标签
        axes[-1].set_xlabel(xlabel)
        
        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # 保存图像
        save_path = os.path.join(self.output_dir, language, f'{signal_name}_denoising_comparison.png')
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        print(f"  ✅ 信号对比图已保存: {save_path}")
        
    def plot_frequency_analysis(self, original_signal, denoised_signals, fs=1000, 
                              signal_name="signal", language='chinese'):
        """
        绘制频域分析图
        
        参数:
        original_signal: 原始信号
        denoised_signals: 降噪信号字典
        fs: 采样频率
        signal_name: 信号名称
        language: 语言
        """
        # 设置语言
        if language == 'chinese':
            title = f"{signal_name} - 频域分析"
            xlabel = "频率 (Hz)"
            ylabel = "功率谱密度 (dB)"
            original_label = "原始信号"
        else:
            title = f"{signal_name} - Frequency Analysis"
            xlabel = "Frequency (Hz)"
            ylabel = "Power Spectral Density (dB)"
            original_label = "Original Signal"
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # 计算原始信号的功率谱
        freqs, original_psd = signal.welch(original_signal, fs=fs, nperseg=min(1024, len(original_signal)//4))
        
        # 绘制原始信号功率谱
        ax1.semilogy(freqs, original_psd, 'b-', linewidth=2, label=original_label, alpha=0.8)
        
        # 选择最佳的几种方法
        best_methods = list(denoised_signals.keys())[:4]
        colors = ['red', 'green', 'orange', 'purple']
        
        for i, method_name in enumerate(best_methods):
            denoised_signal = denoised_signals[method_name]
            _, denoised_psd = signal.welch(denoised_signal, fs=fs, nperseg=min(1024, len(denoised_signal)//4))
            ax1.semilogy(freqs, denoised_psd, color=colors[i % len(colors)], 
                        linewidth=1.5, label=method_name, alpha=0.7)
        
        ax1.set_xlabel(xlabel)
        ax1.set_ylabel(ylabel)
        ax1.set_title(f"{title} - 功率谱密度对比" if language == 'chinese' else f"{title} - PSD Comparison", 
                     fontweight='bold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_xlim(0, min(200, fs/2))  # 关注0-200Hz范围
        
        # 绘制频率响应对比（归一化）
        original_psd_norm = original_psd / np.max(original_psd)
        ax2.plot(freqs, 20 * np.log10(original_psd_norm + 1e-10), 'b-', 
                linewidth=2, label=original_label, alpha=0.8)
        
        for i, method_name in enumerate(best_methods):
            denoised_signal = denoised_signals[method_name]
            _, denoised_psd = signal.welch(denoised_signal, fs=fs, nperseg=min(1024, len(denoised_signal)//4))
            denoised_psd_norm = denoised_psd / np.max(denoised_psd)
            ax2.plot(freqs, 20 * np.log10(denoised_psd_norm + 1e-10), 
                    color=colors[i % len(colors)], linewidth=1.5, label=method_name, alpha=0.7)
        
        ax2.set_xlabel(xlabel)
        ax2.set_ylabel("归一化功率 (dB)" if language == 'chinese' else "Normalized Power (dB)")
        ax2.set_title("归一化频率响应对比" if language == 'chinese' else "Normalized Frequency Response Comparison", 
                     fontweight='bold')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_xlim(0, min(200, fs/2))
        
        plt.tight_layout()
        
        # 保存图像
        save_path = os.path.join(self.output_dir, language, f'{signal_name}_frequency_analysis.png')
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        print(f"  ✅ 频域分析图已保存: {save_path}")
        
    def plot_evaluation_results(self, evaluation_summary, language='chinese'):
        """
        绘制评估结果图表
        
        参数:
        evaluation_summary: 评估结果摘要
        language: 语言
        """
        if not evaluation_summary:
            print("  ⚠️  无评估数据，跳过评估结果图表")
            return
        
        # 设置语言
        if language == 'chinese':
            title = "降噪方法性能评估"
            metrics_labels = {
                'composite_score': '综合评分',
                'snr_improvement': 'SNR改善 (dB)',
                'signal_fidelity': '信号保真度',
                'frequency_preservation': '频域保持度',
                'peak_preservation': '峰值保持度'
            }
        else:
            title = "Denoising Methods Performance Evaluation"
            metrics_labels = {
                'composite_score': 'Composite Score',
                'snr_improvement': 'SNR Improvement (dB)',
                'signal_fidelity': 'Signal Fidelity',
                'frequency_preservation': 'Frequency Preservation',
                'peak_preservation': 'Peak Preservation'
            }
        
        # 准备数据
        methods = list(evaluation_summary.keys())
        metrics = ['composite_score', 'snr_improvement', 'signal_fidelity', 
                  'frequency_preservation', 'peak_preservation']
        
        # 创建数据矩阵
        data_matrix = []
        for method in methods:
            row = []
            for metric in metrics:
                value = evaluation_summary[method].get(metric, 0)
                # 对SNR改善进行归一化处理
                if metric == 'snr_improvement':
                    value = max(0, min(1, (value + 10) / 20))  # -10到10dB映射到0-1
                row.append(value)
            data_matrix.append(row)
        
        data_matrix = np.array(data_matrix)
        
        # 创建热力图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # 热力图
        im = ax1.imshow(data_matrix, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
        ax1.set_xticks(range(len(metrics)))
        ax1.set_xticklabels([metrics_labels[m] for m in metrics], rotation=45, ha='right')
        ax1.set_yticks(range(len(methods)))
        ax1.set_yticklabels(methods)
        ax1.set_title("性能热力图" if language == 'chinese' else "Performance Heatmap", 
                     fontweight='bold')
        
        # 添加数值标注
        for i in range(len(methods)):
            for j in range(len(metrics)):
                text = ax1.text(j, i, f'{data_matrix[i, j]:.3f}', 
                              ha="center", va="center", color="black", fontsize=10)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax1)
        cbar.set_label('性能分数' if language == 'chinese' else 'Performance Score')
        
        # 综合评分柱状图
        composite_scores = [evaluation_summary[method]['composite_score'] for method in methods]
        bars = ax2.bar(range(len(methods)), composite_scores, 
                      color=plt.cm.RdYlGn([score for score in composite_scores]))
        
        ax2.set_xlabel('降噪方法' if language == 'chinese' else 'Denoising Methods')
        ax2.set_ylabel('综合评分' if language == 'chinese' else 'Composite Score')
        ax2.set_title('综合评分对比' if language == 'chinese' else 'Composite Score Comparison', 
                     fontweight='bold')
        ax2.set_xticks(range(len(methods)))
        ax2.set_xticklabels(methods, rotation=45, ha='right')
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标注
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.3f}', ha='center', va='bottom')
        
        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # 保存图像
        save_path = os.path.join(self.output_dir, language, 'denoising_evaluation_results.png')
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        print(f"  ✅ 评估结果图已保存: {save_path}")

    def plot_parameter_optimization(self, optimization_results, method_name, language='chinese'):
        """
        绘制参数优化结果图

        参数:
        optimization_results: 优化结果
        method_name: 方法名称
        language: 语言
        """
        # 设置语言
        if language == 'chinese':
            title = f"{method_name} - 参数优化结果"
            xlabel = "参数组合"
            ylabel = "性能分数"
        else:
            title = f"{method_name} - Parameter Optimization Results"
            xlabel = "Parameter Combinations"
            ylabel = "Performance Score"

        # 这里可以根据具体的优化结果数据结构来实现
        # 暂时创建一个示例图表
        fig, ax = plt.subplots(figsize=(10, 6))

        # 示例数据
        param_combinations = [f"组合{i+1}" if language == 'chinese' else f"Combo{i+1}"
                            for i in range(10)]
        scores = np.random.rand(10) * 0.3 + 0.7  # 示例分数

        bars = ax.bar(param_combinations, scores, color='skyblue', alpha=0.7)

        # 标记最佳参数
        best_idx = np.argmax(scores)
        bars[best_idx].set_color('red')
        bars[best_idx].set_alpha(0.8)

        ax.set_xlabel(xlabel)
        ax.set_ylabel(ylabel)
        ax.set_title(title, fontweight='bold')
        ax.grid(True, alpha=0.3)

        # 添加数值标注
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.3f}', ha='center', va='bottom')

        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存图像
        save_path = os.path.join(self.output_dir, language, f'{method_name}_parameter_optimization.png')
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()

        print(f"  ✅ 参数优化图已保存: {save_path}")

    def plot_snr_improvement_comparison(self, evaluation_summary, language='chinese'):
        """
        绘制SNR改善对比图

        参数:
        evaluation_summary: 评估结果摘要
        language: 语言
        """
        if not evaluation_summary:
            return

        # 设置语言
        if language == 'chinese':
            title = "信噪比改善效果对比"
            xlabel = "降噪方法"
            ylabel = "SNR改善 (dB)"
        else:
            title = "SNR Improvement Comparison"
            xlabel = "Denoising Methods"
            ylabel = "SNR Improvement (dB)"

        methods = list(evaluation_summary.keys())
        snr_improvements = [evaluation_summary[method]['snr_improvement'] for method in methods]

        # 按SNR改善排序
        sorted_data = sorted(zip(methods, snr_improvements), key=lambda x: x[1], reverse=True)
        methods, snr_improvements = zip(*sorted_data)

        fig, ax = plt.subplots(figsize=(12, 8))

        # 创建颜色映射
        colors = plt.cm.RdYlGn([max(0, min(1, (snr + 10) / 20)) for snr in snr_improvements])

        bars = ax.bar(range(len(methods)), snr_improvements, color=colors, alpha=0.8)

        # 添加零线
        ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)

        ax.set_xlabel(xlabel)
        ax.set_ylabel(ylabel)
        ax.set_title(title, fontweight='bold')
        ax.set_xticks(range(len(methods)))
        ax.set_xticklabels(methods, rotation=45, ha='right')
        ax.grid(True, alpha=0.3)

        # 添加数值标注
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.1 if height >= 0 else height - 0.3,
                   f'{height:.2f}', ha='center', va='bottom' if height >= 0 else 'top')

        plt.tight_layout()

        # 保存图像
        save_path = os.path.join(self.output_dir, language, 'snr_improvement_comparison.png')
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()

        print(f"  ✅ SNR改善对比图已保存: {save_path}")

    def plot_processing_time_comparison(self, efficiency_results, language='chinese'):
        """
        绘制处理时间对比图

        参数:
        efficiency_results: 效率测试结果
        language: 语言
        """
        if not efficiency_results:
            return

        # 设置语言
        if language == 'chinese':
            title = "降噪方法处理效率对比"
            xlabel = "降噪方法"
            ylabel1 = "处理时间 (秒)"
            ylabel2 = "吞吐量 (样本/秒)"
        else:
            title = "Processing Efficiency Comparison"
            xlabel = "Denoising Methods"
            ylabel1 = "Processing Time (s)"
            ylabel2 = "Throughput (samples/s)"

        methods = list(efficiency_results.keys())
        processing_times = [efficiency_results[method]['processing_time'] for method in methods]
        throughputs = [efficiency_results[method]['throughput'] for method in methods]

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

        # 处理时间对比
        bars1 = ax1.bar(methods, processing_times, color='lightcoral', alpha=0.7)
        ax1.set_ylabel(ylabel1)
        ax1.set_title(f"{title} - 处理时间" if language == 'chinese' else f"{title} - Processing Time",
                     fontweight='bold')
        ax1.grid(True, alpha=0.3)

        # 添加数值标注
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{height:.4f}', ha='center', va='bottom')

        # 吞吐量对比
        bars2 = ax2.bar(methods, throughputs, color='lightblue', alpha=0.7)
        ax2.set_xlabel(xlabel)
        ax2.set_ylabel(ylabel2)
        ax2.set_title(f"{title} - 吞吐量" if language == 'chinese' else f"{title} - Throughput",
                     fontweight='bold')
        ax2.grid(True, alpha=0.3)

        # 添加数值标注
        for bar in bars2:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{height:.0f}', ha='center', va='bottom')

        # 旋转x轴标签
        for ax in [ax1, ax2]:
            ax.tick_params(axis='x', rotation=45)

        plt.tight_layout()

        # 保存图像
        save_path = os.path.join(self.output_dir, language, 'processing_efficiency_comparison.png')
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()

        print(f"  ✅ 处理效率对比图已保存: {save_path}")

    def generate_comprehensive_visualization_report(self, original_signal, denoising_results,
                                                  evaluation_summary, fs=1000, signal_name="signal"):
        """
        生成综合可视化报告

        参数:
        original_signal: 原始信号
        denoising_results: 降噪结果
        evaluation_summary: 评估摘要
        fs: 采样频率
        signal_name: 信号名称
        """
        print(f"🎨 生成 {signal_name} 的综合降噪可视化报告...")

        # 生成中英文版本
        for language in ['chinese', 'english']:
            print(f"  📊 生成{language}版本图表...")

            # 1. 信号对比图
            self.plot_signal_comparison(original_signal, denoising_results, fs, signal_name, language=language)

            # 2. 频域分析图
            self.plot_frequency_analysis(original_signal, denoising_results, fs, signal_name, language=language)

            # 3. 评估结果图
            if evaluation_summary:
                self.plot_evaluation_results(evaluation_summary, language=language)
                self.plot_snr_improvement_comparison(evaluation_summary, language=language)

        print(f"  ✅ {signal_name} 综合可视化报告生成完成")

        # 生成图表索引文件
        self._generate_visualization_index()

    def _generate_visualization_index(self):
        """生成可视化图表索引"""
        index_content_cn = """# 降噪效果可视化图表索引

## 中文版图表
- `*_denoising_comparison.png` - 降噪效果对比图
- `*_frequency_analysis.png` - 频域分析图
- `denoising_evaluation_results.png` - 评估结果图
- `snr_improvement_comparison.png` - SNR改善对比图
- `processing_efficiency_comparison.png` - 处理效率对比图

## 图表说明
1. **降噪效果对比图**: 显示原始信号与各种降噪方法处理后的时域波形对比
2. **频域分析图**: 显示降噪前后的功率谱密度变化
3. **评估结果图**: 综合评估各种降噪方法的性能指标
4. **SNR改善对比图**: 对比各种方法的信噪比改善效果
5. **处理效率对比图**: 对比各种方法的计算效率

## 使用建议
- 查看降噪效果对比图了解时域特征保持情况
- 查看频域分析图了解频率特征保持情况
- 查看评估结果图选择最适合的降噪方法
"""

        index_content_en = """# Denoising Visualization Charts Index

## English Version Charts
- `*_denoising_comparison.png` - Denoising Effect Comparison
- `*_frequency_analysis.png` - Frequency Domain Analysis
- `denoising_evaluation_results.png` - Evaluation Results
- `snr_improvement_comparison.png` - SNR Improvement Comparison
- `processing_efficiency_comparison.png` - Processing Efficiency Comparison

## Chart Descriptions
1. **Denoising Effect Comparison**: Shows time-domain waveform comparison between original and denoised signals
2. **Frequency Domain Analysis**: Shows power spectral density changes before and after denoising
3. **Evaluation Results**: Comprehensive performance evaluation of different denoising methods
4. **SNR Improvement Comparison**: Compares SNR improvement effects of different methods
5. **Processing Efficiency Comparison**: Compares computational efficiency of different methods

## Usage Recommendations
- Check denoising comparison charts for time-domain feature preservation
- Check frequency analysis charts for frequency feature preservation
- Check evaluation results charts to select the most suitable denoising method
"""

        # 保存索引文件
        try:
            with open(os.path.join(self.output_dir, 'chinese', 'visualization_index.md'), 'w', encoding='utf-8') as f:
                f.write(index_content_cn)

            with open(os.path.join(self.output_dir, 'english', 'visualization_index.md'), 'w', encoding='utf-8') as f:
                f.write(index_content_en)

            print(f"  ✅ 可视化索引文件已生成")

        except Exception as e:
            print(f"  ⚠️  生成索引文件失败: {str(e)}")
