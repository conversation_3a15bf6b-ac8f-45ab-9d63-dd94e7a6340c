#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复unified_vibration_analysis.py数据处理问题
确保系统能够正确处理两种格式的振动数据

作者: AI Assistant
日期: 2024-12-22
"""

import os
import pandas as pd
import numpy as np
import json
from pathlib import Path

def fix_data_integration():
    """修复数据集成问题"""
    print("🛠️  修复振动信号分析系统数据处理问题")
    print("="*80)
    
    # 步骤1：检查现有数据状态
    print("\n📋 步骤1：检查现有数据状态")
    print("-"*50)
    
    current_features = None
    expanded_features = None
    
    # 读取当前特征文件
    if os.path.exists('combined_features.csv'):
        current_features = pd.read_csv('combined_features.csv')
        print(f"✅ 当前特征文件: {current_features.shape}")
        print(f"   样本数: {len(current_features)}")
        print(f"   特征数: {current_features.shape[1]}")
    else:
        print("❌ 当前特征文件不存在")
        return False
    
    # 读取扩展特征文件
    if os.path.exists('combined_features_expanded.csv'):
        expanded_features = pd.read_csv('combined_features_expanded.csv')
        print(f"✅ 扩展特征文件: {expanded_features.shape}")
        print(f"   样本数: {len(expanded_features)}")
        print(f"   特征数: {expanded_features.shape[1]}")
    else:
        print("❌ 扩展特征文件不存在")
        return False
    
    # 步骤2：分析数据差异
    print("\n📊 步骤2：分析数据差异")
    print("-"*50)
    
    # 检查扩展报告
    expansion_report = None
    if os.path.exists('enhanced_data_expansion_report.json'):
        with open('enhanced_data_expansion_report.json', 'r', encoding='utf-8') as f:
            expansion_report = json.load(f)
        
        summary = expansion_report.get('expansion_summary', {})
        print(f"📋 扩展报告摘要:")
        print(f"   原始样本数: {summary.get('original_samples', 0)}")
        print(f"   新增样本数: {summary.get('new_samples', 0)}")
        print(f"   总样本数: {summary.get('total_samples', 0)}")
        print(f"   目标达成: {summary.get('target_achieved', False)}")
    
    # 步骤3：重新处理新格式数据
    print("\n🔄 步骤3：重新处理新格式数据")
    print("-"*50)
    
    try:
        from enhanced_data_expansion_processor import EnhancedDataExpansionProcessor
        
        # 强制重新处理
        processor = EnhancedDataExpansionProcessor()
        print("🚀 启动增强数据扩展处理器...")
        
        # 强制重新处理所有文件
        results = processor.expand_dataset(force_reprocess=True)
        
        if results and results.get('expansion_summary', {}).get('total_samples', 0) > 0:
            print(f"✅ 数据扩展处理完成")
            
            # 重新读取扩展后的数据
            if os.path.exists('combined_features_expanded.csv'):
                new_expanded_features = pd.read_csv('combined_features_expanded.csv')
                print(f"   新扩展数据: {new_expanded_features.shape}")
                
                # 步骤4：智能数据合并
                print("\n🔗 步骤4：智能数据合并")
                print("-"*50)
                
                # 分离新格式和传统格式数据
                legacy_data = current_features.copy()
                new_format_data = None
                
                # 从扩展数据中提取新格式数据
                original_samples = results.get('expansion_summary', {}).get('original_samples', 0)
                total_samples = len(new_expanded_features)
                
                if total_samples > original_samples:
                    # 提取新增的样本
                    new_format_data = new_expanded_features.tail(total_samples - original_samples).copy()
                    print(f"   提取新格式数据: {new_format_data.shape}")
                else:
                    # 如果没有明显的新增样本，尝试其他方法
                    print("   ⚠️  无法明确区分新旧数据，使用替代方法...")
                    
                    # 检查是否有明显的特征差异
                    if new_expanded_features.shape[1] > current_features.shape[1]:
                        # 扩展数据有更多特征，可能包含新格式数据
                        new_format_data = new_expanded_features.copy()
                        print(f"   使用完整扩展数据: {new_format_data.shape}")
                
                # 添加数据源标识
                if new_format_data is not None:
                    legacy_data['data_source'] = 'legacy_format'
                    new_format_data['data_source'] = 'new_format'
                    
                    # 合并数据
                    combined_data = merge_dataframes_intelligently([legacy_data, new_format_data])
                    
                    if combined_data is not None:
                        print(f"✅ 数据合并成功: {combined_data.shape}")
                        
                        # 验证数据源分布
                        if 'data_source' in combined_data.columns:
                            source_counts = combined_data['data_source'].value_counts()
                            print(f"   📊 数据源分布:")
                            for source, count in source_counts.items():
                                print(f"      {source}: {count} 样本")
                        
                        # 保存修复后的数据
                        combined_data.to_csv('combined_features_fixed.csv', index=False)
                        print(f"   💾 已保存修复数据: combined_features_fixed.csv")
                        
                        # 备份原文件并替换
                        if os.path.exists('combined_features.csv'):
                            os.rename('combined_features.csv', 'combined_features_backup.csv')
                            print(f"   📁 已备份原文件: combined_features_backup.csv")
                        
                        combined_data.to_csv('combined_features.csv', index=False)
                        print(f"   🔄 已更新主特征文件: combined_features.csv")
                        
                        return True
                    else:
                        print("❌ 数据合并失败")
                        return False
                else:
                    print("❌ 无法提取新格式数据")
                    return False
            else:
                print("❌ 扩展数据文件未生成")
                return False
        else:
            print("❌ 数据扩展处理失败")
            return False
            
    except Exception as e:
        print(f"❌ 处理过程出错: {str(e)}")
        return False

def merge_dataframes_intelligently(dataframes_list):
    """智能合并多个数据框"""
    if not dataframes_list:
        return None
    
    if len(dataframes_list) == 1:
        return dataframes_list[0]
    
    try:
        print("      🔄 智能合并多个数据框...")
        
        # 获取所有列名的并集
        all_columns = set()
        for df in dataframes_list:
            all_columns.update(df.columns)
        all_columns = sorted(list(all_columns))
        
        print(f"         总列数: {len(all_columns)}")
        
        # 标准化每个数据框
        standardized_dfs = []
        for i, df in enumerate(dataframes_list):
            # 为缺失的列添加NaN值
            df_copy = df.copy()
            for col in all_columns:
                if col not in df_copy.columns:
                    df_copy[col] = np.nan
            
            # 重新排序列
            df_standardized = df_copy[all_columns]
            standardized_dfs.append(df_standardized)
            print(f"         数据框{i+1}: {df.shape} -> {df_standardized.shape}")
        
        # 合并所有数据框
        merged_df = pd.concat(standardized_dfs, ignore_index=True)
        print(f"      ✅ 合并完成: {merged_df.shape}")
        
        return merged_df
        
    except Exception as e:
        print(f"      ❌ 数据框合并失败: {str(e)}")
        return None

def verify_fix():
    """验证修复效果"""
    print("\n✅ 步骤5：验证修复效果")
    print("-"*50)
    
    if os.path.exists('combined_features.csv'):
        df = pd.read_csv('combined_features.csv')
        print(f"✅ 修复后特征文件: {df.shape}")
        
        # 检查数据源分布
        if 'data_source' in df.columns:
            source_counts = df['data_source'].value_counts()
            print(f"   📊 数据源分布:")
            for source, count in source_counts.items():
                print(f"      {source}: {count} 样本 ({count/len(df)*100:.1f}%)")
            
            # 检查是否包含两种格式
            has_new = 'new_format' in source_counts
            has_legacy = 'legacy_format' in source_counts
            
            if has_new and has_legacy:
                print(f"   🎉 成功集成两种数据格式！")
                return True
            elif has_new:
                print(f"   ⚠️  仅包含新格式数据")
                return False
            elif has_legacy:
                print(f"   ⚠️  仅包含传统格式数据")
                return False
            else:
                print(f"   ❌ 数据源标识异常")
                return False
        else:
            print(f"   ❌ 缺少数据源标识列")
            return False
    else:
        print(f"❌ 修复后的特征文件不存在")
        return False

def main():
    """主函数"""
    print("🚀 启动数据处理问题修复程序")
    
    # 执行修复
    success = fix_data_integration()
    
    if success:
        # 验证修复效果
        verification_success = verify_fix()
        
        if verification_success:
            print(f"\n🎉 数据处理问题修复成功！")
            print(f"   ✅ 系统现在支持两种数据格式")
            print(f"   ✅ 数据源标识已添加")
            print(f"   ✅ 样本数量已增加")
            
            print(f"\n💡 下一步建议:")
            print(f"   1. 运行 python unified_vibration_analysis.py 进行完整分析")
            print(f"   2. 检查模型性能是否有所提升")
            print(f"   3. 验证预测准确率是否达到目标")
            
            return True
        else:
            print(f"\n⚠️  修复完成但验证失败")
            print(f"   建议手动检查 combined_features.csv 文件")
            return False
    else:
        print(f"\n❌ 数据处理问题修复失败")
        print(f"   建议检查 enhanced_data_expansion_processor 的状态")
        return False

if __name__ == "__main__":
    main()
