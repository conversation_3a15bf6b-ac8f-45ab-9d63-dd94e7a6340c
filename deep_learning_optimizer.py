#!/usr/bin/env python3
"""
深度学习模型优化模块
针对BP神经网络、CNN-LSTM、TCN进行架构和参数优化
"""

import numpy as np
import pandas as pd
import optuna
import warnings
warnings.filterwarnings('ignore')

class DeepLearningOptimizer:
    """深度学习模型优化器"""
    
    def __init__(self, n_trials=50, random_state=42):
        """初始化优化器"""
        self.n_trials = n_trials
        self.random_state = random_state
        self.best_params = {}
        self.optimization_results = {}
        
    def optimize_bp_neural_network(self, X, y, task_type='regression'):
        """优化BP神经网络"""
        print(f"    🧠 优化BP神经网络 ({task_type})...")
        
        try:
            import tensorflow as tf
            from tensorflow.keras.models import Sequential
            from tensorflow.keras.layers import Dense, Dropout, BatchNormalization
            from tensorflow.keras.optimizers import <PERSON>
            from tensorflow.keras.callbacks import EarlyStopping
            from sklearn.model_selection import train_test_split
            from sklearn.preprocessing import StandardScaler
            
            def objective(trial):
                # 超参数搜索空间
                params = {
                    'hidden_layers': trial.suggest_int('hidden_layers', 2, 5),
                    'neurons_layer1': trial.suggest_int('neurons_layer1', 32, 256),
                    'neurons_layer2': trial.suggest_int('neurons_layer2', 16, 128),
                    'neurons_layer3': trial.suggest_int('neurons_layer3', 8, 64),
                    'dropout_rate': trial.suggest_float('dropout_rate', 0.1, 0.5),
                    'learning_rate': trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True),
                    'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64, 128]),
                    'activation': trial.suggest_categorical('activation', ['relu', 'tanh', 'elu']),
                    'batch_norm': trial.suggest_categorical('batch_norm', [True, False])
                }
                
                # 数据预处理
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X)
                
                X_train, X_val, y_train, y_val = train_test_split(
                    X_scaled, y, test_size=0.2, random_state=self.random_state
                )
                
                # 构建模型
                model = Sequential()
                
                # 输入层
                model.add(Dense(params['neurons_layer1'], 
                               input_dim=X_scaled.shape[1], 
                               activation=params['activation']))
                if params['batch_norm']:
                    model.add(BatchNormalization())
                model.add(Dropout(params['dropout_rate']))
                
                # 隐藏层
                neurons = [params['neurons_layer2'], params['neurons_layer3']]
                for i in range(params['hidden_layers'] - 1):
                    if i < len(neurons):
                        model.add(Dense(neurons[i], activation=params['activation']))
                        if params['batch_norm']:
                            model.add(BatchNormalization())
                        model.add(Dropout(params['dropout_rate']))
                
                # 输出层
                if task_type == 'regression':
                    model.add(Dense(1, activation='linear'))
                    model.compile(
                        optimizer=Adam(learning_rate=params['learning_rate']),
                        loss='mse',
                        metrics=['mae']
                    )
                else:
                    n_classes = len(np.unique(y))
                    if n_classes == 2:
                        model.add(Dense(1, activation='sigmoid'))
                        model.compile(
                            optimizer=Adam(learning_rate=params['learning_rate']),
                            loss='binary_crossentropy',
                            metrics=['accuracy']
                        )
                    else:
                        model.add(Dense(n_classes, activation='softmax'))
                        model.compile(
                            optimizer=Adam(learning_rate=params['learning_rate']),
                            loss='sparse_categorical_crossentropy',
                            metrics=['accuracy']
                        )
                
                # 早停回调
                early_stopping = EarlyStopping(
                    monitor='val_loss',
                    patience=10,
                    restore_best_weights=True
                )
                
                # 训练模型
                history = model.fit(
                    X_train, y_train,
                    validation_data=(X_val, y_val),
                    epochs=100,
                    batch_size=params['batch_size'],
                    callbacks=[early_stopping],
                    verbose=0
                )
                
                # 评估
                if task_type == 'regression':
                    y_pred = model.predict(X_val, verbose=0).flatten()
                    from sklearn.metrics import r2_score
                    score = r2_score(y_val, y_pred)
                else:
                    if n_classes == 2:
                        y_pred = (model.predict(X_val, verbose=0) > 0.5).astype(int).flatten()
                    else:
                        y_pred = np.argmax(model.predict(X_val, verbose=0), axis=1)
                    from sklearn.metrics import accuracy_score
                    score = accuracy_score(y_val, y_pred)
                
                # 清理内存
                del model
                tf.keras.backend.clear_session()
                
                return score
            
            study = optuna.create_study(direction='maximize', 
                                      sampler=optuna.samplers.TPESampler(seed=self.random_state))
            study.optimize(objective, n_trials=self.n_trials, show_progress_bar=False)
            
            self.best_params['BP Neural Network'] = study.best_params
            self.optimization_results['BP Neural Network'] = {
                'best_score': study.best_value,
                'best_params': study.best_params,
                'n_trials': len(study.trials)
            }
            
            print(f"      最佳分数: {study.best_value:.4f}")
            return study.best_params
            
        except ImportError:
            print("      ⚠️  TensorFlow未安装，跳过BP神经网络优化")
            return {}
        except Exception as e:
            print(f"      ❌ BP神经网络优化失败: {str(e)}")
            return {}
    
    def optimize_cnn_lstm(self, X, y, task_type='regression'):
        """优化CNN-LSTM模型"""
        print(f"    🔗 优化CNN-LSTM ({task_type})...")
        
        try:
            import tensorflow as tf
            from tensorflow.keras.models import Sequential
            from tensorflow.keras.layers import Dense, LSTM, Conv1D, MaxPooling1D, Dropout, BatchNormalization
            from tensorflow.keras.optimizers import Adam
            from tensorflow.keras.callbacks import EarlyStopping
            from sklearn.model_selection import train_test_split
            from sklearn.preprocessing import StandardScaler
            
            def objective(trial):
                # 超参数搜索空间
                params = {
                    'timesteps': trial.suggest_int('timesteps', 10, 50),
                    'cnn_filters1': trial.suggest_int('cnn_filters1', 32, 128),
                    'cnn_filters2': trial.suggest_int('cnn_filters2', 16, 64),
                    'cnn_kernel_size': trial.suggest_int('cnn_kernel_size', 3, 7),
                    'lstm_units1': trial.suggest_int('lstm_units1', 32, 128),
                    'lstm_units2': trial.suggest_int('lstm_units2', 16, 64),
                    'dense_units': trial.suggest_int('dense_units', 16, 64),
                    'dropout_rate': trial.suggest_float('dropout_rate', 0.1, 0.5),
                    'learning_rate': trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True),
                    'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64])
                }
                
                # 数据预处理
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X)
                
                # 重塑数据为时序格式
                timesteps = params['timesteps']
                features_per_step = X_scaled.shape[1] // timesteps
                if features_per_step == 0:
                    features_per_step = 1
                    timesteps = X_scaled.shape[1]
                
                n_samples = X_scaled.shape[0]
                total_features_needed = timesteps * features_per_step
                
                if X_scaled.shape[1] >= total_features_needed:
                    X_reshaped = X_scaled[:, :total_features_needed]
                else:
                    padding = np.zeros((n_samples, total_features_needed - X_scaled.shape[1]))
                    X_reshaped = np.concatenate([X_scaled, padding], axis=1)
                
                X_reshaped = X_reshaped.reshape(n_samples, timesteps, features_per_step)
                
                X_train, X_val, y_train, y_val = train_test_split(
                    X_reshaped, y, test_size=0.2, random_state=self.random_state
                )
                
                # 构建模型
                model = Sequential()
                
                # CNN层
                model.add(Conv1D(
                    filters=params['cnn_filters1'],
                    kernel_size=params['cnn_kernel_size'],
                    activation='relu',
                    input_shape=(timesteps, features_per_step)
                ))
                model.add(MaxPooling1D(pool_size=2))
                model.add(Dropout(params['dropout_rate']))
                
                model.add(Conv1D(
                    filters=params['cnn_filters2'],
                    kernel_size=params['cnn_kernel_size'],
                    activation='relu'
                ))
                model.add(Dropout(params['dropout_rate']))
                
                # LSTM层
                model.add(LSTM(params['lstm_units1'], return_sequences=True))
                model.add(Dropout(params['dropout_rate']))
                model.add(LSTM(params['lstm_units2']))
                model.add(Dropout(params['dropout_rate']))
                
                # 全连接层
                model.add(Dense(params['dense_units'], activation='relu'))
                model.add(Dropout(params['dropout_rate']))
                
                # 输出层
                if task_type == 'regression':
                    model.add(Dense(1, activation='linear'))
                    model.compile(
                        optimizer=Adam(learning_rate=params['learning_rate']),
                        loss='mse',
                        metrics=['mae']
                    )
                else:
                    n_classes = len(np.unique(y))
                    if n_classes == 2:
                        model.add(Dense(1, activation='sigmoid'))
                        model.compile(
                            optimizer=Adam(learning_rate=params['learning_rate']),
                            loss='binary_crossentropy',
                            metrics=['accuracy']
                        )
                    else:
                        model.add(Dense(n_classes, activation='softmax'))
                        model.compile(
                            optimizer=Adam(learning_rate=params['learning_rate']),
                            loss='sparse_categorical_crossentropy',
                            metrics=['accuracy']
                        )
                
                # 早停回调
                early_stopping = EarlyStopping(
                    monitor='val_loss',
                    patience=10,
                    restore_best_weights=True
                )
                
                # 训练模型
                try:
                    history = model.fit(
                        X_train, y_train,
                        validation_data=(X_val, y_val),
                        epochs=50,
                        batch_size=params['batch_size'],
                        callbacks=[early_stopping],
                        verbose=0
                    )
                    
                    # 评估
                    if task_type == 'regression':
                        y_pred = model.predict(X_val, verbose=0).flatten()
                        from sklearn.metrics import r2_score
                        score = r2_score(y_val, y_pred)
                    else:
                        if n_classes == 2:
                            y_pred = (model.predict(X_val, verbose=0) > 0.5).astype(int).flatten()
                        else:
                            y_pred = np.argmax(model.predict(X_val, verbose=0), axis=1)
                        from sklearn.metrics import accuracy_score
                        score = accuracy_score(y_val, y_pred)
                    
                except Exception as e:
                    score = -1  # 训练失败
                
                # 清理内存
                del model
                tf.keras.backend.clear_session()
                
                return score
            
            study = optuna.create_study(direction='maximize',
                                      sampler=optuna.samplers.TPESampler(seed=self.random_state))
            study.optimize(objective, n_trials=self.n_trials, show_progress_bar=False)
            
            self.best_params['CNN-LSTM'] = study.best_params
            self.optimization_results['CNN-LSTM'] = {
                'best_score': study.best_value,
                'best_params': study.best_params,
                'n_trials': len(study.trials)
            }
            
            print(f"      最佳分数: {study.best_value:.4f}")
            return study.best_params
            
        except ImportError:
            print("      ⚠️  TensorFlow未安装，跳过CNN-LSTM优化")
            return {}
        except Exception as e:
            print(f"      ❌ CNN-LSTM优化失败: {str(e)}")
            return {}
    
    def optimize_tcn(self, X, y, task_type='regression'):
        """优化TCN模型"""
        print(f"    ⏰ 优化TCN ({task_type})...")
        
        try:
            import tensorflow as tf
            from tensorflow.keras.models import Model
            from tensorflow.keras.layers import Dense, Conv1D, Dropout, BatchNormalization, Add, Activation, Input, GlobalAveragePooling1D
            from tensorflow.keras.optimizers import Adam
            from tensorflow.keras.callbacks import EarlyStopping
            from sklearn.model_selection import train_test_split
            from sklearn.preprocessing import StandardScaler
            
            def objective(trial):
                # 超参数搜索空间
                params = {
                    'timesteps': trial.suggest_int('timesteps', 15, 40),
                    'nb_filters': trial.suggest_int('nb_filters', 32, 128),
                    'kernel_size': trial.suggest_int('kernel_size', 3, 7),
                    'nb_stacks': trial.suggest_int('nb_stacks', 1, 3),
                    'dilations': trial.suggest_categorical('dilations', [[1, 2, 4], [1, 2, 4, 8], [1, 2, 4, 8, 16]]),
                    'dropout_rate': trial.suggest_float('dropout_rate', 0.1, 0.4),
                    'learning_rate': trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True),
                    'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64])
                }
                
                # 数据预处理
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X)
                
                # 重塑数据
                timesteps = params['timesteps']
                features_per_step = X_scaled.shape[1] // timesteps
                if features_per_step == 0:
                    features_per_step = 1
                    timesteps = X_scaled.shape[1]
                
                n_samples = X_scaled.shape[0]
                total_features_needed = timesteps * features_per_step
                
                if X_scaled.shape[1] >= total_features_needed:
                    X_reshaped = X_scaled[:, :total_features_needed]
                else:
                    padding = np.zeros((n_samples, total_features_needed - X_scaled.shape[1]))
                    X_reshaped = np.concatenate([X_scaled, padding], axis=1)
                
                X_reshaped = X_reshaped.reshape(n_samples, timesteps, features_per_step)
                
                X_train, X_val, y_train, y_val = train_test_split(
                    X_reshaped, y, test_size=0.2, random_state=self.random_state
                )
                
                # 构建TCN模型
                def residual_block(x, dilation_rate, nb_filters, kernel_size, dropout_rate):
                    """TCN残差块"""
                    conv1 = Conv1D(filters=nb_filters, kernel_size=kernel_size,
                                  dilation_rate=dilation_rate, padding='causal',
                                  activation='relu')(x)
                    conv1 = BatchNormalization()(conv1)
                    conv1 = Dropout(dropout_rate)(conv1)
                    
                    conv2 = Conv1D(filters=nb_filters, kernel_size=kernel_size,
                                  dilation_rate=dilation_rate, padding='causal',
                                  activation='relu')(conv1)
                    conv2 = BatchNormalization()(conv2)
                    conv2 = Dropout(dropout_rate)(conv2)
                    
                    if x.shape[-1] != nb_filters:
                        x = Conv1D(nb_filters, 1, padding='same')(x)
                    
                    res = Add()([x, conv2])
                    return Activation('relu')(res)
                
                # 构建模型
                inputs = Input(shape=(timesteps, features_per_step))
                x = inputs
                
                # TCN层
                for stack in range(params['nb_stacks']):
                    for dilation in params['dilations']:
                        x = residual_block(x, dilation, params['nb_filters'], 
                                         params['kernel_size'], params['dropout_rate'])
                
                # 全局平均池化
                x = GlobalAveragePooling1D()(x)
                
                # 全连接层
                x = Dense(64, activation='relu')(x)
                x = BatchNormalization()(x)
                x = Dropout(params['dropout_rate'])(x)
                
                # 输出层
                if task_type == 'regression':
                    outputs = Dense(1, activation='linear')(x)
                    model = Model(inputs=inputs, outputs=outputs)
                    model.compile(
                        optimizer=Adam(learning_rate=params['learning_rate']),
                        loss='mse',
                        metrics=['mae']
                    )
                else:
                    n_classes = len(np.unique(y))
                    if n_classes == 2:
                        outputs = Dense(1, activation='sigmoid')(x)
                        model = Model(inputs=inputs, outputs=outputs)
                        model.compile(
                            optimizer=Adam(learning_rate=params['learning_rate']),
                            loss='binary_crossentropy',
                            metrics=['accuracy']
                        )
                    else:
                        outputs = Dense(n_classes, activation='softmax')(x)
                        model = Model(inputs=inputs, outputs=outputs)
                        model.compile(
                            optimizer=Adam(learning_rate=params['learning_rate']),
                            loss='sparse_categorical_crossentropy',
                            metrics=['accuracy']
                        )
                
                # 早停回调
                early_stopping = EarlyStopping(
                    monitor='val_loss',
                    patience=8,
                    restore_best_weights=True
                )
                
                # 训练模型
                try:
                    history = model.fit(
                        X_train, y_train,
                        validation_data=(X_val, y_val),
                        epochs=40,
                        batch_size=params['batch_size'],
                        callbacks=[early_stopping],
                        verbose=0
                    )
                    
                    # 评估
                    if task_type == 'regression':
                        y_pred = model.predict(X_val, verbose=0).flatten()
                        from sklearn.metrics import r2_score
                        score = r2_score(y_val, y_pred)
                    else:
                        if n_classes == 2:
                            y_pred = (model.predict(X_val, verbose=0) > 0.5).astype(int).flatten()
                        else:
                            y_pred = np.argmax(model.predict(X_val, verbose=0), axis=1)
                        from sklearn.metrics import accuracy_score
                        score = accuracy_score(y_val, y_pred)
                    
                except Exception as e:
                    score = -1  # 训练失败
                
                # 清理内存
                del model
                tf.keras.backend.clear_session()
                
                return score
            
            study = optuna.create_study(direction='maximize',
                                      sampler=optuna.samplers.TPESampler(seed=self.random_state))
            study.optimize(objective, n_trials=self.n_trials, show_progress_bar=False)
            
            self.best_params['TCN'] = study.best_params
            self.optimization_results['TCN'] = {
                'best_score': study.best_value,
                'best_params': study.best_params,
                'n_trials': len(study.trials)
            }
            
            print(f"      最佳分数: {study.best_value:.4f}")
            return study.best_params
            
        except ImportError:
            print("      ⚠️  TensorFlow未安装，跳过TCN优化")
            return {}
        except Exception as e:
            print(f"      ❌ TCN优化失败: {str(e)}")
            return {}
    
    def optimize_all_deep_learning_models(self, X, y, task_type='regression'):
        """优化所有深度学习模型"""
        print(f"🧠 开始深度学习模型超参数优化 ({task_type})...")
        
        optimizers = [
            ('BP Neural Network', self.optimize_bp_neural_network),
            ('CNN-LSTM', self.optimize_cnn_lstm),
            ('TCN', self.optimize_tcn)
        ]
        
        for model_name, optimizer_func in optimizers:
            try:
                optimizer_func(X, y, task_type)
            except Exception as e:
                print(f"    ❌ {model_name} 优化失败: {str(e)}")
                continue
        
        print(f"✅ 深度学习模型优化完成")
        return self.best_params
    
    def save_optimization_results(self, filename='deep_learning_optimization_results.json'):
        """保存优化结果"""
        import json
        
        results = {
            'optimization_settings': {
                'n_trials': self.n_trials,
                'random_state': self.random_state
            },
            'best_parameters': self.best_params,
            'optimization_results': self.optimization_results
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 深度学习优化结果已保存: {filename}")

def main():
    """测试函数"""
    from sklearn.datasets import make_regression
    
    print("🧪 测试深度学习优化器...")
    
    # 创建测试数据
    X, y = make_regression(n_samples=500, n_features=20, noise=0.1, random_state=42)
    
    # 初始化优化器
    optimizer = DeepLearningOptimizer(n_trials=5)  # 测试用较少试验次数
    
    # 优化深度学习模型
    optimizer.optimize_all_deep_learning_models(X, y, 'regression')
    
    # 保存结果
    optimizer.save_optimization_results('test_dl_optimization_results.json')
    
    print("✅ 测试完成!")

if __name__ == "__main__":
    main()
