#!/usr/bin/env python3
"""
GPU集成测试脚本
验证所有GPU功能是否正常工作
"""

import sys
import time
import numpy as np
from sklearn.datasets import make_regression, make_classification

def test_xgboost_gpu():
    """测试XGBoost GPU功能"""
    print("🧪 测试XGBoost GPU训练...")
    
    try:
        from ml.gpu_accelerated_training import get_gpu_trainer
        
        # 创建测试数据
        X, y = make_regression(n_samples=1000, n_features=20, random_state=42)
        
        # 获取GPU训练器
        gpu_trainer = get_gpu_trainer()
        
        # 创建GPU优化的XGBoost模型
        model = gpu_trainer.create_optimized_xgboost_regressor(
            n_estimators=50,
            learning_rate=0.1,
            max_depth=6
        )
        
        # 训练模型
        start_time = time.time()
        gpu_trainer.train_with_gpu_optimization(model, X, y)
        training_time = time.time() - start_time
        
        # 预测
        predictions = model.predict(X[:100])
        
        print(f"✅ XGBoost GPU训练成功")
        print(f"   训练时间: {training_time:.2f}秒")
        print(f"   预测样本: {len(predictions)}")
        print(f"   预测范围: [{predictions.min():.2f}, {predictions.max():.2f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ XGBoost GPU测试失败: {str(e)}")
        return False

def test_pytorch_gpu():
    """测试PyTorch GPU功能"""
    print("\n🧪 测试PyTorch GPU训练...")
    
    try:
        import torch
        import torch.nn as nn
        import torch.optim as optim
        
        # 检查GPU可用性
        if not torch.cuda.is_available():
            print("❌ PyTorch CUDA不可用")
            return False
        
        device = torch.device('cuda:0')
        print(f"   使用设备: {device}")
        
        # 创建简单的神经网络
        class SimpleNet(nn.Module):
            def __init__(self):
                super(SimpleNet, self).__init__()
                self.fc1 = nn.Linear(10, 50)
                self.fc2 = nn.Linear(50, 1)
                self.relu = nn.ReLU()
                
            def forward(self, x):
                x = self.relu(self.fc1(x))
                x = self.fc2(x)
                return x
        
        # 创建模型和数据
        model = SimpleNet().to(device)
        X = torch.randn(1000, 10).to(device)
        y = torch.randn(1000, 1).to(device)
        
        # 训练设置
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=0.01)
        
        # 训练循环
        start_time = time.time()
        for epoch in range(10):
            optimizer.zero_grad()
            outputs = model(X)
            loss = criterion(outputs, y)
            loss.backward()
            optimizer.step()
        
        training_time = time.time() - start_time
        
        # 测试预测
        model.eval()
        with torch.no_grad():
            test_input = torch.randn(100, 10).to(device)
            predictions = model(test_input)
        
        print(f"✅ PyTorch GPU训练成功")
        print(f"   训练时间: {training_time:.2f}秒 (10 epochs)")
        print(f"   最终损失: {loss.item():.4f}")
        print(f"   预测形状: {predictions.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ PyTorch GPU测试失败: {str(e)}")
        return False

def test_gpu_training_integration():
    """测试GPU训练集成"""
    print("\n🧪 测试GPU训练集成...")
    
    try:
        from ml.gpu_accelerated_training import get_gpu_trainer
        
        gpu_trainer = get_gpu_trainer()
        
        # 显示GPU状态
        print("GPU状态总结:")
        print(gpu_trainer.get_optimization_summary())
        
        # 测试分类任务
        X_cls, y_cls = make_classification(n_samples=500, n_features=15, n_classes=3, random_state=42)
        
        cls_model = gpu_trainer.create_optimized_xgboost_classifier(
            n_estimators=30,
            learning_rate=0.1
        )
        
        start_time = time.time()
        gpu_trainer.train_with_gpu_optimization(cls_model, X_cls, y_cls)
        cls_time = time.time() - start_time
        
        cls_pred = cls_model.predict(X_cls[:50])
        cls_accuracy = (cls_pred == y_cls[:50]).mean()
        
        print(f"\n✅ GPU分类模型训练成功")
        print(f"   训练时间: {cls_time:.2f}秒")
        print(f"   测试准确率: {cls_accuracy:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ GPU训练集成测试失败: {str(e)}")
        return False

def test_gpu_memory_usage():
    """测试GPU内存使用"""
    print("\n🧪 测试GPU内存使用...")
    
    try:
        import torch
        
        if not torch.cuda.is_available():
            print("❌ CUDA不可用，跳过内存测试")
            return False
        
        # 获取GPU内存信息
        device = torch.device('cuda:0')
        total_memory = torch.cuda.get_device_properties(device).total_memory / 1024**3
        allocated_before = torch.cuda.memory_allocated(device) / 1024**3
        
        print(f"   GPU总内存: {total_memory:.1f} GB")
        print(f"   使用前已分配: {allocated_before:.3f} GB")
        
        # 分配一些GPU内存
        large_tensor = torch.randn(1000, 1000, device=device)
        allocated_after = torch.cuda.memory_allocated(device) / 1024**3
        
        print(f"   分配张量后: {allocated_after:.3f} GB")
        print(f"   新增使用: {allocated_after - allocated_before:.3f} GB")
        
        # 清理内存
        del large_tensor
        torch.cuda.empty_cache()
        allocated_final = torch.cuda.memory_allocated(device) / 1024**3
        
        print(f"   清理后: {allocated_final:.3f} GB")
        print("✅ GPU内存管理正常")
        
        return True
        
    except Exception as e:
        print(f"❌ GPU内存测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 GPU集成功能测试")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("XGBoost GPU", test_xgboost_gpu),
        ("PyTorch GPU", test_pytorch_gpu),
        ("GPU训练集成", test_gpu_training_integration),
        ("GPU内存管理", test_gpu_memory_usage)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            results[test_name] = False
    
    # 显示测试结果
    print(f"\n" + "="*60)
    print("🎉 GPU集成测试结果")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试总结: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有GPU功能测试通过！")
        print("💡 系统已准备好进行GPU加速训练")
    elif passed >= total * 0.75:
        print("⚠️  大部分GPU功能正常，可以开始训练")
        print("💡 建议修复失败的测试以获得最佳性能")
    else:
        print("❌ GPU配置需要进一步调整")
        print("💡 请检查GPU驱动和CUDA安装")
    
    return passed == total

if __name__ == "__main__":
    main()
