#!/usr/bin/env python3
"""
简化的TensorFlow GPU测试
"""

def test_tensorflow_simple():
    """简化的TensorFlow GPU测试"""
    print("🔍 简化TensorFlow GPU测试...")
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow版本: {tf.__version__}")
        
        # 检查GPU设备
        gpus = tf.config.list_physical_devices('GPU')
        print(f"检测到GPU设备数量: {len(gpus)}")
        
        if gpus:
            for i, gpu in enumerate(gpus):
                print(f"   GPU {i}: {gpu}")
            
            # 简单的GPU计算测试
            try:
                with tf.device('/GPU:0'):
                    a = tf.constant([1.0, 2.0, 3.0])
                    b = tf.constant([4.0, 5.0, 6.0])
                    c = tf.add(a, b)
                    print(f"✅ GPU计算测试成功: {c.numpy()}")
                    print(f"   计算设备: {c.device}")
                return True
            except Exception as e:
                print(f"❌ GPU计算失败: {str(e)}")
                return False
        else:
            print("❌ 未检测到GPU设备")
            return False
            
    except Exception as e:
        print(f"❌ TensorFlow测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_tensorflow_simple()
