#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复中文字体显示问题的稳定解决方案
解决matplotlib中文字体显示为方框的问题

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
import os
import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class ChineseFontFixer:
    """中文字体修复器"""
    
    def __init__(self):
        """初始化字体修复器"""
        self.system = platform.system()
        self.available_fonts = []
        self.selected_font = None
        self.font_paths = []
        
    def detect_system_fonts(self):
        """检测系统可用的中文字体"""
        print("🔍 检测系统中文字体...")
        
        # 按系统分类的字体候选列表
        font_candidates = {
            'Windows': [
                'Microsoft YaHei',     # 微软雅黑
                'Microsoft YaHei UI',  # 微软雅黑UI
                'SimHei',             # 黑体
                'SimSun',             # 宋体
                'KaiTi',              # 楷体
                'FangSong',           # 仿宋
                'Microsoft JhengHei'   # 微软正黑体
            ],
            'Darwin': [  # macOS
                'PingFang SC',        # 苹方
                'Hiragino Sans GB',   # 冬青黑体
                'Heiti SC',           # 黑体-简
                'STHeiti',            # 华文黑体
                'STSong',             # 华文宋体
                'STKaiti',            # 华文楷体
                'Apple LiGothic'      # 苹果俪黑
            ],
            'Linux': [
                'Noto Sans CJK SC',      # 思源黑体
                'WenQuanYi Micro Hei',   # 文泉驿微米黑
                'WenQuanYi Zen Hei',     # 文泉驿正黑
                'Source Han Sans SC',     # 思源黑体
                'Droid Sans Fallback',   # Android字体
                'AR PL UMing CN',        # 文鼎字体
                'WenQuanYi Bitmap Song'  # 文泉驿点阵宋体
            ]
        }
        
        # 通用备用字体
        fallback_fonts = [
            'DejaVu Sans',
            'Arial Unicode MS',
            'Lucida Grande',
            'Liberation Sans'
        ]
        
        candidates = font_candidates.get(self.system, []) + fallback_fonts
        
        print(f"   操作系统: {self.system}")
        print(f"   检查 {len(candidates)} 个字体候选...")
        
        # 获取系统所有可用字体
        system_fonts = set(f.name for f in fm.fontManager.ttflist)
        
        for font_name in candidates:
            if font_name in system_fonts:
                # 进一步验证字体文件是否存在
                try:
                    font_path = fm.findfont(fm.FontProperties(family=font_name))
                    if font_path and os.path.exists(font_path):
                        self.available_fonts.append(font_name)
                        self.font_paths.append(font_path)
                        print(f"   ✅ 找到字体: {font_name}")
                        print(f"      路径: {font_path}")
                except Exception as e:
                    print(f"   ⚠️  字体 {font_name} 验证失败: {e}")
                    continue
        
        if not self.available_fonts:
            print("   ❌ 未找到任何中文字体")
            return False
        
        print(f"   📊 共找到 {len(self.available_fonts)} 个可用中文字体")
        return True
    
    def test_font_chinese_support(self, font_name):
        """测试字体是否支持中文显示"""
        try:
            # 创建测试图形
            fig, ax = plt.subplots(figsize=(6, 4))
            
            # 设置字体
            plt.rcParams['font.sans-serif'] = [font_name]
            plt.rcParams['axes.unicode_minus'] = False
            
            # 测试中文文本
            test_text = "振动信号分析系统 - 中文字体测试"
            ax.text(0.5, 0.5, test_text, fontsize=14, ha='center', va='center')
            ax.set_title(f"字体测试: {font_name}", fontsize=16)
            
            # 检查文本是否正确渲染（简单检查）
            plt.tight_layout()
            
            # 保存测试图片
            test_path = f"font_test_{font_name.replace(' ', '_')}.png"
            plt.savefig(test_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            # 检查文件是否成功生成
            if os.path.exists(test_path):
                file_size = os.path.getsize(test_path)
                os.remove(test_path)  # 清理测试文件
                
                # 如果文件大小合理，认为字体支持中文
                if file_size > 1000:  # 至少1KB
                    return True
            
            return False
            
        except Exception as e:
            print(f"   ⚠️  字体 {font_name} 中文支持测试失败: {e}")
            return False
    
    def select_best_font(self):
        """选择最佳中文字体"""
        if not self.available_fonts:
            if not self.detect_system_fonts():
                print("❌ 无法检测到可用的中文字体")
                return None
        
        print("🎯 选择最佳中文字体...")
        
        # 按优先级测试字体
        for font_name in self.available_fonts:
            print(f"   🧪 测试字体: {font_name}")
            if self.test_font_chinese_support(font_name):
                self.selected_font = font_name
                print(f"   ✅ 选择字体: {font_name}")
                return font_name
            else:
                print(f"   ❌ 字体 {font_name} 中文支持不佳")
        
        # 如果所有字体测试都失败，选择第一个可用字体
        if self.available_fonts:
            self.selected_font = self.available_fonts[0]
            print(f"   ⚠️  强制选择字体: {self.selected_font}")
            return self.selected_font
        
        return None
    
    def apply_font_configuration(self):
        """应用字体配置"""
        if not self.selected_font:
            if not self.select_best_font():
                print("❌ 无法选择合适的字体")
                return False
        
        print("🎨 应用字体配置...")
        
        try:
            # 构建字体列表（优先使用选择的字体）
            font_list = [self.selected_font] + self.available_fonts + [
                'DejaVu Sans', 'Arial Unicode MS', 'sans-serif'
            ]
            
            # 去重保持顺序
            font_list = list(dict.fromkeys(font_list))
            
            # 应用matplotlib配置
            plt.rcParams['font.sans-serif'] = font_list
            plt.rcParams['axes.unicode_minus'] = False
            
            # 设置字体大小
            plt.rcParams['font.size'] = 12
            plt.rcParams['axes.titlesize'] = 16
            plt.rcParams['axes.labelsize'] = 14
            plt.rcParams['xtick.labelsize'] = 12
            plt.rcParams['ytick.labelsize'] = 12
            plt.rcParams['legend.fontsize'] = 12
            
            # 清除matplotlib字体缓存（兼容不同版本）
            try:
                if hasattr(fm, '_rebuild'):
                    fm._rebuild()
                elif hasattr(fm.fontManager, 'addfont'):
                    # 新版本matplotlib
                    pass  # 不需要手动重建
                else:
                    # 清除缓存的另一种方法
                    plt.rcParams.update(plt.rcParamsDefault)
                    plt.rcParams['font.sans-serif'] = font_list
                    plt.rcParams['axes.unicode_minus'] = False
            except Exception as e:
                print(f"   ⚠️  字体缓存清除失败: {e}")
                # 继续执行，不影响主要功能
            
            print(f"   ✅ 字体配置已应用")
            print(f"   📝 字体列表: {font_list[:3]}...")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 字体配置应用失败: {e}")
            return False
    
    def create_test_chart(self, save_path="chinese_font_test.png"):
        """创建中文字体测试图表"""
        print("📊 创建中文字体测试图表...")
        
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
            
            # 测试文本
            test_texts = [
                "振动信号分析系统",
                "车辆轴重检测",
                "机器学习模型",
                "数据可视化"
            ]
            
            axes = [ax1, ax2, ax3, ax4]
            
            for i, (ax, text) in enumerate(zip(axes, test_texts)):
                # 创建测试数据
                x = np.linspace(0, 10, 100)
                y = np.sin(x + i) * np.exp(-x/10)
                
                ax.plot(x, y, linewidth=2, label=f"测试曲线 {i+1}")
                ax.set_title(text, fontsize=14, fontweight='bold')
                ax.set_xlabel("时间 (秒)", fontsize=12)
                ax.set_ylabel("幅值", fontsize=12)
                ax.legend()
                ax.grid(True, alpha=0.3)
            
            plt.suptitle("中文字体显示测试 - 振动信号分析系统", fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            # 保存图表
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            print(f"   ✅ 测试图表已保存: {save_path}")
            
            # 检查文件大小
            if os.path.exists(save_path):
                file_size = os.path.getsize(save_path) / 1024  # KB
                print(f"   📁 文件大小: {file_size:.1f} KB")
                
                if file_size > 50:  # 至少50KB
                    print("   ✅ 字体显示测试通过")
                    return True
                else:
                    print("   ⚠️  文件大小异常，可能存在字体问题")
                    return False
            
            return False
            
        except Exception as e:
            print(f"   ❌ 测试图表创建失败: {e}")
            return False
    
    def fix_all_visualization_modules(self):
        """修复所有可视化模块的字体配置"""
        print("🔧 修复所有可视化模块...")
        
        # 需要修复的模块文件
        modules_to_fix = [
            'advanced_visualization.py',
            'process_visualization.py',
            'unified_visualization_manager.py',
            'enhanced_visualization_manager.py',
            'visualization_manager.py'
        ]
        
        font_import_code = f'''
# 自动应用中文字体配置
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['{self.selected_font}'] + {self.available_fonts} + ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
'''
        
        fixed_count = 0
        
        for module_file in modules_to_fix:
            if os.path.exists(module_file):
                try:
                    # 读取文件内容
                    with open(module_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否已经有字体配置
                    if 'font.sans-serif' not in content:
                        # 在import语句后添加字体配置
                        lines = content.split('\n')
                        insert_pos = 0
                        
                        # 找到合适的插入位置（在import语句之后）
                        for i, line in enumerate(lines):
                            if line.strip().startswith('import ') or line.strip().startswith('from '):
                                insert_pos = i + 1
                        
                        # 插入字体配置
                        lines.insert(insert_pos, font_import_code)
                        
                        # 写回文件
                        with open(module_file, 'w', encoding='utf-8') as f:
                            f.write('\n'.join(lines))
                        
                        print(f"   ✅ 已修复: {module_file}")
                        fixed_count += 1
                    else:
                        print(f"   ✓ 已配置: {module_file}")
                        
                except Exception as e:
                    print(f"   ❌ 修复失败 {module_file}: {e}")
        
        print(f"   📊 修复了 {fixed_count} 个模块")
        return fixed_count > 0

def main():
    """主函数"""
    print("🚀 中文字体显示问题修复工具")
    print("=" * 50)
    
    # 创建字体修复器
    fixer = ChineseFontFixer()
    
    # 检测系统字体
    if not fixer.detect_system_fonts():
        print("❌ 字体检测失败，请手动安装中文字体")
        return False
    
    # 选择最佳字体
    if not fixer.select_best_font():
        print("❌ 字体选择失败")
        return False
    
    # 应用字体配置
    if not fixer.apply_font_configuration():
        print("❌ 字体配置失败")
        return False
    
    # 创建测试图表
    if not fixer.create_test_chart():
        print("❌ 测试图表创建失败")
        return False
    
    # 修复可视化模块
    fixer.fix_all_visualization_modules()
    
    print("\n" + "=" * 50)
    print("✅ 中文字体修复完成！")
    print(f"📝 选择的字体: {fixer.selected_font}")
    print("💡 建议重启Python环境以确保配置生效")
    print("🧪 请查看生成的测试图表验证字体显示效果")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 字体修复失败，请检查系统字体安装情况")
        sys.exit(1)
