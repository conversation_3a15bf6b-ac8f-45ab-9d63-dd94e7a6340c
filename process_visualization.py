#!/usr/bin/env python3
"""
数据处理流程可视化模块
为振动信号分析系统创建学术论文级别的流程图表
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import signal
from scipy.fft import fft, fftfreq
import os
import warnings
warnings.filterwarnings('ignore')

class ProcessVisualization:
    """数据处理流程可视化器"""
    
    def __init__(self, output_dir='process_visualization'):
        """初始化可视化器"""
        self.output_dir = output_dir
        self.ensure_output_dir()
        self.setup_academic_style()
        
        # 学术期刊标准配色
        self.colors = {
            'primary': '#1f77b4',      # 蓝色
            'secondary': '#ff7f0e',    # 橙色
            'accent': '#2ca02c',       # 绿色
            'warning': '#d62728',      # 红色
            'info': '#9467bd',         # 紫色
            'success': '#8c564b',      # 棕色
            'grid': '#cccccc',         # 浅灰色
            'text': '#333333'          # 深灰色
        }
        
        # 中英文标签对照
        self.labels = {
            'chinese': {
                'time': '时间 (秒)',
                'amplitude': '幅值 (m/s²)',
                'frequency': '频率 (Hz)',
                'magnitude': '幅度',
                'power': '功率谱密度',
                'features': '特征值',
                'samples': '样本',
                'vehicle_event': '车辆通过事件',
                'max_detection': '最大值检测',
                'data_segment': '数据段',
                'before_processing': '处理前',
                'after_processing': '处理后',
                'training_loss': '训练损失',
                'validation_loss': '验证损失',
                'epochs': '训练轮次',
                'predicted': '预测值',
                'actual': '实际值',
                'performance': '性能',
                'models': '模型'
            },
            'english': {
                'time': 'Time (s)',
                'amplitude': 'Amplitude (m/s²)',
                'frequency': 'Frequency (Hz)',
                'magnitude': 'Magnitude',
                'power': 'Power Spectral Density',
                'features': 'Feature Values',
                'samples': 'Samples',
                'vehicle_event': 'Vehicle Passing Event',
                'max_detection': 'Maximum Detection',
                'data_segment': 'Data Segment',
                'before_processing': 'Before Processing',
                'after_processing': 'After Processing',
                'training_loss': 'Training Loss',
                'validation_loss': 'Validation Loss',
                'epochs': 'Epochs',
                'predicted': 'Predicted',
                'actual': 'Actual',
                'performance': 'Performance',
                'models': 'Models'
            }
        }
    
    def ensure_output_dir(self):
        """确保输出目录存在"""
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'chinese'), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'english'), exist_ok=True)
    
    def setup_academic_style(self):
        """设置学术期刊风格"""
        # IEEE/Elsevier期刊标准设置
        plt.rcParams.update({
            'figure.dpi': 300,
            'savefig.dpi': 300,
            'savefig.bbox': 'tight',
            'savefig.pad_inches': 0.1,
            'font.size': 12,
            'axes.titlesize': 16,
            'axes.labelsize': 14,
            'xtick.labelsize': 12,
            'ytick.labelsize': 12,
            'legend.fontsize': 12,
            'lines.linewidth': 1.5,
            'axes.linewidth': 1.0,
            'grid.alpha': 0.3,
            'grid.linewidth': 0.5,
            'axes.grid': True,
            'axes.axisbelow': True
        })
    
    def set_fonts(self, language='chinese'):
        """设置字体"""
        if language == 'chinese':
            plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
        else:
            plt.rcParams['font.family'] = 'Times New Roman'
            plt.rcParams['font.sans-serif'] = ['Times New Roman', 'DejaVu Sans']
    
    def create_raw_signal_visualization(self, signal_data, fs=1000, duration=10):
        """创建原始振动信号时域波形图"""
        print("📊 生成原始振动信号波形图...")
        
        # 生成模拟信号数据
        if signal_data is None:
            t = np.linspace(0, duration, int(fs * duration))
            # 模拟多传感器振动信号
            signal_data = {
                'sensor_1': 0.5 * np.sin(2 * np.pi * 10 * t) + 0.3 * np.sin(2 * np.pi * 25 * t) + 0.1 * np.random.randn(len(t)),
                'sensor_2': 0.4 * np.sin(2 * np.pi * 12 * t) + 0.2 * np.sin(2 * np.pi * 30 * t) + 0.1 * np.random.randn(len(t)),
                'sensor_3': 0.6 * np.sin(2 * np.pi * 8 * t) + 0.25 * np.sin(2 * np.pi * 20 * t) + 0.1 * np.random.randn(len(t))
            }
            # 添加车辆通过事件（在5秒处）
            event_start = int(4.5 * fs)
            event_end = int(5.5 * fs)
            for sensor in signal_data:
                signal_data[sensor][event_start:event_end] += 2.0 * np.exp(-((t[event_start:event_end] - 5)**2) / 0.1)
        else:
            t = np.linspace(0, duration, len(list(signal_data.values())[0]))
        
        # 生成中英文版本
        for language in ['chinese', 'english']:
            self.set_fonts(language)
            
            fig, axes = plt.subplots(3, 1, figsize=(17.8/2.54, 12/2.54))  # 双栏图宽度
            
            sensors = list(signal_data.keys())
            colors = [self.colors['primary'], self.colors['secondary'], self.colors['accent']]
            
            for i, (sensor, color) in enumerate(zip(sensors, colors)):
                axes[i].plot(t, signal_data[sensor], color=color, linewidth=1.0)
                
                if language == 'chinese':
                    axes[i].set_ylabel(f'传感器{i+1}\n{self.labels[language]["amplitude"]}', fontsize=14)
                    if i == 0:
                        axes[i].set_title('原始振动信号时域波形', fontsize=16, fontweight='bold', pad=20)
                else:
                    axes[i].set_ylabel(f'Sensor {i+1}\n{self.labels[language]["amplitude"]}', fontsize=14)
                    if i == 0:
                        axes[i].set_title('Raw Vibration Signal Time-Domain Waveforms', fontsize=16, fontweight='bold', pad=20)
                
                axes[i].grid(True, alpha=0.3)
                axes[i].set_xlim(0, duration)
            
            axes[-1].set_xlabel(self.labels[language]['time'], fontsize=14)
            
            plt.tight_layout()
            
            # 保存图片
            if language == 'chinese':
                filename = '原始信号_流程图_中文.png'
            else:
                filename = 'raw_signal_process_diagram_english.png'
            
            save_path = os.path.join(self.output_dir, language, filename)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✅ {language.upper()}版原始信号图已保存: {save_path}")
    
    def create_event_detection_visualization(self, signal_data, fs=1000):
        """创建车辆通过事件检测结果图"""
        print("📊 生成车辆通过事件检测图...")
        
        # 生成模拟数据
        if signal_data is None:
            duration = 10
            t = np.linspace(0, duration, int(fs * duration))
            # 融合信号
            fused_signal = 0.5 * np.sin(2 * np.pi * 10 * t) + 0.1 * np.random.randn(len(t))
            # 车辆通过事件
            event_center = 5.0
            event_start = int(4.5 * fs)
            event_end = int(5.5 * fs)
            fused_signal[event_start:event_end] += 3.0 * np.exp(-((t[event_start:event_end] - event_center)**2) / 0.1)
        else:
            t = np.linspace(0, len(signal_data)/fs, len(signal_data))
            fused_signal = signal_data
        
        # 检测最大值
        max_idx = np.argmax(np.abs(fused_signal))
        max_time = t[max_idx]
        max_value = fused_signal[max_idx]
        
        # 截取1秒数据段
        half_duration = 0.5
        half_samples = int(half_duration * fs)
        start_idx = max(0, max_idx - half_samples)
        end_idx = min(len(fused_signal), max_idx + half_samples)
        
        # 生成中英文版本
        for language in ['chinese', 'english']:
            self.set_fonts(language)
            
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(17.8/2.54, 10/2.54))
            
            # 上图：完整信号和事件检测
            ax1.plot(t, fused_signal, color=self.colors['primary'], linewidth=1.0, alpha=0.7)
            ax1.plot(max_time, max_value, 'ro', markersize=8, label=self.labels[language]['max_detection'])
            
            # 标记截取区域
            ax1.axvspan(t[start_idx], t[end_idx], alpha=0.3, color=self.colors['accent'], 
                       label=self.labels[language]['data_segment'])
            
            if language == 'chinese':
                ax1.set_title('车辆通过事件检测与数据截取', fontsize=16, fontweight='bold')
                ax1.set_ylabel('融合信号幅值 (m/s²)', fontsize=14)
            else:
                ax1.set_title('Vehicle Passing Event Detection and Data Segmentation', fontsize=16, fontweight='bold')
                ax1.set_ylabel('Fused Signal Amplitude (m/s²)', fontsize=14)
            
            ax1.legend(loc='upper right')
            ax1.grid(True, alpha=0.3)
            
            # 下图：截取的1秒数据段
            t_segment = t[start_idx:end_idx] - t[start_idx]
            signal_segment = fused_signal[start_idx:end_idx]
            
            ax2.plot(t_segment, signal_segment, color=self.colors['secondary'], linewidth=1.5)
            ax2.axvline(x=0.5, color=self.colors['warning'], linestyle='--', 
                       label=f'{self.labels[language]["max_detection"]} (t=0.5s)')
            
            if language == 'chinese':
                ax2.set_title('截取的1秒数据段 (1000个采样点)', fontsize=14)
                ax2.set_ylabel('幅值 (m/s²)', fontsize=14)
            else:
                ax2.set_title('Extracted 1-Second Data Segment (1000 Sample Points)', fontsize=14)
                ax2.set_ylabel('Amplitude (m/s²)', fontsize=14)
            
            ax2.set_xlabel(self.labels[language]['time'], fontsize=14)
            ax2.legend(loc='upper right')
            ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图片
            if language == 'chinese':
                filename = '事件检测_流程图_中文.png'
            else:
                filename = 'event_detection_process_diagram_english.png'
            
            save_path = os.path.join(self.output_dir, language, filename)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✅ {language.upper()}版事件检测图已保存: {save_path}")
    
    def create_feature_extraction_visualization(self, signal_segment, fs=1000):
        """创建特征提取结果展示图"""
        print("📊 生成特征提取结果图...")
        
        # 生成模拟数据
        if signal_segment is None:
            t = np.linspace(0, 1, fs)
            signal_segment = 2.0 * np.exp(-((t - 0.5)**2) / 0.1) + 0.1 * np.random.randn(len(t))
        
        # 计算特征
        # 时域特征
        time_features = {
            'mean': np.mean(signal_segment),
            'std': np.std(signal_segment),
            'rms': np.sqrt(np.mean(signal_segment**2)),
            'peak': np.max(np.abs(signal_segment)),
            'skewness': self._calculate_skewness(signal_segment),
            'kurtosis': self._calculate_kurtosis(signal_segment)
        }
        
        # 频域特征
        fft_signal = fft(signal_segment)
        freqs = fftfreq(len(signal_segment), 1/fs)
        magnitude = np.abs(fft_signal)
        
        # 只取正频率部分
        positive_freqs = freqs[:len(freqs)//2]
        positive_magnitude = magnitude[:len(magnitude)//2]
        
        # 生成中英文版本
        for language in ['chinese', 'english']:
            self.set_fonts(language)
            
            fig = plt.figure(figsize=(17.8/2.54, 12/2.54))
            
            # 创建网格布局
            gs = fig.add_gridspec(3, 2, height_ratios=[1, 1, 1], width_ratios=[1, 1])
            
            # 时域信号
            ax1 = fig.add_subplot(gs[0, :])
            t = np.linspace(0, 1, len(signal_segment))
            ax1.plot(t, signal_segment, color=self.colors['primary'], linewidth=1.5)
            
            if language == 'chinese':
                ax1.set_title('特征提取：时域和频域分析', fontsize=16, fontweight='bold')
                ax1.set_ylabel('幅值 (m/s²)', fontsize=14)
            else:
                ax1.set_title('Feature Extraction: Time-Domain and Frequency-Domain Analysis', fontsize=16, fontweight='bold')
                ax1.set_ylabel('Amplitude (m/s²)', fontsize=14)
            
            ax1.set_xlabel(self.labels[language]['time'], fontsize=14)
            ax1.grid(True, alpha=0.3)
            
            # 频域谱
            ax2 = fig.add_subplot(gs[1, 0])
            ax2.plot(positive_freqs, positive_magnitude, color=self.colors['secondary'], linewidth=1.5)
            ax2.set_xlabel(self.labels[language]['frequency'], fontsize=14)
            ax2.set_ylabel(self.labels[language]['magnitude'], fontsize=14)
            
            if language == 'chinese':
                ax2.set_title('频域谱', fontsize=14)
            else:
                ax2.set_title('Frequency Spectrum', fontsize=14)
            
            ax2.grid(True, alpha=0.3)
            ax2.set_xlim(0, 100)  # 显示0-100Hz
            
            # 时域特征
            ax3 = fig.add_subplot(gs[1, 1])
            
            if language == 'chinese':
                feature_names_cn = ['均值', '标准差', 'RMS', '峰值', '偏度', '峰度']
                feature_values = list(time_features.values())
                bars = ax3.bar(feature_names_cn, feature_values, color=self.colors['accent'], alpha=0.7)
                ax3.set_title('时域特征', fontsize=14)
                ax3.set_ylabel('特征值', fontsize=14)
            else:
                feature_names_en = ['Mean', 'Std', 'RMS', 'Peak', 'Skew', 'Kurt']
                feature_values = list(time_features.values())
                bars = ax3.bar(feature_names_en, feature_values, color=self.colors['accent'], alpha=0.7)
                ax3.set_title('Time-Domain Features', fontsize=14)
                ax3.set_ylabel('Feature Values', fontsize=14)
            
            # 添加数值标签
            for bar, value in zip(bars, feature_values):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(feature_values)*0.01,
                        f'{value:.3f}', ha='center', va='bottom', fontsize=10)
            
            ax3.tick_params(axis='x', rotation=45)
            ax3.grid(True, alpha=0.3)
            
            # 功率谱密度
            ax4 = fig.add_subplot(gs[2, :])
            f, psd = signal.welch(signal_segment, fs, nperseg=256)
            ax4.semilogy(f, psd, color=self.colors['info'], linewidth=1.5)
            ax4.set_xlabel(self.labels[language]['frequency'], fontsize=14)
            ax4.set_ylabel(self.labels[language]['power'], fontsize=14)
            
            if language == 'chinese':
                ax4.set_title('功率谱密度', fontsize=14)
            else:
                ax4.set_title('Power Spectral Density', fontsize=14)
            
            ax4.grid(True, alpha=0.3)
            ax4.set_xlim(0, 100)
            
            plt.tight_layout()
            
            # 保存图片
            if language == 'chinese':
                filename = '特征提取_流程图_中文.png'
            else:
                filename = 'feature_extraction_process_diagram_english.png'
            
            save_path = os.path.join(self.output_dir, language, filename)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✅ {language.upper()}版特征提取图已保存: {save_path}")
    
    def _calculate_skewness(self, data):
        """计算偏度"""
        mean = np.mean(data)
        std = np.std(data)
        return np.mean(((data - mean) / std) ** 3)
    
    def _calculate_kurtosis(self, data):
        """计算峰度"""
        mean = np.mean(data)
        std = np.std(data)
        return np.mean(((data - mean) / std) ** 4) - 3

    def create_preprocessing_visualization(self, raw_features, processed_features):
        """创建数据预处理效果图"""
        print("📊 生成数据预处理效果图...")

        # 生成模拟数据
        if raw_features is None:
            np.random.seed(42)
            n_samples = 1000
            n_features = 6

            # 原始特征（不同尺度）
            raw_features = np.random.randn(n_samples, n_features)
            raw_features[:, 0] *= 100  # 大尺度特征
            raw_features[:, 1] *= 10   # 中等尺度特征
            raw_features[:, 2] *= 0.1  # 小尺度特征
            raw_features[:, 3] += 50   # 有偏移的特征

            # 标准化后的特征
            from sklearn.preprocessing import StandardScaler
            scaler = StandardScaler()
            processed_features = scaler.fit_transform(raw_features)

        # 生成中英文版本
        for language in ['chinese', 'english']:
            self.set_fonts(language)

            fig, axes = plt.subplots(2, 3, figsize=(17.8/2.54, 10/2.54))

            if language == 'chinese':
                fig.suptitle('数据预处理效果对比：标准化前后', fontsize=16, fontweight='bold')
                feature_names = ['特征1', '特征2', '特征3', '特征4', '特征5', '特征6']
            else:
                fig.suptitle('Data Preprocessing Effects: Before and After Standardization', fontsize=16, fontweight='bold')
                feature_names = ['Feature 1', 'Feature 2', 'Feature 3', 'Feature 4', 'Feature 5', 'Feature 6']

            for i in range(6):
                row = i // 3
                col = i % 3
                ax = axes[row, col]

                # 绘制处理前后的分布
                ax.hist(raw_features[:, i], bins=30, alpha=0.6, color=self.colors['warning'],
                       label=self.labels[language]['before_processing'], density=True)
                ax.hist(processed_features[:, i], bins=30, alpha=0.6, color=self.colors['success'],
                       label=self.labels[language]['after_processing'], density=True)

                ax.set_title(feature_names[i], fontsize=12)
                ax.set_ylabel('密度', fontsize=10) if language == 'chinese' else ax.set_ylabel('Density', fontsize=10)
                ax.legend(fontsize=9)
                ax.grid(True, alpha=0.3)

            plt.tight_layout()

            # 保存图片
            if language == 'chinese':
                filename = '数据预处理_流程图_中文.png'
            else:
                filename = 'data_preprocessing_process_diagram_english.png'

            save_path = os.path.join(self.output_dir, language, filename)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ {language.upper()}版数据预处理图已保存: {save_path}")

    def create_training_process_visualization(self, training_history=None):
        """创建模型训练过程图"""
        print("📊 生成模型训练过程图...")

        # 修复epochs变量作用域问题
        epochs = 100  # 默认epochs数量

        # 生成模拟训练历史
        if training_history is None:
            training_history = {
                'Random Forest': {
                    'train_score': [0.5 + 0.4 * (1 - np.exp(-i/20)) + 0.02 * np.random.random() for i in range(epochs)],
                    'val_score': [0.45 + 0.35 * (1 - np.exp(-i/25)) + 0.03 * np.random.random() for i in range(epochs)]
                },
                'Deep Learning': {
                    'train_loss': [2.0 * np.exp(-i/15) + 0.1 * np.random.random() for i in range(epochs)],
                    'val_loss': [2.2 * np.exp(-i/18) + 0.15 * np.random.random() for i in range(epochs)],
                    'train_acc': [0.3 + 0.6 * (1 - np.exp(-i/12)) + 0.02 * np.random.random() for i in range(epochs)],
                    'val_acc': [0.25 + 0.55 * (1 - np.exp(-i/15)) + 0.03 * np.random.random() for i in range(epochs)]
                }
            }
        else:
            # 从现有训练历史中推断epochs数量
            for model_data in training_history.values():
                for metric_data in model_data.values():
                    if isinstance(metric_data, list):
                        epochs = len(metric_data)
                        break
                break

        # 生成中英文版本
        for language in ['chinese', 'english']:
            self.set_fonts(language)

            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(17.8/2.54, 12/2.54))

            if language == 'chinese':
                fig.suptitle('模型训练过程与收敛分析', fontsize=16, fontweight='bold')
            else:
                fig.suptitle('Model Training Process and Convergence Analysis', fontsize=16, fontweight='bold')

            epochs_range = range(1, epochs + 1)

            # 传统机器学习性能曲线
            rf_history = training_history['Random Forest']
            ax1.plot(epochs_range, rf_history['train_score'], color=self.colors['primary'],
                    linewidth=1.5, label=f'{self.labels[language]["training_loss"]} (训练)' if language == 'chinese' else 'Training Score')
            ax1.plot(epochs_range, rf_history['val_score'], color=self.colors['secondary'],
                    linewidth=1.5, label=f'{self.labels[language]["validation_loss"]} (验证)' if language == 'chinese' else 'Validation Score')

            if language == 'chinese':
                ax1.set_title('随机森林性能收敛', fontsize=14)
                ax1.set_ylabel('R² 分数', fontsize=12)
            else:
                ax1.set_title('Random Forest Performance Convergence', fontsize=14)
                ax1.set_ylabel('R² Score', fontsize=12)

            ax1.set_xlabel(self.labels[language]['epochs'], fontsize=12)
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 深度学习损失曲线
            dl_history = training_history['Deep Learning']
            ax2.plot(epochs_range, dl_history['train_loss'], color=self.colors['warning'],
                    linewidth=1.5, label=f'训练损失' if language == 'chinese' else 'Training Loss')
            ax2.plot(epochs_range, dl_history['val_loss'], color=self.colors['info'],
                    linewidth=1.5, label=f'验证损失' if language == 'chinese' else 'Validation Loss')

            if language == 'chinese':
                ax2.set_title('深度学习损失收敛', fontsize=14)
                ax2.set_ylabel('损失值', fontsize=12)
            else:
                ax2.set_title('Deep Learning Loss Convergence', fontsize=14)
                ax2.set_ylabel('Loss Value', fontsize=12)

            ax2.set_xlabel(self.labels[language]['epochs'], fontsize=12)
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # 深度学习准确率曲线
            ax3.plot(epochs_range, dl_history['train_acc'], color=self.colors['accent'],
                    linewidth=1.5, label=f'训练准确率' if language == 'chinese' else 'Training Accuracy')
            ax3.plot(epochs_range, dl_history['val_acc'], color=self.colors['success'],
                    linewidth=1.5, label=f'验证准确率' if language == 'chinese' else 'Validation Accuracy')

            if language == 'chinese':
                ax3.set_title('深度学习准确率收敛', fontsize=14)
                ax3.set_ylabel('准确率', fontsize=12)
            else:
                ax3.set_title('Deep Learning Accuracy Convergence', fontsize=14)
                ax3.set_ylabel('Accuracy', fontsize=12)

            ax3.set_xlabel(self.labels[language]['epochs'], fontsize=12)
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            # 学习率调度
            learning_rates = [0.001 * (0.95 ** (i // 10)) for i in range(epochs)]
            ax4.plot(epochs_range, learning_rates, color=self.colors['primary'], linewidth=1.5)

            if language == 'chinese':
                ax4.set_title('学习率调度', fontsize=14)
                ax4.set_ylabel('学习率', fontsize=12)
            else:
                ax4.set_title('Learning Rate Schedule', fontsize=14)
                ax4.set_ylabel('Learning Rate', fontsize=12)

            ax4.set_xlabel(self.labels[language]['epochs'], fontsize=12)
            ax4.set_yscale('log')
            ax4.grid(True, alpha=0.3)

            plt.tight_layout()

            # 保存图片
            if language == 'chinese':
                filename = '模型训练_流程图_中文.png'
            else:
                filename = 'model_training_process_diagram_english.png'

            save_path = os.path.join(self.output_dir, language, filename)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ {language.upper()}版模型训练图已保存: {save_path}")

    def create_prediction_results_visualization(self, y_true=None, predictions=None):
        """创建最终预测结果对比图"""
        print("📊 生成最终预测结果对比图...")

        # 生成模拟数据
        if y_true is None:
            np.random.seed(42)
            n_samples = 200
            y_true = np.random.randn(n_samples) * 10 + 50  # 速度预测

            # 不同模型的预测结果
            predictions = {
                'Random Forest': y_true + np.random.randn(n_samples) * 2,
                'XGBoost': y_true + np.random.randn(n_samples) * 1.5,
                'Deep Learning': y_true + np.random.randn(n_samples) * 3,
                'Ensemble': y_true + np.random.randn(n_samples) * 1.2
            }

        # 生成中英文版本
        for language in ['chinese', 'english']:
            self.set_fonts(language)

            fig, axes = plt.subplots(2, 2, figsize=(17.8/2.54, 12/2.54))

            if language == 'chinese':
                fig.suptitle('最终预测结果对比分析', fontsize=16, fontweight='bold')
                model_names_cn = ['随机森林', 'XGBoost', '深度学习', '集成模型']
            else:
                fig.suptitle('Final Prediction Results Comparison Analysis', fontsize=16, fontweight='bold')
                model_names_en = ['Random Forest', 'XGBoost', 'Deep Learning', 'Ensemble']

            colors = [self.colors['primary'], self.colors['secondary'], self.colors['accent'], self.colors['success']]

            for i, (model_name, y_pred) in enumerate(predictions.items()):
                row = i // 2
                col = i % 2
                ax = axes[row, col]

                # 散点图
                ax.scatter(y_true, y_pred, alpha=0.6, color=colors[i], s=20)

                # 理想拟合线
                min_val, max_val = min(np.min(y_true), np.min(y_pred)), max(np.max(y_true), np.max(y_pred))
                ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=1)

                # 计算R²
                from sklearn.metrics import r2_score
                r2 = r2_score(y_true, y_pred)

                if language == 'chinese':
                    title = model_names_cn[i]
                    ax.set_xlabel('实际值 (km/h)', fontsize=12)
                    ax.set_ylabel('预测值 (km/h)', fontsize=12)
                else:
                    title = model_names_en[i]
                    ax.set_xlabel('Actual Values (km/h)', fontsize=12)
                    ax.set_ylabel('Predicted Values (km/h)', fontsize=12)

                ax.set_title(f'{title}\nR² = {r2:.3f}', fontsize=12)
                ax.grid(True, alpha=0.3)
                ax.set_aspect('equal', adjustable='box')

            plt.tight_layout()

            # 保存图片
            if language == 'chinese':
                filename = '预测结果_流程图_中文.png'
            else:
                filename = 'prediction_results_process_diagram_english.png'

            save_path = os.path.join(self.output_dir, language, filename)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ {language.upper()}版预测结果图已保存: {save_path}")

def main():
    """测试函数"""
    print("🧪 测试数据处理流程可视化...")
    
    # 初始化可视化器
    viz = ProcessVisualization()
    
    # 生成原始信号可视化
    viz.create_raw_signal_visualization(None)
    
    # 生成事件检测可视化
    viz.create_event_detection_visualization(None)
    
    # 生成特征提取可视化
    viz.create_feature_extraction_visualization(None)
    
    print("✅ 测试完成!")

if __name__ == "__main__":
    main()
