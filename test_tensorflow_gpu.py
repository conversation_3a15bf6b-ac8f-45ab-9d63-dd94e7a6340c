#!/usr/bin/env python3
"""
TensorFlow GPU测试脚本
"""

import sys
import os

def test_tensorflow_gpu():
    """测试TensorFlow GPU功能"""
    print("🔍 测试TensorFlow GPU功能...")
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow版本: {tf.__version__}")
        
        # 检查GPU设备
        print("\n📊 GPU设备检查:")
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"✅ 检测到 {len(gpus)} 个GPU设备:")
            for i, gpu in enumerate(gpus):
                print(f"   GPU {i}: {gpu.name}")
                
            # 配置GPU内存增长
            try:
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                print("✅ GPU内存增长模式已启用")
            except Exception as e:
                print(f"⚠️  GPU内存配置警告: {str(e)}")
                
            # 测试GPU计算
            print("\n🧪 GPU计算测试:")
            try:
                with tf.device('/GPU:0'):
                    # 创建测试矩阵
                    a = tf.constant([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
                    b = tf.constant([[1.0, 1.0], [0.0, 1.0], [1.0, 0.0]])
                    c = tf.matmul(a, b)
                    
                print("✅ GPU矩阵运算测试成功")
                print(f"   结果形状: {c.shape}")
                print(f"   计算设备: {c.device}")
                
                # 测试更复杂的操作
                with tf.device('/GPU:0'):
                    x = tf.random.normal([1000, 1000])
                    y = tf.random.normal([1000, 1000])
                    z = tf.matmul(x, y)
                    
                print("✅ GPU大矩阵运算测试成功")
                print(f"   大矩阵结果形状: {z.shape}")
                
                return True
                
            except Exception as e:
                print(f"❌ GPU计算测试失败: {str(e)}")
                return False
        else:
            print("❌ 未检测到GPU设备")
            print("💡 可能的原因:")
            print("   1. CUDA驱动未正确安装")
            print("   2. TensorFlow GPU版本不兼容")
            print("   3. CUDA版本不匹配")
            return False
            
    except ImportError as e:
        print(f"❌ TensorFlow导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ TensorFlow GPU测试失败: {str(e)}")
        return False

def test_cuda_compatibility():
    """测试CUDA兼容性"""
    print("\n🔍 CUDA兼容性检查:")
    
    try:
        import tensorflow as tf
        
        # 检查CUDA版本
        cuda_version = tf.sysconfig.get_build_info().get('cuda_version', 'Unknown')
        cudnn_version = tf.sysconfig.get_build_info().get('cudnn_version', 'Unknown')
        
        print(f"   TensorFlow编译时CUDA版本: {cuda_version}")
        print(f"   TensorFlow编译时cuDNN版本: {cudnn_version}")
        
        # 检查是否构建了CUDA支持
        is_built_with_cuda = tf.test.is_built_with_cuda()
        print(f"   TensorFlow是否支持CUDA: {is_built_with_cuda}")
        
        if is_built_with_cuda:
            print("✅ TensorFlow已启用CUDA支持")
        else:
            print("❌ TensorFlow未启用CUDA支持")
            
    except Exception as e:
        print(f"❌ CUDA兼容性检查失败: {str(e)}")

def create_simple_model_test():
    """创建简单模型测试GPU训练"""
    print("\n🧪 GPU模型训练测试:")
    
    try:
        import tensorflow as tf
        import numpy as np
        
        # 检查GPU可用性
        if not tf.config.list_physical_devices('GPU'):
            print("❌ 无GPU设备，跳过模型训练测试")
            return False
        
        # 创建简单的测试数据
        X = np.random.random((1000, 10)).astype(np.float32)
        y = np.random.random((1000, 1)).astype(np.float32)
        
        # 在GPU上创建模型
        with tf.device('/GPU:0'):
            model = tf.keras.Sequential([
                tf.keras.layers.Dense(64, activation='relu', input_shape=(10,)),
                tf.keras.layers.Dense(32, activation='relu'),
                tf.keras.layers.Dense(1)
            ])
            
            model.compile(optimizer='adam', loss='mse', metrics=['mae'])
            
        print("✅ 模型已在GPU上创建")
        
        # 在GPU上训练模型
        print("   开始GPU训练测试...")
        history = model.fit(X, y, epochs=5, batch_size=32, verbose=0)
        
        print("✅ GPU模型训练测试成功")
        print(f"   最终损失: {history.history['loss'][-1]:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ GPU模型训练测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 TensorFlow GPU完整测试")
    print("=" * 50)
    
    # 测试基本GPU功能
    gpu_basic = test_tensorflow_gpu()
    
    # 测试CUDA兼容性
    test_cuda_compatibility()
    
    # 测试模型训练
    if gpu_basic:
        model_test = create_simple_model_test()
    else:
        model_test = False
    
    # 总结
    print(f"\n" + "=" * 50)
    print("📊 TensorFlow GPU测试总结")
    print("=" * 50)
    print(f"   基本GPU功能: {'✅' if gpu_basic else '❌'}")
    print(f"   模型训练测试: {'✅' if model_test else '❌'}")
    
    if gpu_basic and model_test:
        print("\n🎉 TensorFlow GPU配置成功！")
        print("💡 建议:")
        print("   - GPU内存增长模式已启用")
        print("   - 可以开始使用GPU训练深度学习模型")
        return True
    else:
        print("\n⚠️  TensorFlow GPU配置需要进一步调整")
        print("💡 建议:")
        if not gpu_basic:
            print("   - 检查CUDA驱动安装")
            print("   - 验证TensorFlow版本兼容性")
        print("   - 考虑重新安装TensorFlow GPU版本")
        return False

if __name__ == "__main__":
    main()
