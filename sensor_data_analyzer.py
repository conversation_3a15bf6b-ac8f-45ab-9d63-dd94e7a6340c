#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sensor Data Analyzer for Vibration Signal Analysis System
Extracts and visualizes time-frequency domain features from specific sensors

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import sys
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from scipy import signal
from scipy.fft import fft, fftfreq
import pywt
import warnings
warnings.filterwarnings('ignore')

class SensorDataAnalyzer:
    """Sensor-specific vibration data analyzer and visualizer"""
    
    def __init__(self, output_dir: str = "unified_charts", file_prefix: str = "sensor_analysis_"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.file_prefix = file_prefix
        
        # Setup academic style
        self.setup_academic_style()
        
        # Analysis parameters
        self.sampling_rate = 1000  # Hz, typical for vibration sensors
        self.sensor_data = None
        self.sensor_id = None
        self.time_vector = None
        
    def setup_academic_style(self):
        """Setup academic publication style with Times New Roman font"""
        plt.rcParams['font.family'] = 'serif'
        plt.rcParams['font.serif'] = ['Times New Roman', 'Times', 'DejaVu Serif']
        plt.rcParams['mathtext.fontset'] = 'stix'
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['figure.dpi'] = 330
        plt.rcParams['savefig.dpi'] = 330
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.titlesize'] = 16
        plt.rcParams['axes.labelsize'] = 14
        plt.rcParams['xtick.labelsize'] = 12
        plt.rcParams['ytick.labelsize'] = 12
        plt.rcParams['legend.fontsize'] = 12
        plt.rcParams['figure.titlesize'] = 18
        
        # IEEE/Elsevier color scheme
        self.colors = {
            'primary': '#1f77b4', 'secondary': '#ff7f0e', 'success': '#2ca02c',
            'danger': '#d62728', 'warning': '#ff7f0e', 'info': '#17a2b8'
        }
    
    def load_processed_features(self, features_file: str = None, sensor_id: str = None):
        """Load processed features from combined_features.csv or similar files"""
        try:
            # Try to find features file
            if features_file is None:
                possible_files = [
                    'combined_features.csv',
                    './ml/combined_features_clean.csv',
                    './analysis_results/combined_features.csv'
                ]

                features_file = None
                for file_path in possible_files:
                    if os.path.exists(file_path):
                        features_file = file_path
                        break

                if features_file is None:
                    raise FileNotFoundError("No processed features file found. Please run main analysis first.")

            print(f"📂 Loading processed features from: {features_file}")

            # Read features file
            df = pd.read_csv(features_file)
            print(f"   📊 Features data shape: {df.shape}")
            print(f"   📋 Available columns: {len(df.columns)} features")

            # Filter by sensor if specified
            if sensor_id is not None:
                # Convert sensor_id format (e.g., "Sensor_01" -> "sensor_01")
                sensor_filter = sensor_id.lower().replace('sensor_', 'sensor_')

                # Filter data for specific sensor
                if 'sensor_id' in df.columns:
                    sensor_data = df[df['sensor_id'] == sensor_filter]
                else:
                    # If no sensor_id column, use all data
                    sensor_data = df
                    print(f"   ⚠️ No sensor_id column found, using all data")

                if sensor_data.empty:
                    available_sensors = df['sensor_id'].unique() if 'sensor_id' in df.columns else ['all_sensors']
                    raise ValueError(f"No data found for {sensor_id}. Available sensors: {available_sensors}")

                print(f"   🔍 Filtered to {len(sensor_data)} records for {sensor_id}")
            else:
                sensor_data = df
                print(f"   📊 Using all sensor data: {len(sensor_data)} records")

            # Store processed data
            self.features_df = sensor_data
            self.sensor_id = sensor_id or "all_sensors"

            # Extract basic statistics for visualization
            self._extract_visualization_data()

            print(f"   ✅ Successfully loaded processed features for {self.sensor_id}")
            return True

        except Exception as e:
            print(f"   ❌ Error loading processed features: {str(e)}")
            return False

    def _extract_visualization_data(self):
        """Extract data for visualization from processed features"""
        try:
            # Generate synthetic time series data based on features for visualization
            # This is needed since we only have features, not raw time series

            if self.features_df is None or self.features_df.empty:
                return False

            # Use the first record for visualization
            record = self.features_df.iloc[0]

            # Generate synthetic signal based on statistical features
            duration = 1.0  # 1 second
            n_samples = int(duration * self.sampling_rate)
            time_vector = np.linspace(0, duration, n_samples)

            # Extract key features
            mean_val = record.get('mean', 0.0)
            std_val = record.get('std', 1.0)
            dominant_freq = record.get('dominant_frequency', 50.0)

            # Generate synthetic signal that matches the features
            # Base signal with dominant frequency
            signal = np.sin(2 * np.pi * dominant_freq * time_vector)

            # Add harmonics
            signal += 0.5 * np.sin(2 * np.pi * dominant_freq * 2 * time_vector)
            signal += 0.3 * np.sin(2 * np.pi * dominant_freq * 3 * time_vector)

            # Scale to match RMS and add noise
            signal = signal * std_val * 0.7  # Scale to approximate std
            signal += np.random.normal(0, std_val * 0.3, n_samples)  # Add noise
            signal += mean_val  # Add mean offset

            # Store synthetic data for visualization
            self.sensor_data = signal
            self.time_vector = time_vector

            print(f"   🔧 Generated synthetic visualization data based on features")
            print(f"   📊 Signal stats: mean={np.mean(signal):.4f}, std={np.std(signal):.4f}")

            return True

        except Exception as e:
            print(f"   ❌ Error extracting visualization data: {str(e)}")
            return False
    
    def preprocess_data(self, apply_filter: bool = True, filter_type: str = 'bandpass'):
        """Preprocess synthetic visualization data (minimal processing since features are already processed)"""
        if self.sensor_data is None:
            print("❌ No sensor data available for preprocessing.")
            return False

        print(f"🔧 Applying minimal preprocessing to visualization data...")

        try:
            # Since we're working with already processed features,
            # we only need minimal preprocessing for visualization

            # Remove DC component if needed
            if apply_filter:
                self.sensor_data = self.sensor_data - np.mean(self.sensor_data)
                print(f"   🔍 Removed DC component")

            # Light smoothing for better visualization
            if len(self.sensor_data) > 10:
                from scipy.ndimage import gaussian_filter1d
                self.sensor_data = gaussian_filter1d(self.sensor_data, sigma=1.0)
                print(f"   🔍 Applied light smoothing")

            print(f"   ✅ Preprocessing completed (minimal processing for visualization)")
            print(f"   📊 Final data stats: mean={np.mean(self.sensor_data):.4f}, std={np.std(self.sensor_data):.4f}")

            return True

        except Exception as e:
            print(f"   ❌ Error in preprocessing: {str(e)}")
            return False
    
    def generate_time_domain_analysis(self):
        """Generate time domain analysis visualization using processed features"""
        if self.features_df is None or self.features_df.empty:
            print("❌ No processed features available for analysis")
            return False

        print(f"📈 Generating time domain analysis for {self.sensor_id}...")

        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(f'Time Domain Analysis - {self.sensor_id} (From Processed Features)',
                        fontsize=18, fontweight='bold', y=0.95)

            # Use synthetic signal for time series visualization
            if self.sensor_data is not None and self.time_vector is not None:
                ax1.plot(self.time_vector, self.sensor_data, color=self.colors['primary'],
                        linewidth=0.8, alpha=0.8)
                ax1.set_title('Reconstructed Time Series Signal', fontsize=16, pad=20)
                ax1.set_xlabel('Time (s)', fontsize=14)
                ax1.set_ylabel('Amplitude (m/s²)', fontsize=14)
                ax1.grid(True, alpha=0.3)

                # Add statistics from actual features
                record = self.features_df.iloc[0]
                actual_mean = record.get('mean', np.mean(self.sensor_data))
                actual_std = record.get('std', np.std(self.sensor_data))
                actual_rms = record.get('rms', np.sqrt(np.mean(self.sensor_data**2)))

                stats_text = f'Mean: {actual_mean:.4f}\nStd: {actual_std:.4f}\nRMS: {actual_rms:.4f}'
                ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, fontsize=10,
                        verticalalignment='top', bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
            else:
                # If no synthetic signal, show feature-based visualization
                ax1.text(0.5, 0.5, 'Time series reconstructed\nfrom processed features',
                        transform=ax1.transAxes, ha='center', va='center', fontsize=14)
                ax1.set_title('Time Series (Feature-based)', fontsize=16, pad=20)
            
            # Histogram - use processed features for better accuracy
            record = self.features_df.iloc[0]

            if self.sensor_data is not None:
                # Use synthetic data for histogram
                ax2.hist(self.sensor_data, bins=50, alpha=0.7, color=self.colors['secondary'],
                        edgecolor='black', linewidth=0.5, density=True)

                # Use actual feature values for overlay
                mu = record.get('mean', np.mean(self.sensor_data))
                sigma = record.get('std', np.std(self.sensor_data))
                x = np.linspace(np.min(self.sensor_data), np.max(self.sensor_data), 100)
                normal_dist = (1/(sigma * np.sqrt(2 * np.pi))) * np.exp(-0.5 * ((x - mu) / sigma) ** 2)
                ax2.plot(x, normal_dist, 'r--', linewidth=2, alpha=0.8, label='Normal Distribution')
                ax2.legend()
            else:
                # Feature-based histogram visualization
                mu = record.get('mean', 0)
                sigma = record.get('std', 1)
                x = np.linspace(mu - 3*sigma, mu + 3*sigma, 100)
                normal_dist = (1/(sigma * np.sqrt(2 * np.pi))) * np.exp(-0.5 * ((x - mu) / sigma) ** 2)
                ax2.plot(x, normal_dist, color=self.colors['secondary'], linewidth=2, alpha=0.8)
                ax2.fill_between(x, normal_dist, alpha=0.3, color=self.colors['secondary'])

            ax2.set_title('Amplitude Distribution', fontsize=16, pad=20)
            ax2.set_xlabel('Amplitude (m/s²)', fontsize=14)
            ax2.set_ylabel('Probability Density', fontsize=14)
            ax2.grid(True, alpha=0.3)

            # Autocorrelation - simplified for processed features
            if self.sensor_data is not None and len(self.sensor_data) > 100:
                autocorr = np.correlate(self.sensor_data, self.sensor_data, mode='full')
                autocorr = autocorr[autocorr.size // 2:]
                autocorr = autocorr / autocorr[0]  # Normalize
                lags = np.arange(len(autocorr)) / self.sampling_rate

                # Plot only first 200 lags for clarity
                max_lags = min(200, len(autocorr))
                ax3.plot(lags[:max_lags], autocorr[:max_lags], color=self.colors['success'],
                        linewidth=1.5, alpha=0.8)
            else:
                # Generate theoretical autocorrelation based on dominant frequency
                dominant_freq = record.get('dominant_frequency', 50.0)
                lags = np.linspace(0, 0.2, 200)  # 0.2 seconds
                autocorr = np.exp(-lags * 5) * np.cos(2 * np.pi * dominant_freq * lags)
                ax3.plot(lags, autocorr, color=self.colors['success'], linewidth=1.5, alpha=0.8)

            ax3.set_title('Autocorrelation Function', fontsize=16, pad=20)
            ax3.set_xlabel('Lag (s)', fontsize=14)
            ax3.set_ylabel('Autocorrelation', fontsize=14)
            ax3.grid(True, alpha=0.3)
            ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)

            # Statistical features - use processed features directly
            features = {
                'RMS': record.get('rms', 0),
                'Peak': record.get('peak', 0),
                'Crest Factor': record.get('crest_factor', 0),
                'Skewness': record.get('skewness', 0),
                'Kurtosis': record.get('kurtosis', 0),
                'Energy': record.get('energy', 0)
            }
            
            feature_names = list(features.keys())
            feature_values = list(features.values())
            
            bars = ax4.bar(range(len(feature_names)), feature_values, 
                          color=[self.colors['primary'], self.colors['secondary'], self.colors['success'],
                                self.colors['warning'], self.colors['info'], self.colors['danger']], 
                          alpha=0.8)
            ax4.set_title('Statistical Features', fontsize=16, pad=20)
            ax4.set_ylabel('Feature Value', fontsize=14)
            ax4.set_xlabel('Features', fontsize=14)
            ax4.set_xticks(range(len(feature_names)))
            ax4.set_xticklabels(feature_names, rotation=45, ha='right')
            ax4.grid(True, alpha=0.3)
            
            # Add value labels on bars
            for bar, value in zip(bars, feature_values):
                ax4.text(bar.get_x() + bar.get_width()/2., bar.get_height() + max(feature_values) * 0.01,
                        f'{value:.3f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
            
            plt.tight_layout()
            plt.subplots_adjust(top=0.92)
            
            # Save chart
            chart_path = self.output_dir / f'{self.file_prefix}{self.sensor_id}_time_domain_analysis.png'
            plt.savefig(chart_path, dpi=330, bbox_inches='tight', facecolor='white')
            plt.close()
            
            print(f"   ✅ Time domain analysis saved: {chart_path}")
            return True
            
        except Exception as e:
            print(f"   ❌ Error generating time domain analysis: {str(e)}")
            return False

    def generate_frequency_domain_analysis(self):
        """Generate frequency domain analysis visualization"""
        if self.sensor_data is None:
            print("❌ No sensor data available for analysis")
            return False

        print(f"🔊 Generating frequency domain analysis for {self.sensor_id}...")

        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(f'Frequency Domain Analysis - {self.sensor_id}',
                        fontsize=18, fontweight='bold', y=0.95)

            # FFT analysis
            n_samples = len(self.sensor_data)
            fft_values = fft(self.sensor_data)
            fft_freqs = fftfreq(n_samples, 1/self.sampling_rate)

            # Take only positive frequencies
            positive_freq_idx = fft_freqs > 0
            freqs = fft_freqs[positive_freq_idx]
            magnitude = np.abs(fft_values[positive_freq_idx])

            # Power Spectral Density
            ax1.plot(freqs, magnitude, color=self.colors['primary'], linewidth=1, alpha=0.8)
            ax1.set_title('Power Spectral Density', fontsize=16, pad=20)
            ax1.set_xlabel('Frequency (Hz)', fontsize=14)
            ax1.set_ylabel('Magnitude', fontsize=14)
            ax1.set_xlim(0, min(200, self.sampling_rate/2))  # Focus on 0-200 Hz
            ax1.grid(True, alpha=0.3)
            ax1.set_yscale('log')

            # Find dominant frequencies
            peak_indices = signal.find_peaks(magnitude, height=np.max(magnitude)*0.1)[0]
            dominant_freqs = freqs[peak_indices][:5]  # Top 5 peaks
            dominant_mags = magnitude[peak_indices][:5]

            for freq, mag in zip(dominant_freqs, dominant_mags):
                ax1.axvline(x=freq, color='red', linestyle='--', alpha=0.7)
                ax1.text(freq, mag, f'{freq:.1f}Hz', rotation=90,
                        verticalalignment='bottom', fontsize=9)

            # Spectrogram
            f_spec, t_spec, Sxx = signal.spectrogram(self.sensor_data, fs=self.sampling_rate,
                                                    nperseg=min(256, len(self.sensor_data)//4))

            im = ax2.pcolormesh(t_spec, f_spec, 10 * np.log10(Sxx), shading='gouraud', cmap='viridis')
            ax2.set_title('Spectrogram', fontsize=16, pad=20)
            ax2.set_xlabel('Time (s)', fontsize=14)
            ax2.set_ylabel('Frequency (Hz)', fontsize=14)
            ax2.set_ylim(0, min(200, self.sampling_rate/2))
            plt.colorbar(im, ax=ax2, label='Power (dB)')

            # Frequency bands analysis
            freq_bands = {
                'Low (1-10 Hz)': (1, 10),
                'Medium (10-50 Hz)': (10, 50),
                'High (50-100 Hz)': (50, 100),
                'Very High (100-200 Hz)': (100, 200)
            }

            band_powers = []
            band_names = []

            for band_name, (low_f, high_f) in freq_bands.items():
                band_mask = (freqs >= low_f) & (freqs <= high_f)
                if np.any(band_mask):
                    band_power = np.sum(magnitude[band_mask]**2)
                    band_powers.append(band_power)
                    band_names.append(band_name)

            bars = ax3.bar(range(len(band_names)), band_powers,
                          color=[self.colors['primary'], self.colors['secondary'],
                                self.colors['success'], self.colors['warning']], alpha=0.8)
            ax3.set_title('Power Distribution by Frequency Bands', fontsize=16, pad=20)
            ax3.set_ylabel('Power', fontsize=14)
            ax3.set_xlabel('Frequency Bands', fontsize=14)
            ax3.set_xticks(range(len(band_names)))
            ax3.set_xticklabels(band_names, rotation=45, ha='right')
            ax3.grid(True, alpha=0.3)
            ax3.set_yscale('log')

            # Add percentage labels
            total_power = sum(band_powers)
            for bar, power in zip(bars, band_powers):
                percentage = (power / total_power) * 100
                ax3.text(bar.get_x() + bar.get_width()/2., bar.get_height() * 1.1,
                        f'{percentage:.1f}%', ha='center', va='bottom', fontsize=10, fontweight='bold')

            # Phase analysis
            phase = np.angle(fft_values[positive_freq_idx])
            ax4.plot(freqs, phase, color=self.colors['info'], linewidth=1, alpha=0.8)
            ax4.set_title('Phase Spectrum', fontsize=16, pad=20)
            ax4.set_xlabel('Frequency (Hz)', fontsize=14)
            ax4.set_ylabel('Phase (radians)', fontsize=14)
            ax4.set_xlim(0, min(200, self.sampling_rate/2))
            ax4.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.subplots_adjust(top=0.92)

            # Save chart
            chart_path = self.output_dir / f'{self.file_prefix}{self.sensor_id}_frequency_domain_analysis.png'
            plt.savefig(chart_path, dpi=330, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"   ✅ Frequency domain analysis saved: {chart_path}")
            return True

        except Exception as e:
            print(f"   ❌ Error generating frequency domain analysis: {str(e)}")
            return False

    def generate_time_frequency_analysis(self):
        """Generate time-frequency domain analysis visualization"""
        if self.sensor_data is None:
            print("❌ No sensor data available for analysis")
            return False

        print(f"🌊 Generating time-frequency analysis for {self.sensor_id}...")

        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(f'Time-Frequency Analysis - {self.sensor_id}',
                        fontsize=18, fontweight='bold', y=0.95)

            # Short-Time Fourier Transform (STFT)
            f_stft, t_stft, Zxx = signal.stft(self.sensor_data, fs=self.sampling_rate,
                                             nperseg=min(256, len(self.sensor_data)//8))

            im1 = ax1.pcolormesh(t_stft, f_stft, np.abs(Zxx), shading='gouraud', cmap='plasma')
            ax1.set_title('Short-Time Fourier Transform (STFT)', fontsize=16, pad=20)
            ax1.set_xlabel('Time (s)', fontsize=14)
            ax1.set_ylabel('Frequency (Hz)', fontsize=14)
            ax1.set_ylim(0, min(200, self.sampling_rate/2))
            plt.colorbar(im1, ax=ax1, label='Magnitude')

            # Continuous Wavelet Transform
            scales = np.arange(1, 128)
            wavelet = 'morl'  # Morlet wavelet
            coefficients, frequencies = pywt.cwt(self.sensor_data, scales, wavelet,
                                                sampling_period=1/self.sampling_rate)

            time_cwt = np.arange(len(self.sensor_data)) / self.sampling_rate
            im2 = ax2.pcolormesh(time_cwt, frequencies, np.abs(coefficients),
                               shading='gouraud', cmap='viridis')
            ax2.set_title('Continuous Wavelet Transform (CWT)', fontsize=16, pad=20)
            ax2.set_xlabel('Time (s)', fontsize=14)
            ax2.set_ylabel('Frequency (Hz)', fontsize=14)
            ax2.set_ylim(0, min(200, max(frequencies)))
            plt.colorbar(im2, ax=ax2, label='Magnitude')

            # Instantaneous frequency and amplitude
            analytic_signal = signal.hilbert(self.sensor_data)
            instantaneous_amplitude = np.abs(analytic_signal)
            instantaneous_phase = np.unwrap(np.angle(analytic_signal))
            instantaneous_frequency = (np.diff(instantaneous_phase) /
                                     (2.0*np.pi) * self.sampling_rate)

            ax3.plot(self.time_vector[:-1], instantaneous_frequency,
                    color=self.colors['danger'], linewidth=1, alpha=0.8)
            ax3.set_title('Instantaneous Frequency', fontsize=16, pad=20)
            ax3.set_xlabel('Time (s)', fontsize=14)
            ax3.set_ylabel('Frequency (Hz)', fontsize=14)
            ax3.grid(True, alpha=0.3)
            ax3.set_ylim(0, min(100, self.sampling_rate/4))

            # Time-frequency energy distribution
            window_size = min(100, len(self.sensor_data)//10)
            hop_size = window_size // 2

            time_windows = []
            energy_low = []
            energy_mid = []
            energy_high = []

            for i in range(0, len(self.sensor_data) - window_size, hop_size):
                window_data = self.sensor_data[i:i+window_size]
                window_time = self.time_vector[i + window_size//2]

                # FFT of window
                window_fft = fft(window_data)
                window_freqs = fftfreq(len(window_data), 1/self.sampling_rate)
                window_magnitude = np.abs(window_fft)

                # Energy in different bands
                low_mask = (window_freqs >= 1) & (window_freqs <= 20)
                mid_mask = (window_freqs >= 20) & (window_freqs <= 80)
                high_mask = (window_freqs >= 80) & (window_freqs <= 200)

                energy_low.append(np.sum(window_magnitude[low_mask]**2))
                energy_mid.append(np.sum(window_magnitude[mid_mask]**2))
                energy_high.append(np.sum(window_magnitude[high_mask]**2))
                time_windows.append(window_time)

            ax4.plot(time_windows, energy_low, label='Low (1-20 Hz)',
                    color=self.colors['primary'], linewidth=2, alpha=0.8)
            ax4.plot(time_windows, energy_mid, label='Mid (20-80 Hz)',
                    color=self.colors['secondary'], linewidth=2, alpha=0.8)
            ax4.plot(time_windows, energy_high, label='High (80-200 Hz)',
                    color=self.colors['success'], linewidth=2, alpha=0.8)

            ax4.set_title('Time-Varying Frequency Band Energy', fontsize=16, pad=20)
            ax4.set_xlabel('Time (s)', fontsize=14)
            ax4.set_ylabel('Energy', fontsize=14)
            ax4.grid(True, alpha=0.3)
            ax4.legend()
            ax4.set_yscale('log')

            plt.tight_layout()
            plt.subplots_adjust(top=0.92)

            # Save chart
            chart_path = self.output_dir / f'{self.file_prefix}{self.sensor_id}_time_frequency_analysis.png'
            plt.savefig(chart_path, dpi=330, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"   ✅ Time-frequency analysis saved: {chart_path}")
            return True

        except Exception as e:
            print(f"   ❌ Error generating time-frequency analysis: {str(e)}")
            return False

    def analyze_sensor(self, features_file: str = None, sensor_id: str = None,
                      apply_preprocessing: bool = True):
        """Complete sensor analysis workflow using processed features"""
        print(f"🚀 Starting sensor analysis for {sensor_id or 'all sensors'}")
        print("=" * 80)

        # Load processed features instead of raw data
        if not self.load_processed_features(features_file, sensor_id):
            return False

        # Apply minimal preprocessing for visualization
        if apply_preprocessing:
            if not self.preprocess_data():
                print("⚠️ Preprocessing failed, continuing with raw features...")

        # Generate all analyses
        results = {
            'time_domain': self.generate_time_domain_analysis(),
            'frequency_domain': self.generate_frequency_domain_analysis(),
            'time_frequency': self.generate_time_frequency_analysis()
        }

        # Summary
        print("\n" + "=" * 80)
        print("📊 Analysis Summary")
        print("=" * 80)

        success_count = sum(results.values())
        total_analyses = len(results)

        for analysis_type, success in results.items():
            status = "✅ Success" if success else "❌ Failed"
            print(f"   {status} - {analysis_type.replace('_', ' ').title()}")

        print(f"\n📈 Overall Success Rate: {success_count}/{total_analyses} ({success_count/total_analyses*100:.1f}%)")
        print(f"📁 Output Directory: {self.output_dir}")
        print(f"🔧 Sensor Analyzed: {self.sensor_id}")
        print(f"📊 Feature Records: {len(self.features_df) if self.features_df is not None else 0}")
        print(f"⚠️ Note: Analysis based on processed features, not raw sensor data")

        return success_count == total_analyses


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description='Sensor Data Analyzer for Vibration Signal Analysis',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python sensor_data_analyzer.py data.csv Sensor_01
  python sensor_data_analyzer.py data.csv Sensor_01 --start 1000 --end 5000
  python sensor_data_analyzer.py data.csv Sensor_01 --no-preprocessing
        """
    )

    parser.add_argument('file_path', help='Path to CSV data file')
    parser.add_argument('sensor_id', help='Sensor ID (e.g., Sensor_01)')
    parser.add_argument('--start', type=int, help='Start index for data extraction')
    parser.add_argument('--end', type=int, help='End index for data extraction')
    parser.add_argument('--no-preprocessing', action='store_true',
                       help='Skip data preprocessing')
    parser.add_argument('--output-dir', default='unified_charts',
                       help='Output directory for charts (default: unified_charts)')
    parser.add_argument('--prefix', default='sensor_analysis_',
                       help='File prefix for generated charts (default: sensor_analysis_)')

    args = parser.parse_args()

    # Validate inputs
    if not os.path.exists(args.file_path):
        print(f"❌ Error: Data file not found: {args.file_path}")
        sys.exit(1)

    # Create analyzer
    analyzer = SensorDataAnalyzer(output_dir=args.output_dir, file_prefix=args.prefix)

    # Run analysis
    success = analyzer.analyze_sensor(
        file_path=args.file_path,
        sensor_id=args.sensor_id,
        start_idx=args.start,
        end_idx=args.end,
        apply_preprocessing=not args.no_preprocessing
    )

    if success:
        print("\n🎉 Sensor analysis completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Sensor analysis failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
