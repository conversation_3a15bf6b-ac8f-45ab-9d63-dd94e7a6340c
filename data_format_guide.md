# 振动信号数据格式规范

## 1. 振动信号数据文件格式

### 文件命名规范
```
vibration_YYYYMMDD_HHMMSS_sensor[ID].csv
例如: vibration_20241207_143000_sensor01.csv
```

### CSV文件结构
```csv
timestamp,sensor_1_x,sensor_1_y,sensor_1_z,sensor_2_x,sensor_2_y,sensor_2_z,temperature,humidity
2024-12-07 14:30:00.000,0.125,-0.089,0.234,0.156,-0.078,0.198,23.5,45.2
2024-12-07 14:30:00.010,0.128,-0.091,0.231,0.159,-0.081,0.195,23.5,45.2
...
```

### 技术要求
- **采样率**: 1000Hz (推荐) 或 500Hz (最低)
- **传感器数量**: 2-8个三轴加速度传感器
- **数据精度**: 浮点数，至少3位小数
- **时间戳**: ISO 8601格式，毫秒精度
- **文件大小**: 单文件不超过100MB

## 2. 标签数据文件格式

### 速度标签文件 (speed_labels.csv)
```csv
timestamp,vehicle_id,speed_kmh,confidence
2024-12-07 14:30:00.000,VH001,65.5,0.95
2024-12-07 14:30:15.000,VH002,72.3,0.98
```

### 轴重标签文件 (load_labels.csv)
```csv
timestamp,vehicle_id,axle_1_load,axle_2_load,axle_3_load,total_load,confidence
2024-12-07 14:30:00.000,VH001,2.5,3.2,2.8,8.5,0.92
```

### 轴型标签文件 (type_labels.csv)
```csv
timestamp,vehicle_id,axle_type,axle_count,vehicle_class,confidence
2024-12-07 14:30:00.000,VH001,2,2,passenger_car,0.96
2024-12-07 14:30:15.000,VH002,3,3,truck,0.94
```

## 3. 数据质量要求

### 必需字段验证
- 时间戳连续性检查
- 传感器数据完整性验证
- 标签数据匹配性检查
- 异常值检测和标记

### 数据完整性标准
- 缺失值比例 < 5%
- 异常值比例 < 10%
- 时间戳对齐误差 < 50ms
- 传感器信号信噪比 > 20dB
