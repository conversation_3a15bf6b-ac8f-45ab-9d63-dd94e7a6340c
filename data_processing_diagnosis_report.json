{"diagnosis_results": {"has_data_source_column": false, "current_samples": 1398, "current_features": 51, "total_csv_files": 3686, "new_format_files": 3398, "legacy_format_files": 288, "other_files": 0, "processed_files.json_processed": {"total": 3398, "new_format": 3398, "legacy_format": 0}, "expansion_processor_available": true}, "issues": ["缺少data_source列标识数据来源", "样本数量不足: 当前1398个，预期3686个", "新格式数据未被处理"], "recommendations": ["修复数据合并逻辑，确保添加data_source列", "检查文件过滤逻辑和数据处理流程", "检查enhanced_data_expansion_processor的调用和返回值处理"], "fix_plan": {"priority_1": [{"task": "修复enhanced_data_expansion_processor调用逻辑", "description": "确保新格式数据被正确处理并返回", "files": ["unified_vibration_analysis.py"], "action": "修复extract_features_enhanced方法中的数据读取和合并逻辑"}, {"task": "添加数据源标识", "description": "确保所有处理的数据都有data_source列", "files": ["unified_vibration_analysis.py"], "action": "修复数据合并时的列标识逻辑"}], "priority_2": [{"task": "优化文件过滤逻辑", "description": "确保所有符合条件的文件都被处理", "files": ["unified_vibration_analysis.py", "enhanced_data_expansion_processor.py"], "action": "检查并修复文件识别和过滤条件"}], "priority_3": []}, "timestamp": "2025-06-24T10:24:32.174316"}