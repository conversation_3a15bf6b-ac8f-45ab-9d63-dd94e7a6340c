#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集优化和模型性能提升管理器
统一管理数据优化和模型提升流程

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import chinese_font_config  # 中文字体配置
import pandas as pd
import numpy as np
import os
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

from dataset_optimization import DatasetOptimizer
from model_performance_enhancer import ModelPerformanceEnhancer

class OptimizationManager:
    """数据集优化和模型性能提升管理器"""
    
    def __init__(self, output_dir: str = "optimization_results"):
        """
        初始化优化管理器
        
        参数:
        output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化子模块
        self.dataset_optimizer = DatasetOptimizer(str(self.output_dir / "dataset_optimization"))
        self.performance_enhancer = ModelPerformanceEnhancer(str(self.output_dir / "performance_enhancement"))
        
        # 优化结果存储
        self.optimization_results = {
            'dataset_optimization': {},
            'performance_enhancement': {},
            'overall_summary': {}
        }
        
        print(f"🚀 优化管理器已初始化")
        print(f"   输出目录: {self.output_dir}")
    
    def run_complete_optimization(self, data_file: str, target_column: str, 
                                task_name: str = "prediction_task") -> Dict[str, Any]:
        """
        运行完整的优化流程
        
        参数:
        data_file: 数据文件路径
        target_column: 目标列名
        task_name: 任务名称
        
        返回:
        complete_results: 完整优化结果
        """
        try:
            print(f"🚀 开始完整优化流程: {task_name}")
            print(f"=" * 60)
            
            # 1. 加载数据
            print(f"\n📂 步骤1: 加载数据集")
            df = self.dataset_optimizer.load_dataset(data_file)
            if df is None:
                return {'success': False, 'message': '数据加载失败'}
            
            # 2. 数据分布分析
            print(f"\n📊 步骤2: 数据分布分析")
            distribution_info = self.dataset_optimizer.analyze_data_distribution(df, target_column)
            
            # 3. 数据集划分
            print(f"\n🔄 步骤3: 数据集划分")
            datasets = self.dataset_optimizer.stratified_split_dataset(df, target_column)
            if not datasets:
                return {'success': False, 'message': '数据集划分失败'}
            
            # 4. 异常值检测
            print(f"\n🔍 步骤4: 异常值检测")
            outlier_info = self.dataset_optimizer.comprehensive_outlier_detection(df, target_column)
            
            # 5. 异常值可视化
            print(f"\n📊 步骤5: 异常值可视化")
            outlier_viz_path = self.output_dir / f"{task_name}_outlier_analysis.png"
            self.dataset_optimizer.visualize_outliers(df, target_column, outlier_info, outlier_viz_path)
            
            # 6. 数据清理
            print(f"\n🧹 步骤6: 数据清理")
            cleaned_df = self.dataset_optimizer.clean_dataset(df, outlier_info, strategy='remove')
            
            # 重新划分清理后的数据
            cleaned_datasets = self.dataset_optimizer.stratified_split_dataset(cleaned_df, target_column)
            
            # 7. 特征工程
            print(f"\n🔧 步骤7: 增强特征工程")
            X_train = cleaned_datasets['train'].drop(columns=[target_column])
            y_train = cleaned_datasets['train'][target_column]
            X_val = cleaned_datasets['validation'].drop(columns=[target_column])
            y_val = cleaned_datasets['validation'][target_column]
            X_test = cleaned_datasets['test'].drop(columns=[target_column])
            y_test = cleaned_datasets['test'][target_column]
            
            # 增强特征工程
            X_train_enhanced = self.performance_enhancer.enhanced_feature_engineering(X_train, y_train)
            
            # 对验证集和测试集应用相同的特征工程
            # 注意：这里需要确保特征一致性
            common_features = list(set(X_train_enhanced.columns) & set(X_val.columns))
            if len(common_features) < len(X_train_enhanced.columns):
                print(f"   ⚠️  特征数量不一致，使用公共特征: {len(common_features)}")
                X_train_enhanced = X_train_enhanced[common_features]
                X_val_enhanced = X_val[common_features]
                X_test_enhanced = X_test[common_features]
            else:
                X_val_enhanced = X_val
                X_test_enhanced = X_test
            
            # 8. 基线模型训练
            print(f"\n🎯 步骤8: 基线模型训练")
            baseline_model = self.performance_enhancer.models['random_forest']
            baseline_model.fit(X_train, y_train)
            
            y_val_pred_baseline = baseline_model.predict(X_val)
            from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error

            baseline_results = {
                'model': baseline_model,
                'val_metrics': {
                    'r2': r2_score(y_val, y_val_pred_baseline),
                    'mae': mean_absolute_error(y_val, y_val_pred_baseline),
                    'rmse': np.sqrt(mean_squared_error(y_val, y_val_pred_baseline))
                }
            }
            
            # 9. 模型优化
            print(f"\n🚀 步骤9: 模型性能优化")
            optimized_models = {}
            
            for model_name in self.performance_enhancer.models.keys():
                print(f"\n   优化模型: {model_name}")
                result = self.performance_enhancer.optimize_single_model(
                    model_name, X_train_enhanced, y_train, X_val_enhanced, y_val
                )
                if result:
                    optimized_models[model_name] = result
            
            # 10. 集成学习
            print(f"\n🔗 步骤10: 集成学习")
            ensemble_results = self.performance_enhancer.create_ensemble_models(
                optimized_models, X_train_enhanced, y_train, X_val_enhanced, y_val
            )
            
            # 合并所有模型结果
            all_enhanced_results = {**optimized_models, **ensemble_results}
            
            # 11. 性能评估
            print(f"\n📊 步骤11: 性能提升评估")
            improvement_analysis = self.performance_enhancer.evaluate_performance_improvement(
                baseline_results, all_enhanced_results, task_name
            )
            
            # 12. 性能可视化
            print(f"\n📈 步骤12: 性能对比可视化")
            performance_viz_path = self.output_dir / f"{task_name}_performance_comparison.png"
            self.performance_enhancer.visualize_performance_comparison(
                baseline_results, all_enhanced_results, task_name, performance_viz_path
            )
            
            # 13. 最终测试
            print(f"\n🎯 步骤13: 最终模型测试")
            best_model_name = improvement_analysis.get('best_model')
            final_test_results = {}
            
            if best_model_name and best_model_name in all_enhanced_results:
                best_model = all_enhanced_results[best_model_name]['model']
                y_test_pred = best_model.predict(X_test_enhanced)
                
                final_test_results = {
                    'best_model': best_model_name,
                    'test_metrics': {
                        'r2': r2_score(y_test, y_test_pred),
                        'mae': mean_absolute_error(y_test, y_test_pred),
                        'rmse': np.sqrt(mean_squared_error(y_test, y_test_pred))
                    },
                    'predictions': y_test_pred
                }
                
                print(f"   最佳模型: {best_model_name}")
                print(f"   测试R²: {final_test_results['test_metrics']['r2']:.4f}")
                print(f"   测试MAE: {final_test_results['test_metrics']['mae']:.4f}")
            
            # 14. 生成综合报告
            print(f"\n📝 步骤14: 生成综合报告")
            report_path = self.generate_comprehensive_report(
                task_name, distribution_info, outlier_info, 
                improvement_analysis, final_test_results
            )
            
            # 整理完整结果
            complete_results = {
                'success': True,
                'task_name': task_name,
                'data_info': {
                    'original_samples': len(df),
                    'cleaned_samples': len(cleaned_df),
                    'features': len(X_train_enhanced.columns),
                    'target_column': target_column
                },
                'dataset_optimization': {
                    'distribution_info': distribution_info,
                    'outlier_info': outlier_info,
                    'datasets': {k: len(v) for k, v in cleaned_datasets.items()}
                },
                'performance_enhancement': {
                    'baseline_performance': baseline_results['val_metrics'],
                    'best_enhanced_performance': improvement_analysis.get('enhanced_performance', {}),
                    'improvement_analysis': improvement_analysis,
                    'final_test_results': final_test_results
                },
                'output_files': {
                    'outlier_visualization': str(outlier_viz_path),
                    'performance_comparison': str(performance_viz_path),
                    'comprehensive_report': report_path
                }
            }
            
            # 保存结果
            results_file = self.output_dir / f"{task_name}_complete_results.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                # 转换numpy类型为Python原生类型以便JSON序列化
                json_safe_results = self.make_json_serializable(complete_results)
                json.dump(json_safe_results, f, ensure_ascii=False, indent=2)
            
            print(f"\n" + "=" * 60)
            print(f"✅ 完整优化流程完成!")
            print(f"📊 优化摘要:")
            print(f"   原始样本数: {len(df)}")
            print(f"   清理后样本数: {len(cleaned_df)}")
            print(f"   异常值比例: {outlier_info.get('outlier_ratio', 0):.2f}%")
            
            if improvement_analysis.get('target_achieved'):
                print(f"   🎯 性能目标已达成!")
            else:
                print(f"   ⚠️  性能目标未完全达成")
            
            if final_test_results:
                print(f"   最终测试R²: {final_test_results['test_metrics']['r2']:.4f}")
            
            print(f"📁 结果文件: {results_file}")
            
            return complete_results
            
        except Exception as e:
            print(f"❌ 完整优化流程失败: {str(e)}")
            return {'success': False, 'message': str(e)}

    def make_json_serializable(self, obj):
        """
        将对象转换为JSON可序列化格式

        参数:
        obj: 要转换的对象

        返回:
        serializable_obj: JSON可序列化对象
        """
        if isinstance(obj, dict):
            return {key: self.make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self.make_json_serializable(item) for item in obj]
        elif isinstance(obj, set):
            return list(obj)  # 将set转换为list
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.float64, np.float32)):
            return float(obj)
        elif isinstance(obj, pd.Timestamp):
            return str(obj)
        elif hasattr(obj, '__dict__'):
            return str(obj)  # 对于复杂对象，转换为字符串
        else:
            return obj

    def generate_comprehensive_report(self, task_name: str, distribution_info: Dict[str, Any],
                                    outlier_info: Dict[str, Any], improvement_analysis: Dict[str, Any],
                                    final_test_results: Dict[str, Any]) -> str:
        """
        生成综合优化报告

        参数:
        task_name: 任务名称
        distribution_info: 数据分布信息
        outlier_info: 异常值信息
        improvement_analysis: 性能提升分析
        final_test_results: 最终测试结果

        返回:
        report_path: 报告文件路径
        """
        try:
            print(f"📝 生成综合优化报告...")

            report_lines = []
            report_lines.append(f"# {task_name} 数据集优化和模型性能提升报告")
            report_lines.append(f"生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_lines.append("")

            # 执行摘要
            report_lines.append("## 执行摘要")

            target_achieved = improvement_analysis.get('target_achieved', False)
            if target_achieved:
                report_lines.append("✅ **性能目标已达成**")
            else:
                report_lines.append("⚠️ **性能目标未完全达成**")

            if final_test_results:
                test_r2 = final_test_results['test_metrics']['r2']
                test_mae = final_test_results['test_metrics']['mae']
                report_lines.append(f"- 最终测试R²: **{test_r2:.4f}**")
                report_lines.append(f"- 最终测试MAE: **{test_mae:.4f}**")
                report_lines.append(f"- 最佳模型: **{final_test_results.get('best_model', 'Unknown')}**")

            report_lines.append("")

            # 数据集信息
            report_lines.append("## 数据集信息")
            if distribution_info:
                report_lines.append(f"- 总样本数: {distribution_info.get('total_samples', 'Unknown')}")
                report_lines.append(f"- 特征数量: {distribution_info.get('feature_count', 'Unknown')}")
                report_lines.append(f"- 目标变量: {distribution_info.get('target_column', 'Unknown')}")
                report_lines.append(f"- 缺失值: {distribution_info.get('missing_values', 'Unknown')}")
                report_lines.append(f"- 重复行: {distribution_info.get('duplicate_rows', 'Unknown')}")
            report_lines.append("")

            # 异常值检测结果
            report_lines.append("## 异常值检测结果")
            if outlier_info:
                outlier_count = outlier_info.get('outlier_count', 0)
                outlier_ratio = outlier_info.get('outlier_ratio', 0)
                report_lines.append(f"- 检测到异常值: **{outlier_count}** 个")
                report_lines.append(f"- 异常值比例: **{outlier_ratio:.2f}%**")

                if 'statistical_outliers' in outlier_info:
                    stat_count = len(outlier_info['statistical_outliers'].get('outlier_indices', set()))
                    report_lines.append(f"- 统计方法检测: {stat_count} 个")

                if 'model_outliers' in outlier_info:
                    model_count = len(outlier_info['model_outliers'].get('combined_outliers', set()))
                    report_lines.append(f"- 模型方法检测: {model_count} 个")

                # 异常值处理建议
                if outlier_ratio > 10:
                    report_lines.append("- 💡 **建议**: 异常值比例较高，需要进一步检查数据质量")
                elif outlier_ratio > 5:
                    report_lines.append("- 💡 **建议**: 异常值比例适中，已进行清理处理")
                else:
                    report_lines.append("- ✅ **评估**: 异常值比例较低，数据质量良好")

            report_lines.append("")

            # 性能提升分析
            report_lines.append("## 性能提升分析")
            if improvement_analysis:
                target = improvement_analysis.get('performance_target', {})
                baseline_perf = improvement_analysis.get('baseline_performance', {})
                enhanced_perf = improvement_analysis.get('enhanced_performance', {})

                report_lines.append("### 性能目标")
                report_lines.append(f"- 目标R²: {target.get('r2', 'Unknown')}")
                report_lines.append(f"- 目标MAE: {target.get('mae', 'Unknown')}")
                report_lines.append("")

                report_lines.append("### 基线性能")
                if baseline_perf:
                    report_lines.append(f"- R²分数: {baseline_perf.get('r2', 'Unknown'):.4f}")
                    report_lines.append(f"- MAE: {baseline_perf.get('mae', 'Unknown'):.4f}")
                    report_lines.append(f"- RMSE: {baseline_perf.get('rmse', 'Unknown'):.4f}")
                report_lines.append("")

                report_lines.append("### 优化后性能")
                if enhanced_perf:
                    report_lines.append(f"- R²分数: **{enhanced_perf.get('r2', 'Unknown'):.4f}**")
                    report_lines.append(f"- MAE: **{enhanced_perf.get('mae', 'Unknown'):.4f}**")
                    report_lines.append(f"- RMSE: **{enhanced_perf.get('rmse', 'Unknown'):.4f}**")
                    report_lines.append(f"- 最佳模型: **{improvement_analysis.get('best_model', 'Unknown')}**")

                # 性能改进
                if 'r2_improvement' in improvement_analysis:
                    r2_improvement = improvement_analysis['r2_improvement']
                    if r2_improvement > 0:
                        report_lines.append(f"- R²提升: **+{r2_improvement:.4f}** ✅")
                    else:
                        report_lines.append(f"- R²变化: {r2_improvement:.4f} ⚠️")

                report_lines.append("")

            # 优化策略和方法
            report_lines.append("## 优化策略和方法")
            report_lines.append("### 数据优化")
            report_lines.append("- ✅ 分层抽样数据集划分 (70%/15%/15%)")
            report_lines.append("- ✅ 多方法异常值检测 (统计+模型)")
            report_lines.append("- ✅ 智能数据清理")
            report_lines.append("")

            report_lines.append("### 特征工程")
            report_lines.append("- ✅ 多项式特征扩展")
            report_lines.append("- ✅ 统计特征构造")
            report_lines.append("- ✅ 特征选择优化")
            report_lines.append("")

            report_lines.append("### 模型优化")
            report_lines.append("- ✅ 多模型对比训练")
            report_lines.append("- ✅ 网格搜索超参数优化")
            report_lines.append("- ✅ 集成学习 (Voting + Stacking)")
            report_lines.append("")

            # 结论和建议
            report_lines.append("## 结论和建议")

            if target_achieved:
                report_lines.append("### ✅ 优化成功")
                report_lines.append("- 所有性能目标均已达成")
                report_lines.append("- 模型可以投入生产使用")
                report_lines.append("- 建议定期监控模型性能")
            else:
                report_lines.append("### ⚠️ 需要进一步优化")
                report_lines.append("- 部分性能目标未达成")
                report_lines.append("- 建议收集更多高质量数据")
                report_lines.append("- 考虑更复杂的模型架构")
                report_lines.append("- 进行更深入的特征工程")

            report_lines.append("")
            report_lines.append("### 后续改进建议")
            report_lines.append("1. **数据质量**: 持续监控和改善数据质量")
            report_lines.append("2. **特征工程**: 探索领域特定的特征构造")
            report_lines.append("3. **模型选择**: 尝试深度学习等更复杂模型")
            report_lines.append("4. **集成策略**: 优化集成学习的权重分配")
            report_lines.append("5. **在线学习**: 考虑实现在线学习机制")

            # 保存报告
            report_content = "\n".join(report_lines)
            report_path = self.output_dir / f"{task_name}_comprehensive_report.md"

            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)

            print(f"   ✅ 综合报告已保存: {report_path}")

            return str(report_path)

        except Exception as e:
            print(f"❌ 生成综合报告失败: {str(e)}")
            return ""
