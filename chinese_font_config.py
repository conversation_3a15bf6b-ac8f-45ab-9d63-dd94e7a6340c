
# 中文字体配置 - 自动添加
import matplotlib.pyplot as plt
import platform

def setup_chinese_fonts():
    """设置中文字体"""
    system = platform.system()
    
    if system == "Windows":
        fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong']
    elif system == "Darwin":  # macOS
        fonts = ['PingFang SC', 'Hiragino Sans GB', 'Heiti SC', 'STHeiti', 'STSong']
    else:  # Linux
        fonts = ['Noto Sans CJK SC', 'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Source Han Sans SC']
    
    fonts.extend(['DejaVu Sans', 'Arial Unicode MS', 'sans-serif'])
    
    plt.rcParams['font.sans-serif'] = fonts
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.titlesize'] = 16
    plt.rcParams['axes.labelsize'] = 14
    plt.rcParams['xtick.labelsize'] = 12
    plt.rcParams['ytick.labelsize'] = 12
    plt.rcParams['legend.fontsize'] = 12

# 自动执行字体配置
setup_chinese_fonts()
