#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
降噪效果评估模块
提供多种评估指标来量化降噪方法的性能

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import numpy as np
import pandas as pd
from scipy import signal
from scipy.stats import pearsonr
import time
import warnings
warnings.filterwarnings('ignore')

class DenoisingEvaluator:
    """降噪效果评估器"""
    
    def __init__(self, fs=1000):
        """
        初始化评估器
        
        参数:
        fs: 采样频率 (Hz)
        """
        self.fs = fs
        self.evaluation_results = {}
        
    def calculate_snr(self, original_signal, denoised_signal):
        """
        计算信噪比改善程度
        
        参数:
        original_signal: 原始信号
        denoised_signal: 降噪后信号
        
        返回:
        snr_improvement: SNR改善值 (dB)
        """
        try:
            # 估计噪声（假设高频部分主要是噪声）
            b, a = signal.butter(4, 200 / (self.fs / 2), btype='high')
            original_noise = signal.filtfilt(b, a, original_signal)
            denoised_noise = signal.filtfilt(b, a, denoised_signal)
            
            # 计算信号功率（低频部分）
            b, a = signal.butter(4, 200 / (self.fs / 2), btype='low')
            original_signal_power = np.var(signal.filtfilt(b, a, original_signal))
            denoised_signal_power = np.var(signal.filtfilt(b, a, denoised_signal))
            
            # 计算噪声功率
            original_noise_power = np.var(original_noise)
            denoised_noise_power = np.var(denoised_noise)
            
            # 避免除零
            if original_noise_power == 0 or denoised_noise_power == 0:
                return 0
            
            # 计算SNR
            original_snr = 10 * np.log10(original_signal_power / original_noise_power)
            denoised_snr = 10 * np.log10(denoised_signal_power / denoised_noise_power)
            
            snr_improvement = denoised_snr - original_snr
            
            return snr_improvement
            
        except Exception as e:
            print(f"SNR计算失败: {str(e)}")
            return 0
    
    def calculate_rmse(self, original_signal, denoised_signal):
        """
        计算均方根误差
        
        参数:
        original_signal: 原始信号
        denoised_signal: 降噪后信号
        
        返回:
        rmse: 均方根误差
        """
        try:
            # 确保信号长度一致
            min_len = min(len(original_signal), len(denoised_signal))
            original_signal = original_signal[:min_len]
            denoised_signal = denoised_signal[:min_len]
            
            rmse = np.sqrt(np.mean((original_signal - denoised_signal) ** 2))
            return rmse
            
        except Exception as e:
            print(f"RMSE计算失败: {str(e)}")
            return np.inf
    
    def calculate_signal_fidelity(self, original_signal, denoised_signal):
        """
        计算信号保真度（相关系数）
        
        参数:
        original_signal: 原始信号
        denoised_signal: 降噪后信号
        
        返回:
        fidelity: 信号保真度 (0-1)
        """
        try:
            # 确保信号长度一致
            min_len = min(len(original_signal), len(denoised_signal))
            original_signal = original_signal[:min_len]
            denoised_signal = denoised_signal[:min_len]
            
            # 计算皮尔逊相关系数
            correlation, _ = pearsonr(original_signal, denoised_signal)
            
            # 返回相关系数的绝对值作为保真度
            fidelity = abs(correlation)
            
            return fidelity
            
        except Exception as e:
            print(f"信号保真度计算失败: {str(e)}")
            return 0
    
    def calculate_frequency_preservation(self, original_signal, denoised_signal):
        """
        计算频域特征保持程度
        
        参数:
        original_signal: 原始信号
        denoised_signal: 降噪后信号
        
        返回:
        freq_preservation: 频域保持度 (0-1)
        """
        try:
            # 确保信号长度一致
            min_len = min(len(original_signal), len(denoised_signal))
            original_signal = original_signal[:min_len]
            denoised_signal = denoised_signal[:min_len]
            
            # 计算功率谱密度
            freqs, original_psd = signal.welch(original_signal, fs=self.fs, nperseg=min(256, min_len//4))
            _, denoised_psd = signal.welch(denoised_signal, fs=self.fs, nperseg=min(256, min_len//4))
            
            # 关注车辆通过的主要频率范围 (5-100 Hz)
            freq_mask = (freqs >= 5) & (freqs <= 100)
            original_psd_roi = original_psd[freq_mask]
            denoised_psd_roi = denoised_psd[freq_mask]
            
            # 计算频域相关性
            if len(original_psd_roi) > 1 and np.std(original_psd_roi) > 0 and np.std(denoised_psd_roi) > 0:
                freq_correlation, _ = pearsonr(original_psd_roi, denoised_psd_roi)
                freq_preservation = abs(freq_correlation)
            else:
                freq_preservation = 0
            
            return freq_preservation
            
        except Exception as e:
            print(f"频域保持度计算失败: {str(e)}")
            return 0
    
    def calculate_peak_preservation(self, original_signal, denoised_signal):
        """
        计算峰值特征保持程度（车辆通过事件特征）
        
        参数:
        original_signal: 原始信号
        denoised_signal: 降噪后信号
        
        返回:
        peak_preservation: 峰值保持度 (0-1)
        """
        try:
            # 确保信号长度一致
            min_len = min(len(original_signal), len(denoised_signal))
            original_signal = original_signal[:min_len]
            denoised_signal = denoised_signal[:min_len]
            
            # 找到原始信号的峰值
            original_peaks, _ = signal.find_peaks(np.abs(original_signal), 
                                                height=np.std(original_signal) * 2,
                                                distance=int(0.1 * self.fs))  # 最小间距0.1秒
            
            # 找到降噪信号的峰值
            denoised_peaks, _ = signal.find_peaks(np.abs(denoised_signal), 
                                                height=np.std(denoised_signal) * 2,
                                                distance=int(0.1 * self.fs))
            
            if len(original_peaks) == 0:
                return 1.0  # 如果原始信号没有明显峰值，认为保持度为1
            
            # 计算峰值位置的保持程度
            peak_matches = 0
            tolerance = int(0.05 * self.fs)  # 允许5%采样率的误差
            
            for orig_peak in original_peaks:
                # 在容差范围内寻找匹配的峰值
                matches = denoised_peaks[np.abs(denoised_peaks - orig_peak) <= tolerance]
                if len(matches) > 0:
                    peak_matches += 1
            
            peak_preservation = peak_matches / len(original_peaks)
            
            return peak_preservation
            
        except Exception as e:
            print(f"峰值保持度计算失败: {str(e)}")
            return 0
    
    def measure_computational_efficiency(self, denoising_func, signal_data, iterations=5):
        """
        测量计算效率
        
        参数:
        denoising_func: 降噪函数
        signal_data: 测试信号
        iterations: 测试迭代次数
        
        返回:
        avg_time: 平均处理时间 (秒)
        throughput: 处理吞吐量 (样本/秒)
        """
        try:
            times = []
            
            for _ in range(iterations):
                start_time = time.time()
                _ = denoising_func(signal_data)
                end_time = time.time()
                times.append(end_time - start_time)
            
            avg_time = np.mean(times)
            throughput = len(signal_data) / avg_time if avg_time > 0 else 0
            
            return avg_time, throughput
            
        except Exception as e:
            print(f"效率测量失败: {str(e)}")
            return np.inf, 0
    
    def comprehensive_evaluation(self, original_signal, denoised_signal, method_name, 
                               denoising_func=None, include_efficiency=True):
        """
        综合评估降噪效果
        
        参数:
        original_signal: 原始信号
        denoised_signal: 降噪后信号
        method_name: 方法名称
        denoising_func: 降噪函数（用于效率测试）
        include_efficiency: 是否包含效率测试
        
        返回:
        evaluation_results: 评估结果字典
        """
        results = {
            'method_name': method_name,
            'snr_improvement': self.calculate_snr(original_signal, denoised_signal),
            'rmse': self.calculate_rmse(original_signal, denoised_signal),
            'signal_fidelity': self.calculate_signal_fidelity(original_signal, denoised_signal),
            'frequency_preservation': self.calculate_frequency_preservation(original_signal, denoised_signal),
            'peak_preservation': self.calculate_peak_preservation(original_signal, denoised_signal)
        }
        
        # 计算综合评分
        weights = {
            'snr_improvement': 0.25,
            'signal_fidelity': 0.25,
            'frequency_preservation': 0.25,
            'peak_preservation': 0.25
        }
        
        # 归一化各项指标
        normalized_snr = max(0, min(1, (results['snr_improvement'] + 10) / 20))  # SNR改善-10到10dB映射到0-1
        normalized_rmse = max(0, min(1, 1 - results['rmse'] / np.std(original_signal)))  # RMSE越小越好
        
        composite_score = (
            weights['snr_improvement'] * normalized_snr +
            weights['signal_fidelity'] * results['signal_fidelity'] +
            weights['frequency_preservation'] * results['frequency_preservation'] +
            weights['peak_preservation'] * results['peak_preservation']
        )
        
        results['composite_score'] = composite_score
        
        # 效率测试
        if include_efficiency and denoising_func is not None:
            avg_time, throughput = self.measure_computational_efficiency(denoising_func, original_signal)
            results['processing_time'] = avg_time
            results['throughput'] = throughput
        
        return results

    def evaluate_all_methods(self, denoising_results, original_signal_name="original"):
        """
        评估所有降噪方法

        参数:
        denoising_results: 降噪结果字典
        original_signal_name: 原始信号的键名

        返回:
        evaluation_summary: 评估摘要
        """
        if original_signal_name not in denoising_results:
            print(f"❌ 未找到原始信号 '{original_signal_name}'")
            return {}

        original_signal = denoising_results[original_signal_name]
        evaluation_summary = {}

        print(f"📊 评估 {len(denoising_results)-1} 种降噪方法...")

        for method_name, denoised_signal in denoising_results.items():
            if method_name == original_signal_name:
                continue

            try:
                evaluation = self.comprehensive_evaluation(
                    original_signal, denoised_signal, method_name, include_efficiency=False
                )
                evaluation_summary[method_name] = evaluation

            except Exception as e:
                print(f"  ⚠️  评估 {method_name} 失败: {str(e)}")
                continue

        # 排序并找出最佳方法
        if evaluation_summary:
            sorted_methods = sorted(evaluation_summary.items(),
                                  key=lambda x: x[1]['composite_score'], reverse=True)

            print(f"\n🏆 降噪方法排名 (按综合评分):")
            for i, (method_name, results) in enumerate(sorted_methods[:5]):
                score = results['composite_score']
                snr = results['snr_improvement']
                fidelity = results['signal_fidelity']
                print(f"  {i+1}. {method_name}: 综合评分={score:.3f}, SNR改善={snr:.2f}dB, 保真度={fidelity:.3f}")

            # 保存评估结果
            self.evaluation_results = evaluation_summary

            return evaluation_summary

        return {}

    def get_best_method(self, evaluation_summary=None, criterion='composite_score'):
        """
        获取最佳降噪方法

        参数:
        evaluation_summary: 评估摘要（如果为None则使用最近的评估结果）
        criterion: 评估准则

        返回:
        best_method: 最佳方法信息
        """
        if evaluation_summary is None:
            evaluation_summary = self.evaluation_results

        if not evaluation_summary:
            return None

        # 根据指定准则找出最佳方法
        best_method_name = max(evaluation_summary.keys(),
                             key=lambda x: evaluation_summary[x].get(criterion, 0))

        best_method_info = {
            'method_name': best_method_name,
            'evaluation_results': evaluation_summary[best_method_name],
            'recommendation_reason': self._generate_recommendation_reason(
                best_method_name, evaluation_summary[best_method_name]
            )
        }

        return best_method_info

    def _generate_recommendation_reason(self, method_name, evaluation_results):
        """
        生成推荐理由

        参数:
        method_name: 方法名称
        evaluation_results: 评估结果

        返回:
        reason: 推荐理由
        """
        reasons = []

        # 综合评分
        score = evaluation_results.get('composite_score', 0)
        if score > 0.8:
            reasons.append(f"综合评分优秀({score:.3f})")
        elif score > 0.6:
            reasons.append(f"综合评分良好({score:.3f})")

        # SNR改善
        snr = evaluation_results.get('snr_improvement', 0)
        if snr > 5:
            reasons.append(f"显著改善信噪比({snr:.1f}dB)")
        elif snr > 2:
            reasons.append(f"有效改善信噪比({snr:.1f}dB)")

        # 信号保真度
        fidelity = evaluation_results.get('signal_fidelity', 0)
        if fidelity > 0.9:
            reasons.append(f"信号保真度极高({fidelity:.3f})")
        elif fidelity > 0.8:
            reasons.append(f"信号保真度高({fidelity:.3f})")

        # 频域保持
        freq_pres = evaluation_results.get('frequency_preservation', 0)
        if freq_pres > 0.9:
            reasons.append("频域特征保持完好")

        # 峰值保持
        peak_pres = evaluation_results.get('peak_preservation', 0)
        if peak_pres > 0.9:
            reasons.append("车辆通过事件特征保持完整")

        if not reasons:
            reasons.append("在当前条件下表现最佳")

        return "; ".join(reasons)

    def generate_evaluation_report(self, evaluation_summary=None, save_path=None):
        """
        生成评估报告

        参数:
        evaluation_summary: 评估摘要
        save_path: 保存路径

        返回:
        report: 报告内容
        """
        if evaluation_summary is None:
            evaluation_summary = self.evaluation_results

        if not evaluation_summary:
            return "无评估数据"

        # 生成报告
        report_lines = [
            "# 振动信号降噪方法评估报告",
            "",
            f"## 评估概况",
            f"- 评估方法数量: {len(evaluation_summary)}",
            f"- 采样频率: {self.fs} Hz",
            f"- 评估指标: SNR改善、RMSE、信号保真度、频域保持度、峰值保持度",
            "",
            "## 详细评估结果",
            ""
        ]

        # 按综合评分排序
        sorted_methods = sorted(evaluation_summary.items(),
                              key=lambda x: x[1]['composite_score'], reverse=True)

        for i, (method_name, results) in enumerate(sorted_methods):
            report_lines.extend([
                f"### {i+1}. {method_name}",
                f"- 综合评分: {results['composite_score']:.4f}",
                f"- SNR改善: {results['snr_improvement']:.2f} dB",
                f"- RMSE: {results['rmse']:.6f}",
                f"- 信号保真度: {results['signal_fidelity']:.4f}",
                f"- 频域保持度: {results['frequency_preservation']:.4f}",
                f"- 峰值保持度: {results['peak_preservation']:.4f}",
                ""
            ])

        # 最佳方法推荐
        best_method = self.get_best_method(evaluation_summary)
        if best_method:
            report_lines.extend([
                "## 推荐方案",
                f"**最佳降噪方法**: {best_method['method_name']}",
                f"**推荐理由**: {best_method['recommendation_reason']}",
                ""
            ])

        # 应用建议
        report_lines.extend([
            "## 应用建议",
            "1. 对于实时处理要求高的场景，建议使用简单滤波方法",
            "2. 对于离线分析且要求高精度的场景，建议使用小波降噪",
            "3. 对于多传感器融合场景，建议结合多种降噪方法",
            "4. 建议根据具体的车辆类型和速度范围调整参数",
            ""
        ])

        report = "\n".join(report_lines)

        # 保存报告
        if save_path:
            try:
                with open(save_path, 'w', encoding='utf-8') as f:
                    f.write(report)
                print(f"✅ 评估报告已保存到: {save_path}")
            except Exception as e:
                print(f"⚠️  保存报告失败: {str(e)}")

        return report
