#!/usr/bin/env python3
"""
集成学习模块
实现Voting、Stacking、Blending等集成策略
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import cross_val_score, KFold, StratifiedKFold
from sklearn.ensemble import VotingRegressor, VotingClassifier
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.ensemble import GradientBoostingRegressor, GradientBoostingClassifier
from sklearn.ensemble import ExtraTreesRegressor, ExtraTreesClassifier
from sklearn.metrics import r2_score, accuracy_score
import joblib
import warnings
warnings.filterwarnings('ignore')

class EnsembleLearning:
    """集成学习器"""
    
    def __init__(self, random_state=42):
        """初始化集成学习器"""
        self.random_state = random_state
        self.ensemble_models = {}
        self.ensemble_results = {}
        
    def create_base_models(self, optimized_params, task_type='regression'):
        """创建基础模型（使用优化后的参数）"""
        base_models = []
        
        try:
            # Random Forest
            if 'Random Forest' in optimized_params:
                params = optimized_params['Random Forest'].copy()
                if task_type == 'regression':
                    model = RandomForestRegressor(**params)
                else:
                    model = RandomForestClassifier(**params)
                base_models.append(('rf', model))
        except Exception as e:
            print(f"    ⚠️  Random Forest创建失败: {str(e)}")
        
        try:
            # Extra Trees
            if 'Extra Trees' in optimized_params:
                params = optimized_params['Extra Trees'].copy()
                if task_type == 'regression':
                    model = ExtraTreesRegressor(**params)
                else:
                    model = ExtraTreesClassifier(**params)
                base_models.append(('et', model))
        except Exception as e:
            print(f"    ⚠️  Extra Trees创建失败: {str(e)}")
        
        try:
            # Gradient Boosting
            if 'Gradient Boosting' in optimized_params:
                params = optimized_params['Gradient Boosting'].copy()
                if task_type == 'regression':
                    model = GradientBoostingRegressor(**params)
                else:
                    model = GradientBoostingClassifier(**params)
                base_models.append(('gb', model))
        except Exception as e:
            print(f"    ⚠️  Gradient Boosting创建失败: {str(e)}")
        
        try:
            # XGBoost
            if 'XGBoost' in optimized_params:
                import xgboost as xgb
                params = optimized_params['XGBoost'].copy()
                if task_type == 'regression':
                    model = xgb.XGBRegressor(**params)
                else:
                    model = xgb.XGBClassifier(**params)
                base_models.append(('xgb', model))
        except Exception as e:
            print(f"    ⚠️  XGBoost创建失败: {str(e)}")
        
        return base_models
    
    def create_voting_ensemble(self, base_models, task_type='regression'):
        """创建投票集成模型"""
        print(f"    🗳️  创建Voting集成模型...")
        
        if not base_models:
            print(f"    ❌ 没有可用的基础模型")
            return None
        
        try:
            if task_type == 'regression':
                ensemble = VotingRegressor(
                    estimators=base_models,
                    n_jobs=-1
                )
            else:
                ensemble = VotingClassifier(
                    estimators=base_models,
                    voting='soft',  # 使用软投票
                    n_jobs=-1
                )
            
            return ensemble
            
        except Exception as e:
            print(f"    ❌ Voting集成创建失败: {str(e)}")
            return None
    
    def create_stacking_ensemble(self, base_models, task_type='regression'):
        """创建堆叠集成模型"""
        print(f"    📚 创建Stacking集成模型...")
        
        if not base_models:
            print(f"    ❌ 没有可用的基础模型")
            return None
        
        try:
            from sklearn.ensemble import StackingRegressor, StackingClassifier
            
            if task_type == 'regression':
                # 使用线性回归作为元学习器
                meta_learner = LinearRegression()
                ensemble = StackingRegressor(
                    estimators=base_models,
                    final_estimator=meta_learner,
                    cv=5,
                    n_jobs=-1
                )
            else:
                # 使用逻辑回归作为元学习器
                meta_learner = LogisticRegression(random_state=self.random_state, max_iter=1000)
                ensemble = StackingClassifier(
                    estimators=base_models,
                    final_estimator=meta_learner,
                    cv=5,
                    n_jobs=-1
                )
            
            return ensemble
            
        except Exception as e:
            print(f"    ❌ Stacking集成创建失败: {str(e)}")
            return None
    
    def create_weighted_ensemble(self, base_models, X, y, task_type='regression'):
        """创建加权集成模型"""
        print(f"    ⚖️  创建加权集成模型...")
        
        if not base_models:
            print(f"    ❌ 没有可用的基础模型")
            return None
        
        try:
            # 使用交叉验证评估每个模型的性能
            model_scores = []
            
            if task_type == 'regression':
                cv = KFold(n_splits=5, shuffle=True, random_state=self.random_state)
                scoring = 'r2'
            else:
                cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)
                scoring = 'accuracy'
            
            for name, model in base_models:
                try:
                    scores = cross_val_score(model, X, y, cv=cv, scoring=scoring)
                    avg_score = scores.mean()
                    model_scores.append((name, model, avg_score))
                    print(f"      {name}: {avg_score:.4f}")
                except Exception as e:
                    print(f"      ❌ {name} 评估失败: {str(e)}")
                    continue
            
            if not model_scores:
                return None
            
            # 计算权重（基于性能）
            scores = np.array([score for _, _, score in model_scores])
            # 使用softmax转换为权重
            weights = np.exp(scores * 5) / np.sum(np.exp(scores * 5))  # 放大差异
            
            # 创建加权集成
            weighted_models = [(name, model) for name, model, _ in model_scores]
            
            if task_type == 'regression':
                ensemble = VotingRegressor(
                    estimators=weighted_models,
                    weights=weights,
                    n_jobs=-1
                )
            else:
                ensemble = VotingClassifier(
                    estimators=weighted_models,
                    voting='soft',
                    weights=weights,
                    n_jobs=-1
                )
            
            # 保存权重信息
            weight_info = {name: weight for (name, _), weight in zip(weighted_models, weights)}
            print(f"      权重分配: {weight_info}")
            
            return ensemble, weight_info
            
        except Exception as e:
            print(f"    ❌ 加权集成创建失败: {str(e)}")
            return None
    
    def create_blending_ensemble(self, base_models, X, y, task_type='regression', blend_ratio=0.2):
        """创建混合集成模型"""
        print(f"    🔀 创建Blending集成模型...")
        
        if not base_models:
            print(f"    ❌ 没有可用的基础模型")
            return None
        
        try:
            from sklearn.model_selection import train_test_split
            
            # 分割数据：一部分用于训练基础模型，一部分用于训练混合器
            X_blend, X_holdout, y_blend, y_holdout = train_test_split(
                X, y, test_size=blend_ratio, random_state=self.random_state, 
                stratify=y if task_type == 'classification' else None
            )
            
            # 训练基础模型并生成预测
            blend_features = []
            trained_models = []
            
            for name, model in base_models:
                try:
                    model.fit(X_blend, y_blend)
                    if task_type == 'regression':
                        pred = model.predict(X_holdout)
                    else:
                        pred = model.predict_proba(X_holdout)[:, 1] if hasattr(model, 'predict_proba') else model.predict(X_holdout)
                    
                    blend_features.append(pred)
                    trained_models.append((name, model))
                    
                except Exception as e:
                    print(f"      ❌ {name} 训练失败: {str(e)}")
                    continue
            
            if not blend_features:
                return None
            
            # 创建混合特征矩阵
            blend_X = np.column_stack(blend_features)
            
            # 训练混合器
            if task_type == 'regression':
                blender = LinearRegression()
            else:
                blender = LogisticRegression(random_state=self.random_state, max_iter=1000)
            
            blender.fit(blend_X, y_holdout)
            
            # 创建完整的混合集成模型
            class BlendingEnsemble:
                def __init__(self, base_models, blender, task_type):
                    self.base_models = base_models
                    self.blender = blender
                    self.task_type = task_type
                
                def fit(self, X, y):
                    # 基础模型已经训练过了
                    return self
                
                def predict(self, X):
                    # 获取基础模型预测
                    base_preds = []
                    for name, model in self.base_models:
                        if self.task_type == 'regression':
                            pred = model.predict(X)
                        else:
                            pred = model.predict_proba(X)[:, 1] if hasattr(model, 'predict_proba') else model.predict(X)
                        base_preds.append(pred)
                    
                    # 混合预测
                    blend_X = np.column_stack(base_preds)
                    return self.blender.predict(blend_X)
                
                def predict_proba(self, X):
                    if self.task_type == 'classification' and hasattr(self.blender, 'predict_proba'):
                        base_preds = []
                        for name, model in self.base_models:
                            pred = model.predict_proba(X)[:, 1] if hasattr(model, 'predict_proba') else model.predict(X)
                            base_preds.append(pred)
                        
                        blend_X = np.column_stack(base_preds)
                        return self.blender.predict_proba(blend_X)
                    return None
            
            ensemble = BlendingEnsemble(trained_models, blender, task_type)
            
            return ensemble
            
        except Exception as e:
            print(f"    ❌ Blending集成创建失败: {str(e)}")
            return None
    
    def evaluate_ensemble_methods(self, optimized_params, X, y, task_type='regression'):
        """评估所有集成方法"""
        print(f"🔬 评估集成学习方法 ({task_type})...")
        
        # 创建基础模型
        base_models = self.create_base_models(optimized_params, task_type)
        
        if not base_models:
            print(f"❌ 没有可用的基础模型进行集成")
            return {}
        
        print(f"   使用 {len(base_models)} 个基础模型: {[name for name, _ in base_models]}")
        
        ensemble_results = {}
        
        # 1. Voting集成
        try:
            voting_ensemble = self.create_voting_ensemble(base_models, task_type)
            if voting_ensemble:
                if task_type == 'regression':
                    cv = KFold(n_splits=5, shuffle=True, random_state=self.random_state)
                    scores = cross_val_score(voting_ensemble, X, y, cv=cv, scoring='r2')
                else:
                    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)
                    scores = cross_val_score(voting_ensemble, X, y, cv=cv, scoring='accuracy')
                
                ensemble_results['Voting'] = {
                    'model': voting_ensemble,
                    'cv_score': scores.mean(),
                    'cv_std': scores.std()
                }
                print(f"   🗳️  Voting: {scores.mean():.4f} (±{scores.std():.4f})")
        except Exception as e:
            print(f"   ❌ Voting集成评估失败: {str(e)}")
        
        # 2. Stacking集成
        try:
            stacking_ensemble = self.create_stacking_ensemble(base_models, task_type)
            if stacking_ensemble:
                if task_type == 'regression':
                    cv = KFold(n_splits=5, shuffle=True, random_state=self.random_state)
                    scores = cross_val_score(stacking_ensemble, X, y, cv=cv, scoring='r2')
                else:
                    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)
                    scores = cross_val_score(stacking_ensemble, X, y, cv=cv, scoring='accuracy')
                
                ensemble_results['Stacking'] = {
                    'model': stacking_ensemble,
                    'cv_score': scores.mean(),
                    'cv_std': scores.std()
                }
                print(f"   📚 Stacking: {scores.mean():.4f} (±{scores.std():.4f})")
        except Exception as e:
            print(f"   ❌ Stacking集成评估失败: {str(e)}")
        
        # 3. 加权集成
        try:
            weighted_result = self.create_weighted_ensemble(base_models, X, y, task_type)
            if weighted_result and len(weighted_result) == 2:
                weighted_ensemble, weight_info = weighted_result
                if task_type == 'regression':
                    cv = KFold(n_splits=5, shuffle=True, random_state=self.random_state)
                    scores = cross_val_score(weighted_ensemble, X, y, cv=cv, scoring='r2')
                else:
                    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)
                    scores = cross_val_score(weighted_ensemble, X, y, cv=cv, scoring='accuracy')
                
                ensemble_results['Weighted'] = {
                    'model': weighted_ensemble,
                    'cv_score': scores.mean(),
                    'cv_std': scores.std(),
                    'weights': weight_info
                }
                print(f"   ⚖️  Weighted: {scores.mean():.4f} (±{scores.std():.4f})")
        except Exception as e:
            print(f"   ❌ 加权集成评估失败: {str(e)}")
        
        # 4. Blending集成
        try:
            blending_ensemble = self.create_blending_ensemble(base_models, X, y, task_type)
            if blending_ensemble:
                # 对于Blending，使用简单的训练测试分割评估
                from sklearn.model_selection import train_test_split
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=0.2, random_state=self.random_state,
                    stratify=y if task_type == 'classification' else None
                )
                
                blending_ensemble.fit(X_train, y_train)
                y_pred = blending_ensemble.predict(X_test)
                
                if task_type == 'regression':
                    score = r2_score(y_test, y_pred)
                else:
                    score = accuracy_score(y_test, y_pred)
                
                ensemble_results['Blending'] = {
                    'model': blending_ensemble,
                    'test_score': score
                }
                print(f"   🔀 Blending: {score:.4f}")
        except Exception as e:
            print(f"   ❌ Blending集成评估失败: {str(e)}")
        
        self.ensemble_results[task_type] = ensemble_results
        return ensemble_results
    
    def get_best_ensemble(self, task_type='regression'):
        """获取最佳集成模型"""
        if task_type not in self.ensemble_results:
            return None
        
        results = self.ensemble_results[task_type]
        if not results:
            return None
        
        best_method = None
        best_score = -1
        
        for method, result in results.items():
            score = result.get('cv_score', result.get('test_score', 0))
            if score > best_score:
                best_score = score
                best_method = method
        
        if best_method:
            return {
                'method': best_method,
                'model': results[best_method]['model'],
                'score': best_score,
                'details': results[best_method]
            }
        
        return None

def main():
    """测试函数"""
    from sklearn.datasets import make_regression
    
    print("🧪 测试集成学习...")
    
    # 创建测试数据
    X, y = make_regression(n_samples=1000, n_features=20, noise=0.1, random_state=42)
    
    # 模拟优化参数
    optimized_params = {
        'Random Forest': {'n_estimators': 100, 'max_depth': 10, 'random_state': 42},
        'Extra Trees': {'n_estimators': 100, 'max_depth': 10, 'random_state': 42},
        'Gradient Boosting': {'n_estimators': 100, 'max_depth': 6, 'learning_rate': 0.1, 'random_state': 42}
    }
    
    # 初始化集成学习器
    ensemble = EnsembleLearning()
    
    # 评估集成方法
    results = ensemble.evaluate_ensemble_methods(optimized_params, X, y, 'regression')
    
    # 获取最佳集成
    best = ensemble.get_best_ensemble('regression')
    if best:
        print(f"\n🏆 最佳集成方法: {best['method']} (分数: {best['score']:.4f})")
    
    print("✅ 测试完成!")

if __name__ == "__main__":
    main()
