#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一机器学习模型模块
集成XGBoost、CNN-LSTM和BP神经网络

作者: AI Assistant
版本: 2.0
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import r2_score, accuracy_score, classification_report, confusion_matrix
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.ensemble import GradientBoostingRegressor, GradientBoostingClassifier
from sklearn.ensemble import AdaBoostRegressor, AdaBoostClassifier
from sklearn.ensemble import ExtraTreesRegressor, ExtraTreesClassifier
from sklearn.svm import SVR, SVC
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
import time
import warnings
warnings.filterwarnings('ignore')

class UnifiedMLModels:
    """统一机器学习模型类"""
    
    def __init__(self):
        """初始化"""
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.training_times = {}
        self.feature_importances = {}
        self.training_histories = {}
    
    def create_xgboost_model(self, task_type: str):
        """创建XGBoost模型"""
        try:
            import xgboost as xgb
            
            if task_type == 'regression':
                model = xgb.XGBRegressor(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42,
                    tree_method='gpu_hist' if self._check_gpu() else 'hist'
                )
            else:
                model = xgb.XGBClassifier(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42,
                    tree_method='gpu_hist' if self._check_gpu() else 'hist'
                )
            
            return model
            
        except ImportError:
            print("⚠️  XGBoost未安装，跳过")
            return None

    def create_random_forest_model(self, task_type: str):
        """创建随机森林模型"""
        if task_type == 'regression':
            return RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
        else:
            return RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )

    def create_gradient_boosting_model(self, task_type: str):
        """创建梯度提升模型"""
        if task_type == 'regression':
            return GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            )
        else:
            return GradientBoostingClassifier(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            )

    def create_adaboost_model(self, task_type: str):
        """创建AdaBoost模型"""
        if task_type == 'regression':
            return AdaBoostRegressor(
                n_estimators=100,
                learning_rate=0.1,
                random_state=42
            )
        else:
            return AdaBoostClassifier(
                n_estimators=100,
                learning_rate=0.1,
                random_state=42
            )

    def create_extra_trees_model(self, task_type: str):
        """创建Extra Trees模型"""
        if task_type == 'regression':
            return ExtraTreesRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
        else:
            return ExtraTreesClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )

    def create_svm_model(self, task_type: str):
        """创建支持向量机模型"""
        if task_type == 'regression':
            return SVR(
                kernel='rbf',
                C=1.0,
                gamma='scale',
                epsilon=0.1
            )
        else:
            return SVC(
                kernel='rbf',
                C=1.0,
                gamma='scale',
                probability=True,
                random_state=42
            )
    
    def create_bp_neural_network(self, input_dim: int, task_type: str):
        """创建BP神经网络模型"""
        try:
            import tensorflow as tf
            from tensorflow.keras.models import Sequential
            from tensorflow.keras.layers import Dense, Dropout, BatchNormalization
            from tensorflow.keras.optimizers import Adam
            
            model = Sequential()
            
            # 输入层
            model.add(Dense(128, input_dim=input_dim, activation='relu'))
            model.add(BatchNormalization())
            model.add(Dropout(0.3))
            
            # 隐藏层1
            model.add(Dense(64, activation='relu'))
            model.add(BatchNormalization())
            model.add(Dropout(0.3))
            
            # 隐藏层2
            model.add(Dense(32, activation='relu'))
            model.add(BatchNormalization())
            model.add(Dropout(0.2))
            
            # 输出层
            if task_type == 'regression':
                model.add(Dense(1, activation='linear'))
                model.compile(
                    optimizer=Adam(learning_rate=0.001),
                    loss='mse',
                    metrics=['mae']
                )
            else:
                # 假设最多10个类别
                model.add(Dense(10, activation='softmax'))
                model.compile(
                    optimizer=Adam(learning_rate=0.001),
                    loss='sparse_categorical_crossentropy',
                    metrics=['accuracy']
                )
            
            return model
            
        except ImportError:
            print("⚠️  TensorFlow未安装，跳过BP神经网络")
            return None
    
    def create_cnn_lstm_model(self, input_dim: int, task_type: str):
        """创建CNN-LSTM模型"""
        try:
            import tensorflow as tf
            from tensorflow.keras.models import Sequential
            from tensorflow.keras.layers import Dense, LSTM, Conv1D, MaxPooling1D, Flatten, Dropout
            from tensorflow.keras.optimizers import Adam
            
            # 将输入重塑为时间序列格式
            # 假设将特征分成时间步
            timesteps = min(10, input_dim // 5)  # 时间步数
            features_per_step = input_dim // timesteps
            
            model = Sequential()
            
            # CNN层
            model.add(Conv1D(
                filters=64, 
                kernel_size=3, 
                activation='relu',
                input_shape=(timesteps, features_per_step)
            ))
            model.add(MaxPooling1D(pool_size=2))
            model.add(Dropout(0.3))
            
            model.add(Conv1D(filters=32, kernel_size=3, activation='relu'))
            model.add(Dropout(0.3))
            
            # LSTM层
            model.add(LSTM(50, return_sequences=True))
            model.add(Dropout(0.3))
            model.add(LSTM(25))
            model.add(Dropout(0.3))
            
            # 全连接层
            model.add(Dense(25, activation='relu'))
            model.add(Dropout(0.2))
            
            # 输出层
            if task_type == 'regression':
                model.add(Dense(1, activation='linear'))
                model.compile(
                    optimizer=Adam(learning_rate=0.001),
                    loss='mse',
                    metrics=['mae']
                )
            else:
                model.add(Dense(10, activation='softmax'))
                model.compile(
                    optimizer=Adam(learning_rate=0.001),
                    loss='sparse_categorical_crossentropy',
                    metrics=['accuracy']
                )
            
            return model, timesteps, features_per_step
            
        except ImportError:
            print("⚠️  TensorFlow未安装，跳过CNN-LSTM")
            return None, None, None

    def create_tcn_model(self, input_dim: int, task_type: str):
        """创建时间卷积网络(TCN)模型"""
        try:
            import tensorflow as tf
            from tensorflow.keras.models import Sequential
            from tensorflow.keras.layers import Dense, Conv1D, Dropout, BatchNormalization, Add, Activation
            from tensorflow.keras.optimizers import Adam

            # TCN参数
            timesteps = min(20, input_dim // 8)  # 时间步数
            features_per_step = input_dim // timesteps
            nb_filters = 64
            kernel_size = 3
            nb_stacks = 2
            dilations = [1, 2, 4, 8]

            def residual_block(x, dilation_rate, nb_filters, kernel_size, dropout_rate=0.1):
                """TCN残差块"""
                # 第一个卷积层
                conv1 = Conv1D(filters=nb_filters, kernel_size=kernel_size,
                              dilation_rate=dilation_rate, padding='causal',
                              activation='relu')(x)
                conv1 = BatchNormalization()(conv1)
                conv1 = Dropout(dropout_rate)(conv1)

                # 第二个卷积层
                conv2 = Conv1D(filters=nb_filters, kernel_size=kernel_size,
                              dilation_rate=dilation_rate, padding='causal',
                              activation='relu')(conv1)
                conv2 = BatchNormalization()(conv2)
                conv2 = Dropout(dropout_rate)(conv2)

                # 残差连接
                if x.shape[-1] != nb_filters:
                    # 调整维度
                    x = Conv1D(nb_filters, 1, padding='same')(x)

                # 添加残差连接
                res = Add()([x, conv2])
                return Activation('relu')(res)

            # 构建TCN模型
            model = Sequential()

            # 输入层
            model.add(tf.keras.layers.Input(shape=(timesteps, features_per_step)))

            # TCN层
            x = model.output if hasattr(model, 'output') else model.layers[-1].output if model.layers else None

            # 使用函数式API构建TCN
            inputs = tf.keras.layers.Input(shape=(timesteps, features_per_step))
            x = inputs

            # 多个TCN块
            for stack in range(nb_stacks):
                for dilation in dilations:
                    x = residual_block(x, dilation, nb_filters, kernel_size)

            # 全局平均池化
            x = tf.keras.layers.GlobalAveragePooling1D()(x)

            # 全连接层
            x = Dense(128, activation='relu')(x)
            x = BatchNormalization()(x)
            x = Dropout(0.3)(x)

            x = Dense(64, activation='relu')(x)
            x = BatchNormalization()(x)
            x = Dropout(0.2)(x)

            # 输出层
            if task_type == 'regression':
                outputs = Dense(1, activation='linear')(x)
                model = tf.keras.Model(inputs=inputs, outputs=outputs)
                model.compile(
                    optimizer=Adam(learning_rate=0.001),
                    loss='mse',
                    metrics=['mae']
                )
            else:
                outputs = Dense(10, activation='softmax')(x)  # 假设最多10个类别
                model = tf.keras.Model(inputs=inputs, outputs=outputs)
                model.compile(
                    optimizer=Adam(learning_rate=0.001),
                    loss='sparse_categorical_crossentropy',
                    metrics=['accuracy']
                )

            return model, timesteps, features_per_step

        except ImportError:
            print("⚠️  TensorFlow未安装，跳过TCN")
            return None, None, None
    
    def _check_gpu(self) -> bool:
        """检查GPU可用性"""
        try:
            import tensorflow as tf
            return len(tf.config.list_physical_devices('GPU')) > 0
        except:
            return False
    
    def _reshape_for_cnn_lstm(self, X: np.ndarray, timesteps: int, features_per_step: int) -> np.ndarray:
        """为CNN-LSTM重塑数据"""
        n_samples = X.shape[0]
        total_features_needed = timesteps * features_per_step
        
        if X.shape[1] >= total_features_needed:
            # 截取需要的特征数
            X_reshaped = X[:, :total_features_needed]
        else:
            # 填充到需要的特征数
            padding = np.zeros((n_samples, total_features_needed - X.shape[1]))
            X_reshaped = np.concatenate([X, padding], axis=1)
        
        # 重塑为(samples, timesteps, features)
        return X_reshaped.reshape(n_samples, timesteps, features_per_step)
    
    def train_single_model(self, model, model_name: str, X_train, X_test, y_train, y_test,
                          task_type: str, **kwargs) -> dict:
        """训练单个模型"""
        print(f"    训练 {model_name}...")

        start_time = time.time()

        try:
            if 'CNN-LSTM' in model_name:
                # CNN-LSTM特殊处理
                timesteps = kwargs.get('timesteps')
                features_per_step = kwargs.get('features_per_step')
                
                X_train_reshaped = self._reshape_for_cnn_lstm(X_train, timesteps, features_per_step)
                X_test_reshaped = self._reshape_for_cnn_lstm(X_test, timesteps, features_per_step)
                
                # 训练
                history = model.fit(
                    X_train_reshaped, y_train,
                    epochs=50,
                    batch_size=32,
                    validation_split=0.2,
                    verbose=0
                )
                
                # 预测
                y_pred = model.predict(X_test_reshaped, verbose=0)
                if task_type == 'regression':
                    y_pred = y_pred.flatten()
                else:
                    y_pred = np.argmax(y_pred, axis=1)
                
            elif 'BP' in model_name:
                # BP神经网络
                history = model.fit(
                    X_train, y_train,
                    epochs=100,
                    batch_size=32,
                    validation_split=0.2,
                    verbose=0
                )
                
                # 预测
                y_pred = model.predict(X_test, verbose=0)
                if task_type == 'regression':
                    y_pred = y_pred.flatten()
                else:
                    y_pred = np.argmax(y_pred, axis=1)
                
            elif 'TCN' in model_name:
                # TCN特殊处理
                timesteps = kwargs.get('timesteps')
                features_per_step = kwargs.get('features_per_step')

                X_train_reshaped = self._reshape_for_cnn_lstm(X_train, timesteps, features_per_step)
                X_test_reshaped = self._reshape_for_cnn_lstm(X_test, timesteps, features_per_step)

                # 训练
                history = model.fit(
                    X_train_reshaped, y_train,
                    epochs=50,
                    batch_size=32,
                    validation_split=0.2,
                    verbose=0
                )

                # 保存训练历史
                self.training_histories[model_name] = history.history

                # 预测
                y_pred = model.predict(X_test_reshaped, verbose=0)
                if task_type == 'regression':
                    y_pred = y_pred.flatten()
                else:
                    y_pred = np.argmax(y_pred, axis=1)

            else:
                # 传统机器学习模型
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)

                # 保存特征重要性（如果支持）
                if hasattr(model, 'feature_importances_'):
                    self.feature_importances[model_name] = model.feature_importances_
            
            # 计算评估指标
            if task_type == 'regression':
                r2 = r2_score(y_test, y_pred)
                mae = np.mean(np.abs(y_test - y_pred))
                rmse = np.sqrt(np.mean((y_test - y_pred) ** 2))
                
                metrics = {
                    'r2_score': r2,
                    'mae': mae,
                    'rmse': rmse
                }
                
                print(f"      R² = {r2:.4f}, MAE = {mae:.4f}")
                
            else:
                accuracy = accuracy_score(y_test, y_pred)
                
                metrics = {
                    'accuracy': accuracy
                }
                
                print(f"      准确率 = {accuracy:.4f}")
            
            # 记录训练时间
            training_time = time.time() - start_time
            self.training_times[model_name] = training_time
            metrics['training_time'] = training_time

            # 保存模型
            model_filename = f"model_{model_name.lower().replace('-', '_').replace(' ', '_')}.pkl"
            if 'tensorflow' in str(type(model)):
                model.save(model_filename.replace('.pkl', '.h5'))
            else:
                joblib.dump(model, model_filename)

            return metrics
            
        except Exception as e:
            print(f"      ❌ 训练失败: {str(e)}")
            return {'error': str(e)}
    
    def train_all_models(self, X: np.ndarray, y: np.ndarray, task_type: str, dataset_name: str) -> dict:
        """训练所有模型"""
        print(f"  准备数据...")
        
        # 数据预处理
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 保存scaler
        self.scalers[dataset_name] = scaler
        
        # 处理分类标签
        if task_type == 'classification':
            encoder = LabelEncoder()
            y_encoded = encoder.fit_transform(y)
            self.encoders[dataset_name] = encoder
            y_processed = y_encoded
        else:
            y_processed = y
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y_processed, test_size=0.2, random_state=42
        )
        
        print(f"  训练集: {X_train.shape}, 测试集: {X_test.shape}")
        
        results = {}

        # 传统机器学习算法
        traditional_models = [
            ('Random Forest', self.create_random_forest_model(task_type)),
            ('Gradient Boosting', self.create_gradient_boosting_model(task_type)),
            ('AdaBoost', self.create_adaboost_model(task_type)),
            ('Extra Trees', self.create_extra_trees_model(task_type)),
            ('SVM', self.create_svm_model(task_type)),
            ('XGBoost', self.create_xgboost_model(task_type))
        ]

        for model_name, model in traditional_models:
            if model is not None:
                results[model_name] = self.train_single_model(
                    model, model_name, X_train, X_test, y_train, y_test, task_type
                )
        
        # 深度学习算法
        deep_learning_models = [
            ('BP Neural Network', self.create_bp_neural_network(X_scaled.shape[1], task_type), {}),
            ('CNN-LSTM', *self.create_cnn_lstm_model(X_scaled.shape[1], task_type)),
            ('TCN', *self.create_tcn_model(X_scaled.shape[1], task_type))
        ]

        for model_info in deep_learning_models:
            if len(model_info) == 3:
                model_name, model, kwargs = model_info
                if model is not None:
                    results[model_name] = self.train_single_model(
                        model, model_name, X_train, X_test, y_train, y_test, task_type, **kwargs
                    )
            elif len(model_info) == 4:
                model_name, model, timesteps, features_per_step = model_info
                if model is not None:
                    results[model_name] = self.train_single_model(
                        model, model_name, X_train, X_test, y_train, y_test, task_type,
                        timesteps=timesteps, features_per_step=features_per_step
                    )
        
        return results

    def create_visualizations(self, results: dict, dataset_name: str, task_type: str):
        """创建可视化图表"""
        print(f"    生成可视化图表...")

        try:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建图表目录
            import os
            os.makedirs('visualizations', exist_ok=True)

            # 1. 模型性能对比图
            self._plot_model_comparison(results, dataset_name, task_type)

            # 2. 训练时间对比图
            self._plot_training_time_comparison(results, dataset_name)

            # 3. 特征重要性图
            self._plot_feature_importance(dataset_name)

            # 4. 训练历史图（深度学习模型）
            self._plot_training_history(dataset_name)

            print(f"    ✅ 可视化图表已保存到 visualizations/ 目录")

        except Exception as e:
            print(f"    ⚠️  可视化生成失败: {str(e)}")

    def _plot_model_comparison(self, results: dict, dataset_name: str, task_type: str):
        """绘制模型性能对比图"""
        try:
            models = []
            scores = []

            for model_name, metrics in results.items():
                if 'error' not in metrics:
                    models.append(model_name)
                    if task_type == 'regression':
                        scores.append(metrics.get('r2_score', 0))
                    else:
                        scores.append(metrics.get('accuracy', 0))

            if not models:
                return

            plt.figure(figsize=(12, 8))
            bars = plt.bar(models, scores, color='skyblue', alpha=0.7)

            # 添加数值标签
            for bar, score in zip(bars, scores):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{score:.3f}', ha='center', va='bottom')

            plt.title(f'{dataset_name} - 模型性能对比', fontsize=16, fontweight='bold')
            plt.xlabel('模型', fontsize=12)

            if task_type == 'regression':
                plt.ylabel('R² 分数', fontsize=12)
                plt.axhline(y=0.75, color='red', linestyle='--', alpha=0.7, label='目标线 (0.75)')
            else:
                plt.ylabel('准确率', fontsize=12)
                plt.axhline(y=0.85, color='red', linestyle='--', alpha=0.7, label='目标线 (0.85)')

            plt.xticks(rotation=45, ha='right')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.tight_layout()

            plt.savefig(f'visualizations/{dataset_name}_model_comparison.png', dpi=300, bbox_inches='tight')
            plt.close()

        except Exception as e:
            print(f"      模型对比图生成失败: {str(e)}")

    def _plot_training_time_comparison(self, results: dict, dataset_name: str):
        """绘制训练时间对比图"""
        try:
            models = []
            times = []

            for model_name, metrics in results.items():
                if 'training_time' in metrics:
                    models.append(model_name)
                    times.append(metrics['training_time'])

            if not models:
                return

            plt.figure(figsize=(12, 6))
            bars = plt.bar(models, times, color='lightcoral', alpha=0.7)

            # 添加数值标签
            for bar, time_val in zip(bars, times):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(times)*0.01,
                        f'{time_val:.2f}s', ha='center', va='bottom')

            plt.title(f'{dataset_name} - 训练时间对比', fontsize=16, fontweight='bold')
            plt.xlabel('模型', fontsize=12)
            plt.ylabel('训练时间 (秒)', fontsize=12)
            plt.xticks(rotation=45, ha='right')
            plt.grid(True, alpha=0.3)
            plt.tight_layout()

            plt.savefig(f'visualizations/{dataset_name}_training_time.png', dpi=300, bbox_inches='tight')
            plt.close()

        except Exception as e:
            print(f"      训练时间图生成失败: {str(e)}")

    def _plot_feature_importance(self, dataset_name: str):
        """绘制特征重要性图"""
        try:
            if not self.feature_importances:
                return

            for model_name, importance in self.feature_importances.items():
                if len(importance) > 0:
                    # 选择前20个最重要的特征
                    top_indices = np.argsort(importance)[-20:]
                    top_importance = importance[top_indices]
                    feature_names = [f'Feature_{i}' for i in top_indices]

                    plt.figure(figsize=(10, 8))
                    plt.barh(feature_names, top_importance, color='lightgreen', alpha=0.7)
                    plt.title(f'{dataset_name} - {model_name} 特征重要性', fontsize=14, fontweight='bold')
                    plt.xlabel('重要性分数', fontsize=12)
                    plt.ylabel('特征', fontsize=12)
                    plt.grid(True, alpha=0.3)
                    plt.tight_layout()

                    plt.savefig(f'visualizations/{dataset_name}_{model_name}_feature_importance.png',
                              dpi=300, bbox_inches='tight')
                    plt.close()

        except Exception as e:
            print(f"      特征重要性图生成失败: {str(e)}")

    def _plot_training_history(self, dataset_name: str):
        """绘制训练历史图"""
        try:
            if not self.training_histories:
                return

            for model_name, history in self.training_histories.items():
                if 'loss' in history:
                    plt.figure(figsize=(12, 4))

                    # 损失曲线
                    plt.subplot(1, 2, 1)
                    plt.plot(history['loss'], label='训练损失')
                    if 'val_loss' in history:
                        plt.plot(history['val_loss'], label='验证损失')
                    plt.title(f'{model_name} - 损失曲线')
                    plt.xlabel('Epoch')
                    plt.ylabel('损失')
                    plt.legend()
                    plt.grid(True, alpha=0.3)

                    # 指标曲线
                    plt.subplot(1, 2, 2)
                    metric_key = 'mae' if 'mae' in history else 'accuracy'
                    if metric_key in history:
                        plt.plot(history[metric_key], label=f'训练{metric_key}')
                        if f'val_{metric_key}' in history:
                            plt.plot(history[f'val_{metric_key}'], label=f'验证{metric_key}')
                        plt.title(f'{model_name} - {metric_key.upper()}曲线')
                        plt.xlabel('Epoch')
                        plt.ylabel(metric_key.upper())
                        plt.legend()
                        plt.grid(True, alpha=0.3)

                    plt.tight_layout()
                    plt.savefig(f'visualizations/{dataset_name}_{model_name}_training_history.png',
                              dpi=300, bbox_inches='tight')
                    plt.close()

        except Exception as e:
            print(f"      训练历史图生成失败: {str(e)}")

    def select_best_model(self, results: dict, task_type: str) -> dict:
        """自动选择最佳模型"""
        try:
            best_model = None
            best_score = -1
            best_metrics = None

            metric_key = 'r2_score' if task_type == 'regression' else 'accuracy'

            for model_name, metrics in results.items():
                if 'error' not in metrics and metric_key in metrics:
                    score = metrics[metric_key]
                    if score > best_score:
                        best_score = score
                        best_model = model_name
                        best_metrics = metrics

            if best_model:
                recommendation = {
                    'best_model': best_model,
                    'best_score': best_score,
                    'best_metrics': best_metrics,
                    'recommendation_reason': self._get_recommendation_reason(best_model, best_score, task_type)
                }

                return recommendation
            else:
                return {'error': '未找到有效的模型结果'}

        except Exception as e:
            return {'error': f'模型选择失败: {str(e)}'}

    def _get_recommendation_reason(self, model_name: str, score: float, task_type: str) -> str:
        """获取推荐理由"""
        target_score = 0.75 if task_type == 'regression' else 0.85
        metric_name = 'R²' if task_type == 'regression' else '准确率'

        reason = f"{model_name} 在所有模型中表现最佳，{metric_name}达到 {score:.4f}"

        if score >= target_score:
            reason += f"，超过目标值 {target_score}，推荐用于生产环境。"
        else:
            reason += f"，但未达到目标值 {target_score}，建议进一步优化。"

        # 添加模型特点说明
        model_characteristics = {
            'Random Forest': '随机森林具有良好的泛化能力和特征重要性分析功能',
            'XGBoost': 'XGBoost在结构化数据上通常表现优异，支持GPU加速',
            'Gradient Boosting': '梯度提升树能够捕捉复杂的非线性关系',
            'SVM': '支持向量机在小样本数据上表现稳定',
            'BP Neural Network': 'BP神经网络能够学习复杂的非线性映射',
            'CNN-LSTM': 'CNN-LSTM适合处理时序数据的空间和时间特征',
            'TCN': '时间卷积网络在长序列建模上具有优势',
            'AdaBoost': 'AdaBoost通过集成弱学习器提高性能',
            'Extra Trees': 'Extra Trees具有更强的随机性，能够减少过拟合'
        }

        if model_name in model_characteristics:
            reason += f" {model_characteristics[model_name]}。"

        return reason

    def generate_performance_statistics(self, results: dict, task_type: str) -> dict:
        """生成性能统计分析"""
        try:
            metric_key = 'r2_score' if task_type == 'regression' else 'accuracy'

            scores = []
            training_times = []
            model_names = []

            for model_name, metrics in results.items():
                if 'error' not in metrics and metric_key in metrics:
                    scores.append(metrics[metric_key])
                    training_times.append(metrics.get('training_time', 0))
                    model_names.append(model_name)

            if not scores:
                return {'error': '没有有效的性能数据'}

            statistics = {
                'model_count': len(scores),
                'score_statistics': {
                    'mean': np.mean(scores),
                    'std': np.std(scores),
                    'min': np.min(scores),
                    'max': np.max(scores),
                    'median': np.median(scores)
                },
                'time_statistics': {
                    'mean': np.mean(training_times),
                    'std': np.std(training_times),
                    'min': np.min(training_times),
                    'max': np.max(training_times),
                    'total': np.sum(training_times)
                },
                'model_rankings': self._rank_models(results, task_type),
                'performance_analysis': self._analyze_performance_patterns(results, task_type)
            }

            return statistics

        except Exception as e:
            return {'error': f'统计分析失败: {str(e)}'}

    def _rank_models(self, results: dict, task_type: str) -> list:
        """对模型进行排名"""
        metric_key = 'r2_score' if task_type == 'regression' else 'accuracy'

        model_scores = []
        for model_name, metrics in results.items():
            if 'error' not in metrics and metric_key in metrics:
                model_scores.append({
                    'model': model_name,
                    'score': metrics[metric_key],
                    'training_time': metrics.get('training_time', 0)
                })

        # 按分数排序
        model_scores.sort(key=lambda x: x['score'], reverse=True)

        return model_scores

    def _analyze_performance_patterns(self, results: dict, task_type: str) -> dict:
        """分析性能模式"""
        analysis = {
            'traditional_ml_performance': [],
            'deep_learning_performance': [],
            'ensemble_performance': []
        }

        traditional_ml = ['Random Forest', 'XGBoost', 'Gradient Boosting', 'SVM', 'AdaBoost', 'Extra Trees']
        deep_learning = ['BP Neural Network', 'CNN-LSTM', 'TCN']
        ensemble_methods = ['Random Forest', 'XGBoost', 'Gradient Boosting', 'AdaBoost', 'Extra Trees']

        metric_key = 'r2_score' if task_type == 'regression' else 'accuracy'

        for model_name, metrics in results.items():
            if 'error' not in metrics and metric_key in metrics:
                score = metrics[metric_key]

                if model_name in traditional_ml:
                    analysis['traditional_ml_performance'].append(score)
                if model_name in deep_learning:
                    analysis['deep_learning_performance'].append(score)
                if model_name in ensemble_methods:
                    analysis['ensemble_performance'].append(score)

        # 计算各类别平均性能
        for category in analysis:
            if analysis[category]:
                analysis[f'{category}_mean'] = np.mean(analysis[category])
                analysis[f'{category}_std'] = np.std(analysis[category])
            else:
                analysis[f'{category}_mean'] = 0
                analysis[f'{category}_std'] = 0

        return analysis

def main():
    """测试函数"""
    # 创建测试数据
    from sklearn.datasets import make_regression, make_classification
    
    print("🧪 测试统一机器学习模型...")
    
    trainer = UnifiedMLModels()
    
    # 测试回归
    print("\n📈 测试回归任务...")
    X_reg, y_reg = make_regression(n_samples=1000, n_features=20, noise=0.1, random_state=42)
    results_reg = trainer.train_all_models(X_reg, y_reg, 'regression', 'test_regression')
    
    # 测试分类
    print("\n📊 测试分类任务...")
    X_cls, y_cls = make_classification(n_samples=1000, n_features=20, n_classes=3, random_state=42)
    results_cls = trainer.train_all_models(X_cls, y_cls, 'classification', 'test_classification')
    
    print("\n✅ 测试完成!")

if __name__ == "__main__":
    main()
