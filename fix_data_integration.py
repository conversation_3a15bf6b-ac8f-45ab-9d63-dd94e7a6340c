#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据整合问题修复脚本
修复振动信号分析系统的数据预处理和特征提取问题

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import os
import json
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List
import shutil

def fix_data_integration():
    """修复数据整合问题"""
    print("🔧 开始修复数据整合问题...")
    print("=" * 80)
    
    # 步骤1: 清理现有的特征文件
    print("\n📁 步骤1: 清理现有特征文件")
    print("-" * 50)
    
    feature_files_to_clean = [
        "combined_features.csv",
        "./ml/combined_features_clean.csv",
        "./analysis_results/combined_features.csv"
    ]
    
    for file_path in feature_files_to_clean:
        if os.path.exists(file_path):
            try:
                # 备份现有文件
                backup_path = f"{file_path}.backup"
                shutil.copy2(file_path, backup_path)
                os.remove(file_path)
                print(f"   ✅ 已清理并备份: {file_path} -> {backup_path}")
            except Exception as e:
                print(f"   ⚠️  清理文件失败 {file_path}: {str(e)}")
        else:
            print(f"   ℹ️  文件不存在: {file_path}")
    
    # 步骤2: 强制使用新格式数据预处理
    print("\n🔄 步骤2: 强制使用新格式数据预处理")
    print("-" * 50)
    
    # 检查新格式预处理数据是否存在
    new_format_dir = Path("data_preprocessed")
    if not new_format_dir.exists():
        print("❌ 新格式预处理目录不存在，开始预处理...")
        
        try:
            from new_data_preprocessor import NewDataPreprocessor
            
            # 初始化新格式预处理器
            preprocessor = NewDataPreprocessor("data", "data_preprocessed")
            
            # 执行预处理
            summary = preprocessor.process_all_files()
            
            if summary['success']:
                print(f"✅ 新格式数据预处理完成")
                print(f"   处理文件数: {summary['processed_files']}/{summary['total_files']}")
                print(f"   成功率: {summary['success_rate']:.1f}%")
            else:
                print(f"❌ 新格式数据预处理失败")
                return False
                
        except Exception as e:
            print(f"❌ 新格式预处理失败: {str(e)}")
            return False
    else:
        print(f"✅ 新格式预处理目录已存在: {new_format_dir}")
        
        # 检查预处理文件数量
        csv_files = list(new_format_dir.glob("**/*.csv"))
        print(f"   预处理文件数: {len(csv_files)}")
    
    # 步骤3: 从新格式预处理数据提取特征
    print("\n📊 步骤3: 从新格式预处理数据提取特征")
    print("-" * 50)
    
    try:
        features_df = extract_features_from_new_format_data(new_format_dir)
        
        if features_df is not None and not features_df.empty:
            print(f"✅ 特征提取成功")
            print(f"   数据形状: {features_df.shape}")
            print(f"   样本数量: {len(features_df)}")
            
            # 保存新的特征文件
            features_df.to_csv('combined_features.csv', index=False)
            print(f"   ✅ 已保存新特征文件: combined_features.csv")
            
            # 显示数据统计
            print_data_statistics(features_df)
            
            return True
        else:
            print("❌ 特征提取失败")
            return False
            
    except Exception as e:
        print(f"❌ 特征提取过程出错: {str(e)}")
        return False

def extract_features_from_new_format_data(data_dir: Path) -> pd.DataFrame:
    """从新格式预处理数据提取特征"""
    print("   🔍 扫描新格式预处理数据...")
    
    all_features = []
    file_count = 0
    
    # 遍历预处理数据目录
    for csv_file in data_dir.glob("**/*.csv"):
        try:
            print(f"   📄 处理文件: {csv_file.name}")
            
            # 读取CSV文件
            df = pd.read_csv(csv_file)
            
            if df.empty:
                print(f"      ⚠️  文件为空，跳过")
                continue
            
            # 提取路径信息
            path_info = extract_info_from_path(str(csv_file))
            
            # 提取特征
            features = extract_comprehensive_features(df, str(csv_file), path_info)
            
            if features:
                all_features.append(features)
                file_count += 1
                
                if file_count % 100 == 0:
                    print(f"      已处理 {file_count} 个文件...")
            else:
                print(f"      ⚠️  特征提取失败，跳过")
                
        except Exception as e:
            print(f"      ❌ 处理文件失败 {csv_file.name}: {str(e)}")
            continue
    
    if all_features:
        features_df = pd.DataFrame(all_features)
        print(f"   ✅ 特征提取完成，处理了 {file_count} 个文件")
        return features_df
    else:
        print(f"   ❌ 没有成功提取任何特征")
        return None

def extract_info_from_path(file_path: str) -> dict:
    """从文件路径提取信息"""
    path_parts = file_path.replace('\\', '/').split('/')
    
    info = {
        'speed_kmh': None,
        'load_tons': None,
        'axle_type': None,
        'lane_number': None
    }
    
    for part in path_parts:
        # 提取速度信息
        if 'km_h' in part or 'km/h' in part:
            try:
                speed_str = part.replace('km_h', '').replace('km/h', '').replace('_repeat', '')
                info['speed_kmh'] = float(speed_str)
            except:
                pass
        
        # 提取载重信息
        if '吨' in part:
            try:
                load_str = part.replace('吨', '')
                info['load_tons'] = float(load_str)
            except:
                pass
        
        # 提取轴型信息
        if '双轴' in part:
            info['axle_type'] = 2
        elif '三轴' in part:
            info['axle_type'] = 3
        elif '四轴' in part:
            info['axle_type'] = 4
    
    return info

def extract_comprehensive_features(df: pd.DataFrame, file_path: str, path_info: dict) -> dict:
    """提取综合特征"""
    try:
        # 获取传感器列（排除元数据列）
        exclude_cols = ['count', 'speed_kmh', 'load_tons', 'axle_type', 'lane_number', 
                       'monitoring_point', 'valid_sensors_count', 'total_sensors_count',
                       'valid_sensors_list', 'sensor_mapping_info']
        
        sensor_columns = [col for col in df.columns if col not in exclude_cols]
        
        if not sensor_columns:
            return None
        
        # 基础信息
        features = {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'sensor_count': len(sensor_columns)
        }
        
        # 从文件中读取元数据（如果存在）
        if 'speed_kmh' in df.columns and not df['speed_kmh'].isna().all():
            features['speed_kmh'] = df['speed_kmh'].iloc[0]
        else:
            features['speed_kmh'] = path_info.get('speed_kmh')
            
        if 'load_tons' in df.columns and not df['load_tons'].isna().all():
            features['load_tons'] = df['load_tons'].iloc[0]
        else:
            features['load_tons'] = path_info.get('load_tons')
            
        if 'axle_type' in df.columns and not df['axle_type'].isna().all():
            features['axle_type'] = df['axle_type'].iloc[0]
        else:
            features['axle_type'] = path_info.get('axle_type')
        
        # 为每个传感器提取特征
        for col in sensor_columns[:20]:  # 限制传感器数量避免特征过多
            try:
                signal = df[col].dropna().values
                if len(signal) > 100:  # 确保有足够的数据点
                    
                    # 时域特征
                    features[f'{col}_mean'] = np.mean(signal)
                    features[f'{col}_std'] = np.std(signal)
                    features[f'{col}_var'] = np.var(signal)
                    features[f'{col}_rms'] = np.sqrt(np.mean(signal**2))
                    features[f'{col}_peak'] = np.max(np.abs(signal))
                    features[f'{col}_peak_to_peak'] = np.max(signal) - np.min(signal)
                    features[f'{col}_crest_factor'] = features[f'{col}_peak'] / features[f'{col}_rms'] if features[f'{col}_rms'] > 0 else 0
                    features[f'{col}_skewness'] = calculate_skewness(signal)
                    features[f'{col}_kurtosis'] = calculate_kurtosis(signal)
                    features[f'{col}_energy'] = np.sum(signal**2)
                    
                    # 频域特征
                    try:
                        fft_signal = np.fft.fft(signal)
                        fft_magnitude = np.abs(fft_signal[:len(fft_signal)//2])
                        
                        if len(fft_magnitude) > 0:
                            features[f'{col}_dominant_freq'] = np.argmax(fft_magnitude)
                            features[f'{col}_spectral_energy'] = np.sum(fft_magnitude**2)
                            features[f'{col}_spectral_centroid'] = np.sum(np.arange(len(fft_magnitude)) * fft_magnitude) / np.sum(fft_magnitude) if np.sum(fft_magnitude) > 0 else 0
                    except:
                        pass
                        
            except Exception:
                continue
        
        return features
        
    except Exception as e:
        return None

def calculate_skewness(data):
    """计算偏度"""
    try:
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0
        return np.mean(((data - mean) / std) ** 3)
    except:
        return 0

def calculate_kurtosis(data):
    """计算峰度"""
    try:
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0
        return np.mean(((data - mean) / std) ** 4) - 3
    except:
        return 0

def print_data_statistics(df: pd.DataFrame):
    """打印数据统计信息"""
    print(f"\n📊 数据统计信息:")
    print(f"   总样本数: {len(df)}")
    print(f"   总特征数: {len(df.columns)}")
    
    # 目标变量统计
    target_vars = ['speed_kmh', 'load_tons', 'axle_type']
    for var in target_vars:
        if var in df.columns:
            non_null_count = df[var].notna().sum()
            print(f"   {var}: {non_null_count} 个有效值")
            
            if non_null_count > 0:
                if var in ['speed_kmh', 'load_tons']:
                    print(f"      范围: {df[var].min():.1f} - {df[var].max():.1f}")
                    print(f"      平均: {df[var].mean():.1f}")
                else:
                    unique_values = df[var].unique()
                    print(f"      类别: {sorted([x for x in unique_values if pd.notna(x)])}")

if __name__ == "__main__":
    success = fix_data_integration()
    if success:
        print(f"\n✅ 数据整合问题修复完成！")
        print(f"   新的combined_features.csv已生成")
        print(f"   可以继续运行主程序进行分析")
    else:
        print(f"\n❌ 数据整合问题修复失败")
        print(f"   请检查错误信息并手动处理")
