#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试集成的可视化系统
验证所有修改是否正确集成到主程序中

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import sys
from pathlib import Path

def test_enhanced_academic_visualizations():
    """测试增强版学术可视化"""
    print("🧪 测试增强版学术可视化...")
    
    try:
        from visualization_generator_enhanced import EnhancedVisualizationGenerator
        
        # 初始化生成器
        generator = EnhancedVisualizationGenerator(output_dir='test_academic_visualizations')
        
        # 生成所有图表
        generator.generate_all_visualizations()
        
        # 检查输出文件
        output_dir = Path('test_academic_visualizations')
        expected_files = [
            'data_expansion_comparison.png',
            'model_performance_comparison.png',
            'optimization_results.png',
            'data_distribution_analysis.png',
            'feature_importance_analysis.png',
            'confusion_matrix_analysis.png',
            'roc_curves_multiclass.png',
            'precision_recall_curves.png',
            'precision_recall_summary.png'
        ]
        
        success_count = 0
        for file_name in expected_files:
            file_path = output_dir / file_name
            if file_path.exists():
                print(f"   ✅ {file_name} - 生成成功")
                success_count += 1
            else:
                print(f"   ❌ {file_name} - 生成失败")
        
        print(f"   📊 成功率: {success_count}/{len(expected_files)} ({success_count/len(expected_files)*100:.1f}%)")
        return success_count == len(expected_files)
        
    except Exception as e:
        print(f"   ❌ 增强版学术可视化测试失败: {str(e)}")
        return False

def test_technical_workflow_visualizations():
    """测试技术工作流可视化"""
    print("\n🧪 测试技术工作流可视化...")
    
    try:
        from technical_workflow_visualizer import TechnicalWorkflowVisualizer
        
        # 初始化生成器
        generator = TechnicalWorkflowVisualizer(output_base_dir='test_technical_visualizations')
        
        # 生成所有图表
        generator.generate_all_technical_visualizations()
        
        # 检查输出目录结构
        base_dir = Path('test_technical_visualizations')
        expected_dirs = [
            'system_overview',
            'workflow_diagrams', 
            'signal_plots',
            'feature_extraction',
            'signal_preprocessing'
        ]
        
        success_count = 0
        total_files = 0
        
        for dir_name in expected_dirs:
            dir_path = base_dir / dir_name
            if dir_path.exists():
                files = list(dir_path.glob('*.png'))
                print(f"   ✅ {dir_name}/ - {len(files)} 个文件")
                success_count += 1
                total_files += len(files)
            else:
                print(f"   ❌ {dir_name}/ - 目录不存在")
        
        print(f"   📊 目录成功率: {success_count}/{len(expected_dirs)} ({success_count/len(expected_dirs)*100:.1f}%)")
        print(f"   📊 总文件数: {total_files}")
        return success_count == len(expected_dirs) and total_files >= 10
        
    except Exception as e:
        print(f"   ❌ 技术工作流可视化测试失败: {str(e)}")
        return False

def test_unified_system_integration():
    """测试统一系统集成"""
    print("\n🧪 测试统一系统集成...")
    
    try:
        # 检查主程序是否包含新的可视化方法
        with open('unified_vibration_analysis.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键方法是否存在
        required_methods = [
            '_generate_enhanced_academic_visualizations',
            '_generate_technical_workflow_visualizations',
            'EnhancedVisualizationGenerator',
            'TechnicalWorkflowVisualizer'
        ]
        
        success_count = 0
        for method in required_methods:
            if method in content:
                print(f"   ✅ {method} - 已集成")
                success_count += 1
            else:
                print(f"   ❌ {method} - 未找到")
        
        # 检查报告部分是否包含新的可视化说明
        visualization_sections = [
            'academic_visualizations_enhanced',
            'technical_visualizations',
            '330 DPI',
            'Times New Roman',
            'English'
        ]
        
        for section in visualization_sections:
            if section in content:
                print(f"   ✅ 报告包含: {section}")
                success_count += 1
            else:
                print(f"   ❌ 报告缺少: {section}")
        
        total_checks = len(required_methods) + len(visualization_sections)
        print(f"   📊 集成完整性: {success_count}/{total_checks} ({success_count/total_checks*100:.1f}%)")
        return success_count >= total_checks * 0.8  # 80%通过率
        
    except Exception as e:
        print(f"   ❌ 统一系统集成测试失败: {str(e)}")
        return False

def test_language_conversion():
    """测试语言转换"""
    print("\n🧪 测试语言转换...")
    
    try:
        # 检查可视化文件中是否还有中文字符
        import re
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        
        files_to_check = [
            'visualization_generator_enhanced.py',
            'technical_workflow_visualizer.py'
        ]
        
        success_count = 0
        for file_name in files_to_check:
            if os.path.exists(file_name):
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找中文字符
                chinese_matches = chinese_pattern.findall(content)
                if chinese_matches:
                    print(f"   ⚠️  {file_name} - 发现 {len(chinese_matches)} 个中文字符")
                    # 显示前几个中文字符
                    unique_chars = list(set(chinese_matches))[:5]
                    print(f"      示例: {unique_chars}")
                else:
                    print(f"   ✅ {file_name} - 无中文字符")
                    success_count += 1
            else:
                print(f"   ❌ {file_name} - 文件不存在")
        
        print(f"   📊 语言转换成功率: {success_count}/{len(files_to_check)} ({success_count/len(files_to_check)*100:.1f}%)")
        return success_count == len(files_to_check)
        
    except Exception as e:
        print(f"   ❌ 语言转换测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始集成可视化系统测试")
    print("=" * 80)
    
    # 运行所有测试
    tests = [
        ("增强版学术可视化", test_enhanced_academic_visualizations),
        ("技术工作流可视化", test_technical_workflow_visualizations),
        ("统一系统集成", test_unified_system_integration),
        ("语言转换", test_language_conversion)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结测试结果
    print("\n" + "=" * 80)
    print("🎯 测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！集成可视化系统工作正常。")
    elif passed >= total * 0.75:
        print("⚠️  大部分测试通过，系统基本可用，建议检查失败项目。")
    else:
        print("❌ 多个测试失败，需要修复问题后重新测试。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
