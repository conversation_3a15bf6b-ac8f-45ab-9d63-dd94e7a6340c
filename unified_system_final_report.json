{"system_info": {"version": "2.0", "execution_time": 52.830708265304565, "execution_time_formatted": "0分52秒", "timestamp": "2025-06-12T19:43:06.859952", "status": "初始化"}, "data_expansion": {"status": "success", "results": {"expansion_summary": {"original_samples": 1398, "new_samples": 0, "total_samples": 1398, "expansion_rate": 0.0, "target_achieved": false}, "deduplication_info": {"deduplication_enabled": true, "duplicate_removal_methods": ["file_path", "file_hash", "feature_values"]}, "quality_assessment": {"total_samples": 1398, "total_features": 52, "missing_values": {"columns_with_missing": 0, "max_missing_rate": 0.0, "avg_missing_rate": 0.0}, "target_variables": {"speed_kmh": {"valid_samples": 1398, "coverage": 100.0, "min_value": 40.0, "max_value": 100.0, "mean_value": 55.46137339055794}, "axle_type": {"valid_samples": 0, "coverage": 0.0, "min_value": null, "max_value": null, "mean_value": null}}, "data_distribution": {}, "quality_score": 60.383333333333326}, "processing_log": [], "timestamp": "2025-06-12T19:42:49.020057"}, "summary": {"original_samples": 1398, "final_samples": 1398, "expansion_rate": 0.0, "quality_score": 60.383333333333326, "deduplication_enabled": true}}, "machine_learning": {"status": "failed", "error": "模型训练失败: 'load_tons'", "summary": {"models_trained": 0, "speed_r2": 0.0, "load_r2": 0.0, "axle_accuracy": 0.0, "all_targets_achieved": false}}, "visualization": {"status": "success", "summary": {"output_directory": "academic_visualizations/", "resolution": "330 DPI", "languages": ["中文", "英文"], "chart_count": 8}}, "technical_workflow_visualization": {"status": "success", "results": {"generation_timestamp": "2025-06-12T19:43:06.852616", "total_charts": 10, "output_directory": "technical_visualizations", "chart_categories": {"System Overview": ["technical_visualizations\\system_overview\\system_overview_diagram.png"], "Workflow Diagrams": ["technical_visualizations\\workflow_diagrams\\data_processing_pipeline.png", "technical_visualizations\\workflow_diagrams\\comprehensive_workflow.png"], "Signal Analysis": ["technical_visualizations\\signal_plots\\sample_vibration_signals.png", "technical_visualizations\\signal_plots\\multi_sensor_comparison.png", "technical_visualizations\\signal_preprocessing\\signal_preprocessing_demo.png", "technical_visualizations\\signal_preprocessing\\filtering_comparison.png"], "Feature Extraction": ["technical_visualizations\\feature_extraction\\time_domain_features.png", "technical_visualizations\\feature_extraction\\frequency_domain_features.png", "technical_visualizations\\feature_extraction\\time_frequency_features.png"], "Signal Preprocessing": []}, "chart_registry": {"System Overview": "technical_visualizations\\system_overview\\system_overview_diagram.png", "Data Processing Pipeline": "technical_visualizations\\workflow_diagrams\\data_processing_pipeline.png", "Sample Vibration Signals": "technical_visualizations\\signal_plots\\sample_vibration_signals.png", "Multi-Sensor Comparison": "technical_visualizations\\signal_plots\\multi_sensor_comparison.png", "Time-Domain Features": "technical_visualizations\\feature_extraction\\time_domain_features.png", "Frequency-Domain Features": "technical_visualizations\\feature_extraction\\frequency_domain_features.png", "Time-Frequency Features": "technical_visualizations\\feature_extraction\\time_frequency_features.png", "Signal Preprocessing": "technical_visualizations\\signal_preprocessing\\signal_preprocessing_demo.png", "Filtering Comparison": "technical_visualizations\\signal_preprocessing\\filtering_comparison.png", "Comprehensive Workflow": "technical_visualizations\\workflow_diagrams\\comprehensive_workflow.png"}, "technical_specifications": {"resolution": "330 DPI", "font_family": "Times New Roman", "format": "PNG", "color_scheme": "Academic/IEEE Standard", "title_size": "16pt", "label_size": "14pt", "tick_size": "12pt"}}, "summary": {"charts_generated": 10, "output_directory": "technical_visualizations", "resolution": "330 DPI", "font_family": "Times New Roman", "categories": 5}}, "performance_summary": {"data_quality_score": 60.383333333333326, "deduplication_enabled": true, "final_sample_count": 1398, "models_trained": 0, "best_speed_r2": 0.0, "best_load_r2": 0.0, "best_axle_accuracy": 0.0, "all_ml_targets_achieved": false, "visualization_charts": 8, "technical_charts": 10}, "deliverables": {"enhanced_dataset": "combined_features.csv", "processing_history": "processed_files.json", "trained_models": "ml_models/", "visualizations": "academic_visualizations/", "technical_visualizations": "technical_visualizations", "reports": ["enhanced_data_expansion_report.json", "ml_training_report_optimized.json", "unified_system_final_report.json"]}, "error_log": ["模型训练失败: 'load_tons'"], "technical_features": {"data_deduplication": "✅ 支持文件哈希和特征值去重", "incremental_updates": "✅ 支持增量数据更新", "processing_history": "✅ 完整的文件处理历史记录", "error_handling": "✅ 全面的错误处理和恢复机制", "backward_compatibility": "✅ 与现有功能完全兼容"}}