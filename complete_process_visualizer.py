#!/usr/bin/env python3
"""
完整的数据处理流程可视化管理器
统一管理所有流程可视化功能，生成学术论文级别的完整图表集
"""

import numpy as np
import pandas as pd
import os
from process_visualization import ProcessVisualization
from advanced_process_visualization import AdvancedProcessVisualization
from documentation_generator import DocumentationGenerator

class CompleteProcessVisualizer:
    """完整的数据处理流程可视化管理器"""
    
    def __init__(self, output_dir='process_visualization'):
        """初始化完整可视化管理器"""
        self.output_dir = output_dir
        self.basic_viz = ProcessVisualization(output_dir)
        self.advanced_viz = AdvancedProcessVisualization(output_dir)
        self.doc_gen = DocumentationGenerator(output_dir)
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
    def generate_complete_visualization_suite(self, data_dict=None):
        """生成完整的可视化图表套件"""
        print("🎨 开始生成完整的数据处理流程可视化图表套件...")
        print("=" * 80)
        
        try:
            # 1. 基础流程可视化
            print("\n📊 第一阶段：基础数据处理流程可视化")
            self._generate_basic_process_visualizations(data_dict)
            
            # 2. 高级分析可视化
            print("\n📊 第二阶段：高级分析可视化")
            self._generate_advanced_visualizations(data_dict)
            
            # 3. 生成文档
            print("\n📚 第三阶段：生成配套文档")
            self._generate_documentation()
            
            # 4. 生成总结报告
            print("\n📋 第四阶段：生成总结报告")
            self._generate_summary_report()
            
            print("\n🎉 完整可视化图表套件生成完成!")
            self._print_final_summary()
            
        except Exception as e:
            print(f"❌ 生成可视化图表套件失败: {str(e)}")
    
    def _generate_basic_process_visualizations(self, data_dict):
        """生成基础流程可视化"""
        print("  🔧 生成基础数据处理流程图表...")
        
        # 原始信号可视化
        signal_data = data_dict.get('raw_signals') if data_dict else None
        self.basic_viz.create_raw_signal_visualization(signal_data)
        
        # 事件检测可视化
        event_data = data_dict.get('event_signal') if data_dict else None
        self.basic_viz.create_event_detection_visualization(event_data)
        
        # 特征提取可视化
        feature_data = data_dict.get('signal_segment') if data_dict else None
        self.basic_viz.create_feature_extraction_visualization(feature_data)
        
        # 数据预处理可视化
        raw_features = data_dict.get('raw_features') if data_dict else None
        processed_features = data_dict.get('processed_features') if data_dict else None
        self.basic_viz.create_preprocessing_visualization(raw_features, processed_features)
        
        # 模型训练可视化
        training_history = data_dict.get('training_history') if data_dict else None
        self.basic_viz.create_training_process_visualization(training_history)
        
        # 预测结果可视化
        y_true = data_dict.get('y_true') if data_dict else None
        predictions = data_dict.get('predictions') if data_dict else None
        self.basic_viz.create_prediction_results_visualization(y_true, predictions)
        
        print("  ✅ 基础流程可视化完成")
    
    def _generate_advanced_visualizations(self, data_dict):
        """生成高级分析可视化"""
        print("  🔬 生成高级分析图表...")
        
        # 时频分析
        signal_data = data_dict.get('time_freq_signal') if data_dict else None
        self.advanced_viz.create_time_frequency_analysis(signal_data)
        
        # 传感器融合
        sensor_data = data_dict.get('multi_sensor_data') if data_dict else None
        self.advanced_viz.create_sensor_fusion_visualization(sensor_data)
        
        # 算法性能雷达图
        performance_data = data_dict.get('algorithm_performance') if data_dict else None
        self.advanced_viz.create_algorithm_performance_radar(performance_data)
        
        print("  ✅ 高级分析可视化完成")
    
    def _generate_documentation(self):
        """生成配套文档"""
        print("  📚 生成配套文档...")
        
        self.doc_gen.generate_all_documentation()
        
        print("  ✅ 配套文档生成完成")
    
    def _generate_summary_report(self):
        """生成总结报告"""
        print("  📋 生成总结报告...")
        
        # 统计生成的图表
        chart_stats = self._count_generated_charts()
        
        # 生成中英文总结报告
        for language in ['chinese', 'english']:
            content = self._create_summary_content(chart_stats, language)
            
            filename = 'visualization_summary_chinese.md' if language == 'chinese' else 'visualization_summary_english.md'
            filepath = os.path.join(self.output_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"  ✅ {language.upper()}版总结报告已保存: {filepath}")
    
    def _count_generated_charts(self):
        """统计生成的图表"""
        stats = {'chinese': 0, 'english': 0, 'total': 0}
        
        for language in ['chinese', 'english']:
            lang_dir = os.path.join(self.output_dir, language)
            if os.path.exists(lang_dir):
                png_files = [f for f in os.listdir(lang_dir) if f.endswith('.png')]
                stats[language] = len(png_files)
                stats['total'] += len(png_files)
        
        return stats
    
    def _create_summary_content(self, chart_stats, language):
        """创建总结报告内容"""
        from datetime import datetime
        
        content = []
        
        if language == 'chinese':
            content.append("# 振动信号分析系统 - 数据处理流程可视化总结报告")
            content.append("=" * 80)
            content.append("")
            content.append(f"**生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
            content.append(f"**系统版本**: 振动信号分析系统 v3.0")
            content.append("")
            
            content.append("## 📊 可视化成果概览")
            content.append("")
            content.append(f"本次生成了完整的数据处理流程可视化图表套件，包含：")
            content.append("")
            content.append(f"- **中文版图表**: {chart_stats['chinese']} 个")
            content.append(f"- **英文版图表**: {chart_stats['english']} 个")
            content.append(f"- **图表总数**: {chart_stats['total']} 个")
            content.append("")
            
            content.append("## 🎯 图表类别")
            content.append("")
            content.append("### 基础数据处理流程图表")
            content.append("1. **原始振动信号时域波形图** - 展示多传感器原始数据")
            content.append("2. **车辆通过事件检测图** - 最大值检测和数据截取")
            content.append("3. **特征提取结果展示图** - 时域和频域特征分析")
            content.append("4. **数据预处理效果图** - 标准化前后对比")
            content.append("5. **模型训练过程图** - 收敛分析和学习曲线")
            content.append("6. **最终预测结果图** - 多算法性能对比")
            content.append("")
            
            content.append("### 高级分析图表")
            content.append("1. **时频分析图** - 小波变换和STFT分析")
            content.append("2. **传感器融合图** - 多传感器数据融合过程")
            content.append("3. **算法性能雷达图** - 多维度性能对比")
            content.append("")
            
            content.append("## 📚 配套文档")
            content.append("")
            content.append("- **图表索引文档** (中英文版)")
            content.append("- **LaTeX引用代码** (中英文版)")
            content.append("- **图表质量检查清单** (中英文版)")
            content.append("")
            
            content.append("## 🎨 技术标准")
            content.append("")
            content.append("所有图表均符合IEEE/Elsevier期刊标准：")
            content.append("")
            content.append("- **分辨率**: 300 DPI高分辨率")
            content.append("- **字体规范**: 标题16pt，轴标签14pt，刻度12pt")
            content.append("- **颜色方案**: 专业学术配色，黑白打印友好")
            content.append("- **图表尺寸**: 符合期刊单栏/双栏要求")
            content.append("- **文件格式**: PNG格式，适合论文插图")
            content.append("")
            
            content.append("## 💡 使用建议")
            content.append("")
            content.append("1. **学术论文**: 可直接用于中英文学术论文")
            content.append("2. **技术报告**: 适合技术文档和研究报告")
            content.append("3. **演示文稿**: 高质量图表适合学术演示")
            content.append("4. **专利申请**: 可用于技术专利的图表说明")
            content.append("")
            
        else:
            content.append("# Vibration Signal Analysis System - Data Processing Flow Visualization Summary Report")
            content.append("=" * 80)
            content.append("")
            content.append(f"**Generated Time**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            content.append(f"**System Version**: Vibration Signal Analysis System v3.0")
            content.append("")
            
            content.append("## 📊 Visualization Results Overview")
            content.append("")
            content.append(f"A complete data processing flow visualization chart suite has been generated, including:")
            content.append("")
            content.append(f"- **Chinese Charts**: {chart_stats['chinese']} charts")
            content.append(f"- **English Charts**: {chart_stats['english']} charts")
            content.append(f"- **Total Charts**: {chart_stats['total']} charts")
            content.append("")
            
            content.append("## 🎯 Chart Categories")
            content.append("")
            content.append("### Basic Data Processing Flow Charts")
            content.append("1. **Raw Vibration Signal Waveforms** - Multi-sensor raw data display")
            content.append("2. **Vehicle Passing Event Detection** - Maximum detection and data segmentation")
            content.append("3. **Feature Extraction Results** - Time and frequency domain feature analysis")
            content.append("4. **Data Preprocessing Effects** - Before and after standardization comparison")
            content.append("5. **Model Training Process** - Convergence analysis and learning curves")
            content.append("6. **Final Prediction Results** - Multi-algorithm performance comparison")
            content.append("")
            
            content.append("### Advanced Analysis Charts")
            content.append("1. **Time-Frequency Analysis** - Wavelet transform and STFT analysis")
            content.append("2. **Sensor Fusion Visualization** - Multi-sensor data fusion process")
            content.append("3. **Algorithm Performance Radar** - Multi-dimensional performance comparison")
            content.append("")
            
            content.append("## 📚 Supporting Documentation")
            content.append("")
            content.append("- **Chart Index Documents** (Chinese and English versions)")
            content.append("- **LaTeX Reference Code** (Chinese and English versions)")
            content.append("- **Chart Quality Checklist** (Chinese and English versions)")
            content.append("")
            
            content.append("## 🎨 Technical Standards")
            content.append("")
            content.append("All charts comply with IEEE/Elsevier journal standards:")
            content.append("")
            content.append("- **Resolution**: 300 DPI high resolution")
            content.append("- **Font Standards**: Title 16pt, axis labels 14pt, ticks 12pt")
            content.append("- **Color Scheme**: Professional academic colors, black-and-white print friendly")
            content.append("- **Chart Dimensions**: Compliant with journal single/double column requirements")
            content.append("- **File Format**: PNG format, suitable for paper illustrations")
            content.append("")
            
            content.append("## 💡 Usage Recommendations")
            content.append("")
            content.append("1. **Academic Papers**: Can be directly used in Chinese and English academic papers")
            content.append("2. **Technical Reports**: Suitable for technical documentation and research reports")
            content.append("3. **Presentations**: High-quality charts suitable for academic presentations")
            content.append("4. **Patent Applications**: Can be used for technical patent illustrations")
            content.append("")
        
        return '\n'.join(content)
    
    def _print_final_summary(self):
        """打印最终总结"""
        chart_stats = self._count_generated_charts()
        
        print("=" * 80)
        print("🎉 振动信号分析系统 - 完整可视化图表套件生成完成!")
        print("=" * 80)
        print("")
        print("📊 生成统计:")
        print(f"   中文版图表: {chart_stats['chinese']} 个")
        print(f"   英文版图表: {chart_stats['english']} 个")
        print(f"   图表总数: {chart_stats['total']} 个")
        print("")
        print("📁 输出目录结构:")
        print(f"   {self.output_dir}/")
        print(f"   ├── chinese/ (中文版图表)")
        print(f"   ├── english/ (英文版图表)")
        print(f"   ├── chart_index_*.md (图表索引)")
        print(f"   ├── latex_references_*.tex (LaTeX引用)")
        print(f"   ├── quality_checklist_*.md (质量检查)")
        print(f"   └── visualization_summary_*.md (总结报告)")
        print("")
        print("🎯 应用场景:")
        print("   ✅ 学术论文插图 (IEEE/Elsevier标准)")
        print("   ✅ 技术报告配图")
        print("   ✅ 学术演示文稿")
        print("   ✅ 专利申请图表")
        print("")
        print("💡 使用提示:")
        print("   - 所有图表均为300 DPI高分辨率")
        print("   - 支持黑白打印")
        print("   - 包含完整的LaTeX引用代码")
        print("   - 提供中英文双语版本")

def main():
    """主函数 - 演示完整可视化功能"""
    print("🚀 振动信号分析系统 - 完整数据处理流程可视化演示")
    print("=" * 80)
    
    # 初始化完整可视化管理器
    visualizer = CompleteProcessVisualizer()
    
    # 生成完整的可视化图表套件
    visualizer.generate_complete_visualization_suite()

if __name__ == "__main__":
    main()
