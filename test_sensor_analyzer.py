#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script for Sensor Data Analyzer
Tests the functionality of the sensor-specific vibration data analyzer

Author: AI Assistant
Version: 1.0
Date: 2024-12-07
"""

import os
import sys
import numpy as np
import pandas as pd
from pathlib import Path
import time

def create_test_data():
    """Create synthetic test data for sensor analysis"""
    print("🔧 Creating synthetic test data...")
    
    try:
        # Parameters
        duration = 5.0  # seconds
        sampling_rate = 1000  # Hz
        n_samples = int(duration * sampling_rate)
        time_vector = np.linspace(0, duration, n_samples)
        
        # Create synthetic vibration signals for 20 sensors
        np.random.seed(42)
        
        data = {'count': range(n_samples)}
        
        for sensor_id in range(1, 21):
            # Base signal: combination of sinusoids + noise
            signal = (
                2.0 * np.sin(2 * np.pi * 10 * time_vector) +     # 10 Hz component
                1.5 * np.sin(2 * np.pi * 25 * time_vector) +     # 25 Hz component
                1.0 * np.sin(2 * np.pi * 50 * time_vector) +     # 50 Hz component
                0.5 * np.sin(2 * np.pi * 100 * time_vector) +    # 100 Hz component
                np.random.normal(0, 0.3, n_samples)              # Noise
            )
            
            # Add sensor-specific characteristics
            if sensor_id <= 5:  # Group 1: Lower frequency emphasis
                signal += 1.0 * np.sin(2 * np.pi * 5 * time_vector)
            elif sensor_id <= 10:  # Group 2: Higher frequency emphasis
                signal += 0.8 * np.sin(2 * np.pi * 80 * time_vector)
            elif sensor_id <= 15:  # Group 3: Mid frequency emphasis
                signal += 1.2 * np.sin(2 * np.pi * 30 * time_vector)
            else:  # Group 4: Mixed frequencies
                signal += 0.6 * np.sin(2 * np.pi * 15 * time_vector)
                signal += 0.4 * np.sin(2 * np.pi * 60 * time_vector)
            
            # Add some outliers
            outlier_indices = np.random.choice(n_samples, 20, replace=False)
            signal[outlier_indices] += np.random.uniform(-5, 5, 20)
            
            # Store in dataframe
            data[f'Sensor_{sensor_id:02d}'] = signal
        
        # Create DataFrame and save
        df = pd.DataFrame(data)
        test_file_path = 'test_vibration_data.csv'
        df.to_csv(test_file_path, index=False)
        
        print(f"   ✅ Test data created: {test_file_path}")
        print(f"   📊 Data shape: {df.shape}")
        print(f"   ⏱️ Duration: {duration} seconds")
        print(f"   📈 Sampling rate: {sampling_rate} Hz")
        print(f"   🔢 Sensors: {len([col for col in df.columns if 'Sensor' in col])}")
        
        return test_file_path, df
        
    except Exception as e:
        print(f"   ❌ Error creating test data: {str(e)}")
        return None, None

def test_sensor_analyzer_basic():
    """Test basic functionality of sensor analyzer"""
    print("\n🧪 Testing basic sensor analyzer functionality...")
    
    try:
        from sensor_data_analyzer import SensorDataAnalyzer
        
        # Create test data
        test_file, test_df = create_test_data()
        if test_file is None:
            return False
        
        # Initialize analyzer
        analyzer = SensorDataAnalyzer(
            output_dir='test_sensor_charts',
            file_prefix='test_'
        )
        
        # Test data loading
        print("   📂 Testing data loading...")
        success = analyzer.load_sensor_data(test_file, 'Sensor_01')
        if not success:
            print("   ❌ Data loading failed")
            return False
        print("   ✅ Data loading successful")
        
        # Test preprocessing
        print("   🔧 Testing data preprocessing...")
        success = analyzer.preprocess_data()
        if not success:
            print("   ❌ Data preprocessing failed")
            return False
        print("   ✅ Data preprocessing successful")
        
        # Test time domain analysis
        print("   📈 Testing time domain analysis...")
        success = analyzer.generate_time_domain_analysis()
        if not success:
            print("   ❌ Time domain analysis failed")
            return False
        print("   ✅ Time domain analysis successful")
        
        # Test frequency domain analysis
        print("   🔊 Testing frequency domain analysis...")
        success = analyzer.generate_frequency_domain_analysis()
        if not success:
            print("   ❌ Frequency domain analysis failed")
            return False
        print("   ✅ Frequency domain analysis successful")
        
        # Test time-frequency analysis
        print("   🌊 Testing time-frequency analysis...")
        success = analyzer.generate_time_frequency_analysis()
        if not success:
            print("   ❌ Time-frequency analysis failed")
            return False
        print("   ✅ Time-frequency analysis successful")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Basic functionality test failed: {str(e)}")
        return False

def test_sensor_analyzer_complete():
    """Test complete sensor analysis workflow"""
    print("\n🧪 Testing complete sensor analysis workflow...")
    
    try:
        from sensor_data_analyzer import SensorDataAnalyzer
        
        # Create test data
        test_file, test_df = create_test_data()
        if test_file is None:
            return False
        
        # Initialize analyzer
        analyzer = SensorDataAnalyzer(
            output_dir='test_sensor_charts',
            file_prefix='complete_test_'
        )
        
        # Test complete workflow
        print("   🚀 Running complete analysis workflow...")
        start_time = time.time()
        
        success = analyzer.analyze_sensor(
            file_path=test_file,
            sensor_id='Sensor_05',
            start_idx=1000,
            end_idx=4000,
            apply_preprocessing=True
        )
        
        end_time = time.time()
        
        if success:
            print(f"   ✅ Complete workflow successful ({end_time - start_time:.1f}s)")
            return True
        else:
            print("   ❌ Complete workflow failed")
            return False
        
    except Exception as e:
        print(f"   ❌ Complete workflow test failed: {str(e)}")
        return False

def test_multiple_sensors():
    """Test analysis of multiple sensors"""
    print("\n🧪 Testing multiple sensor analysis...")
    
    try:
        from sensor_data_analyzer import SensorDataAnalyzer
        
        # Create test data
        test_file, test_df = create_test_data()
        if test_file is None:
            return False
        
        # Test multiple sensors
        test_sensors = ['Sensor_01', 'Sensor_10', 'Sensor_20']
        success_count = 0
        
        for sensor_id in test_sensors:
            print(f"   🔍 Testing {sensor_id}...")
            
            analyzer = SensorDataAnalyzer(
                output_dir='test_sensor_charts',
                file_prefix=f'multi_{sensor_id.lower()}_'
            )
            
            success = analyzer.analyze_sensor(
                file_path=test_file,
                sensor_id=sensor_id,
                apply_preprocessing=True
            )
            
            if success:
                print(f"      ✅ {sensor_id} analysis successful")
                success_count += 1
            else:
                print(f"      ❌ {sensor_id} analysis failed")
        
        print(f"   📊 Multiple sensor test: {success_count}/{len(test_sensors)} successful")
        return success_count == len(test_sensors)
        
    except Exception as e:
        print(f"   ❌ Multiple sensor test failed: {str(e)}")
        return False

def test_output_quality():
    """Test output chart quality and naming"""
    print("\n🧪 Testing output chart quality and naming...")
    
    try:
        output_dir = Path('test_sensor_charts')
        
        if not output_dir.exists():
            print("   ❌ Output directory not found")
            return False
        
        # Check generated files
        chart_files = list(output_dir.glob('*.png'))
        
        if not chart_files:
            print("   ❌ No chart files found")
            return False
        
        print(f"   📊 Found {len(chart_files)} chart files")
        
        # Check naming convention
        naming_issues = 0
        quality_issues = 0
        
        for chart_file in chart_files:
            # Check naming convention
            if not (chart_file.name.startswith('test_') or 
                   chart_file.name.startswith('complete_test_') or
                   chart_file.name.startswith('multi_')):
                naming_issues += 1
                print(f"      ⚠️ Naming issue: {chart_file.name}")
            
            # Check file size (should be reasonable for 330 DPI)
            file_size = chart_file.stat().st_size
            if file_size < 50 * 1024:  # Less than 50KB might be too small
                quality_issues += 1
                print(f"      ⚠️ Small file size: {chart_file.name} ({file_size/1024:.1f}KB)")
        
        print(f"   📋 Naming convention: {len(chart_files) - naming_issues}/{len(chart_files)} correct")
        print(f"   📏 File quality: {len(chart_files) - quality_issues}/{len(chart_files)} good size")
        
        # List some example files
        print("   📁 Example generated files:")
        for i, chart_file in enumerate(chart_files[:5]):
            print(f"      {i+1}. {chart_file.name}")
        if len(chart_files) > 5:
            print(f"      ... and {len(chart_files) - 5} more files")
        
        success_rate = (len(chart_files) - naming_issues - quality_issues) / len(chart_files)
        return success_rate >= 0.8  # 80% success rate
        
    except Exception as e:
        print(f"   ❌ Output quality test failed: {str(e)}")
        return False

def cleanup_test_files():
    """Clean up test files"""
    print("\n🧹 Cleaning up test files...")
    
    try:
        # Remove test data file
        test_file = 'test_vibration_data.csv'
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"   ✅ Removed: {test_file}")
        
        # Remove test chart directory
        import shutil
        test_dir = Path('test_sensor_charts')
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"   ✅ Removed: {test_dir}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Cleanup failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Sensor Data Analyzer Tests")
    print("=" * 80)
    
    # Run all tests
    tests = [
        ("Basic Functionality", test_sensor_analyzer_basic),
        ("Complete Workflow", test_sensor_analyzer_complete),
        ("Multiple Sensors", test_multiple_sensors),
        ("Output Quality", test_output_quality)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 Sensor Data Analyzer Test Results")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Sensor Data Analyzer is working correctly.")
    elif passed >= total * 0.75:
        print("⚠️  Most tests passed. System is functional with minor issues.")
    else:
        print("❌ Multiple test failures. Please check the implementation.")
    
    # Cleanup
    cleanup_test_files()
    
    return passed >= total * 0.75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
