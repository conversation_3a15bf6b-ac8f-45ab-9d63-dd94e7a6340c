#!/usr/bin/env python3
"""
传感器数据质量分析模块
针对20个加速度传感器的布设方案进行数据质量分析和优化
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.signal import correlate
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.feature_selection import mutual_info_regression, f_regression
import os
import warnings
warnings.filterwarnings('ignore')

class SensorAnalysis:
    """传感器数据质量分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.sensor_config = self._initialize_sensor_config()
        self.data_quality_results = {}
        self.correlation_results = {}
        self.feature_importance_results = {}
        
    def _initialize_sensor_config(self):
        """初始化传感器配置信息"""
        return {
            'group_1': {
                'sensors': ['sensor_01', 'sensor_02', 'sensor_03', 'sensor_04', 'sensor_05'],
                'depth': 5.0,  # cm
                'location': 'main_lane',
                'description': '第1组：埋深5cm，主车道'
            },
            'group_2': {
                'sensors': ['sensor_06', 'sensor_07', 'sensor_08', 'sensor_09', 'sensor_10'],
                'depth': 5.0,  # cm
                'location': 'mixed',  # sensor_06在超车道，其他在主车道
                'description': '第2组：埋深5cm，sensor_06位于超车道',
                'special_sensor': 'sensor_06'
            },
            'group_3': {
                'sensors': ['sensor_11', 'sensor_12', 'sensor_13', 'sensor_14', 'sensor_15'],
                'depth': 3.5,  # cm
                'location': 'main_lane',
                'description': '第3组：埋深3.5cm，主车道'
            },
            'group_4': {
                'sensors': ['sensor_16', 'sensor_17', 'sensor_18', 'sensor_19', 'sensor_20'],
                'depth': 3.5,  # cm
                'location': 'mixed',  # sensor_16在超车道，其他在主车道
                'description': '第4组：埋深3.5cm，sensor_16位于超车道',
                'special_sensor': 'sensor_16'
            }
        }
    
    def analyze_data_quality(self, data_df):
        """分析传感器数据质量"""
        print("📊 开始传感器数据质量分析...")
        
        # 获取传感器列
        sensor_columns = [col for col in data_df.columns if 'sensor' in col.lower()]
        
        if len(sensor_columns) == 0:
            print("❌ 未找到传感器数据列")
            return None
        
        print(f"   发现 {len(sensor_columns)} 个传感器数据列")
        
        quality_results = {}
        
        for sensor in sensor_columns:
            if sensor in data_df.columns:
                sensor_data = data_df[sensor].dropna()
                
                quality_metrics = {
                    'mean': np.mean(sensor_data),
                    'std': np.std(sensor_data),
                    'min': np.min(sensor_data),
                    'max': np.max(sensor_data),
                    'range': np.max(sensor_data) - np.min(sensor_data),
                    'skewness': stats.skew(sensor_data),
                    'kurtosis': stats.kurtosis(sensor_data),
                    'missing_ratio': data_df[sensor].isnull().sum() / len(data_df),
                    'zero_ratio': (sensor_data == 0).sum() / len(sensor_data),
                    'outlier_ratio': self._calculate_outlier_ratio(sensor_data),
                    'signal_to_noise_ratio': self._calculate_snr(sensor_data)
                }
                
                quality_results[sensor] = quality_metrics
        
        self.data_quality_results = quality_results
        
        # 分析特殊传感器
        self._analyze_special_sensors(quality_results)
        
        print("✅ 传感器数据质量分析完成")
        return quality_results
    
    def _calculate_outlier_ratio(self, data):
        """计算异常值比例（使用IQR方法）"""
        Q1 = np.percentile(data, 25)
        Q3 = np.percentile(data, 75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = (data < lower_bound) | (data > upper_bound)
        return np.sum(outliers) / len(data)
    
    def _calculate_snr(self, data):
        """计算信噪比"""
        signal_power = np.mean(data**2)
        noise_power = np.var(data - np.mean(data))
        if noise_power > 0:
            return 10 * np.log10(signal_power / noise_power)
        else:
            return float('inf')
    
    def _analyze_special_sensors(self, quality_results):
        """分析特殊位置传感器（sensor_06和sensor_16）"""
        special_sensors = ['sensor_06', 'sensor_16']
        main_lane_sensors = []
        
        # 收集主车道传感器
        for group_info in self.sensor_config.values():
            for sensor in group_info['sensors']:
                if sensor not in special_sensors and sensor in quality_results:
                    main_lane_sensors.append(sensor)
        
        print(f"\n🔍 特殊传感器分析:")
        print(f"   特殊传感器: {special_sensors} (位于超车道)")
        print(f"   主车道传感器: {len(main_lane_sensors)} 个")
        
        # 比较特殊传感器与主车道传感器的数据质量
        for special_sensor in special_sensors:
            if special_sensor in quality_results:
                special_metrics = quality_results[special_sensor]
                
                # 计算主车道传感器的平均指标
                main_lane_metrics = {}
                for metric in special_metrics.keys():
                    values = [quality_results[sensor][metric] for sensor in main_lane_sensors 
                             if sensor in quality_results]
                    if values:
                        main_lane_metrics[metric] = np.mean(values)
                
                print(f"\n   {special_sensor} vs 主车道平均:")
                print(f"     信噪比: {special_metrics['signal_to_noise_ratio']:.2f} vs {main_lane_metrics.get('signal_to_noise_ratio', 0):.2f}")
                print(f"     异常值比例: {special_metrics['outlier_ratio']:.3f} vs {main_lane_metrics.get('outlier_ratio', 0):.3f}")
                print(f"     标准差: {special_metrics['std']:.4f} vs {main_lane_metrics.get('std', 0):.4f}")
    
    def analyze_sensor_correlations(self, data_df, target_column=None):
        """分析传感器间相关性"""
        print("\n📈 开始传感器相关性分析...")
        
        sensor_columns = [col for col in data_df.columns if 'sensor' in col.lower()]
        
        if len(sensor_columns) < 2:
            print("❌ 传感器数据不足，无法进行相关性分析")
            return None
        
        # 计算传感器间相关性矩阵
        sensor_data = data_df[sensor_columns].dropna()
        correlation_matrix = sensor_data.corr()
        
        # 分析特殊传感器的相关性
        special_sensors = ['sensor_06', 'sensor_16']
        correlation_analysis = {}
        
        for special_sensor in special_sensors:
            if special_sensor in sensor_columns:
                # 与其他传感器的相关性
                correlations = correlation_matrix[special_sensor].drop(special_sensor)
                
                correlation_analysis[special_sensor] = {
                    'mean_correlation': np.mean(np.abs(correlations)),
                    'max_correlation': np.max(np.abs(correlations)),
                    'min_correlation': np.min(np.abs(correlations)),
                    'high_correlation_count': np.sum(np.abs(correlations) > 0.7),
                    'low_correlation_count': np.sum(np.abs(correlations) < 0.3)
                }
        
        # 如果有目标变量，分析与目标的相关性
        if target_column and target_column in data_df.columns:
            target_correlations = {}
            for sensor in sensor_columns:
                if sensor in data_df.columns:
                    corr = data_df[sensor].corr(data_df[target_column])
                    target_correlations[sensor] = corr
            
            correlation_analysis['target_correlations'] = target_correlations
        
        self.correlation_results = {
            'correlation_matrix': correlation_matrix,
            'analysis': correlation_analysis
        }
        
        print("✅ 传感器相关性分析完成")
        return correlation_analysis
    
    def analyze_feature_importance(self, data_df, target_column):
        """分析传感器特征重要性"""
        print("\n🎯 开始特征重要性分析...")
        
        sensor_columns = [col for col in data_df.columns if 'sensor' in col.lower()]
        
        if target_column not in data_df.columns:
            print(f"❌ 目标列 {target_column} 不存在")
            return None
        
        # 准备数据
        X = data_df[sensor_columns].dropna()
        y = data_df.loc[X.index, target_column]
        
        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 计算不同的特征重要性指标
        importance_results = {}
        
        # 1. F检验
        f_scores, f_pvalues = f_regression(X_scaled, y)
        importance_results['f_test'] = dict(zip(sensor_columns, f_scores))
        importance_results['f_pvalues'] = dict(zip(sensor_columns, f_pvalues))
        
        # 2. 互信息
        mi_scores = mutual_info_regression(X_scaled, y, random_state=42)
        importance_results['mutual_info'] = dict(zip(sensor_columns, mi_scores))
        
        # 3. 相关系数
        correlations = [np.abs(np.corrcoef(X_scaled[:, i], y)[0, 1]) for i in range(X_scaled.shape[1])]
        importance_results['correlation'] = dict(zip(sensor_columns, correlations))
        
        # 分析特殊传感器的重要性
        special_sensors = ['sensor_06', 'sensor_16']
        special_analysis = {}
        
        for special_sensor in special_sensors:
            if special_sensor in sensor_columns:
                idx = sensor_columns.index(special_sensor)
                special_analysis[special_sensor] = {
                    'f_score': f_scores[idx],
                    'f_pvalue': f_pvalues[idx],
                    'mutual_info': mi_scores[idx],
                    'correlation': correlations[idx]
                }
        
        importance_results['special_sensor_analysis'] = special_analysis
        
        self.feature_importance_results = importance_results
        
        print("✅ 特征重要性分析完成")
        return importance_results
    
    def recommend_sensor_selection(self):
        """推荐传感器选择策略"""
        print("\n💡 生成传感器选择建议...")
        
        recommendations = {
            'include_special_sensors': True,
            'reasoning': [],
            'alternative_strategies': []
        }
        
        # 基于数据质量分析
        if self.data_quality_results:
            special_sensors = ['sensor_06', 'sensor_16']
            quality_issues = []
            
            for sensor in special_sensors:
                if sensor in self.data_quality_results:
                    metrics = self.data_quality_results[sensor]
                    
                    if metrics['outlier_ratio'] > 0.1:
                        quality_issues.append(f"{sensor}: 异常值比例过高 ({metrics['outlier_ratio']:.3f})")
                    
                    if metrics['signal_to_noise_ratio'] < 10:
                        quality_issues.append(f"{sensor}: 信噪比较低 ({metrics['signal_to_noise_ratio']:.2f})")
                    
                    if metrics['missing_ratio'] > 0.05:
                        quality_issues.append(f"{sensor}: 缺失值比例过高 ({metrics['missing_ratio']:.3f})")
            
            if quality_issues:
                recommendations['reasoning'].extend(quality_issues)
                recommendations['include_special_sensors'] = False
        
        # 基于相关性分析
        if self.correlation_results:
            analysis = self.correlation_results['analysis']
            
            for sensor in ['sensor_06', 'sensor_16']:
                if sensor in analysis:
                    metrics = analysis[sensor]
                    
                    if metrics['mean_correlation'] < 0.3:
                        recommendations['reasoning'].append(
                            f"{sensor}: 与其他传感器相关性较低 (平均相关性: {metrics['mean_correlation']:.3f})"
                        )
                        recommendations['include_special_sensors'] = False
        
        # 基于特征重要性分析
        if self.feature_importance_results:
            special_analysis = self.feature_importance_results.get('special_sensor_analysis', {})
            
            for sensor, metrics in special_analysis.items():
                if metrics['f_pvalue'] > 0.05:
                    recommendations['reasoning'].append(
                        f"{sensor}: F检验p值过高 ({metrics['f_pvalue']:.4f})，统计显著性不足"
                    )
                    recommendations['include_special_sensors'] = False
                
                if metrics['mutual_info'] < 0.1:
                    recommendations['reasoning'].append(
                        f"{sensor}: 互信息分数较低 ({metrics['mutual_info']:.4f})"
                    )
        
        # 生成替代策略
        recommendations['alternative_strategies'] = [
            "策略1: 完全排除sensor_06和sensor_16",
            "策略2: 对特殊传感器数据进行额外预处理（如滤波、异常值处理）",
            "策略3: 使用加权方法降低特殊传感器的影响",
            "策略4: 创建组合特征，将特殊传感器与同组其他传感器结合"
        ]
        
        return recommendations

def main():
    """测试函数"""
    print("🧪 测试传感器数据质量分析...")
    
    # 创建模拟数据
    np.random.seed(42)
    n_samples = 1000
    
    # 模拟20个传感器数据
    sensor_data = {}
    for i in range(1, 21):
        sensor_name = f'sensor_{i:02d}'
        
        # 主车道传感器（正常信号）
        if sensor_name not in ['sensor_06', 'sensor_16']:
            signal = np.random.normal(0, 1, n_samples)
            # 添加一些车辆通过事件
            for j in range(0, n_samples, 200):
                if j + 50 < n_samples:
                    signal[j:j+50] += np.random.normal(2, 0.5, 50)
        else:
            # 特殊传感器（可能有不同的信号特征）
            signal = np.random.normal(0, 1.2, n_samples)  # 更高的噪声
            # 较少的车辆通过事件
            for j in range(0, n_samples, 300):
                if j + 30 < n_samples:
                    signal[j:j+30] += np.random.normal(1.5, 0.8, 30)
        
        sensor_data[sensor_name] = signal
    
    # 添加目标变量（速度）
    sensor_data['speed_kmh'] = np.random.uniform(20, 80, n_samples)
    
    # 创建DataFrame
    data_df = pd.DataFrame(sensor_data)
    
    # 初始化分析器
    analyzer = SensorAnalysis()
    
    # 进行分析
    quality_results = analyzer.analyze_data_quality(data_df)
    correlation_results = analyzer.analyze_sensor_correlations(data_df, 'speed_kmh')
    importance_results = analyzer.analyze_feature_importance(data_df, 'speed_kmh')
    
    # 生成建议
    recommendations = analyzer.recommend_sensor_selection()
    
    print(f"\n📋 传感器选择建议:")
    print(f"   包含特殊传感器: {recommendations['include_special_sensors']}")
    print(f"   理由:")
    for reason in recommendations['reasoning']:
        print(f"     - {reason}")
    
    print("✅ 测试完成!")

if __name__ == "__main__":
    main()
