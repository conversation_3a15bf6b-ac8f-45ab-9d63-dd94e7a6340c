#!/usr/bin/env python3
"""
依赖包安装验证脚本
快速检查所有必要包是否正确安装
"""

import sys
import importlib
import platform
from typing import Dict, List, Tuple

def check_python_version() -> bool:
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要3.8+")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_package_installation() -> Dict[str, Tuple[bool, str]]:
    """检查包安装情况"""
    print("\n🔍 检查依赖包安装情况...")
    
    # 按功能分组的包列表
    packages = {
        # 核心数据处理
        'numpy': 'numpy',
        'pandas': 'pandas',
        'scipy': 'scipy',
        
        # 机器学习
        'scikit-learn': 'sklearn',
        'joblib': 'joblib',
        
        # 深度学习
        'torch': 'torch',
        'torchvision': 'torchvision',
        'torchaudio': 'torchaudio',
        
        # 梯度提升
        'xgboost': 'xgboost',
        'lightgbm': 'lightgbm',
        'optuna': 'optuna',
        
        # 可视化
        'matplotlib': 'matplotlib',
        'seaborn': 'seaborn',
        'plotly': 'plotly',
        
        # 工具包
        'chardet': 'chardet',
        'tqdm': 'tqdm',
        'psutil': 'psutil',
        'openpyxl': 'openpyxl',
        
        # 信号处理
        'PyWavelets': 'pywt',
        'statsmodels': 'statsmodels'
    }
    
    results = {}
    
    for display_name, import_name in packages.items():
        try:
            module = importlib.import_module(import_name)
            version = getattr(module, '__version__', 'unknown')
            print(f"  ✅ {display_name}: {version}")
            results[display_name] = (True, version)
        except ImportError as e:
            print(f"  ❌ {display_name}: 未安装")
            results[display_name] = (False, str(e))
        except Exception as e:
            print(f"  ⚠️  {display_name}: 导入错误 - {str(e)}")
            results[display_name] = (False, str(e))
    
    return results

def check_gpu_support() -> bool:
    """检查GPU支持"""
    print("\n🚀 检查GPU支持...")
    
    try:
        import torch
        
        print(f"PyTorch版本: {torch.__version__}")
        
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            print(f"✅ CUDA可用")
            print(f"GPU数量: {gpu_count}")
            
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
            
            # 测试GPU计算
            try:
                x = torch.randn(100, 100).cuda()
                y = torch.mm(x, x.t())
                print("✅ GPU计算测试通过")
                return True
            except Exception as e:
                print(f"❌ GPU计算测试失败: {str(e)}")
                return False
        else:
            print("❌ CUDA不可用，将使用CPU")
            return False
            
    except ImportError:
        print("❌ PyTorch未安装")
        return False
    except Exception as e:
        print(f"❌ GPU检查失败: {str(e)}")
        return False

def check_system_requirements() -> Dict[str, bool]:
    """检查系统要求"""
    print("\n💻 检查系统要求...")
    
    results = {}
    
    # 操作系统
    os_name = platform.system()
    print(f"操作系统: {os_name}")
    results['os_compatible'] = os_name in ['Windows', 'Linux', 'Darwin']
    
    # 内存检查
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / 1024**3
        print(f"系统内存: {memory_gb:.1f} GB")
        results['memory_sufficient'] = memory_gb >= 8
        
        if memory_gb >= 16:
            print("✅ 内存充足 (推荐)")
        elif memory_gb >= 8:
            print("✅ 内存满足最低要求")
        else:
            print("⚠️  内存可能不足，推荐16GB+")
    except:
        print("⚠️  无法检查内存")
        results['memory_sufficient'] = True  # 假设满足
    
    # 磁盘空间检查
    try:
        import shutil
        disk_space_gb = shutil.disk_usage('.').free / 1024**3
        print(f"可用磁盘空间: {disk_space_gb:.1f} GB")
        results['disk_space_sufficient'] = disk_space_gb >= 5
        
        if disk_space_gb >= 10:
            print("✅ 磁盘空间充足")
        elif disk_space_gb >= 5:
            print("✅ 磁盘空间满足要求")
        else:
            print("⚠️  磁盘空间可能不足")
    except:
        print("⚠️  无法检查磁盘空间")
        results['disk_space_sufficient'] = True  # 假设满足
    
    return results

def test_core_functionality() -> bool:
    """测试核心功能"""
    print("\n🧪 测试核心功能...")
    
    try:
        # 测试数据处理
        print("  测试数据处理...")
        import numpy as np
        import pandas as pd
        
        data = np.random.randn(100, 5)
        df = pd.DataFrame(data, columns=['A', 'B', 'C', 'D', 'E'])
        print("  ✅ 数据处理功能正常")
        
        # 测试机器学习
        print("  测试机器学习...")
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.model_selection import train_test_split
        
        X = data[:, :-1]
        y = data[:, -1]
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2)
        
        model = RandomForestRegressor(n_estimators=10)
        model.fit(X_train, y_train)
        score = model.score(X_test, y_test)
        print(f"  ✅ 机器学习功能正常 (R² = {score:.3f})")
        
        # 测试深度学习
        print("  测试深度学习...")
        import torch
        import torch.nn as nn
        
        model = nn.Sequential(
            nn.Linear(4, 10),
            nn.ReLU(),
            nn.Linear(10, 1)
        )
        
        x = torch.randn(10, 4)
        y = model(x)
        print("  ✅ 深度学习功能正常")
        
        # 测试优化
        print("  测试贝叶斯优化...")
        import optuna
        
        def objective(trial):
            x = trial.suggest_float('x', -10, 10)
            return (x - 2) ** 2
        
        study = optuna.create_study()
        study.optimize(objective, n_trials=5, show_progress_bar=False)
        print("  ✅ 贝叶斯优化功能正常")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 功能测试失败: {str(e)}")
        return False

def generate_verification_report(package_results: Dict, gpu_support: bool, 
                               system_results: Dict, functionality_test: bool):
    """生成验证报告"""
    print("\n📊 生成验证报告...")
    
    report_file = "verification_report.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("深度学习增强振动信号分析系统 - 安装验证报告\n")
        f.write("=" * 60 + "\n")
        f.write(f"验证时间: {platform.uname().node} - {sys.version}\n\n")
        
        # 包安装情况
        f.write("📦 依赖包安装情况:\n")
        f.write("-" * 30 + "\n")
        
        installed_count = 0
        total_count = len(package_results)
        
        for package, (installed, version) in package_results.items():
            status = "✅ 已安装" if installed else "❌ 未安装"
            f.write(f"{package}: {status}")
            if installed and version != 'unknown':
                f.write(f" (v{version})")
            f.write("\n")
            
            if installed:
                installed_count += 1
        
        f.write(f"\n安装成功率: {installed_count}/{total_count} ({installed_count/total_count*100:.1f}%)\n")
        
        # GPU支持
        f.write(f"\n🚀 GPU支持: {'✅ 可用' if gpu_support else '❌ 不可用'}\n")
        
        # 系统要求
        f.write(f"\n💻 系统要求:\n")
        f.write("-" * 30 + "\n")
        for requirement, met in system_results.items():
            status = "✅ 满足" if met else "❌ 不满足"
            f.write(f"{requirement}: {status}\n")
        
        # 功能测试
        f.write(f"\n🧪 功能测试: {'✅ 通过' if functionality_test else '❌ 失败'}\n")
        
        # 总体评估
        f.write(f"\n📋 总体评估:\n")
        f.write("-" * 30 + "\n")
        
        if (installed_count >= total_count * 0.8 and 
            all(system_results.values()) and 
            functionality_test):
            f.write("🎉 系统完全就绪，可以开始使用!\n")
        elif installed_count >= total_count * 0.6:
            f.write("⚠️  系统基本可用，但建议解决缺失的包\n")
        else:
            f.write("❌ 系统未就绪，请重新安装依赖包\n")
    
    print(f"✅ 验证报告已保存: {report_file}")

def main():
    """主函数"""
    print("🔍 深度学习增强振动信号分析系统 - 安装验证")
    print("=" * 60)
    
    # 1. 检查Python版本
    if not check_python_version():
        return
    
    # 2. 检查包安装
    package_results = check_package_installation()
    
    # 3. 检查GPU支持
    gpu_support = check_gpu_support()
    
    # 4. 检查系统要求
    system_results = check_system_requirements()
    
    # 5. 测试核心功能
    functionality_test = test_core_functionality()
    
    # 6. 生成报告
    generate_verification_report(package_results, gpu_support, system_results, functionality_test)
    
    # 7. 显示总结
    installed_count = sum(1 for installed, _ in package_results.values() if installed)
    total_count = len(package_results)
    
    print(f"\n🎉 验证完成!")
    print(f"包安装成功率: {installed_count}/{total_count} ({installed_count/total_count*100:.1f}%)")
    print(f"GPU支持: {'✅' if gpu_support else '❌'}")
    print(f"功能测试: {'✅' if functionality_test else '❌'}")
    
    if (installed_count >= total_count * 0.8 and functionality_test):
        print("\n✅ 系统已准备就绪!")
        print("可以运行: python setup_real_data_training.py")
    else:
        print("\n⚠️  系统未完全就绪，请检查缺失的包")

if __name__ == "__main__":
    main()
