{"axle_distribution": {"25.0": {"count": 347, "percentage": 24.82117310443491}, "34.98": {"count": 347, "percentage": 24.82117310443491}, "45.39": {"count": 292, "percentage": 20.88698140200286}, "55.62": {"count": 252, "percentage": 18.025751072961373}, "2.0": {"count": 160, "percentage": 11.444921316165951}}, "axle_classification_method": "基于重量的间接分类", "weight_stats": {"min": 2.0, "max": 55.62, "mean": 34.623161659513585, "std": 15.769684834807764, "median": 34.98, "unique_count": 5}, "weight_categories": {"light": {"range": [0, 5], "name": "轻型车辆", "count": 160}, "medium": {"range": [5, 20], "name": "中型车辆", "count": 0}, "heavy": {"range": [20, 50], "name": "重型车辆", "count": 986}, "super_heavy": {"range": [50, 100], "name": "超重型车辆", "count": 252}}, "speed_stats": {"min": 40.0, "max": 100.0, "mean": 55.46137339055794, "std": 13.544312442648664, "median": 50.0, "unique_count": 7}, "speed_ranges": {"low": {"range": [0, 40], "name": "低速区间", "count": 0}, "medium_low": {"range": [40, 60], "name": "中低速区间", "count": 712}, "medium_high": {"range": [60, 80], "name": "中高速区间", "count": 606}, "high": {"range": [80, 100], "name": "高速区间", "count": 40}, "very_high": {"range": [100, 120], "name": "超高速区间", "count": 40}}, "quality_scores": {"axle_score": 60.0, "weight_score": 54.333749999999995, "speed_score": 43.0, "overall_score": 52.444583333333334, "rating": "⚠️  一般"}}