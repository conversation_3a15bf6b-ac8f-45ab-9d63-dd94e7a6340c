#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型性能提升模块
实现模型训练优化、集成学习和性能评估

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import chinese_font_config  # 中文字体配置
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import cross_val_score, GridSearchCV, RandomizedSearchCV
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor, StackingRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.svm import SVR
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.feature_selection import SelectKBest, f_regression, RFE
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')

import os
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelPerformanceEnhancer:
    """模型性能提升器"""
    
    def __init__(self, output_dir: str = "performance_enhancement_results"):
        """
        初始化模型性能提升器
        
        参数:
        output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 性能目标
        self.performance_targets = {
            'speed_prediction': {'r2': 0.90, 'mae': 5.0},
            'axle_weight_prediction': {'r2': 0.85, 'mae': 2.0}
        }
        
        # 模型配置
        self.models = {
            'linear_regression': LinearRegression(),
            'ridge': Ridge(alpha=1.0),
            'lasso': Lasso(alpha=1.0),
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'gradient_boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'xgboost': xgb.XGBRegressor(n_estimators=100, random_state=42),
            'svr': SVR(kernel='rbf')
        }
        
        # 超参数搜索空间
        self.param_grids = {
            'ridge': {'alpha': [0.1, 1.0, 10.0, 100.0]},
            'lasso': {'alpha': [0.01, 0.1, 1.0, 10.0]},
            'random_forest': {
                'n_estimators': [50, 100, 200],
                'max_depth': [5, 10, 15, None],
                'min_samples_split': [2, 5, 10]
            },
            'gradient_boosting': {
                'n_estimators': [50, 100, 200],
                'learning_rate': [0.01, 0.1, 0.2],
                'max_depth': [3, 5, 7]
            },
            'xgboost': {
                'n_estimators': [50, 100, 200],
                'learning_rate': [0.01, 0.1, 0.2],
                'max_depth': [3, 5, 7],
                'subsample': [0.8, 0.9, 1.0]
            },
            'svr': {
                'C': [0.1, 1, 10, 100],
                'gamma': ['scale', 'auto', 0.001, 0.01, 0.1],
                'epsilon': [0.01, 0.1, 0.2]
            }
        }
        
        self.enhancement_results = {}
        
        print(f"🚀 模型性能提升器已初始化")
        print(f"   输出目录: {self.output_dir}")
        print(f"   可用模型: {list(self.models.keys())}")
    
    def enhanced_feature_engineering(self, X: pd.DataFrame, y: pd.Series) -> pd.DataFrame:
        """
        增强特征工程
        
        参数:
        X: 特征数据
        y: 目标变量
        
        返回:
        X_enhanced: 增强后的特征数据
        """
        try:
            print(f"🔧 执行增强特征工程...")
            
            X_enhanced = X.copy()
            
            # 1. 多项式特征
            print(f"   添加多项式特征...")
            poly_features = PolynomialFeatures(degree=2, include_bias=False, interaction_only=True)
            
            # 选择前10个最重要的特征进行多项式扩展
            if len(X.columns) > 10:
                selector = SelectKBest(score_func=f_regression, k=10)
                X_selected = selector.fit_transform(X, y)
                selected_features = X.columns[selector.get_support()]
                
                poly_data = poly_features.fit_transform(X[selected_features])
                poly_feature_names = poly_features.get_feature_names_out(selected_features)
                
                # 添加多项式特征
                for i, name in enumerate(poly_feature_names):
                    if name not in X.columns:  # 避免重复
                        X_enhanced[f'poly_{name}'] = poly_data[:, i]
            
            # 2. 统计特征
            print(f"   添加统计特征...")
            numeric_cols = X.select_dtypes(include=[np.number]).columns

            if len(numeric_cols) >= 2:
                # 特征间的比值
                for i, col1 in enumerate(numeric_cols[:5]):
                    for col2 in numeric_cols[i+1:6]:
                        if X[col2].std() > 0:
                            ratio_values = X[col1] / (X[col2] + 1e-8)
                            # 处理无穷大值
                            ratio_values = ratio_values.replace([np.inf, -np.inf], np.nan)
                            X_enhanced[f'ratio_{col1}_{col2}'] = ratio_values

                # 特征的滚动统计
                window_size = min(5, len(numeric_cols))
                for col in numeric_cols[:5]:
                    rolling_mean = X[col].rolling(window=window_size, min_periods=1).mean()
                    rolling_std = X[col].rolling(window=window_size, min_periods=1).std()

                    X_enhanced[f'rolling_mean_{col}'] = rolling_mean
                    X_enhanced[f'rolling_std_{col}'] = rolling_std.fillna(0)  # 填充NaN为0
            
            # 3. 数据清理
            print(f"   执行数据清理...")
            # 处理无穷大值和NaN值
            X_enhanced = X_enhanced.replace([np.inf, -np.inf], np.nan)

            # 删除全为NaN的列
            X_enhanced = X_enhanced.dropna(axis=1, how='all')

            # 填充剩余的NaN值
            for col in X_enhanced.columns:
                if X_enhanced[col].dtype in ['int64', 'float64']:
                    X_enhanced[col] = X_enhanced[col].fillna(X_enhanced[col].median())
                else:
                    X_enhanced[col] = X_enhanced[col].fillna(X_enhanced[col].mode().iloc[0] if not X_enhanced[col].mode().empty else 0)

            # 4. 特征选择
            print(f"   执行特征选择...")
            if len(X_enhanced.columns) > 50:  # 如果特征太多，进行选择
                try:
                    selector = SelectKBest(score_func=f_regression, k=50)
                    X_selected = selector.fit_transform(X_enhanced, y)
                    selected_features = X_enhanced.columns[selector.get_support()]
                    X_enhanced = X_enhanced[selected_features]
                except Exception as e:
                    print(f"      ⚠️  特征选择失败，保持原特征: {str(e)}")
                    # 如果特征选择失败，至少保留原始特征
                    X_enhanced = X_enhanced[X.columns] if all(col in X_enhanced.columns for col in X.columns) else X
            
            print(f"   原始特征数: {len(X.columns)}")
            print(f"   增强后特征数: {len(X_enhanced.columns)}")
            
            return X_enhanced
            
        except Exception as e:
            print(f"❌ 特征工程失败: {str(e)}")
            return X
    
    def optimize_single_model(self, model_name: str, X_train: pd.DataFrame, y_train: pd.Series,
                            X_val: pd.DataFrame, y_val: pd.Series) -> Dict[str, Any]:
        """
        优化单个模型
        
        参数:
        model_name: 模型名称
        X_train: 训练特征
        y_train: 训练目标
        X_val: 验证特征
        y_val: 验证目标
        
        返回:
        optimization_result: 优化结果
        """
        try:
            print(f"🔧 优化模型: {model_name}")
            
            base_model = self.models[model_name]
            param_grid = self.param_grids.get(model_name, {})
            
            if not param_grid:
                # 没有超参数网格，直接训练
                model = base_model
                model.fit(X_train, y_train)
            else:
                # 网格搜索优化
                grid_search = GridSearchCV(
                    base_model, 
                    param_grid, 
                    cv=5, 
                    scoring='r2',
                    n_jobs=-1
                )
                grid_search.fit(X_train, y_train)
                model = grid_search.best_estimator_
                
                print(f"   最佳参数: {grid_search.best_params_}")
            
            # 预测和评估
            y_train_pred = model.predict(X_train)
            y_val_pred = model.predict(X_val)
            
            # 计算指标
            train_r2 = r2_score(y_train, y_train_pred)
            val_r2 = r2_score(y_val, y_val_pred)
            train_mae = mean_absolute_error(y_train, y_train_pred)
            val_mae = mean_absolute_error(y_val, y_val_pred)
            train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))
            val_rmse = np.sqrt(mean_squared_error(y_val, y_val_pred))
            
            optimization_result = {
                'model': model,
                'model_name': model_name,
                'best_params': getattr(model, 'get_params', lambda: {})(),
                'train_metrics': {
                    'r2': train_r2,
                    'mae': train_mae,
                    'rmse': train_rmse
                },
                'val_metrics': {
                    'r2': val_r2,
                    'mae': val_mae,
                    'rmse': val_rmse
                },
                'predictions': {
                    'train': y_train_pred,
                    'val': y_val_pred
                }
            }
            
            print(f"   训练R²: {train_r2:.4f}, 验证R²: {val_r2:.4f}")
            print(f"   训练MAE: {train_mae:.4f}, 验证MAE: {val_mae:.4f}")
            
            return optimization_result
            
        except Exception as e:
            print(f"❌ 模型 {model_name} 优化失败: {str(e)}")
            return {}
    
    def create_ensemble_models(self, optimized_models: Dict[str, Any], 
                             X_train: pd.DataFrame, y_train: pd.Series,
                             X_val: pd.DataFrame, y_val: pd.Series) -> Dict[str, Any]:
        """
        创建集成模型
        
        参数:
        optimized_models: 优化后的模型字典
        X_train: 训练特征
        y_train: 训练目标
        X_val: 验证特征
        y_val: 验证目标
        
        返回:
        ensemble_results: 集成模型结果
        """
        try:
            print(f"🔗 创建集成模型...")
            
            # 获取有效的模型
            valid_models = [(name, result['model']) for name, result in optimized_models.items() 
                          if result and 'model' in result]
            
            if len(valid_models) < 2:
                print(f"   ⚠️  有效模型数量不足，跳过集成学习")
                return {}
            
            ensemble_results = {}
            
            # 1. Voting Regressor
            print(f"   创建投票回归器...")
            voting_regressor = VotingRegressor(estimators=valid_models)
            voting_regressor.fit(X_train, y_train)
            
            y_train_pred = voting_regressor.predict(X_train)
            y_val_pred = voting_regressor.predict(X_val)
            
            ensemble_results['voting'] = {
                'model': voting_regressor,
                'model_name': 'voting_regressor',
                'train_metrics': {
                    'r2': r2_score(y_train, y_train_pred),
                    'mae': mean_absolute_error(y_train, y_train_pred),
                    'rmse': np.sqrt(mean_squared_error(y_train, y_train_pred))
                },
                'val_metrics': {
                    'r2': r2_score(y_val, y_val_pred),
                    'mae': mean_absolute_error(y_val, y_val_pred),
                    'rmse': np.sqrt(mean_squared_error(y_val, y_val_pred))
                },
                'predictions': {
                    'train': y_train_pred,
                    'val': y_val_pred
                }
            }
            
            # 2. Stacking Regressor
            print(f"   创建堆叠回归器...")
            stacking_regressor = StackingRegressor(
                estimators=valid_models,
                final_estimator=LinearRegression(),
                cv=5
            )
            stacking_regressor.fit(X_train, y_train)
            
            y_train_pred = stacking_regressor.predict(X_train)
            y_val_pred = stacking_regressor.predict(X_val)
            
            ensemble_results['stacking'] = {
                'model': stacking_regressor,
                'model_name': 'stacking_regressor',
                'train_metrics': {
                    'r2': r2_score(y_train, y_train_pred),
                    'mae': mean_absolute_error(y_train, y_train_pred),
                    'rmse': np.sqrt(mean_squared_error(y_train, y_train_pred))
                },
                'val_metrics': {
                    'r2': r2_score(y_val, y_val_pred),
                    'mae': mean_absolute_error(y_val, y_val_pred),
                    'rmse': np.sqrt(mean_squared_error(y_val, y_val_pred))
                },
                'predictions': {
                    'train': y_train_pred,
                    'val': y_val_pred
                }
            }
            
            # 显示集成结果
            for ensemble_name, result in ensemble_results.items():
                val_r2 = result['val_metrics']['r2']
                val_mae = result['val_metrics']['mae']
                print(f"   {ensemble_name}: 验证R²={val_r2:.4f}, MAE={val_mae:.4f}")
            
            return ensemble_results
            
        except Exception as e:
            print(f"❌ 集成模型创建失败: {str(e)}")
            return {}

    def evaluate_performance_improvement(self, baseline_results: Dict[str, Any],
                                       enhanced_results: Dict[str, Any],
                                       task_name: str) -> Dict[str, Any]:
        """
        评估性能提升效果

        参数:
        baseline_results: 基线结果
        enhanced_results: 增强结果
        task_name: 任务名称

        返回:
        improvement_analysis: 性能提升分析
        """
        try:
            print(f"📊 评估性能提升效果: {task_name}")

            target = self.performance_targets.get(task_name, {'r2': 0.8, 'mae': 5.0})

            improvement_analysis = {
                'task_name': task_name,
                'performance_target': target,
                'baseline_performance': {},
                'enhanced_performance': {},
                'best_model': None,
                'improvement_achieved': False,
                'target_achieved': False
            }

            # 提取基线性能
            if baseline_results and 'val_metrics' in baseline_results:
                improvement_analysis['baseline_performance'] = baseline_results['val_metrics']

            # 找到最佳增强模型
            best_r2 = -np.inf
            best_model_name = None
            best_metrics = None

            for model_name, result in enhanced_results.items():
                if result and 'val_metrics' in result:
                    val_r2 = result['val_metrics']['r2']
                    if val_r2 > best_r2:
                        best_r2 = val_r2
                        best_model_name = model_name
                        best_metrics = result['val_metrics']

            if best_metrics:
                improvement_analysis['enhanced_performance'] = best_metrics
                improvement_analysis['best_model'] = best_model_name

                # 计算改进程度
                if improvement_analysis['baseline_performance']:
                    baseline_r2 = improvement_analysis['baseline_performance']['r2']
                    enhanced_r2 = best_metrics['r2']

                    r2_improvement = enhanced_r2 - baseline_r2
                    improvement_analysis['r2_improvement'] = r2_improvement
                    improvement_analysis['improvement_achieved'] = r2_improvement > 0.01

                # 检查是否达到目标
                target_r2_achieved = best_metrics['r2'] >= target['r2']
                target_mae_achieved = best_metrics['mae'] <= target['mae']
                improvement_analysis['target_achieved'] = target_r2_achieved and target_mae_achieved

                print(f"   最佳模型: {best_model_name}")
                print(f"   验证R²: {best_metrics['r2']:.4f} (目标: {target['r2']:.2f})")
                print(f"   验证MAE: {best_metrics['mae']:.4f} (目标: {target['mae']:.2f})")
                print(f"   目标达成: {'✅' if improvement_analysis['target_achieved'] else '❌'}")

            return improvement_analysis

        except Exception as e:
            print(f"❌ 性能提升评估失败: {str(e)}")
            return {}

    def visualize_performance_comparison(self, baseline_results: Dict[str, Any],
                                       enhanced_results: Dict[str, Any],
                                       task_name: str, save_path: str = None):
        """
        可视化性能对比

        参数:
        baseline_results: 基线结果
        enhanced_results: 增强结果
        task_name: 任务名称
        save_path: 保存路径
        """
        try:
            print(f"📊 生成性能对比可视化...")

            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

            # 收集所有模型的性能数据
            model_names = []
            r2_scores = []
            mae_scores = []
            model_types = []

            # 基线模型
            if baseline_results and 'val_metrics' in baseline_results:
                model_names.append('基线模型')
                r2_scores.append(baseline_results['val_metrics']['r2'])
                mae_scores.append(baseline_results['val_metrics']['mae'])
                model_types.append('基线')

            # 增强模型
            for model_name, result in enhanced_results.items():
                if result and 'val_metrics' in result:
                    model_names.append(model_name)
                    r2_scores.append(result['val_metrics']['r2'])
                    mae_scores.append(result['val_metrics']['mae'])
                    model_types.append('增强')

            # 子图1：R²分数对比
            colors = ['red' if t == '基线' else 'blue' for t in model_types]
            bars1 = ax1.bar(range(len(model_names)), r2_scores, color=colors, alpha=0.7)

            # 添加目标线
            target_r2 = self.performance_targets.get(task_name, {}).get('r2', 0.8)
            ax1.axhline(y=target_r2, color='green', linestyle='--', linewidth=2, label=f'目标R²={target_r2}')

            ax1.set_title(f'{task_name} - R²分数对比', fontsize=14, fontweight='bold')
            ax1.set_ylabel('R²分数', fontsize=12)
            ax1.set_xticks(range(len(model_names)))
            ax1.set_xticklabels(model_names, rotation=45, ha='right')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 添加数值标签
            for bar, score in zip(bars1, r2_scores):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{score:.3f}', ha='center', va='bottom', fontsize=10)

            # 子图2：MAE对比
            bars2 = ax2.bar(range(len(model_names)), mae_scores, color=colors, alpha=0.7)

            # 添加目标线
            target_mae = self.performance_targets.get(task_name, {}).get('mae', 5.0)
            ax2.axhline(y=target_mae, color='green', linestyle='--', linewidth=2, label=f'目标MAE={target_mae}')

            ax2.set_title(f'{task_name} - MAE对比', fontsize=14, fontweight='bold')
            ax2.set_ylabel('平均绝对误差', fontsize=12)
            ax2.set_xticks(range(len(model_names)))
            ax2.set_xticklabels(model_names, rotation=45, ha='right')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # 添加数值标签
            for bar, score in zip(bars2, mae_scores):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{score:.2f}', ha='center', va='bottom', fontsize=10)

            # 子图3：散点图 - R² vs MAE
            baseline_mask = [t == '基线' for t in model_types]
            enhanced_mask = [t == '增强' for t in model_types]

            if any(baseline_mask):
                baseline_r2 = [r2_scores[i] for i, mask in enumerate(baseline_mask) if mask]
                baseline_mae = [mae_scores[i] for i, mask in enumerate(baseline_mask) if mask]
                ax3.scatter(baseline_r2, baseline_mae, color='red', s=100, alpha=0.7, label='基线模型', marker='o')

            if any(enhanced_mask):
                enhanced_r2 = [r2_scores[i] for i, mask in enumerate(enhanced_mask) if mask]
                enhanced_mae = [mae_scores[i] for i, mask in enumerate(enhanced_mask) if mask]
                ax3.scatter(enhanced_r2, enhanced_mae, color='blue', s=100, alpha=0.7, label='增强模型', marker='s')

            # 添加目标区域
            ax3.axvline(x=target_r2, color='green', linestyle='--', alpha=0.5)
            ax3.axhline(y=target_mae, color='green', linestyle='--', alpha=0.5)
            ax3.fill([target_r2, 1, 1, target_r2], [0, 0, target_mae, target_mae],
                    color='green', alpha=0.1, label='目标区域')

            ax3.set_title('性能散点图 (R² vs MAE)', fontsize=14, fontweight='bold')
            ax3.set_xlabel('R²分数', fontsize=12)
            ax3.set_ylabel('平均绝对误差', fontsize=12)
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            # 子图4：性能提升雷达图
            if len(enhanced_results) > 0:
                # 选择最佳模型进行雷达图展示
                best_model = max(enhanced_results.items(),
                               key=lambda x: x[1].get('val_metrics', {}).get('r2', 0) if x[1] else 0)

                if best_model[1] and 'val_metrics' in best_model[1]:
                    metrics = ['R²分数', 'MAE', 'RMSE']

                    # 标准化指标（转换为0-1范围，越高越好）
                    best_metrics = best_model[1]['val_metrics']
                    values = [
                        best_metrics['r2'],  # R²已经是0-1范围
                        1 / (1 + best_metrics['mae']),  # MAE转换：越小越好
                        1 / (1 + best_metrics['rmse'])  # RMSE转换：越小越好
                    ]

                    # 创建雷达图
                    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
                    values += values[:1]  # 闭合图形
                    angles += angles[:1]

                    ax4.plot(angles, values, 'o-', linewidth=2, label=f'最佳模型: {best_model[0]}')
                    ax4.fill(angles, values, alpha=0.25)
                    ax4.set_xticks(angles[:-1])
                    ax4.set_xticklabels(metrics)
                    ax4.set_ylim(0, 1)
                    ax4.set_title('最佳模型性能雷达图', fontsize=14, fontweight='bold')
                    ax4.legend()
                    ax4.grid(True)

            plt.suptitle(f'{task_name} 性能提升分析报告', fontsize=16, fontweight='bold')
            plt.tight_layout()

            # 保存图表
            if save_path is None:
                save_path = self.output_dir / f'{task_name}_performance_comparison.png'

            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"   ✅ 性能对比可视化已保存: {save_path}")

        except Exception as e:
            print(f"❌ 性能对比可视化失败: {str(e)}")
