function features = extract_comprehensive_features(sensor_data)
% EXTRACT_COMPREHENSIVE_FEATURES 综合特征提取函数
%
% 功能：
%   - 时域特征提取（统计特征、形状特征等）
%   - 频域特征提取（功率谱、频域能量等）
%   - 小波特征提取（多尺度分析）
%   - 传感器融合特征
%
% 输入：
%   sensor_data - 传感器数据矩阵 [时间点 × 传感器数量]
%
% 输出：
%   features - 特征结构体

if isempty(sensor_data) || size(sensor_data, 1) < 100
    features = [];
    return;
end

features = struct();
fs = 1000; % 采样频率 1000 Hz

% 获取传感器数量和有效传感器
[n_samples, n_sensors] = size(sensor_data);
valid_sensors = [];

% 检查传感器有效性
for i = 1:n_sensors
    sensor_signal = sensor_data(:, i);
    if ~all(isnan(sensor_signal)) && std(sensor_signal) > 1e-6
        valid_sensors = [valid_sensors, i];
    end
end

if length(valid_sensors) < 5
    features = [];
    return;
end

fprintf('   提取特征 - 有效传感器: %d/%d\n', length(valid_sensors), n_sensors);

%% 1. 时域特征提取
for i = 1:length(valid_sensors)
    sensor_idx = valid_sensors(i);
    signal = sensor_data(:, sensor_idx);
    
    % 基本统计特征
    features.(sprintf('Sensor_%02d_mean', sensor_idx)) = mean(signal);
    features.(sprintf('Sensor_%02d_std', sensor_idx)) = std(signal);
    features.(sprintf('Sensor_%02d_var', sensor_idx)) = var(signal);
    features.(sprintf('Sensor_%02d_rms', sensor_idx)) = rms(signal);
    features.(sprintf('Sensor_%02d_max', sensor_idx)) = max(signal);
    features.(sprintf('Sensor_%02d_min', sensor_idx)) = min(signal);
    features.(sprintf('Sensor_%02d_range', sensor_idx)) = range(signal);
    
    % 形状特征
    features.(sprintf('Sensor_%02d_skewness', sensor_idx)) = skewness(signal);
    features.(sprintf('Sensor_%02d_kurtosis', sensor_idx)) = kurtosis(signal);
    
    % 能量特征
    features.(sprintf('Sensor_%02d_energy', sensor_idx)) = sum(signal.^2);
    features.(sprintf('Sensor_%02d_power', sensor_idx)) = mean(signal.^2);
    
    % 峰值特征
    features.(sprintf('Sensor_%02d_peak_factor', sensor_idx)) = max(abs(signal)) / rms(signal);
    features.(sprintf('Sensor_%02d_crest_factor', sensor_idx)) = max(abs(signal)) / mean(abs(signal));
    
    % 波形特征
    features.(sprintf('Sensor_%02d_form_factor', sensor_idx)) = rms(signal) / mean(abs(signal));
    features.(sprintf('Sensor_%02d_impulse_factor', sensor_idx)) = max(abs(signal)) / mean(abs(signal));
    
    % 零交叉率
    zero_crossings = sum(diff(sign(signal)) ~= 0);
    features.(sprintf('Sensor_%02d_zero_crossing_rate', sensor_idx)) = zero_crossings / length(signal);
end

%% 2. 频域特征提取
for i = 1:length(valid_sensors)
    sensor_idx = valid_sensors(i);
    signal = sensor_data(:, sensor_idx);
    
    % FFT计算
    N = length(signal);
    Y = fft(signal);
    P = abs(Y/N).^2;
    P = P(1:floor(N/2)+1);
    P(2:end-1) = 2*P(2:end-1);
    f = fs*(0:floor(N/2))/N;
    
    % 频域统计特征
    features.(sprintf('Sensor_%02d_freq_mean', sensor_idx)) = sum(f.*P) / sum(P);
    features.(sprintf('Sensor_%02d_freq_std', sensor_idx)) = sqrt(sum((f - features.(sprintf('Sensor_%02d_freq_mean', sensor_idx))).^2 .* P) / sum(P));
    features.(sprintf('Sensor_%02d_freq_max', sensor_idx)) = f(P == max(P));
    if length(features.(sprintf('Sensor_%02d_freq_max', sensor_idx))) > 1
        features.(sprintf('Sensor_%02d_freq_max', sensor_idx)) = features.(sprintf('Sensor_%02d_freq_max', sensor_idx))(1);
    end
    
    % 频带能量分布
    freq_bands = [0 10; 10 50; 50 100; 100 200; 200 400]; % Hz
    for j = 1:size(freq_bands, 1)
        band_mask = f >= freq_bands(j,1) & f <= freq_bands(j,2);
        band_energy = sum(P(band_mask));
        features.(sprintf('Sensor_%02d_band_%d_%d_energy', sensor_idx, freq_bands(j,1), freq_bands(j,2))) = band_energy;
    end
    
    % 谱质心和谱扩散
    features.(sprintf('Sensor_%02d_spectral_centroid', sensor_idx)) = sum(f.*P) / sum(P);
    features.(sprintf('Sensor_%02d_spectral_spread', sensor_idx)) = sqrt(sum((f - features.(sprintf('Sensor_%02d_spectral_centroid', sensor_idx))).^2 .* P) / sum(P));
    
    % 谱滚降
    cumulative_power = cumsum(P);
    rolloff_threshold = 0.85 * cumulative_power(end);
    rolloff_idx = find(cumulative_power >= rolloff_threshold, 1);
    if ~isempty(rolloff_idx)
        features.(sprintf('Sensor_%02d_spectral_rolloff', sensor_idx)) = f(rolloff_idx);
    else
        features.(sprintf('Sensor_%02d_spectral_rolloff', sensor_idx)) = f(end);
    end
end

%% 3. 小波特征提取（如果有Wavelet Toolbox）
if license('test', 'wavelet')
    for i = 1:min(4, length(valid_sensors)) % 限制计算量
        sensor_idx = valid_sensors(i);
        signal = sensor_data(:, sensor_idx);
        
        try
            % 连续小波变换
            [wt, f_cwt] = cwt(signal, fs);
            
            % 小波能量特征
            wavelet_energy = sum(abs(wt).^2, 2);
            features.(sprintf('Sensor_%02d_wavelet_energy_mean', sensor_idx)) = mean(wavelet_energy);
            features.(sprintf('Sensor_%02d_wavelet_energy_std', sensor_idx)) = std(wavelet_energy);
            features.(sprintf('Sensor_%02d_wavelet_energy_max', sensor_idx)) = max(wavelet_energy);
            
            % 小波频率特征
            [~, max_energy_idx] = max(wavelet_energy);
            features.(sprintf('Sensor_%02d_wavelet_dominant_freq', sensor_idx)) = f_cwt(max_energy_idx);
            
        catch
            % 小波变换失败，跳过
        end
    end
end

%% 4. 传感器融合特征
if length(valid_sensors) >= 2
    % 多传感器信号融合
    valid_signals = sensor_data(:, valid_sensors);
    
    % 主成分分析特征
    try
        [coeff, score, latent] = pca(valid_signals);
        features.pca_first_component_variance = latent(1) / sum(latent);
        features.pca_cumulative_variance_3 = sum(latent(1:min(3, length(latent)))) / sum(latent);
        
        % 第一主成分的统计特征
        pc1 = score(:, 1);
        features.pca_pc1_mean = mean(pc1);
        features.pca_pc1_std = std(pc1);
        features.pca_pc1_energy = sum(pc1.^2);
    catch
        % PCA失败，跳过
    end
    
    % 传感器间相关性
    corr_matrix = corrcoef(valid_signals);
    features.sensor_correlation_mean = mean(corr_matrix(triu(true(size(corr_matrix)), 1)));
    features.sensor_correlation_std = std(corr_matrix(triu(true(size(corr_matrix)), 1)));
    features.sensor_correlation_max = max(corr_matrix(triu(true(size(corr_matrix)), 1)));
    
    % 信号融合（加权平均）
    weights = 1 ./ (std(valid_signals) + eps); % 基于方差的权重
    weights = weights / sum(weights);
    fused_signal = valid_signals * weights';
    
    % 融合信号特征
    features.fused_signal_mean = mean(fused_signal);
    features.fused_signal_std = std(fused_signal);
    features.fused_signal_rms = rms(fused_signal);
    features.fused_signal_energy = sum(fused_signal.^2);
    features.fused_signal_peak_factor = max(abs(fused_signal)) / rms(fused_signal);
end

%% 5. 高级统计特征
% 使用所有有效传感器的统计特征
all_valid_data = sensor_data(:, valid_sensors);
all_data_vector = all_valid_data(:);

features.global_mean = mean(all_data_vector);
features.global_std = std(all_data_vector);
features.global_skewness = skewness(all_data_vector);
features.global_kurtosis = kurtosis(all_data_vector);
features.global_energy = sum(all_data_vector.^2);

% 传感器数量特征
features.valid_sensor_count = length(valid_sensors);
features.sensor_coverage_ratio = length(valid_sensors) / n_sensors;

% 数据质量特征
features.signal_length = n_samples;
features.sampling_rate = fs;
features.signal_duration = n_samples / fs;

fprintf('   特征提取完成 - 总特征数: %d\n', length(fieldnames(features)));

end
