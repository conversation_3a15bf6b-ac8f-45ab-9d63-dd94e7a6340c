function test_results = test_matlab_system()
% TEST_MATLAB_SYSTEM MATLAB振动信号分析系统测试脚本
%
% 功能：
%   - 测试系统各个模块的功能
%   - 验证与Python版本的兼容性
%   - 生成测试报告
%
% 输出：
%   test_results - 测试结果结构体

fprintf('🧪 MATLAB振动信号分析系统测试\n');
fprintf('=' * ones(1, 60)); fprintf('\n');

test_results = struct();
test_results.start_time = datetime('now');
test_results.tests = {};

%% 1. 环境检查测试
fprintf('\n🔍 测试1：环境检查\n');
env_test = test_environment();
test_results.tests{end+1} = struct('name', 'Environment Check', 'result', env_test);

%% 2. 数据加载测试
fprintf('\n📂 测试2：数据加载功能\n');
data_test = test_data_loading();
test_results.tests{end+1} = struct('name', 'Data Loading', 'result', data_test);

%% 3. 特征提取测试
fprintf('\n🔧 测试3：特征提取功能\n');
feature_test = test_feature_extraction();
test_results.tests{end+1} = struct('name', 'Feature Extraction', 'result', feature_test);

%% 4. 模型训练测试
fprintf('\n🤖 测试4：模型训练功能\n');
model_test = test_model_training();
test_results.tests{end+1} = struct('name', 'Model Training', 'result', model_test);

%% 5. 可视化测试
fprintf('\n📊 测试5：可视化功能\n');
viz_test = test_visualization();
test_results.tests{end+1} = struct('name', 'Visualization', 'result', viz_test);

%% 6. 完整系统测试（如果有测试数据）
fprintf('\n🚀 测试6：完整系统测试\n');
system_test = test_complete_system();
test_results.tests{end+1} = struct('name', 'Complete System', 'result', system_test);

%% 生成测试报告
test_results.end_time = datetime('now');
test_results.duration = test_results.end_time - test_results.start_time;

% 统计测试结果
passed_tests = 0;
failed_tests = 0;
for i = 1:length(test_results.tests)
    if test_results.tests{i}.result.passed
        passed_tests = passed_tests + 1;
    else
        failed_tests = failed_tests + 1;
    end
end

test_results.summary = struct();
test_results.summary.total_tests = length(test_results.tests);
test_results.summary.passed = passed_tests;
test_results.summary.failed = failed_tests;
test_results.summary.success_rate = passed_tests / length(test_results.tests);

% 显示测试总结
fprintf('\n📋 测试总结\n');
fprintf('=' * ones(1, 40)); fprintf('\n');
fprintf('总测试数: %d\n', test_results.summary.total_tests);
fprintf('通过: %d\n', test_results.summary.passed);
fprintf('失败: %d\n', test_results.summary.failed);
fprintf('成功率: %.1f%%\n', test_results.summary.success_rate * 100);
fprintf('测试时长: %s\n', char(test_results.duration));

if test_results.summary.success_rate >= 0.8
    fprintf('✅ 系统测试通过！\n');
else
    fprintf('❌ 系统测试失败，请检查错误信息\n');
end

% 保存测试结果
save('matlab_system_test_results.mat', 'test_results');
fprintf('💾 测试结果已保存到: matlab_system_test_results.mat\n');

end

%% 测试函数

function result = test_environment()
% 测试环境检查
result = struct('passed', false, 'message', '', 'details', struct());

try
    % 检查MATLAB版本
    matlab_version = version('-release');
    year = str2double(matlab_version(1:4));
    
    if year >= 2019
        result.details.matlab_version = matlab_version;
        result.details.matlab_version_ok = true;
    else
        result.message = sprintf('MATLAB版本过低: %s (需要2019b+)', matlab_version);
        return;
    end
    
    % 检查必需工具箱
    required_toolboxes = {
        'Statistics and Machine Learning Toolbox', 'stats';
        'Deep Learning Toolbox', 'nnet';
        'Signal Processing Toolbox', 'signal';
        'Optimization Toolbox', 'optim';
    };
    
    missing_toolboxes = {};
    for i = 1:size(required_toolboxes, 1)
        if ~license('test', required_toolboxes{i, 2})
            missing_toolboxes{end+1} = required_toolboxes{i, 1};
        end
    end
    
    result.details.missing_toolboxes = missing_toolboxes;
    
    if isempty(missing_toolboxes)
        result.passed = true;
        result.message = '环境检查通过';
    else
        result.message = sprintf('缺少工具箱: %s', strjoin(missing_toolboxes, ', '));
    end
    
    % 检查GPU
    try
        gpu = gpuDevice;
        result.details.gpu_available = true;
        result.details.gpu_name = gpu.Name;
    catch
        result.details.gpu_available = false;
    end
    
catch ME
    result.message = sprintf('环境检查失败: %s', ME.message);
end

fprintf('   MATLAB版本: %s %s\n', matlab_version, ...
        result.details.matlab_version_ok ? '✅' : '❌');
fprintf('   缺少工具箱: %d\n', length(result.details.missing_toolboxes));
fprintf('   GPU可用: %s\n', result.details.gpu_available ? '✅' : '❌');

end

function result = test_data_loading()
% 测试数据加载功能
result = struct('passed', false, 'message', '', 'details', struct());

try
    % 创建测试数据
    test_data_dir = 'test_data_matlab';
    if ~exist(test_data_dir, 'dir')
        mkdir(test_data_dir);
    end
    
    % 生成模拟CSV文件
    test_file = fullfile(test_data_dir, '25吨_三轴_60.0kmh_实验1.csv');
    create_test_csv(test_file);
    
    % 测试数据加载
    addpath('utils');
    raw_data = load_csv_data(test_file, 'legacy_21_columns');
    
    if ~isempty(raw_data) && height(raw_data) > 0
        result.passed = true;
        result.message = '数据加载测试通过';
        result.details.test_file = test_file;
        result.details.data_shape = [height(raw_data), width(raw_data)];
    else
        result.message = '数据加载失败';
    end
    
    % 清理测试文件
    if exist(test_file, 'file')
        delete(test_file);
    end
    if exist(test_data_dir, 'dir')
        rmdir(test_data_dir);
    end
    
catch ME
    result.message = sprintf('数据加载测试失败: %s', ME.message);
end

fprintf('   测试文件创建: %s\n', result.passed ? '✅' : '❌');
if result.passed
    fprintf('   数据形状: %d × %d\n', result.details.data_shape(1), result.details.data_shape(2));
end

end

function result = test_feature_extraction()
% 测试特征提取功能
result = struct('passed', false, 'message', '', 'details', struct());

try
    % 创建模拟传感器数据
    fs = 1000;
    duration = 2; % 2秒
    t = (0:1/fs:duration-1/fs)';
    n_sensors = 20;
    
    % 生成模拟振动信号
    sensor_data = zeros(length(t), n_sensors);
    for i = 1:n_sensors
        % 基础信号 + 噪声
        base_freq = 10 + i; % 不同传感器的基础频率
        sensor_data(:, i) = sin(2*pi*base_freq*t) + 0.1*randn(size(t));
        
        % 添加车辆通过事件（在中间位置）
        event_start = round(length(t)/2 - 0.5*fs);
        event_end = round(length(t)/2 + 0.5*fs);
        event_amplitude = 2 + 0.5*randn();
        sensor_data(event_start:event_end, i) = sensor_data(event_start:event_end, i) + ...
            event_amplitude * exp(-((1:length(event_start:event_end)) - length(event_start:event_end)/2).^2 / (0.2*fs)^2);
    end
    
    % 测试特征提取
    features = extract_comprehensive_features(sensor_data);
    
    if ~isempty(features) && isstruct(features)
        feature_count = length(fieldnames(features));
        result.passed = true;
        result.message = '特征提取测试通过';
        result.details.feature_count = feature_count;
        result.details.signal_shape = size(sensor_data);
    else
        result.message = '特征提取失败';
    end
    
catch ME
    result.message = sprintf('特征提取测试失败: %s', ME.message);
end

fprintf('   信号生成: %s\n', result.passed ? '✅' : '❌');
if result.passed
    fprintf('   提取特征数: %d\n', result.details.feature_count);
    fprintf('   信号形状: %d × %d\n', result.details.signal_shape(1), result.details.signal_shape(2));
end

end

function result = test_model_training()
% 测试模型训练功能
result = struct('passed', false, 'message', '', 'details', struct());

try
    % 创建模拟数据集
    n_samples = 200;
    n_features = 50;
    
    % 回归数据集
    X = randn(n_samples, n_features);
    y_reg = X(:, 1) + 0.5*X(:, 2) + 0.1*randn(n_samples, 1); % 线性关系 + 噪声
    
    % 分类数据集
    y_class = categorical(randi([1, 3], n_samples, 1));
    
    datasets = struct();
    datasets.test_regression = struct('X', X, 'y', y_reg, 'task_type', 'regression', ...
                                     'feature_names', cellstr(string(1:n_features)), ...
                                     'target_name', 'test_target');
    datasets.test_classification = struct('X', X, 'y', y_class, 'task_type', 'classification', ...
                                         'feature_names', cellstr(string(1:n_features)), ...
                                         'target_name', 'test_class');
    
    % 测试配置
    config = struct('OptimizationTrials', 5, 'Verbose', false);
    
    % 简化的模型训练测试
    if license('test', 'stats')
        % 测试Random Forest
        mdl = fitensemble(X, y_reg, 'Bag', 10, 'Tree');
        y_pred = predict(mdl, X);
        r2 = 1 - sum((y_reg - y_pred).^2) / sum((y_reg - mean(y_reg)).^2);
        
        if r2 > 0.5 % 简单的性能检查
            result.passed = true;
            result.message = '模型训练测试通过';
            result.details.test_r2 = r2;
        else
            result.message = sprintf('模型性能不佳: R² = %.3f', r2);
        end
    else
        result.message = '缺少Statistics and Machine Learning Toolbox';
    end
    
catch ME
    result.message = sprintf('模型训练测试失败: %s', ME.message);
end

fprintf('   数据生成: %s\n', result.passed ? '✅' : '❌');
if result.passed
    fprintf('   测试R²: %.3f\n', result.details.test_r2);
end

end

function result = test_visualization()
% 测试可视化功能
result = struct('passed', false, 'message', '', 'details', struct());

try
    % 创建简单的测试图表
    fig = figure('Visible', 'off');
    x = 1:10;
    y = x.^2 + randn(size(x));
    plot(x, y, 'o-');
    title('Test Visualization');
    xlabel('X');
    ylabel('Y');
    
    % 测试保存
    test_file = 'test_visualization.png';
    print(fig, test_file, '-dpng', '-r150');
    close(fig);
    
    if exist(test_file, 'file')
        result.passed = true;
        result.message = '可视化测试通过';
        result.details.test_file = test_file;
        
        % 清理测试文件
        delete(test_file);
    else
        result.message = '图表保存失败';
    end
    
catch ME
    result.message = sprintf('可视化测试失败: %s', ME.message);
end

fprintf('   图表生成: %s\n', result.passed ? '✅' : '❌');

end

function result = test_complete_system()
% 测试完整系统（简化版）
result = struct('passed', false, 'message', '', 'details', struct());

try
    % 检查主程序文件是否存在
    main_file = 'main_vibration_analysis.m';
    if exist(main_file, 'file')
        result.passed = true;
        result.message = '完整系统文件检查通过';
        result.details.main_file_exists = true;
    else
        result.message = '主程序文件不存在';
        result.details.main_file_exists = false;
    end
    
    % 检查其他关键文件
    required_files = {
        'load_and_preprocess_data.m';
        'extract_comprehensive_features.m';
        'prepare_training_datasets.m';
        'generate_visualizations.m';
    };
    
    missing_files = {};
    for i = 1:length(required_files)
        if ~exist(required_files{i}, 'file')
            missing_files{end+1} = required_files{i};
        end
    end
    
    result.details.missing_files = missing_files;
    
    if isempty(missing_files) && result.passed
        result.message = '完整系统检查通过';
    else
        result.passed = false;
        result.message = sprintf('缺少文件: %s', strjoin(missing_files, ', '));
    end
    
catch ME
    result.message = sprintf('完整系统测试失败: %s', ME.message);
end

fprintf('   主程序文件: %s\n', result.details.main_file_exists ? '✅' : '❌');
fprintf('   缺少文件: %d\n', length(result.details.missing_files));

end

function create_test_csv(filename)
% 创建测试CSV文件
n_samples = 1000;
n_sensors = 20;

% 生成模拟数据
data = zeros(n_samples, n_sensors + 1);
data(:, 1) = 1:n_samples; % count列

% 生成传感器数据
for i = 1:n_sensors
    base_signal = sin(2*pi*10*((1:n_samples)/1000)) + 0.1*randn(1, n_samples);
    data(:, i+1) = base_signal;
end

% 写入CSV文件
writematrix(data, filename);

end
