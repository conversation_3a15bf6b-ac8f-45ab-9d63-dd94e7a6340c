function [model_results, optimization_results] = train_and_optimize_models(datasets, config)
% TRAIN_AND_OPTIMIZE_MODELS 模型训练和优化函数
%
% 功能：
%   - 训练多种机器学习模型（Random Forest, SVM, Neural Network等）
%   - 贝叶斯超参数优化
%   - 5折交叉验证
%   - 性能评估和模型选择
%
% 输入：
%   datasets - 训练数据集结构体
%   config - 配置参数
%
% 输出：
%   model_results - 模型训练结果
%   optimization_results - 优化结果

fprintf('🤖 开始模型训练和优化...\n');

model_results = struct();
optimization_results = struct();

% 定义模型列表
model_configs = {
    struct('name', 'RandomForest', 'type', 'tree', 'supports_regression', true, 'supports_classification', true);
    struct('name', 'SVM', 'type', 'svm', 'supports_regression', true, 'supports_classification', true);
    struct('name', 'NeuralNetwork', 'type', 'neural', 'supports_regression', true, 'supports_classification', true);
    struct('name', 'TreeBagger', 'type', 'ensemble', 'supports_regression', true, 'supports_classification', true);
};

% 处理每个数据集
dataset_names = fieldnames(datasets);
for d = 1:length(dataset_names)
    dataset_name = dataset_names{d};
    dataset = datasets.(dataset_name);
    
    fprintf('\n📊 处理数据集: %s\n', dataset_name);
    fprintf('   数据形状: %d × %d\n', size(dataset.X, 1), size(dataset.X, 2));
    fprintf('   任务类型: %s\n', dataset.task_type);
    
    % 数据预处理
    [X_processed, y_processed, preprocessing_info] = preprocess_dataset(dataset);
    
    if isempty(X_processed)
        fprintf('   ⚠️  数据预处理失败，跳过此数据集\n');
        continue;
    end
    
    % 数据分割
    cv_partition = cvpartition(length(y_processed), 'HoldOut', 0.2);
    train_idx = training(cv_partition);
    test_idx = test(cv_partition);
    
    X_train = X_processed(train_idx, :);
    y_train = y_processed(train_idx);
    X_test = X_processed(test_idx, :);
    y_test = y_processed(test_idx);
    
    fprintf('   训练集: %d 样本, 测试集: %d 样本\n', length(y_train), length(y_test));
    
    % 训练和优化每个模型
    dataset_results = struct();
    
    for m = 1:length(model_configs)
        model_config = model_configs{m};
        
        % 检查模型是否支持当前任务
        if strcmp(dataset.task_type, 'regression') && ~model_config.supports_regression
            continue;
        elseif strcmp(dataset.task_type, 'classification') && ~model_config.supports_classification
            continue;
        end
        
        fprintf('\n   🔧 训练模型: %s\n', model_config.name);
        
        try
            % 贝叶斯优化
            if config.OptimizationTrials > 0
                fprintf('      🎯 贝叶斯优化 (%d trials)...\n', config.OptimizationTrials);
                [best_params, optimization_info] = optimize_hyperparameters(...
                    model_config, X_train, y_train, dataset.task_type, config);
            else
                best_params = get_default_parameters(model_config, dataset.task_type);
                optimization_info = struct('optimization_skipped', true);
            end
            
            % 使用最优参数训练模型
            fprintf('      🏋️  训练最终模型...\n');
            [trained_model, training_info] = train_model_with_params(...
                model_config, X_train, y_train, best_params, dataset.task_type);
            
            % 模型评估
            fprintf('      📊 模型评估...\n');
            evaluation_results = evaluate_model(trained_model, X_test, y_test, ...
                                               X_train, y_train, dataset.task_type);
            
            % 5折交叉验证
            fprintf('      🔄 5折交叉验证...\n');
            cv_results = cross_validate_model(model_config, X_processed, y_processed, ...
                                             best_params, dataset.task_type);
            
            % 保存结果
            dataset_results.(model_config.name) = struct(...
                'model', trained_model, ...
                'best_params', best_params, ...
                'optimization_info', optimization_info, ...
                'training_info', training_info, ...
                'evaluation', evaluation_results, ...
                'cross_validation', cv_results, ...
                'preprocessing_info', preprocessing_info ...
            );
            
            % 显示性能
            if strcmp(dataset.task_type, 'regression')
                fprintf('      ✅ R² = %.4f, RMSE = %.4f\n', ...
                    evaluation_results.test_r2, evaluation_results.test_rmse);
            else
                fprintf('      ✅ 准确率 = %.4f, F1 = %.4f\n', ...
                    evaluation_results.test_accuracy, evaluation_results.test_f1);
            end
            
        catch ME
            fprintf('      ❌ 模型训练失败: %s\n', ME.message);
            dataset_results.(model_config.name) = struct('error', ME.message);
        end
    end
    
    model_results.(dataset_name) = dataset_results;
end

% 生成优化总结
optimization_results = generate_optimization_summary(model_results);

fprintf('\n✅ 模型训练和优化完成\n');

end

%% 辅助函数

function [X_processed, y_processed, preprocessing_info] = preprocess_dataset(dataset)
% 数据预处理
X = dataset.X;
y = dataset.y;

% 移除缺失值
valid_rows = ~any(isnan(X), 2) & ~isnan(y);
X = X(valid_rows, :);
y = y(valid_rows);

if isempty(X)
    X_processed = [];
    y_processed = [];
    preprocessing_info = struct('error', 'No valid data after removing NaN');
    return;
end

% 移除常数特征
feature_std = std(X);
valid_features = feature_std > 1e-6;
X = X(:, valid_features);

% 特征标准化
X_mean = mean(X);
X_std = std(X);
X_std(X_std == 0) = 1; % 避免除零
X_processed = (X - X_mean) ./ X_std;

y_processed = y;

% 分类任务的标签编码
if strcmp(dataset.task_type, 'classification')
    [y_processed, label_map] = encode_categorical_labels(y);
else
    label_map = [];
end

preprocessing_info = struct(...
    'original_size', [length(valid_rows), size(dataset.X, 2)], ...
    'processed_size', size(X_processed), ...
    'removed_samples', sum(~valid_rows), ...
    'removed_features', sum(~valid_features), ...
    'feature_mean', X_mean, ...
    'feature_std', X_std, ...
    'valid_features', valid_features, ...
    'label_map', label_map ...
);
end

function [best_params, optimization_info] = optimize_hyperparameters(model_config, X, y, task_type, config)
% 贝叶斯超参数优化
optimization_info = struct();

% 定义超参数搜索空间
param_space = define_parameter_space(model_config, task_type);

if isempty(param_space)
    best_params = get_default_parameters(model_config, task_type);
    optimization_info.message = 'No parameters to optimize';
    return;
end

% 定义目标函数
objective_function = @(params) evaluate_hyperparameters(params, model_config, X, y, task_type);

try
    % 使用贝叶斯优化
    if license('test', 'optim') && exist('bayesopt', 'file')
        % 转换参数空间为bayesopt格式
        variables = convert_to_bayesopt_variables(param_space);
        
        results = bayesopt(objective_function, variables, ...
            'MaxObjectiveEvaluations', config.OptimizationTrials, ...
            'Verbose', 0, ...
            'UseParallel', false);
        
        best_params = table2struct(results.XAtMinObjective);
        optimization_info.best_objective = results.MinObjective;
        optimization_info.num_evaluations = results.NumObjectiveEvaluations;
        
    else
        % 回退到网格搜索
        fprintf('        使用网格搜索替代贝叶斯优化\n');
        [best_params, optimization_info] = grid_search_optimization(param_space, objective_function, config);
    end
    
catch ME
    fprintf('        优化失败，使用默认参数: %s\n', ME.message);
    best_params = get_default_parameters(model_config, task_type);
    optimization_info.error = ME.message;
end
end

function param_space = define_parameter_space(model_config, task_type)
% 定义超参数搜索空间
param_space = [];

switch model_config.name
    case 'RandomForest'
        param_space.NumTrees = [50, 100, 200, 500];
        param_space.MinLeafSize = [1, 5, 10, 20];
        
    case 'SVM'
        param_space.BoxConstraint = [0.1, 1, 10, 100];
        param_space.KernelScale = [0.1, 1, 10];
        
    case 'NeuralNetwork'
        param_space.HiddenLayerSize = [10, 25, 50, 100];
        param_space.TrainingAlgorithm = {'trainlm', 'trainbr', 'trainscg'};
        
    case 'TreeBagger'
        param_space.NumTrees = [50, 100, 200];
        param_space.MinLeafSize = [1, 5, 10];
end
end
