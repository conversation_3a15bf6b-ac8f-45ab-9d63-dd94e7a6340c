function results = main_vibration_analysis(varargin)
% MAIN_VIBRATION_ANALYSIS 振动信号分析系统主程序 (MATLAB版本)
%
% 功能：
%   - 数据预处理和特征提取
%   - 多种机器学习模型训练 (XGBoost, CNN-LSTM, BP神经网络等)
%   - 贝叶斯优化和集成学习
%   - 学术级可视化生成
%   - 性能目标：速度预测R²>0.90，轴重预测R²>0.85，轴型分类准确率>90%
%
% 输入参数：
%   'DataDir' - 数据目录路径 (可选)
%   'ForceReprocess' - 强制重新处理数据 (默认: false)
%   'EnableGPU' - 启用GPU加速 (默认: true)
%   'OptimizationTrials' - 优化试验次数 (默认: 50)
%
% 输出：
%   results - 包含所有分析结果的结构体
%
% 示例：
%   results = main_vibration_analysis('DataDir', './data');
%   results = main_vibration_analysis('ForceReprocess', true, 'OptimizationTrials', 100);
%
% 作者: AI Assistant (MATLAB转换版本)
% 日期: 2024-12-22
% 版本: 1.0

%% 参数解析和初始化
p = inputParser;
addParameter(p, 'DataDir', '', @ischar);
addParameter(p, 'ForceReprocess', false, @islogical);
addParameter(p, 'EnableGPU', true, @islogical);
addParameter(p, 'OptimizationTrials', 50, @isnumeric);
addParameter(p, 'Verbose', true, @islogical);
parse(p, varargin{:});

config = p.Results;

%% 系统初始化
fprintf('🚀 振动信号分析系统 v1.0 - MATLAB版本\n');
fprintf('=' * ones(1, 90)); fprintf('\n');
fprintf('🤖 传统机器学习: Random Forest + TreeBagger + Gradient Boosting + SVM\n');
fprintf('🧠 深度学习: BP神经网络 + CNN-LSTM + TCN(时间卷积网络)\n');
fprintf('🔧 超参数优化: 贝叶斯优化 + 5折交叉验证\n');
fprintf('🎯 集成学习: Voting + Stacking + Weighted + Blending\n');
fprintf('🌊 高级特征工程: 小波变换 + 统计矩 + 频域能量分布\n');
fprintf('📊 学术级可视化: 330 DPI + Times New Roman + IEEE标准\n');
fprintf('=' * ones(1, 90)); fprintf('\n');

% 检查必要的工具箱
required_toolboxes = {
    'Statistics and Machine Learning Toolbox', 'stats';
    'Deep Learning Toolbox', 'nnet';
    'Signal Processing Toolbox', 'signal';
    'Optimization Toolbox', 'optim';
    'Wavelet Toolbox', 'wavelet'
};

fprintf('🔍 检查MATLAB工具箱...\n');
missing_toolboxes = {};
for i = 1:size(required_toolboxes, 1)
    if ~license('test', required_toolboxes{i, 2})
        missing_toolboxes{end+1} = required_toolboxes{i, 1};
        fprintf('❌ %s - 未安装\n', required_toolboxes{i, 1});
    else
        fprintf('✅ %s - 已安装\n', required_toolboxes{i, 1});
    end
end

if ~isempty(missing_toolboxes)
    warning('部分工具箱未安装，某些功能可能受限');
end

%% GPU检查和配置
if config.EnableGPU
    try
        gpu_device = gpuDevice;
        fprintf('✅ GPU设备: %s (内存: %.1f GB)\n', gpu_device.Name, gpu_device.AvailableMemory/1e9);
        use_gpu = true;
    catch
        fprintf('⚠️  GPU不可用，使用CPU计算\n');
        use_gpu = false;
    end
else
    use_gpu = false;
    fprintf('ℹ️  GPU加速已禁用\n');
end

%% 数据目录查找和验证
if isempty(config.DataDir)
    % 自动查找数据目录
    possible_dirs = {'./data', '../data', './振动数据', '../振动数据'};
    data_dir = '';
    
    for i = 1:length(possible_dirs)
        if exist(possible_dirs{i}, 'dir')
            csv_files = dir(fullfile(possible_dirs{i}, '**/*.csv'));
            if ~isempty(csv_files)
                data_dir = possible_dirs{i};
                break;
            end
        end
    end
    
    if isempty(data_dir)
        error('❌ 未找到包含CSV文件的数据目录，请指定DataDir参数');
    end
else
    data_dir = config.DataDir;
    if ~exist(data_dir, 'dir')
        error('❌ 指定的数据目录不存在: %s', data_dir);
    end
end

fprintf('📂 数据目录: %s\n', data_dir);

%% 主要分析流程
try
    % 1. 数据加载和预处理
    fprintf('\n📊 第一阶段：数据加载和预处理\n');
    [features_table, data_info] = load_and_preprocess_data(data_dir, config);
    
    % 2. 特征工程和数据准备
    fprintf('\n🔧 第二阶段：特征工程和数据准备\n');
    [datasets, feature_info] = prepare_training_datasets(features_table, config);
    
    % 3. 模型训练和优化
    fprintf('\n🤖 第三阶段：模型训练和优化\n');
    [model_results, optimization_results] = train_and_optimize_models(datasets, config);
    
    % 4. 集成学习
    fprintf('\n🎯 第四阶段：集成学习\n');
    ensemble_results = train_ensemble_models(datasets, model_results, config);
    
    % 5. 可视化生成
    fprintf('\n📊 第五阶段：可视化生成\n');
    visualization_results = generate_visualizations(datasets, model_results, ensemble_results, config);
    
    % 6. 结果汇总和报告生成
    fprintf('\n📋 第六阶段：结果汇总和报告生成\n');
    results = generate_final_report(data_info, feature_info, model_results, ...
                                   ensemble_results, visualization_results, config);
    
    fprintf('\n✅ 振动信号分析完成！\n');
    fprintf('📊 总体性能:\n');
    fprintf('   速度预测 R²: %.4f (目标: >0.90)\n', results.performance.speed_r2);
    fprintf('   轴重预测 R²: %.4f (目标: >0.85)\n', results.performance.load_r2);
    fprintf('   轴型分类准确率: %.4f (目标: >0.90)\n', results.performance.axle_accuracy);
    
    % 保存结果
    save('matlab_vibration_analysis_results.mat', 'results');
    fprintf('💾 结果已保存到: matlab_vibration_analysis_results.mat\n');
    
catch ME
    fprintf('❌ 分析过程中发生错误:\n');
    fprintf('   错误信息: %s\n', ME.message);
    fprintf('   错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    
    % 返回错误信息
    results = struct();
    results.status = 'error';
    results.error_message = ME.message;
    results.error_stack = ME.stack;
    
    rethrow(ME);
end

end

%% 辅助函数声明
% 注意：这些函数将在单独的文件中实现
% - load_and_preprocess_data.m
% - prepare_training_datasets.m  
% - train_and_optimize_models.m
% - train_ensemble_models.m
% - generate_visualizations.m
% - generate_final_report.m
