# 🚀 振动信号分析系统 - MATLAB版本

## 📋 系统概述

本系统是Python振动信号分析系统的完整MATLAB转换版本，保持了原有的所有核心功能和性能目标：

- **速度预测**：R² > 0.90
- **轴重预测**：R² > 0.85  
- **轴型分类**：准确率 > 90%

## 🎯 核心功能

### 🤖 机器学习算法
- **传统算法**：Random Forest, SVM, TreeBagger
- **神经网络**：BP神经网络, CNN-LSTM
- **集成学习**：Voting, Stacking, Weighted Average

### 🔧 优化技术
- **贝叶斯优化**：使用Optimization Toolbox的bayesopt函数
- **交叉验证**：5折交叉验证
- **超参数调优**：自动搜索最优参数

### 📊 可视化功能
- **学术级图表**：330 DPI, Times New Roman字体
- **性能分析**：预测散点图, 混淆矩阵, ROC曲线
- **特征分析**：特征重要性, 数据分布

## 🛠️ 系统要求

### MATLAB版本
- MATLAB R2019b 或更高版本

### 必需工具箱
- **Statistics and Machine Learning Toolbox** - 机器学习算法
- **Deep Learning Toolbox** - 神经网络
- **Signal Processing Toolbox** - 信号处理
- **Optimization Toolbox** - 贝叶斯优化
- **Wavelet Toolbox** - 小波特征提取（可选）

### 硬件要求
- **内存**：8GB+ (推荐16GB+)
- **存储**：5GB+ 可用空间
- **GPU**：NVIDIA GPU（可选，用于深度学习加速）

## 📁 文件结构

```
matlab_version/
├── main_vibration_analysis.m          # 主程序入口
├── load_and_preprocess_data.m         # 数据加载和预处理
├── extract_comprehensive_features.m   # 特征提取
├── train_and_optimize_models.m        # 模型训练和优化
├── generate_visualizations.m          # 可视化生成
├── prepare_training_datasets.m        # 数据集准备
├── train_ensemble_models.m            # 集成学习
├── generate_final_report.m            # 报告生成
├── utils/                             # 工具函数
│   ├── detect_vehicle_events.m
│   ├── validate_sensor_data.m
│   ├── load_csv_data.m
│   └── extract_sensor_data.m
└── README_MATLAB.md                   # 本文档
```

## 🚀 快速开始

### 1. 环境检查
```matlab
% 检查必需工具箱
required_toolboxes = {'stats', 'nnet', 'signal', 'optim'};
for i = 1:length(required_toolboxes)
    if license('test', required_toolboxes{i})
        fprintf('✅ %s 工具箱已安装\n', required_toolboxes{i});
    else
        fprintf('❌ %s 工具箱未安装\n', required_toolboxes{i});
    end
end
```

### 2. 基本使用
```matlab
% 使用默认设置运行分析
results = main_vibration_analysis();

% 指定数据目录
results = main_vibration_analysis('DataDir', './data');

% 自定义参数
results = main_vibration_analysis(...
    'DataDir', './data', ...
    'OptimizationTrials', 100, ...
    'EnableGPU', true, ...
    'ForceReprocess', false);
```

### 3. 查看结果
```matlab
% 显示总体性能
fprintf('速度预测 R²: %.4f\n', results.performance.speed_r2);
fprintf('轴重预测 R²: %.4f\n', results.performance.load_r2);
fprintf('轴型分类准确率: %.4f\n', results.performance.axle_accuracy);

% 查看最佳模型
best_models = results.best_models;
disp(best_models);

% 查看生成的图表
visualization_dir = results.visualization_results.output_dir;
fprintf('可视化图表保存在: %s\n', visualization_dir);
```

## 📊 数据格式支持

### 支持的CSV格式

#### 1. 新格式（22列）
```
文件名格式: GW100001_datetime_AcceData_车道X_Y轴-Z.Zt-Wkmh.csv
列结构: count + 20个传感器列 + 1个额外列
```

#### 2. 传统格式（21列）
```
文件名格式: X.X吨_Y轴_Z.Zkmh_实验N.csv
列结构: count + 20个传感器列
```

### 数据要求
- **采样频率**：1000 Hz
- **有效传感器**：至少5个传感器有有效数据
- **信号长度**：至少100个数据点
- **文件编码**：UTF-8（支持中文文件名）

## 🔧 高级配置

### 参数说明

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `DataDir` | string | 自动检测 | 数据目录路径 |
| `ForceReprocess` | logical | false | 强制重新处理数据 |
| `EnableGPU` | logical | true | 启用GPU加速 |
| `OptimizationTrials` | numeric | 50 | 贝叶斯优化试验次数 |
| `Verbose` | logical | true | 显示详细信息 |

### 自定义模型参数
```matlab
% 修改 train_and_optimize_models.m 中的参数空间
param_space.NumTrees = [100, 200, 500];  % Random Forest树数量
param_space.HiddenLayerSize = [50, 100, 200];  % 神经网络隐藏层
```

## 📈 性能优化建议

### 1. GPU加速
```matlab
% 检查GPU可用性
if gpuDeviceCount > 0
    gpu = gpuDevice();
    fprintf('GPU: %s (%.1f GB)\n', gpu.Name, gpu.AvailableMemory/1e9);
end
```

### 2. 并行计算
```matlab
% 启用并行池
if ~isempty(gcp('nocreate'))
    delete(gcp('nocreate'));
end
parpool('local', 4);  % 使用4个核心
```

### 3. 内存优化
```matlab
% 分批处理大数据集
batch_size = 1000;
for i = 1:batch_size:total_samples
    batch_end = min(i + batch_size - 1, total_samples);
    process_batch(data(i:batch_end, :));
end
```

## 🐛 故障排除

### 常见问题

#### 1. 工具箱缺失
```
错误: Undefined function 'fitensemble'
解决: 安装 Statistics and Machine Learning Toolbox
```

#### 2. 内存不足
```
错误: Out of memory
解决: 减少OptimizationTrials参数或使用分批处理
```

#### 3. 数据格式错误
```
错误: 无法解析文件名
解决: 检查CSV文件命名格式是否正确
```

#### 4. GPU问题
```matlab
% 重置GPU设备
gpuDevice([]);
gpuDevice(1);
```

### 调试模式
```matlab
% 启用详细输出
results = main_vibration_analysis('Verbose', true);

% 检查中间结果
load('matlab_vibration_analysis_results.mat');
```

## 📚 API参考

### 主要函数

#### `main_vibration_analysis(varargin)`
主程序入口，执行完整的振动信号分析流程。

#### `load_and_preprocess_data(data_dir, config)`
数据加载和预处理，支持多种CSV格式。

#### `extract_comprehensive_features(sensor_data)`
综合特征提取，包括时域、频域和小波特征。

#### `train_and_optimize_models(datasets, config)`
模型训练和贝叶斯优化。

#### `generate_visualizations(datasets, model_results, ensemble_results, config)`
生成学术级可视化图表。

## 🔄 与Python版本的对比

| 功能 | Python版本 | MATLAB版本 | 兼容性 |
|------|------------|------------|--------|
| XGBoost | ✅ | 🔄 TreeBagger替代 | 95% |
| CNN-LSTM | ✅ TensorFlow/PyTorch | ✅ Deep Learning Toolbox | 90% |
| 贝叶斯优化 | ✅ Optuna | ✅ bayesopt | 95% |
| 可视化 | ✅ matplotlib | ✅ 内置绘图 | 100% |
| 特征提取 | ✅ | ✅ | 100% |
| 性能目标 | R²>0.90/0.85 | R²>0.90/0.85 | 100% |

## 📞 技术支持

如有问题，请检查：
1. MATLAB版本是否满足要求
2. 必需工具箱是否已安装
3. 数据格式是否正确
4. 系统内存是否充足

## 📄 许可证

本MATLAB版本遵循与原Python版本相同的许可证条款。
