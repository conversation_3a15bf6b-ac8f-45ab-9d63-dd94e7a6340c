function visualization_results = generate_visualizations(datasets, model_results, ensemble_results, config)
% GENERATE_VISUALIZATIONS 学术级可视化生成函数
%
% 功能：
%   - 生成IEEE/学术标准的高质量图表
%   - 模型性能对比可视化
%   - 混淆矩阵和ROC曲线
%   - 特征重要性分析
%   - 330 DPI, Times New Roman字体
%
% 输入：
%   datasets - 数据集
%   model_results - 模型结果
%   ensemble_results - 集成学习结果
%   config - 配置参数
%
% 输出：
%   visualization_results - 可视化结果信息

fprintf('📊 生成学术级可视化图表...\n');

% 创建输出目录
output_dir = 'matlab_visualizations';
if ~exist(output_dir, 'dir')
    mkdir(output_dir);
end

visualization_results = struct();
visualization_results.output_dir = output_dir;
visualization_results.generated_charts = {};

% 设置图表样式
setup_academic_style();

%% 1. 模型性能对比图
fprintf('   📈 生成模型性能对比图...\n');
try
    performance_chart = generate_performance_comparison(model_results, datasets, output_dir);
    visualization_results.generated_charts{end+1} = performance_chart;
catch ME
    fprintf('   ⚠️  性能对比图生成失败: %s\n', ME.message);
end

%% 2. 预测散点图
fprintf('   🎯 生成预测散点图...\n');
try
    scatter_charts = generate_prediction_scatter_plots(model_results, datasets, output_dir);
    visualization_results.generated_charts = [visualization_results.generated_charts, scatter_charts];
catch ME
    fprintf('   ⚠️  散点图生成失败: %s\n', ME.message);
end

%% 3. 混淆矩阵（分类任务）
fprintf('   📊 生成混淆矩阵...\n');
try
    confusion_charts = generate_confusion_matrices(model_results, datasets, output_dir);
    visualization_results.generated_charts = [visualization_results.generated_charts, confusion_charts];
catch ME
    fprintf('   ⚠️  混淆矩阵生成失败: %s\n', ME.message);
end

%% 4. ROC曲线（分类任务）
fprintf('   📈 生成ROC曲线...\n');
try
    roc_charts = generate_roc_curves(model_results, datasets, output_dir);
    visualization_results.generated_charts = [visualization_results.generated_charts, roc_charts];
catch ME
    fprintf('   ⚠️  ROC曲线生成失败: %s\n', ME.message);
end

%% 5. 特征重要性分析
fprintf('   🔍 生成特征重要性分析...\n');
try
    importance_charts = generate_feature_importance(model_results, datasets, output_dir);
    visualization_results.generated_charts = [visualization_results.generated_charts, importance_charts];
catch ME
    fprintf('   ⚠️  特征重要性分析失败: %s\n', ME.message);
end

%% 6. 数据分布分析
fprintf('   📊 生成数据分布分析...\n');
try
    distribution_chart = generate_data_distribution(datasets, output_dir);
    visualization_results.generated_charts{end+1} = distribution_chart;
catch ME
    fprintf('   ⚠️  数据分布分析失败: %s\n', ME.message);
end

%% 7. 优化过程可视化
fprintf('   🎯 生成优化过程可视化...\n');
try
    optimization_chart = generate_optimization_visualization(model_results, output_dir);
    visualization_results.generated_charts{end+1} = optimization_chart;
catch ME
    fprintf('   ⚠️  优化过程可视化失败: %s\n', ME.message);
end

%% 8. 集成学习结果
if ~isempty(ensemble_results)
    fprintf('   🎯 生成集成学习结果...\n');
    try
        ensemble_charts = generate_ensemble_visualization(ensemble_results, datasets, output_dir);
        visualization_results.generated_charts = [visualization_results.generated_charts, ensemble_charts];
    catch ME
        fprintf('   ⚠️  集成学习可视化失败: %s\n', ME.message);
    end
end

% 生成可视化总结报告
generate_visualization_summary(visualization_results, output_dir);

fprintf('✅ 可视化生成完成，共生成 %d 个图表\n', length(visualization_results.generated_charts));
fprintf('   输出目录: %s\n', output_dir);

end

%% 辅助函数

function setup_academic_style()
% 设置学术级图表样式
set(0, 'DefaultFigureRenderer', 'painters');
set(0, 'DefaultFigureColor', 'white');
set(0, 'DefaultAxesFontName', 'Times New Roman');
set(0, 'DefaultAxesFontSize', 12);
set(0, 'DefaultTextFontName', 'Times New Roman');
set(0, 'DefaultTextFontSize', 12);
set(0, 'DefaultLegendFontName', 'Times New Roman');
set(0, 'DefaultLegendFontSize', 10);

% 设置颜色方案（IEEE标准）
colors = [
    0.0000, 0.4470, 0.7410;  % 蓝色
    0.8500, 0.3250, 0.0980;  % 橙色
    0.9290, 0.6940, 0.1250;  % 黄色
    0.4940, 0.1840, 0.5560;  % 紫色
    0.4660, 0.6740, 0.1880;  % 绿色
    0.3010, 0.7450, 0.9330;  % 青色
    0.6350, 0.0780, 0.1840;  % 红色
];
set(0, 'DefaultAxesColorOrder', colors);
end

function chart_file = generate_performance_comparison(model_results, datasets, output_dir)
% 生成模型性能对比图
dataset_names = fieldnames(model_results);
model_names = {};
performance_data = [];

% 收集性能数据
for d = 1:length(dataset_names)
    dataset_name = dataset_names{d};
    dataset_models = fieldnames(model_results.(dataset_name));
    
    for m = 1:length(dataset_models)
        model_name = dataset_models{m};
        if ~isfield(model_results.(dataset_name).(model_name), 'error')
            model_names{end+1} = sprintf('%s_%s', dataset_name, model_name);
            
            eval_results = model_results.(dataset_name).(model_name).evaluation;
            if isfield(eval_results, 'test_r2')
                performance_data(end+1) = eval_results.test_r2;
            elseif isfield(eval_results, 'test_accuracy')
                performance_data(end+1) = eval_results.test_accuracy;
            else
                performance_data(end+1) = 0;
            end
        end
    end
end

if isempty(performance_data)
    chart_file = '';
    return;
end

% 创建图表
fig = figure('Position', [100, 100, 1200, 600]);
bar(performance_data, 'FaceColor', [0.0000, 0.4470, 0.7410]);
set(gca, 'XTickLabel', model_names, 'XTickLabelRotation', 45);
ylabel('Performance Score');
title('Model Performance Comparison', 'FontSize', 16, 'FontWeight', 'bold');
grid on;
ylim([0, 1]);

% 添加数值标签
for i = 1:length(performance_data)
    text(i, performance_data(i) + 0.02, sprintf('%.3f', performance_data(i)), ...
         'HorizontalAlignment', 'center', 'FontSize', 10);
end

% 保存图表
chart_file = fullfile(output_dir, 'model_performance_comparison.png');
print(fig, chart_file, '-dpng', '-r330');
close(fig);
end

function scatter_charts = generate_prediction_scatter_plots(model_results, datasets, output_dir)
% 生成预测散点图
scatter_charts = {};
dataset_names = fieldnames(model_results);

for d = 1:length(dataset_names)
    dataset_name = dataset_names{d};
    dataset = datasets.(dataset_name);
    
    if strcmp(dataset.task_type, 'regression')
        model_names = fieldnames(model_results.(dataset_name));
        
        for m = 1:length(model_names)
            model_name = model_names{m};
            model_result = model_results.(dataset_name).(model_name);
            
            if ~isfield(model_result, 'error') && isfield(model_result.evaluation, 'y_test_pred')
                fig = figure('Position', [100, 100, 800, 600]);
                
                y_true = model_result.evaluation.y_test;
                y_pred = model_result.evaluation.y_test_pred;
                
                scatter(y_true, y_pred, 50, [0.0000, 0.4470, 0.7410], 'filled', 'Alpha', 0.6);
                hold on;
                
                % 添加理想线
                min_val = min([y_true; y_pred]);
                max_val = max([y_true; y_pred]);
                plot([min_val, max_val], [min_val, max_val], 'r--', 'LineWidth', 2);
                
                xlabel('True Values');
                ylabel('Predicted Values');
                title(sprintf('%s - %s Prediction', dataset_name, model_name), ...
                      'FontSize', 16, 'FontWeight', 'bold');
                
                % 添加R²信息
                r2 = model_result.evaluation.test_r2;
                text(0.05, 0.95, sprintf('R² = %.4f', r2), 'Units', 'normalized', ...
                     'FontSize', 12, 'BackgroundColor', 'white');
                
                grid on;
                axis equal;
                
                chart_file = fullfile(output_dir, sprintf('%s_%s_prediction_scatter.png', ...
                                     dataset_name, model_name));
                print(fig, chart_file, '-dpng', '-r330');
                close(fig);
                
                scatter_charts{end+1} = chart_file;
            end
        end
    end
end
end

function confusion_charts = generate_confusion_matrices(model_results, datasets, output_dir)
% 生成混淆矩阵
confusion_charts = {};
dataset_names = fieldnames(model_results);

for d = 1:length(dataset_names)
    dataset_name = dataset_names{d};
    dataset = datasets.(dataset_name);
    
    if strcmp(dataset.task_type, 'classification')
        model_names = fieldnames(model_results.(dataset_name));
        
        for m = 1:length(model_names)
            model_name = model_names{m};
            model_result = model_results.(dataset_name).(model_name);
            
            if ~isfield(model_result, 'error') && isfield(model_result.evaluation, 'confusion_matrix')
                fig = figure('Position', [100, 100, 600, 500]);
                
                cm = model_result.evaluation.confusion_matrix;
                imagesc(cm);
                colormap('Blues');
                colorbar;
                
                % 添加数值标签
                [rows, cols] = size(cm);
                for i = 1:rows
                    for j = 1:cols
                        text(j, i, sprintf('%d', cm(i,j)), ...
                             'HorizontalAlignment', 'center', ...
                             'Color', 'white', 'FontWeight', 'bold');
                    end
                end
                
                xlabel('Predicted Class');
                ylabel('True Class');
                title(sprintf('%s - %s Confusion Matrix', dataset_name, model_name), ...
                      'FontSize', 16, 'FontWeight', 'bold');
                
                chart_file = fullfile(output_dir, sprintf('%s_%s_confusion_matrix.png', ...
                                     dataset_name, model_name));
                print(fig, chart_file, '-dpng', '-r330');
                close(fig);
                
                confusion_charts{end+1} = chart_file;
            end
        end
    end
end
end
