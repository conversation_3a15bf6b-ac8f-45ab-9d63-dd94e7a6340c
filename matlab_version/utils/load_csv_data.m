function raw_data = load_csv_data(file_path, data_format)
% LOAD_CSV_DATA CSV数据加载函数
%
% 功能：
%   - 加载不同格式的CSV文件
%   - 处理中文文件名和编码问题
%   - 自动检测和处理数据格式
%
% 输入：
%   file_path - CSV文件路径
%   data_format - 数据格式类型
%
% 输出：
%   raw_data - 原始数据表格

raw_data = [];

if ~exist(file_path, 'file')
    warning('文件不存在: %s', file_path);
    return;
end

try
    % 检测文件编码和格式
    opts = detectImportOptions(file_path);
    
    % 根据数据格式调整导入选项
    switch data_format
        case 'new_22_columns'
            % 新格式：22列数据
            expected_cols = 22;
            
        case 'legacy_21_columns'
            % 传统格式：21列数据
            expected_cols = 21;
            
        otherwise
            % 未知格式，尝试自动检测
            expected_cols = [];
    end
    
    % 设置导入选项
    opts.Delimiter = ',';
    opts.ConsecutiveDelimitersRule = 'join';
    opts.LeadingDelimitersRule = 'ignore';
    opts.TrailingDelimitersRule = 'ignore';
    opts.EmptyLineRule = 'skip';
    
    % 处理变量名
    if ~isempty(expected_cols) && width(opts) ~= expected_cols
        warning('文件 %s 的列数 (%d) 与期望的列数 (%d) 不匹配', ...
                file_path, width(opts), expected_cols);
    end
    
    % 设置所有列为数值类型
    for i = 1:width(opts)
        opts = setvartype(opts, i, 'double');
    end
    
    % 读取数据
    raw_data = readtable(file_path, opts);
    
    % 数据验证
    if isempty(raw_data) || height(raw_data) == 0
        warning('文件 %s 为空或无法读取', file_path);
        raw_data = [];
        return;
    end
    
    % 检查数据质量
    if width(raw_data) < 20
        warning('文件 %s 的列数太少 (%d < 20)', file_path, width(raw_data));
        raw_data = [];
        return;
    end
    
    if height(raw_data) < 100
        warning('文件 %s 的行数太少 (%d < 100)', file_path, height(raw_data));
        raw_data = [];
        return;
    end
    
    % 处理缺失值和异常值
    raw_data = clean_raw_data(raw_data);
    
    % 最终验证
    if isempty(raw_data)
        warning('文件 %s 在清洗后无有效数据', file_path);
        return;
    end
    
catch ME
    warning('读取文件 %s 时发生错误: %s', file_path, ME.message);
    raw_data = [];
end

end

function cleaned_data = clean_raw_data(raw_data)
% 清洗原始数据
cleaned_data = raw_data;

if isempty(raw_data)
    return;
end

% 1. 移除完全为NaN的行
all_nan_rows = all(isnan(table2array(raw_data)), 2);
cleaned_data(all_nan_rows, :) = [];

if isempty(cleaned_data)
    return;
end

% 2. 处理无穷大值
data_array = table2array(cleaned_data);
inf_mask = isinf(data_array);
if any(inf_mask(:))
    % 将无穷大值替换为NaN
    data_array(inf_mask) = NaN;
    
    % 转换回表格
    cleaned_data = array2table(data_array, 'VariableNames', cleaned_data.Properties.VariableNames);
end

% 3. 检测和处理异常值（使用IQR方法）
data_array = table2array(cleaned_data);
[n_rows, n_cols] = size(data_array);

for col = 1:n_cols
    column_data = data_array(:, col);
    valid_data = column_data(~isnan(column_data));
    
    if length(valid_data) > 10 % 需要足够的数据点来计算统计量
        Q1 = quantile(valid_data, 0.25);
        Q3 = quantile(valid_data, 0.75);
        IQR = Q3 - Q1;
        
        if IQR > 0
            lower_bound = Q1 - 3 * IQR; % 使用3倍IQR作为更宽松的阈值
            upper_bound = Q3 + 3 * IQR;
            
            % 标记异常值
            outlier_mask = column_data < lower_bound | column_data > upper_bound;
            
            % 如果异常值太多（>20%），则不进行处理
            outlier_ratio = sum(outlier_mask) / length(column_data);
            if outlier_ratio <= 0.2
                data_array(outlier_mask, col) = NaN;
            end
        end
    end
end

% 4. 移除缺失值过多的行（>50%缺失）
missing_ratio = sum(isnan(data_array), 2) / n_cols;
valid_rows = missing_ratio <= 0.5;
data_array = data_array(valid_rows, :);

if isempty(data_array)
    cleaned_data = [];
    return;
end

% 5. 对剩余的缺失值进行插值
for col = 1:n_cols
    column_data = data_array(:, col);
    nan_mask = isnan(column_data);
    
    if any(nan_mask) && ~all(nan_mask)
        % 使用线性插值填充缺失值
        valid_indices = find(~nan_mask);
        if length(valid_indices) >= 2
            try
                data_array(nan_mask, col) = interp1(valid_indices, ...
                    column_data(valid_indices), find(nan_mask), 'linear', 'extrap');
            catch
                % 插值失败，使用均值填充
                data_array(nan_mask, col) = mean(column_data(~nan_mask));
            end
        else
            % 只有一个有效值，用该值填充
            data_array(nan_mask, col) = column_data(valid_indices(1));
        end
    end
end

% 6. 最终检查：移除仍然包含NaN的行
remaining_nan_rows = any(isnan(data_array), 2);
data_array(remaining_nan_rows, :) = [];

if isempty(data_array)
    cleaned_data = [];
    return;
end

% 转换回表格格式
cleaned_data = array2table(data_array, 'VariableNames', raw_data.Properties.VariableNames);

% 7. 数据范围检查
% 检查是否有列的值全部相同（常数列）
data_array = table2array(cleaned_data);
constant_cols = std(data_array) < 1e-10;

if any(constant_cols)
    % 移除常数列，但保留至少20列
    non_constant_cols = find(~constant_cols);
    if length(non_constant_cols) >= 20
        cleaned_data = cleaned_data(:, non_constant_cols);
    end
end

% 8. 最终大小检查
if height(cleaned_data) < 50 || width(cleaned_data) < 15
    cleaned_data = [];
    return;
end

end
