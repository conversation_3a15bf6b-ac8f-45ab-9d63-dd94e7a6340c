function event_segments = detect_vehicle_events(sensor_data)
% DETECT_VEHICLE_EVENTS 车辆通过事件检测函数
%
% 功能：
%   - 检测振动信号中的车辆通过事件
%   - 基于信号幅值和能量变化识别事件
%   - 提取1秒（1000个数据点）的事件段
%
% 输入：
%   sensor_data - 传感器数据矩阵 [时间点 × 传感器数量]
%
% 输出：
%   event_segments - 事件段元胞数组

if isempty(sensor_data) || size(sensor_data, 1) < 1000
    event_segments = {};
    return;
end

[n_samples, n_sensors] = size(sensor_data);
fs = 1000; % 采样频率
event_segments = {};

% 计算多传感器融合信号
valid_sensors = [];
for i = 1:n_sensors
    if std(sensor_data(:, i)) > 1e-6 && ~all(isnan(sensor_data(:, i)))
        valid_sensors = [valid_sensors, i];
    end
end

if length(valid_sensors) < 2
    event_segments = {};
    return;
end

% 使用有效传感器的加权平均作为融合信号
weights = 1 ./ (std(sensor_data(:, valid_sensors)) + eps);
weights = weights / sum(weights);
fused_signal = sensor_data(:, valid_sensors) * weights';

% 信号预处理
% 1. 去除直流分量
fused_signal = fused_signal - mean(fused_signal);

% 2. 带通滤波（1-200 Hz）
if license('test', 'signal')
    try
        [b, a] = butter(4, [1, 200]/(fs/2), 'bandpass');
        fused_signal = filtfilt(b, a, fused_signal);
    catch
        % 滤波失败，使用原信号
    end
end

% 3. 计算信号包络
envelope = abs(hilbert(fused_signal));

% 4. 平滑包络
window_size = round(0.1 * fs); % 100ms窗口
if window_size > 1
    envelope = movmean(envelope, window_size);
end

% 事件检测参数
min_event_duration = 0.5 * fs; % 最小事件持续时间：0.5秒
max_event_duration = 3.0 * fs; % 最大事件持续时间：3秒
event_segment_length = 1.0 * fs; % 提取的事件段长度：1秒

% 动态阈值计算
baseline_level = median(envelope);
noise_level = mad(envelope, 1); % 中位数绝对偏差
threshold = baseline_level + 3 * noise_level;

% 如果阈值太低，使用信号标准差的倍数
if threshold < 2 * std(fused_signal)
    threshold = 2 * std(fused_signal);
end

% 检测超过阈值的区域
above_threshold = envelope > threshold;

% 查找连续的超阈值区域
diff_above = diff([0; above_threshold; 0]);
event_starts = find(diff_above == 1);
event_ends = find(diff_above == -1) - 1;

% 过滤事件
valid_events = [];
for i = 1:length(event_starts)
    event_duration = event_ends(i) - event_starts(i) + 1;
    
    % 检查事件持续时间
    if event_duration >= min_event_duration && event_duration <= max_event_duration
        % 检查事件强度
        event_signal = envelope(event_starts(i):event_ends(i));
        event_peak = max(event_signal);
        
        if event_peak > threshold * 1.5 % 峰值必须显著高于阈值
            valid_events = [valid_events, i];
        end
    end
end

% 提取事件段
for i = 1:length(valid_events)
    event_idx = valid_events(i);
    event_start = event_starts(event_idx);
    event_end = event_ends(event_idx);
    
    % 找到事件中的最大值位置
    event_envelope = envelope(event_start:event_end);
    [~, max_idx] = max(event_envelope);
    peak_position = event_start + max_idx - 1;
    
    % 以峰值为中心提取1秒数据
    segment_start = peak_position - round(event_segment_length / 2);
    segment_end = peak_position + round(event_segment_length / 2) - 1;
    
    % 确保段在有效范围内
    if segment_start < 1
        segment_start = 1;
        segment_end = min(event_segment_length, n_samples);
    elseif segment_end > n_samples
        segment_end = n_samples;
        segment_start = max(1, n_samples - event_segment_length + 1);
    end
    
    % 提取事件段
    if segment_end - segment_start + 1 >= round(0.8 * event_segment_length) % 至少80%的目标长度
        event_segment = sensor_data(segment_start:segment_end, :);
        
        % 验证事件段质量
        if validate_event_segment(event_segment)
            event_segments{end+1} = event_segment;
        end
    end
end

% 如果没有检测到事件，尝试降低阈值
if isempty(event_segments) && length(fused_signal) >= event_segment_length
    % 使用更宽松的阈值
    relaxed_threshold = baseline_level + 1.5 * noise_level;
    
    % 简单地从信号最强的部分提取一个段
    [~, max_idx] = max(envelope);
    
    segment_start = max(1, max_idx - round(event_segment_length / 2));
    segment_end = min(n_samples, segment_start + event_segment_length - 1);
    
    if segment_end - segment_start + 1 >= round(0.8 * event_segment_length)
        event_segment = sensor_data(segment_start:segment_end, :);
        
        if validate_event_segment(event_segment)
            event_segments{end+1} = event_segment;
        end
    end
end

end

function is_valid = validate_event_segment(event_segment)
% 验证事件段的质量
is_valid = false;

if isempty(event_segment)
    return;
end

[n_samples, n_sensors] = size(event_segment);

% 检查长度
if n_samples < 500 % 至少0.5秒
    return;
end

% 检查有效传感器数量
valid_sensor_count = 0;
for i = 1:n_sensors
    sensor_signal = event_segment(:, i);
    if ~all(isnan(sensor_signal)) && std(sensor_signal) > 1e-6
        valid_sensor_count = valid_sensor_count + 1;
    end
end

if valid_sensor_count < 3 % 至少3个有效传感器
    return;
end

% 检查信号动态范围
signal_ranges = range(event_segment, 1);
median_range = median(signal_ranges(signal_ranges > 0));

if median_range < 1e-3 % 信号变化太小
    return;
end

% 检查信号是否包含明显的车辆通过特征
% 计算信号能量的时间分布
energy_profile = sum(event_segment.^2, 2);
energy_profile = movmean(energy_profile, round(0.1 * length(energy_profile)));

% 寻找能量峰值
[~, peak_idx] = max(energy_profile);
peak_ratio = energy_profile(peak_idx) / mean(energy_profile);

if peak_ratio > 2 % 峰值应该显著高于平均值
    is_valid = true;
end

end
