function [datasets, feature_info] = prepare_training_datasets(features_table, config)
% PREPARE_TRAINING_DATASETS 准备训练数据集函数
%
% 功能：
%   - 从特征表中准备不同任务的数据集
%   - 速度预测、轴重预测、轴型分类
%   - 数据分层和平衡处理
%   - 特征选择和工程
%
% 输入：
%   features_table - 特征数据表
%   config - 配置参数
%
% 输出：
%   datasets - 数据集结构体
%   feature_info - 特征信息

fprintf('🔧 准备训练数据集...\n');

datasets = struct();
feature_info = struct();

if isempty(features_table) || height(features_table) == 0
    fprintf('❌ 特征表为空，无法准备数据集\n');
    return;
end

% 获取所有数值特征列
all_vars = features_table.Properties.VariableNames;
target_vars = {'speed_kmh', 'load_tons', 'axle_type', 'lane', 'file_index', 'event_index'};
feature_vars = setdiff(all_vars, target_vars);

% 过滤数值特征
numeric_features = {};
for i = 1:length(feature_vars)
    var_name = feature_vars{i};
    if isnumeric(features_table.(var_name))
        numeric_features{end+1} = var_name;
    end
end

if isempty(numeric_features)
    fprintf('❌ 没有找到有效的数值特征\n');
    return;
end

fprintf('   发现 %d 个数值特征\n', length(numeric_features));

% 提取特征矩阵
X = table2array(features_table(:, numeric_features));

% 特征质量检查和清洗
[X_clean, valid_features, cleaning_info] = clean_feature_matrix(X, numeric_features);

if isempty(X_clean)
    fprintf('❌ 特征清洗后无有效数据\n');
    return;
end

fprintf('   特征清洗完成：%d -> %d 特征\n', length(numeric_features), length(valid_features));

% 更新特征名称
clean_feature_names = numeric_features(valid_features);

%% 1. 速度预测数据集
if ismember('speed_kmh', all_vars)
    fprintf('   📊 准备速度预测数据集...\n');
    
    speed_values = features_table.speed_kmh;
    valid_speed_mask = ~isnan(speed_values) & speed_values > 0 & speed_values <= 200;
    
    if sum(valid_speed_mask) >= 50
        speed_dataset = struct();
        speed_dataset.X = X_clean(valid_speed_mask, :);
        speed_dataset.y = speed_values(valid_speed_mask);
        speed_dataset.feature_names = clean_feature_names;
        speed_dataset.task_type = 'regression';
        speed_dataset.target_name = 'speed_kmh';
        speed_dataset.target_unit = 'km/h';
        
        % 数据统计
        speed_dataset.stats = struct();
        speed_dataset.stats.n_samples = length(speed_dataset.y);
        speed_dataset.stats.y_range = [min(speed_dataset.y), max(speed_dataset.y)];
        speed_dataset.stats.y_mean = mean(speed_dataset.y);
        speed_dataset.stats.y_std = std(speed_dataset.y);
        
        datasets.speed_prediction = speed_dataset;
        fprintf('      ✅ 速度预测数据集：%d 样本，范围 %.1f-%.1f km/h\n', ...
                speed_dataset.stats.n_samples, speed_dataset.stats.y_range(1), speed_dataset.stats.y_range(2));
    else
        fprintf('      ⚠️  速度数据不足，跳过速度预测数据集\n');
    end
end

%% 2. 轴重预测数据集
if ismember('load_tons', all_vars)
    fprintf('   📊 准备轴重预测数据集...\n');
    
    load_values = features_table.load_tons;
    valid_load_mask = ~isnan(load_values) & load_values > 0 & load_values <= 100;
    
    if sum(valid_load_mask) >= 50
        load_dataset = struct();
        load_dataset.X = X_clean(valid_load_mask, :);
        load_dataset.y = load_values(valid_load_mask);
        load_dataset.feature_names = clean_feature_names;
        load_dataset.task_type = 'regression';
        load_dataset.target_name = 'load_tons';
        load_dataset.target_unit = 'tons';
        
        % 数据统计
        load_dataset.stats = struct();
        load_dataset.stats.n_samples = length(load_dataset.y);
        load_dataset.stats.y_range = [min(load_dataset.y), max(load_dataset.y)];
        load_dataset.stats.y_mean = mean(load_dataset.y);
        load_dataset.stats.y_std = std(load_dataset.y);
        
        datasets.load_prediction = load_dataset;
        fprintf('      ✅ 轴重预测数据集：%d 样本，范围 %.1f-%.1f 吨\n', ...
                load_dataset.stats.n_samples, load_dataset.stats.y_range(1), load_dataset.stats.y_range(2));
    else
        fprintf('      ⚠️  轴重数据不足，跳过轴重预测数据集\n');
    end
end

%% 3. 轴型分类数据集
if ismember('axle_type', all_vars)
    fprintf('   📊 准备轴型分类数据集...\n');
    
    axle_types = features_table.axle_type;
    valid_axle_mask = ~cellfun(@isempty, axle_types) & ~strcmp(axle_types, '');
    
    if sum(valid_axle_mask) >= 50
        % 统计轴型分布
        unique_axles = unique(axle_types(valid_axle_mask));
        axle_counts = zeros(size(unique_axles));
        
        for i = 1:length(unique_axles)
            axle_counts(i) = sum(strcmp(axle_types(valid_axle_mask), unique_axles{i}));
        end
        
        % 过滤样本数量太少的类别（至少10个样本）
        valid_axle_types = unique_axles(axle_counts >= 10);
        
        if length(valid_axle_types) >= 2
            % 创建最终的掩码
            final_axle_mask = valid_axle_mask & ismember(axle_types, valid_axle_types);
            
            axle_dataset = struct();
            axle_dataset.X = X_clean(final_axle_mask, :);
            axle_dataset.y = axle_types(final_axle_mask);
            axle_dataset.feature_names = clean_feature_names;
            axle_dataset.task_type = 'classification';
            axle_dataset.target_name = 'axle_type';
            axle_dataset.target_unit = 'category';
            
            % 数据统计
            axle_dataset.stats = struct();
            axle_dataset.stats.n_samples = length(axle_dataset.y);
            axle_dataset.stats.n_classes = length(valid_axle_types);
            axle_dataset.stats.class_names = valid_axle_types;
            
            % 计算类别分布
            class_distribution = zeros(size(valid_axle_types));
            for i = 1:length(valid_axle_types)
                class_distribution(i) = sum(strcmp(axle_dataset.y, valid_axle_types{i}));
            end
            axle_dataset.stats.class_distribution = class_distribution;
            
            datasets.axle_classification = axle_dataset;
            fprintf('      ✅ 轴型分类数据集：%d 样本，%d 类别\n', ...
                    axle_dataset.stats.n_samples, axle_dataset.stats.n_classes);
            
            % 显示类别分布
            for i = 1:length(valid_axle_types)
                fprintf('         %s: %d 样本 (%.1f%%)\n', valid_axle_types{i}, ...
                        class_distribution(i), class_distribution(i)/sum(class_distribution)*100);
            end
        else
            fprintf('      ⚠️  轴型类别不足，跳过轴型分类数据集\n');
        end
    else
        fprintf('      ⚠️  轴型数据不足，跳过轴型分类数据集\n');
    end
end

%% 4. 特征工程增强
if ~isempty(datasets)
    fprintf('   🔧 应用特征工程增强...\n');
    datasets = apply_feature_engineering(datasets);
end

%% 5. 生成特征信息
feature_info.original_features = numeric_features;
feature_info.clean_features = clean_feature_names;
feature_info.cleaning_info = cleaning_info;
feature_info.n_original_features = length(numeric_features);
feature_info.n_clean_features = length(clean_feature_names);
feature_info.feature_retention_rate = length(clean_feature_names) / length(numeric_features);

% 数据集总结
dataset_names = fieldnames(datasets);
feature_info.datasets = struct();
for i = 1:length(dataset_names)
    dataset_name = dataset_names{i};
    dataset = datasets.(dataset_name);
    
    feature_info.datasets.(dataset_name) = struct();
    feature_info.datasets.(dataset_name).n_samples = size(dataset.X, 1);
    feature_info.datasets.(dataset_name).n_features = size(dataset.X, 2);
    feature_info.datasets.(dataset_name).task_type = dataset.task_type;
    feature_info.datasets.(dataset_name).target_name = dataset.target_name;
end

fprintf('✅ 数据集准备完成\n');
fprintf('   总数据集数量: %d\n', length(dataset_names));
fprintf('   特征保留率: %.1f%% (%d/%d)\n', ...
        feature_info.feature_retention_rate*100, ...
        feature_info.n_clean_features, feature_info.n_original_features);

end

%% 辅助函数

function [X_clean, valid_features, cleaning_info] = clean_feature_matrix(X, feature_names)
% 清洗特征矩阵
cleaning_info = struct();

% 1. 移除包含NaN或Inf的特征
valid_features = true(1, size(X, 2));

for i = 1:size(X, 2)
    feature_data = X(:, i);
    
    % 检查NaN和Inf
    if any(isnan(feature_data)) || any(isinf(feature_data))
        valid_features(i) = false;
        continue;
    end
    
    % 检查常数特征
    if std(feature_data) < 1e-10
        valid_features(i) = false;
        continue;
    end
    
    % 检查异常值比例
    Q1 = quantile(feature_data, 0.25);
    Q3 = quantile(feature_data, 0.75);
    IQR = Q3 - Q1;
    
    if IQR > 0
        outlier_mask = feature_data < (Q1 - 3*IQR) | feature_data > (Q3 + 3*IQR);
        outlier_ratio = sum(outlier_mask) / length(feature_data);
        
        if outlier_ratio > 0.3 % 超过30%的异常值
            valid_features(i) = false;
            continue;
        end
    end
end

X_clean = X(:, valid_features);

% 记录清洗信息
cleaning_info.original_features = length(feature_names);
cleaning_info.valid_features = sum(valid_features);
cleaning_info.removed_features = sum(~valid_features);
cleaning_info.retention_rate = sum(valid_features) / length(feature_names);

end

function datasets_enhanced = apply_feature_engineering(datasets)
% 应用特征工程增强
datasets_enhanced = datasets;
dataset_names = fieldnames(datasets);

for i = 1:length(dataset_names)
    dataset_name = dataset_names{i};
    dataset = datasets.(dataset_name);
    
    X = dataset.X;
    
    % 1. 特征标准化
    X_mean = mean(X);
    X_std = std(X);
    X_std(X_std == 0) = 1; % 避免除零
    X_normalized = (X - X_mean) ./ X_std;
    
    % 2. 添加多项式特征（选择性）
    if size(X, 2) <= 20 % 只对特征数量较少的情况添加
        % 添加平方特征
        X_squared = X_normalized.^2;
        X_enhanced = [X_normalized, X_squared];
        
        % 更新特征名称
        original_names = dataset.feature_names;
        squared_names = cellfun(@(x) [x, '_squared'], original_names, 'UniformOutput', false);
        enhanced_names = [original_names, squared_names];
    else
        X_enhanced = X_normalized;
        enhanced_names = dataset.feature_names;
    end
    
    % 3. 特征选择（基于方差）
    feature_vars = var(X_enhanced);
    high_var_features = feature_vars > quantile(feature_vars, 0.1); % 保留方差前90%的特征
    
    X_final = X_enhanced(:, high_var_features);
    final_names = enhanced_names(high_var_features);
    
    % 更新数据集
    datasets_enhanced.(dataset_name).X = X_final;
    datasets_enhanced.(dataset_name).feature_names = final_names;
    datasets_enhanced.(dataset_name).preprocessing = struct();
    datasets_enhanced.(dataset_name).preprocessing.mean = X_mean;
    datasets_enhanced.(dataset_name).preprocessing.std = X_std;
    datasets_enhanced.(dataset_name).preprocessing.selected_features = high_var_features;
end

end
