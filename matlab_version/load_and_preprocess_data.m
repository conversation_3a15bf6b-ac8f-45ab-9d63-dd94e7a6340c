function [features_table, data_info] = load_and_preprocess_data(data_dir, config)
% LOAD_AND_PREPROCESS_DATA 数据加载和预处理函数
%
% 功能：
%   - 自动检测CSV文件格式（21列传统格式 vs 22列新格式）
%   - 批量加载和处理振动信号数据
%   - 提取时域和频域特征
%   - 数据清洗和质量控制
%
% 输入：
%   data_dir - 数据目录路径
%   config - 配置参数结构体
%
% 输出：
%   features_table - 特征数据表
%   data_info - 数据信息结构体

fprintf('📂 扫描数据文件...\n');

% 查找所有CSV文件
csv_files = dir(fullfile(data_dir, '**/*.csv'));
fprintf('   发现 %d 个CSV文件\n', length(csv_files));

if isempty(csv_files)
    error('❌ 数据目录中未找到CSV文件');
end

% 初始化数据存储
all_features = [];
file_info = [];
processing_errors = {};

% 检测数据格式
fprintf('🔍 检测数据格式...\n');
sample_file = fullfile(csv_files(1).folder, csv_files(1).name);
[data_format, column_info] = detect_data_format(sample_file);
fprintf('   检测到数据格式: %s\n', data_format);

% 批量处理文件
fprintf('⚙️  批量处理数据文件...\n');
processed_count = 0;
error_count = 0;

for i = 1:length(csv_files)
    file_path = fullfile(csv_files(i).folder, csv_files(i).name);
    
    if config.Verbose && mod(i, 50) == 0
        fprintf('   处理进度: %d/%d (%.1f%%)\n', i, length(csv_files), i/length(csv_files)*100);
    end
    
    try
        % 解析文件名获取标签信息
        [speed, load, axle_type, lane] = parse_filename(csv_files(i).name, data_format);
        
        % 跳过无效数据
        if isnan(speed) || isnan(load) || isempty(axle_type)
            continue;
        end
        
        % 加载CSV数据
        raw_data = load_csv_data(file_path, data_format);
        
        if isempty(raw_data)
            continue;
        end
        
        % 提取传感器数据
        sensor_data = extract_sensor_data(raw_data, data_format);
        
        % 数据质量检查
        if ~validate_sensor_data(sensor_data)
            continue;
        end
        
        % 车辆通过事件检测
        event_segments = detect_vehicle_events(sensor_data);
        
        % 特征提取
        for j = 1:length(event_segments)
            features = extract_comprehensive_features(event_segments{j});
            
            if ~isempty(features)
                % 添加标签信息
                features.speed_kmh = speed;
                features.load_tons = load;
                features.axle_type = axle_type;
                features.lane = lane;
                features.file_index = i;
                features.event_index = j;
                
                all_features = [all_features; features];
                processed_count = processed_count + 1;
            end
        end
        
        % 记录文件信息
        file_info = [file_info; struct('filename', csv_files(i).name, ...
                                      'speed', speed, 'load', load, ...
                                      'axle_type', axle_type, 'lane', lane, ...
                                      'events_found', length(event_segments))];
        
    catch ME
        error_count = error_count + 1;
        processing_errors{end+1} = sprintf('文件 %s: %s', csv_files(i).name, ME.message);
        
        if config.Verbose
            fprintf('   ⚠️  处理文件失败: %s\n', csv_files(i).name);
        end
    end
end

fprintf('✅ 数据处理完成\n');
fprintf('   成功处理: %d 个事件\n', processed_count);
fprintf('   处理错误: %d 个文件\n', error_count);

if processed_count == 0
    error('❌ 没有成功处理任何数据文件');
end

% 转换为表格格式
features_table = struct2table(all_features);

% 数据清洗
fprintf('🧹 数据清洗...\n');
original_size = height(features_table);

% 移除缺失值
features_table = rmmissing(features_table);

% 移除异常值（使用IQR方法）
numeric_vars = varfun(@isnumeric, features_table, 'output', 'uniform');
numeric_cols = features_table.Properties.VariableNames(numeric_vars);

for i = 1:length(numeric_cols)
    col_name = numeric_cols{i};
    if ~ismember(col_name, {'speed_kmh', 'load_tons', 'file_index', 'event_index'})
        data = features_table.(col_name);
        Q1 = quantile(data, 0.25);
        Q3 = quantile(data, 0.75);
        IQR = Q3 - Q1;
        lower_bound = Q1 - 1.5 * IQR;
        upper_bound = Q3 + 1.5 * IQR;
        
        outlier_mask = data < lower_bound | data > upper_bound;
        features_table(outlier_mask, :) = [];
    end
end

cleaned_size = height(features_table);
fprintf('   原始样本数: %d\n', original_size);
fprintf('   清洗后样本数: %d\n', cleaned_size);
fprintf('   数据保留率: %.1f%%\n', cleaned_size/original_size*100);

% 生成数据信息
data_info = struct();
data_info.total_files = length(csv_files);
data_info.processed_files = length(file_info);
data_info.error_files = error_count;
data_info.total_events = original_size;
data_info.clean_events = cleaned_size;
data_info.data_format = data_format;
data_info.column_info = column_info;
data_info.file_info = file_info;
data_info.processing_errors = processing_errors;

% 数据分布统计
data_info.speed_range = [min(features_table.speed_kmh), max(features_table.speed_kmh)];
data_info.load_range = [min(features_table.load_tons), max(features_table.load_tons)];
data_info.axle_types = unique(features_table.axle_type);
data_info.lanes = unique(features_table.lane);

fprintf('📊 数据统计:\n');
fprintf('   速度范围: %.1f - %.1f km/h\n', data_info.speed_range(1), data_info.speed_range(2));
fprintf('   载重范围: %.1f - %.1f 吨\n', data_info.load_range(1), data_info.load_range(2));
fprintf('   轴型类别: %s\n', strjoin(data_info.axle_types, ', '));
fprintf('   车道数量: %d\n', length(data_info.lanes));

end

%% 辅助函数

function [data_format, column_info] = detect_data_format(file_path)
% 检测数据格式（21列 vs 22列）
try
    % 尝试读取前几行
    opts = detectImportOptions(file_path);
    sample_data = readtable(file_path, opts, 'Range', '1:5');
    
    num_cols = width(sample_data);
    column_info = sample_data.Properties.VariableNames;
    
    if num_cols == 21
        data_format = 'legacy_21_columns';
    elseif num_cols == 22
        data_format = 'new_22_columns';
    else
        data_format = sprintf('unknown_%d_columns', num_cols);
    end
    
catch
    data_format = 'unknown';
    column_info = {};
end
end

function [speed, load, axle_type, lane] = parse_filename(filename, data_format)
% 从文件名解析车辆信息
speed = NaN;
load = NaN;
axle_type = '';
lane = '';

try
    if strcmp(data_format, 'new_22_columns')
        % 新格式: GW100001_datetime_AcceData_lane_axles-weight-speed.csv
        pattern = 'GW\d+_\d+_AcceData_车道(\d+)_(\d+)轴-([0-9.]+)t-(\d+)kmh\.csv';
        tokens = regexp(filename, pattern, 'tokens');
        
        if ~isempty(tokens)
            lane = str2double(tokens{1}{1});
            axle_type = sprintf('%d轴', str2double(tokens{1}{2}));
            load = str2double(tokens{1}{3});
            speed = str2double(tokens{1}{4});
        end
    else
        % 传统格式: weight_axle_speed_experiment.csv
        pattern = '([0-9.]+)吨_([^_]+)_([0-9.]+)kmh?_实验\d+';
        tokens = regexp(filename, pattern, 'tokens');
        
        if ~isempty(tokens)
            load = str2double(tokens{1}{1});
            axle_type = tokens{1}{2};
            speed = str2double(tokens{1}{3});
            lane = 1; % 默认车道
        end
    end
catch
    % 解析失败，返回默认值
end
end
