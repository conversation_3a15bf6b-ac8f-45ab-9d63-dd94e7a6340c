#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新格式数据预处理模块
处理新命名格式的CSV文件，使其与现有振动信号分析系统兼容

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import os
import re
import shutil
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

class NewDataPreprocessor:
    """新格式数据预处理器"""
    
    def __init__(self, input_dir: str, output_dir: str = "preprocessed_data"):
        """
        初始化预处理器
        
        参数:
        input_dir: 输入数据目录（包含新格式CSV文件）
        output_dir: 输出数据目录（转换后的数据）
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.processed_files = []
        self.failed_files = []
        self.file_mapping = {}  # 原文件名到新文件名的映射
        
        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🔧 新格式数据预处理器已初始化")
        print(f"   输入目录: {self.input_dir}")
        print(f"   输出目录: {self.output_dir}")
    
    def parse_filename(self, filename: str) -> Dict[str, any]:
        """
        解析新格式文件名，提取车辆参数信息
        
        文件名格式：监测点位_监测时间_AcceData_车道_轴型_载重_速度
        示例：GW100001_20231101174605_AcceData_车道1_2轴-2.5t-100kmh
        
        参数:
        filename: 文件名
        
        返回:
        parsed_info: 解析出的信息字典
        """
        try:
            # 移除文件扩展名
            name_without_ext = filename.replace('.csv', '')

            # 使用正则表达式匹配整个文件名格式
            # 格式：监测点位_监测时间_AcceData_车道_轴型-载重-速度
            pattern = r'^([A-Z0-9]+)_(\d{14})_AcceData_(.+?)_(\d+)轴-(\d+\.?\d*)t-(\d+)km/?h?$'

            match = re.match(pattern, name_without_ext)

            if not match:
                # 尝试更宽松的匹配
                # 按下划线分割并尝试提取信息
                parts = name_without_ext.split('_')

                if len(parts) < 4 or 'AcceData' not in parts:
                    print(f"   ⚠️  文件名格式不符合预期: {filename}")
                    return None

                # 找到AcceData的位置
                acce_index = parts.index('AcceData')

                if acce_index < 2 or acce_index >= len(parts) - 1:
                    print(f"   ⚠️  文件名格式不符合预期: {filename}")
                    return None

                monitoring_point = parts[0]
                monitoring_time = parts[1]
                data_type = parts[acce_index]
                lane_info = parts[acce_index + 1]

                # 处理剩余部分（轴型_载重_速度）
                remaining_parts = '_'.join(parts[acce_index + 2:])

                # 使用正则表达式提取信息
                axle_type = None
                load_tons = None
                speed_kmh = None

                # 轴型：如"2轴"、"3轴"
                axle_match = re.search(r'(\d+)轴', remaining_parts)
                if axle_match:
                    axle_type = int(axle_match.group(1))

                # 载重：如"2.5t"、"25t"
                load_match = re.search(r'(\d+\.?\d*)t', remaining_parts)
                if load_match:
                    load_tons = float(load_match.group(1))

                # 速度：如"100kmh"、"60km/h"
                speed_match = re.search(r'(\d+)km/?h?', remaining_parts)
                if speed_match:
                    speed_kmh = int(speed_match.group(1))

                # 提取车道号
                lane_number = None
                lane_match = re.search(r'车道(\d+)', lane_info)
                if lane_match:
                    lane_number = int(lane_match.group(1))

            else:
                # 精确匹配成功
                monitoring_point = match.group(1)
                monitoring_time = match.group(2)
                data_type = 'AcceData'
                lane_info = match.group(3)
                axle_type = int(match.group(4))
                load_tons = float(match.group(5))
                speed_kmh = int(match.group(6))

                # 提取车道号
                lane_number = None
                lane_match = re.search(r'车道(\d+)', lane_info)
                if lane_match:
                    lane_number = int(lane_match.group(1))

            # 验证必要信息是否提取成功
            if not all([monitoring_point, monitoring_time, axle_type, load_tons, speed_kmh]):
                print(f"   ⚠️  无法提取完整信息: {filename}")
                return None

            parsed_info = {
                'monitoring_point': monitoring_point,
                'monitoring_time': monitoring_time,
                'data_type': data_type,
                'lane_info': lane_info,
                'lane_number': lane_number,
                'axle_type': axle_type,
                'load_tons': load_tons,
                'speed_kmh': speed_kmh,
                'original_filename': filename
            }

            return parsed_info
            
        except Exception as e:
            print(f"   ❌ 解析文件名失败 {filename}: {str(e)}")
            return None
    
    def validate_csv_structure(self, file_path: Path) -> bool:
        """
        验证CSV文件结构是否符合预期

        参数:
        file_path: CSV文件路径

        返回:
        is_valid: 是否有效
        """
        try:
            # 安全读取CSV文件
            df = self.safe_read_csv(file_path)
            if df is None:
                return False

            # 检查列数（应该是22列）
            if df.shape[1] != 22:
                print(f"   ⚠️  列数不符合预期: {df.shape[1]} (期望22列)")
                return False

            # 检查第一列（count列，可能没有表头）
            first_col = df.columns[0].lower()
            # 第一列可能是数字索引或者unnamed，这是正常的
            if not (first_col.startswith('unnamed') or first_col.isdigit() or 'count' in first_col):
                # 如果第一列有明确的非count名称，检查数据是否为数值型
                try:
                    pd.to_numeric(df.iloc[:, 0], errors='raise')
                except:
                    print(f"   ⚠️  第一列数据不是数值型: {df.columns[0]}")
                    return False

            # 检查第3-22列是否为传感器数据（跳过第2列无用列）
            sensor_columns = df.columns[2:]  # 第3-22列
            expected_pattern = re.compile(r'acce\d{2}')

            valid_sensor_cols = 0
            for col in sensor_columns:
                if expected_pattern.match(col.lower()):
                    valid_sensor_cols += 1

            # 降低最低传感器数量要求，适应传感器损坏情况
            min_required_sensors = 5  # 最少需要5个有效传感器
            if valid_sensor_cols < min_required_sensors:
                print(f"   ❌ 有效传感器列数不足: {valid_sensor_cols} (至少需要{min_required_sensors}列)")
                return False
            elif valid_sensor_cols < 10:
                print(f"   ⚠️  传感器数量较少: {valid_sensor_cols} (可能存在传感器损坏)")

            # 检查传感器列的数据类型
            sensor_data = df.iloc[:, 2:]  # 第3-22列数据
            numeric_sensor_cols = sensor_data.select_dtypes(include=[np.number]).columns
            if len(numeric_sensor_cols) < min_required_sensors:  # 至少5列传感器数据应该是数值型
                print(f"   ❌ 数值型传感器列数不足: {len(numeric_sensor_cols)} (至少需要{min_required_sensors}列)")
                return False

            return True

        except Exception as e:
            print(f"   ❌ 验证CSV结构失败: {str(e)}")
            return False

    def identify_valid_sensors(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        识别有效的传感器列

        参数:
        df: 数据框

        返回:
        sensor_info: 传感器状态信息
        """
        try:
            sensor_info = {
                'valid_sensors': [],
                'invalid_sensors': [],
                'sensor_status': {},
                'total_sensors': 0,
                'valid_count': 0
            }

            # 获取传感器列（第3-22列）
            sensor_columns = df.columns[2:]
            expected_pattern = re.compile(r'acce\d{2}')

            for col in sensor_columns:
                if expected_pattern.match(col.lower()):
                    sensor_info['total_sensors'] += 1
                    sensor_data = df[col]

                    # 检查传感器是否有效
                    is_valid, status_reason = self._check_sensor_validity(sensor_data, col)

                    sensor_info['sensor_status'][col] = {
                        'is_valid': is_valid,
                        'reason': status_reason,
                        'data_range': [float(sensor_data.min()), float(sensor_data.max())] if is_valid else [0, 0],
                        'std_dev': float(sensor_data.std()) if is_valid else 0,
                        'non_zero_ratio': float((sensor_data != 0).sum() / len(sensor_data)) if is_valid else 0
                    }

                    if is_valid:
                        sensor_info['valid_sensors'].append(col)
                        sensor_info['valid_count'] += 1
                    else:
                        sensor_info['invalid_sensors'].append(col)

            print(f"   📊 传感器状态分析:")
            print(f"      - 总传感器数: {sensor_info['total_sensors']}")
            print(f"      - 有效传感器: {sensor_info['valid_count']}")
            print(f"      - 损坏传感器: {len(sensor_info['invalid_sensors'])}")

            if sensor_info['invalid_sensors']:
                print(f"      - 损坏传感器列表: {sensor_info['invalid_sensors']}")

            return sensor_info

        except Exception as e:
            print(f"   ❌ 传感器状态识别失败: {str(e)}")
            return {
                'valid_sensors': [],
                'invalid_sensors': [],
                'sensor_status': {},
                'total_sensors': 0,
                'valid_count': 0
            }

    def _check_sensor_validity(self, sensor_data: pd.Series, sensor_name: str) -> Tuple[bool, str]:
        """
        检查单个传感器的有效性

        参数:
        sensor_data: 传感器数据
        sensor_name: 传感器名称

        返回:
        is_valid: 是否有效
        reason: 状态原因
        """
        try:
            # 转换为数值型
            numeric_data = pd.to_numeric(sensor_data, errors='coerce')

            # 检查1: 是否全为NaN
            if numeric_data.isnull().all():
                return False, "全部为缺失值"

            # 检查2: 缺失值比例过高
            missing_ratio = numeric_data.isnull().sum() / len(numeric_data)
            if missing_ratio > 0.5:  # 超过50%缺失
                return False, f"缺失值过多({missing_ratio:.1%})"

            # 填充缺失值进行后续检查
            filled_data = numeric_data.fillna(0)

            # 检查3: 是否全为零
            if (filled_data == 0).all():
                return False, "全部为零值"

            # 检查4: 零值比例过高
            zero_ratio = (filled_data == 0).sum() / len(filled_data)
            if zero_ratio > 0.9:  # 超过90%为零
                return False, f"零值过多({zero_ratio:.1%})"

            # 检查5: 数据变化范围
            data_std = filled_data.std()
            if data_std < 1e-6:  # 标准差过小，数据几乎无变化
                return False, "数据无变化"

            # 检查6: 异常值比例
            mean_val = filled_data.mean()
            outlier_mask = np.abs(filled_data - mean_val) > 5 * data_std
            outlier_ratio = outlier_mask.sum() / len(filled_data)
            if outlier_ratio > 0.3:  # 超过30%为异常值
                return False, f"异常值过多({outlier_ratio:.1%})"

            # 检查7: 数据范围合理性
            data_range = filled_data.max() - filled_data.min()
            if data_range > 1000:  # 数据范围过大，可能传感器故障
                return False, f"数据范围异常({data_range:.2f})"

            return True, "传感器正常"

        except Exception as e:
            return False, f"检查失败: {str(e)}"
    
    def safe_read_csv(self, file_path: Path) -> Optional[pd.DataFrame]:
        """
        安全读取CSV文件，处理编码问题
        
        参数:
        file_path: 文件路径
        
        返回:
        df: 数据框或None
        """
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                return df
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"   ⚠️  读取文件失败 (编码 {encoding}): {str(e)}")
                break
        
        return None
    
    def generate_output_structure(self, parsed_info: Dict[str, any]) -> Tuple[str, str]:
        """
        根据解析的信息生成输出目录结构和文件名
        
        参数:
        parsed_info: 解析的文件信息
        
        返回:
        output_subdir: 输出子目录路径
        new_filename: 新文件名
        """
        try:
            # 构建目录结构：载重/轴型/速度
            load_str = f"{parsed_info['load_tons']}吨" if parsed_info['load_tons'] else "未知载重"
            
            if parsed_info['axle_type'] == 2:
                axle_str = "双轴"
            elif parsed_info['axle_type'] == 3:
                axle_str = "三轴"
            elif parsed_info['axle_type'] == 4:
                axle_str = "四轴"
            else:
                axle_str = f"{parsed_info['axle_type']}轴" if parsed_info['axle_type'] else "未知轴型"
            
            speed_str = f"{parsed_info['speed_kmh']}km_h" if parsed_info['speed_kmh'] else "未知速度"
            
            # 创建子目录路径
            output_subdir = os.path.join(load_str, axle_str, speed_str)
            
            # 生成新文件名：acce_监测点位_时间_车道.csv
            new_filename = f"acce_{parsed_info['monitoring_point']}_{parsed_info['monitoring_time']}"
            if parsed_info['lane_number']:
                new_filename += f"_lane{parsed_info['lane_number']}"
            new_filename += ".csv"
            
            return output_subdir, new_filename
            
        except Exception as e:
            print(f"   ❌ 生成输出结构失败: {str(e)}")
            # 使用默认结构
            return "未分类", f"acce_{parsed_info.get('monitoring_point', 'unknown')}.csv"
    
    def process_single_file(self, file_path: Path) -> bool:
        """
        处理单个CSV文件
        
        参数:
        file_path: 文件路径
        
        返回:
        success: 是否成功处理
        """
        try:
            filename = file_path.name
            print(f"   📄 处理文件: {filename}")
            
            # 1. 解析文件名
            parsed_info = self.parse_filename(filename)
            if parsed_info is None:
                self.failed_files.append(filename)
                return False
            
            # 2. 验证CSV结构
            if not self.validate_csv_structure(file_path):
                print(f"   ❌ CSV结构验证失败: {filename}")
                self.failed_files.append(filename)
                return False
            
            # 3. 读取数据
            df = self.safe_read_csv(file_path)
            if df is None:
                print(f"   ❌ 读取数据失败: {filename}")
                self.failed_files.append(filename)
                return False
            
            # 4. 数据清理和标准化
            df_cleaned = self.clean_and_standardize_data(df, parsed_info)
            if df_cleaned is None:
                print(f"   ❌ 数据清理失败: {filename}")
                self.failed_files.append(filename)
                return False
            
            # 5. 生成输出路径
            output_subdir, new_filename = self.generate_output_structure(parsed_info)
            output_dir_full = self.output_dir / output_subdir
            output_dir_full.mkdir(parents=True, exist_ok=True)
            
            output_file_path = output_dir_full / new_filename
            
            # 6. 保存处理后的数据
            df_cleaned.to_csv(output_file_path, index=False, encoding='utf-8')
            
            # 7. 记录处理结果
            self.processed_files.append(filename)
            self.file_mapping[filename] = str(output_file_path.relative_to(self.output_dir))
            
            print(f"   ✅ 成功处理: {filename} -> {output_file_path.relative_to(self.output_dir)}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 处理文件失败 {filename}: {str(e)}")
            self.failed_files.append(filename)
            return False

    def clean_and_standardize_data(self, df: pd.DataFrame, parsed_info: Dict[str, any]) -> Optional[pd.DataFrame]:
        """
        清理和标准化数据（支持动态传感器数量）

        参数:
        df: 原始数据框（22列：count + 无用列 + 20个传感器列）
        parsed_info: 解析的文件信息

        返回:
        df_cleaned: 清理后的数据框（count + N个有效传感器列 + 元数据列）
        """
        try:
            # 1. 识别有效传感器
            sensor_info = self.identify_valid_sensors(df)

            if sensor_info['valid_count'] < 5:
                print(f"   ❌ 有效传感器数量不足: {sensor_info['valid_count']} (至少需要5个)")
                return None

            # 2. 创建新的数据框，只保留有用的列
            df_cleaned = pd.DataFrame()

            # 添加count列（第1列）
            count_data = df.iloc[:, 0]  # 第1列数据
            df_cleaned['count'] = pd.to_numeric(count_data, errors='coerce')

            # 如果count列有缺失值，用行索引填充
            if df_cleaned['count'].isnull().any():
                df_cleaned['count'] = df_cleaned['count'].fillna(pd.Series(range(len(df_cleaned))))

            # 3. 只添加有效的传感器列
            valid_sensor_mapping = {}  # 原始列名到新列名的映射

            for i, original_col in enumerate(sensor_info['valid_sensors']):
                # 生成新的传感器列名
                new_sensor_name = f'sensor_{i+1:02d}'
                valid_sensor_mapping[original_col] = new_sensor_name

                try:
                    df_cleaned[new_sensor_name] = pd.to_numeric(df[original_col], errors='coerce')
                except:
                    df_cleaned[new_sensor_name] = df[original_col]

            # 4. 数据类型转换
            # count列确保为整数
            try:
                df_cleaned['count'] = df_cleaned['count'].astype(int)
            except:
                df_cleaned['count'] = pd.to_numeric(df_cleaned['count'], errors='coerce').fillna(0).astype(int)

            # 传感器列转换为浮点数
            sensor_columns = [col for col in df_cleaned.columns if col.startswith('sensor_')]
            for col in sensor_columns:
                try:
                    df_cleaned[col] = pd.to_numeric(df_cleaned[col], errors='coerce')
                except:
                    pass

            # 5. 处理缺失值
            # 对于传感器数据，使用前向填充和后向填充
            for col in sensor_columns:
                if df_cleaned[col].isnull().sum() > 0:
                    df_cleaned[col] = df_cleaned[col].fillna(method='ffill').fillna(method='bfill')

                    # 如果仍有缺失值，用0填充
                    df_cleaned[col] = df_cleaned[col].fillna(0)

            # 6. 异常值检测和处理
            for col in sensor_columns:
                # 使用3σ准则检测异常值
                mean_val = df_cleaned[col].mean()
                std_val = df_cleaned[col].std()

                if std_val > 0:
                    # 标记异常值
                    outlier_mask = np.abs(df_cleaned[col] - mean_val) > 3 * std_val

                    # 用中位数替换异常值
                    if outlier_mask.sum() > 0:
                        median_val = df_cleaned[col].median()
                        df_cleaned.loc[outlier_mask, col] = median_val

            # 7. 添加元数据列（包含传感器状态信息）
            df_cleaned['speed_kmh'] = parsed_info.get('speed_kmh')
            df_cleaned['load_tons'] = parsed_info.get('load_tons')
            df_cleaned['axle_type'] = parsed_info.get('axle_type')
            df_cleaned['lane_number'] = parsed_info.get('lane_number')
            df_cleaned['monitoring_point'] = parsed_info.get('monitoring_point')

            # 新增传感器状态元数据
            df_cleaned['valid_sensors_count'] = sensor_info['valid_count']
            df_cleaned['total_sensors_count'] = sensor_info['total_sensors']
            df_cleaned['valid_sensors_list'] = ','.join(sensor_info['valid_sensors'])
            # 传感器映射信息存储为简化格式（只存储数量信息，避免字符串过长）
            df_cleaned['sensor_mapping_info'] = f"mapped_{sensor_info['valid_count']}_sensors"

            # 8. 数据质量检查
            if len(df_cleaned) < 100:
                print(f"   ⚠️  数据行数过少: {len(df_cleaned)}")
                return None

            # 检查有效传感器数量
            if len(sensor_columns) < 5:
                print(f"   ❌ 有效传感器列数不足: {len(sensor_columns)} (至少需要5列)")
                return None

            # 检查传感器数据是否全为零
            sensor_data = df_cleaned[sensor_columns]
            if (sensor_data == 0).all().all():
                print(f"   ⚠️  所有传感器数据为零")
                return None

            print(f"   ✅ 数据清理完成: {len(df_cleaned)}行 × {len(df_cleaned.columns)}列")
            print(f"      - count列: 1个")
            print(f"      - 有效传感器列: {len(sensor_columns)}个")
            print(f"      - 元数据列: {len(df_cleaned.columns) - len(sensor_columns) - 1}个")
            print(f"      - 传感器映射: {dict(list(valid_sensor_mapping.items())[:3])}...")

            return df_cleaned

        except Exception as e:
            print(f"   ❌ 数据清理失败: {str(e)}")
            return None

    def process_all_files(self) -> Dict[str, any]:
        """
        处理输入目录中的所有CSV文件

        返回:
        processing_summary: 处理摘要
        """
        print(f"\n🔄 开始处理输入目录中的所有CSV文件...")
        print(f"   输入目录: {self.input_dir}")

        # 查找所有CSV文件
        csv_files = list(self.input_dir.glob("**/*.csv"))

        if not csv_files:
            print(f"❌ 在输入目录中未找到CSV文件")
            return {'success': False, 'message': '未找到CSV文件'}

        print(f"   找到 {len(csv_files)} 个CSV文件")

        # 处理每个文件
        for i, file_path in enumerate(csv_files, 1):
            print(f"\n📊 处理进度: {i}/{len(csv_files)}")
            self.process_single_file(file_path)

        # 生成处理摘要
        processing_summary = {
            'success': True,
            'total_files': len(csv_files),
            'processed_files': len(self.processed_files),
            'failed_files': len(self.failed_files),
            'success_rate': len(self.processed_files) / len(csv_files) * 100,
            'processed_list': self.processed_files,
            'failed_list': self.failed_files,
            'file_mapping': self.file_mapping,
            'output_directory': str(self.output_dir)
        }

        # 保存处理摘要
        self.save_processing_summary(processing_summary)

        # 生成数据信息文件
        self.generate_data_info_file()

        # 验证兼容性
        self.validate_output_compatibility()

        return processing_summary

    def save_processing_summary(self, summary: Dict[str, any]):
        """
        保存处理摘要到文件

        参数:
        summary: 处理摘要
        """
        try:
            import json

            # 保存JSON格式摘要
            summary_file = self.output_dir / 'preprocessing_summary.json'
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)

            # 保存Markdown格式报告
            report_file = self.output_dir / 'preprocessing_report.md'
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("# 数据预处理报告\n\n")
                f.write(f"## 处理概况\n")
                f.write(f"- 总文件数: {summary['total_files']}\n")
                f.write(f"- 成功处理: {summary['processed_files']}\n")
                f.write(f"- 处理失败: {summary['failed_files']}\n")
                f.write(f"- 成功率: {summary['success_rate']:.1f}%\n\n")

                if summary['processed_list']:
                    f.write(f"## 成功处理的文件\n")
                    for filename in summary['processed_list']:
                        new_path = summary['file_mapping'].get(filename, '未知')
                        f.write(f"- {filename} -> {new_path}\n")
                    f.write("\n")

                if summary['failed_list']:
                    f.write(f"## 处理失败的文件\n")
                    for filename in summary['failed_list']:
                        f.write(f"- {filename}\n")
                    f.write("\n")

                f.write(f"## 输出目录结构\n")
                f.write(f"```\n")
                f.write(f"{summary['output_directory']}/\n")

                # 生成目录树
                self._generate_directory_tree(f, self.output_dir)
                f.write(f"```\n")

            print(f"   ✅ 处理摘要已保存:")
            print(f"      - {summary_file}")
            print(f"      - {report_file}")

        except Exception as e:
            print(f"   ⚠️  保存处理摘要失败: {str(e)}")

    def _generate_directory_tree(self, file_handle, directory: Path, prefix: str = ""):
        """
        生成目录树结构

        参数:
        file_handle: 文件句柄
        directory: 目录路径
        prefix: 前缀字符串
        """
        try:
            items = sorted(directory.iterdir())
            dirs = [item for item in items if item.is_dir()]
            files = [item for item in items if item.is_file()]

            # 写入目录
            for i, dir_path in enumerate(dirs):
                is_last_dir = (i == len(dirs) - 1) and len(files) == 0
                current_prefix = "└── " if is_last_dir else "├── "
                file_handle.write(f"{prefix}{current_prefix}{dir_path.name}/\n")

                next_prefix = prefix + ("    " if is_last_dir else "│   ")
                self._generate_directory_tree(file_handle, dir_path, next_prefix)

            # 写入文件
            for i, file_path in enumerate(files):
                is_last = i == len(files) - 1
                current_prefix = "└── " if is_last else "├── "
                file_handle.write(f"{prefix}{current_prefix}{file_path.name}\n")

        except Exception as e:
            file_handle.write(f"{prefix}├── (读取目录失败: {str(e)})\n")

    def generate_data_info_file(self):
        """
        生成数据信息文件，用于现有系统识别
        """
        try:
            info_file = self.output_dir / 'data_info.json'

            # 统计数据信息
            data_info = {
                'data_format': 'preprocessed_new_format',
                'total_files': len(self.processed_files),
                'directory_structure': 'load_tons/axle_type/speed_kmh',
                'file_naming': 'acce_monitoring_point_time_lane.csv',
                'column_format': {
                    'count_column': 'count',
                    'sensor_columns': ['sensor_01', 'sensor_02', '...', 'sensor_20'],
                    'metadata_columns': ['speed_kmh', 'load_tons', 'axle_type', 'lane_number', 'monitoring_point']
                },
                'compatible_with': 'unified_vibration_analysis.py',
                'preprocessing_date': pd.Timestamp.now().isoformat(),
                'file_mapping': self.file_mapping
            }

            # 保存信息文件
            import json
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(data_info, f, ensure_ascii=False, indent=2)

            print(f"   ✅ 数据信息文件已生成: {info_file}")

        except Exception as e:
            print(f"   ⚠️  生成数据信息文件失败: {str(e)}")

    def validate_output_compatibility(self) -> bool:
        """
        验证输出数据与现有系统的兼容性（支持动态传感器数量）

        返回:
        is_compatible: 是否兼容
        """
        try:
            print(f"\n🔍 验证输出数据兼容性...")

            # 检查是否有处理成功的文件
            if not self.processed_files:
                print(f"   ❌ 没有成功处理的文件")
                return False

            # 随机选择一个文件进行验证
            sample_file_mapping = list(self.file_mapping.items())[0]
            sample_file_path = self.output_dir / sample_file_mapping[1]

            # 读取样本文件
            df = self.safe_read_csv(sample_file_path)
            if df is None:
                print(f"   ❌ 无法读取样本文件")
                return False

            # 检查基本元数据列
            basic_metadata_columns = ['speed_kmh', 'load_tons', 'axle_type', 'lane_number', 'monitoring_point']
            sensor_metadata_columns = ['valid_sensors_count', 'total_sensors_count', 'valid_sensors_list', 'sensor_mapping_info']

            missing_basic_meta = [col for col in basic_metadata_columns if col not in df.columns]
            if missing_basic_meta:
                print(f"   ❌ 缺少基本元数据列: {missing_basic_meta}")
                return False

            missing_sensor_meta = [col for col in sensor_metadata_columns if col not in df.columns]
            if missing_sensor_meta:
                print(f"   ❌ 缺少传感器元数据列: {missing_sensor_meta}")
                return False

            # 检查传感器列数量（动态数量，至少5个）
            sensor_columns = [col for col in df.columns if col.startswith('sensor_')]
            if len(sensor_columns) < 5:
                print(f"   ❌ 传感器列数不足: {len(sensor_columns)} (至少需要5列)")
                return False

            # 验证传感器数量与元数据一致性
            if 'valid_sensors_count' in df.columns:
                expected_sensor_count = df['valid_sensors_count'].iloc[0]
                if len(sensor_columns) != expected_sensor_count:
                    print(f"   ⚠️  传感器列数与元数据不一致: {len(sensor_columns)} vs {expected_sensor_count}")

            # 检查数据类型
            numeric_sensor_cols = df[sensor_columns].select_dtypes(include=[np.number]).columns

            if len(numeric_sensor_cols) < len(sensor_columns) * 0.9:  # 至少90%的传感器列应该是数值型
                print(f"   ⚠️  传感器数据类型问题: {len(numeric_sensor_cols)}/{len(sensor_columns)} 为数值型")
                return False

            # 检查count列
            if 'count' not in df.columns:
                print(f"   ❌ 缺少count列")
                return False

            try:
                pd.to_numeric(df['count'], errors='raise')
            except:
                print(f"   ⚠️  count列不是数值型")
                return False

            # 检查传感器数据质量
            for col in sensor_columns:
                sensor_data = df[col]
                if (sensor_data == 0).all():
                    print(f"   ⚠️  传感器 {col} 全为零值")
                elif sensor_data.std() < 1e-6:
                    print(f"   ⚠️  传感器 {col} 数据无变化")

            print(f"   ✅ 输出数据兼容性验证通过")
            print(f"      - count列: 1个")
            print(f"      - 有效传感器列数: {len(sensor_columns)}")
            print(f"      - 数值型传感器列: {len(numeric_sensor_cols)}")
            print(f"      - 基本元数据列数: {len(basic_metadata_columns)}")
            print(f"      - 传感器元数据列数: {len(sensor_metadata_columns)}")
            print(f"      - 数据行数: {len(df)}")
            print(f"      - 总列数: {len(df.columns)}")

            # 显示传感器状态信息
            if 'valid_sensors_list' in df.columns and not df['valid_sensors_list'].isnull().all():
                valid_sensors = df['valid_sensors_list'].iloc[0]
                print(f"      - 有效传感器: {valid_sensors}")

            return True

        except Exception as e:
            print(f"   ❌ 兼容性验证失败: {str(e)}")
            return False
