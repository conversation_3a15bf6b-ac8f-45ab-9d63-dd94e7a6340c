#!/usr/bin/env python3
"""
测试select_best_model方法修复效果
验证OptimizedMLModels类的select_best_model方法是否正常工作
"""

import numpy as np
from sklearn.datasets import make_regression, make_classification
from optimized_ml_models import OptimizedMLModels

def test_select_best_model_regression():
    """测试回归任务的select_best_model方法"""
    print("🧪 测试回归任务的select_best_model方法...")
    
    # 创建测试数据
    X, y = make_regression(n_samples=200, n_features=10, noise=0.1, random_state=42)
    
    # 初始化训练器
    trainer = OptimizedMLModels()
    
    # 训练模型
    results = trainer.train_all_models(X, y, 'regression', 'test_regression')
    
    print(f"\n📊 训练结果:")
    for model_name, metrics in results.items():
        if 'error' not in metrics:
            score = metrics.get('r2_score', 0)
            time_taken = metrics.get('training_time', 0)
            print(f"   {model_name}: R² = {score:.4f} (时间: {time_taken:.2f}s)")
        else:
            print(f"   {model_name}: ❌ {metrics['error']}")
    
    # 测试select_best_model方法
    print(f"\n🏆 测试select_best_model方法:")
    try:
        best_model_info = trainer.select_best_model(results, 'regression')
        
        print(f"   ✅ select_best_model方法调用成功!")
        print(f"   🥇 最佳模型: {best_model_info['best_model']}")
        print(f"   📊 最佳分数: {best_model_info['best_score']:.4f}")
        print(f"   📝 推荐理由: {best_model_info['recommendation_reason']}")
        print(f"   📈 总模型数: {best_model_info['total_models']}")
        print(f"   ✅ 成功模型数: {best_model_info['successful_models']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ select_best_model方法调用失败: {str(e)}")
        return False

def test_select_best_model_classification():
    """测试分类任务的select_best_model方法"""
    print("\n🧪 测试分类任务的select_best_model方法...")
    
    # 创建测试数据
    X, y = make_classification(n_samples=200, n_features=10, n_classes=3, n_informative=5, n_redundant=2, random_state=42)
    
    # 初始化训练器
    trainer = OptimizedMLModels()
    
    # 训练模型（只训练几个快速的模型）
    optimized_params = {
        'Random Forest': {'n_estimators': 50, 'random_state': 42},
        'Extra Trees': {'n_estimators': 50, 'random_state': 42}
    }
    
    results = trainer.train_all_models(X, y, 'classification', 'test_classification', optimized_params)
    
    print(f"\n📊 训练结果:")
    for model_name, metrics in results.items():
        if 'error' not in metrics:
            score = metrics.get('accuracy', 0)
            time_taken = metrics.get('training_time', 0)
            print(f"   {model_name}: 准确率 = {score:.4f} (时间: {time_taken:.2f}s)")
        else:
            print(f"   {model_name}: ❌ {metrics['error']}")
    
    # 测试select_best_model方法
    print(f"\n🏆 测试select_best_model方法:")
    try:
        best_model_info = trainer.select_best_model(results, 'classification')
        
        print(f"   ✅ select_best_model方法调用成功!")
        print(f"   🥇 最佳模型: {best_model_info['best_model']}")
        print(f"   📊 最佳分数: {best_model_info['best_score']:.4f}")
        print(f"   📝 推荐理由: {best_model_info['recommendation_reason']}")
        print(f"   📈 总模型数: {best_model_info['total_models']}")
        print(f"   ✅ 成功模型数: {best_model_info['successful_models']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ select_best_model方法调用失败: {str(e)}")
        return False

def test_generate_performance_statistics():
    """测试generate_performance_statistics方法"""
    print("\n🧪 测试generate_performance_statistics方法...")
    
    # 创建模拟结果数据
    mock_results = {
        'Random Forest': {'r2_score': 0.85, 'training_time': 1.2},
        'XGBoost': {'r2_score': 0.88, 'training_time': 2.1},
        'SVM': {'r2_score': 0.75, 'training_time': 0.8},
        'Failed Model': {'error': 'Training failed'}
    }
    
    trainer = OptimizedMLModels()
    
    try:
        stats = trainer.generate_performance_statistics(mock_results, 'regression')
        
        print(f"   ✅ generate_performance_statistics方法调用成功!")
        print(f"   📊 性能统计:")
        print(f"      平均分数: {stats['performance_stats']['mean_score']:.4f}")
        print(f"      最高分数: {stats['performance_stats']['max_score']:.4f}")
        print(f"      最低分数: {stats['performance_stats']['min_score']:.4f}")
        print(f"   ⏱️  时间统计:")
        print(f"      平均时间: {stats['timing_stats']['mean_time']:.2f}s")
        print(f"      总时间: {stats['timing_stats']['total_time']:.2f}s")
        print(f"   🏆 模型排名:")
        for rank_info in stats['model_ranking'][:3]:  # 显示前3名
            print(f"      {rank_info['rank']}. {rank_info['model']}: {rank_info['score']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ generate_performance_statistics方法调用失败: {str(e)}")
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况...")
    
    trainer = OptimizedMLModels()
    
    # 测试空结果
    print("   测试空结果...")
    try:
        result = trainer.select_best_model({}, 'regression')
        print(f"   ✅ 空结果处理正常: {result['best_model']}")
    except Exception as e:
        print(f"   ❌ 空结果处理失败: {str(e)}")
        return False
    
    # 测试全部失败的结果
    print("   测试全部失败的结果...")
    try:
        failed_results = {
            'Model1': {'error': 'Failed'},
            'Model2': {'error': 'Failed'}
        }
        result = trainer.select_best_model(failed_results, 'regression')
        print(f"   ✅ 全部失败结果处理正常: {result['best_model']}")
    except Exception as e:
        print(f"   ❌ 全部失败结果处理失败: {str(e)}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🔧 OptimizedMLModels类select_best_model方法修复验证")
    print("=" * 80)
    
    test_results = []
    
    # 测试回归任务
    test_results.append(test_select_best_model_regression())
    
    # 测试分类任务
    test_results.append(test_select_best_model_classification())
    
    # 测试性能统计方法
    test_results.append(test_generate_performance_statistics())
    
    # 测试边界情况
    test_results.append(test_edge_cases())
    
    # 总结测试结果
    print("\n" + "=" * 80)
    print("🎉 测试结果总结")
    print("=" * 80)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"📊 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("✅ 所有测试通过! select_best_model方法修复成功!")
        print("🎯 OptimizedMLModels类现在可以正常使用了")
        print("💡 建议: 现在可以运行unified_vibration_analysis.py进行完整测试")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
