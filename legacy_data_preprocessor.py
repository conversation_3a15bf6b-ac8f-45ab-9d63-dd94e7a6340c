#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
旧格式数据预处理模块
处理data文件夹下的旧格式数据，支持三级目录结构和文件合并

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import os
import re
import shutil
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

class LegacyDataPreprocessor:
    """旧格式数据预处理器"""
    
    def __init__(self, input_dir: str, output_dir: str = "legacy_preprocessed_data"):
        """
        初始化预处理器
        
        参数:
        input_dir: 输入数据目录（包含旧格式数据）
        output_dir: 输出数据目录（转换后的数据）
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.processed_files = []
        self.failed_files = []
        self.file_mapping = {}  # 原文件路径到新文件路径的映射
        self.merged_files = {}  # 合并文件的信息
        
        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🔧 旧格式数据预处理器已初始化")
        print(f"   输入目录: {self.input_dir}")
        print(f"   输出目录: {self.output_dir}")
    
    def scan_legacy_structure(self) -> Dict[str, any]:
        """
        扫描旧格式目录结构
        
        返回:
        structure_info: 目录结构信息
        """
        try:
            structure_info = {
                'weight_dirs': {},
                'total_groups': 0,
                'total_csv_files': 0
            }
            
            print(f"\n🔍 扫描旧格式目录结构...")
            
            # 扫描第一级目录（轴重）
            weight_dirs = [d for d in self.input_dir.iterdir() if d.is_dir()]
            
            for weight_dir in weight_dirs:
                if '吨' in weight_dir.name:
                    weight_name = weight_dir.name
                    structure_info['weight_dirs'][weight_name] = {
                        'axle_dirs': {},
                        'path': weight_dir
                    }
                    
                    print(f"   📁 发现轴重目录: {weight_name}")
                    
                    # 扫描第二级目录（轴型）
                    axle_dirs = [d for d in weight_dir.iterdir() if d.is_dir()]
                    
                    for axle_dir in axle_dirs:
                        if '轴' in axle_dir.name:
                            axle_name = axle_dir.name
                            structure_info['weight_dirs'][weight_name]['axle_dirs'][axle_name] = {
                                'speed_dirs': {},
                                'path': axle_dir
                            }
                            
                            print(f"      📁 发现轴型目录: {axle_name}")
                            
                            # 扫描第三级目录（速度）
                            speed_dirs = [d for d in axle_dir.iterdir() if d.is_dir()]
                            
                            for speed_dir in speed_dirs:
                                if 'km' in speed_dir.name and ('h' in speed_dir.name or '_' in speed_dir.name):
                                    speed_name = speed_dir.name
                                    csv_files = list(speed_dir.glob("*.csv"))
                                    
                                    if csv_files:
                                        structure_info['weight_dirs'][weight_name]['axle_dirs'][axle_name]['speed_dirs'][speed_name] = {
                                            'csv_files': csv_files,
                                            'path': speed_dir,
                                            'file_count': len(csv_files)
                                        }
                                        
                                        structure_info['total_csv_files'] += len(csv_files)
                                        structure_info['total_groups'] += 1
                                        
                                        print(f"         📁 发现速度目录: {speed_name} ({len(csv_files)} 个CSV文件)")
            
            print(f"\n📊 扫描结果:")
            print(f"   轴重目录数: {len(structure_info['weight_dirs'])}")
            print(f"   数据组数: {structure_info['total_groups']}")
            print(f"   CSV文件总数: {structure_info['total_csv_files']}")
            
            return structure_info
            
        except Exception as e:
            print(f"❌ 扫描目录结构失败: {str(e)}")
            return {'weight_dirs': {}, 'total_groups': 0, 'total_csv_files': 0}
    
    def merge_csv_files(self, csv_files: List[Path], group_info: Dict[str, str]) -> Optional[pd.DataFrame]:
        """
        合并同一速度组下的CSV文件
        
        参数:
        csv_files: CSV文件列表
        group_info: 组信息（轴重、轴型、速度）
        
        返回:
        merged_df: 合并后的数据框
        """
        try:
            if not csv_files:
                return None
            
            print(f"   🔗 合并 {len(csv_files)} 个CSV文件...")
            
            # 按文件名排序（通常包含时间信息）
            sorted_files = sorted(csv_files, key=lambda x: x.name)
            
            merged_data = []
            total_rows = 0
            
            for i, csv_file in enumerate(sorted_files):
                try:
                    # 读取CSV文件
                    df = self.safe_read_csv(csv_file)
                    if df is None:
                        print(f"      ⚠️  跳过无法读取的文件: {csv_file.name}")
                        continue
                    
                    # 验证列结构（应该是21列）
                    if df.shape[1] != 21:
                        print(f"      ⚠️  跳过列数不正确的文件: {csv_file.name} ({df.shape[1]}列)")
                        continue
                    
                    # 标准化列名
                    df = self.standardize_column_names(df)
                    
                    # 调整count列（连续编号）
                    if 'count' in df.columns:
                        df['count'] = range(total_rows, total_rows + len(df))
                    
                    merged_data.append(df)
                    total_rows += len(df)
                    
                    print(f"      ✅ 文件 {csv_file.name}: {len(df)} 行")
                    
                except Exception as e:
                    print(f"      ❌ 处理文件失败 {csv_file.name}: {str(e)}")
                    continue
            
            if not merged_data:
                print(f"      ❌ 没有有效的数据文件")
                return None
            
            # 合并所有数据
            merged_df = pd.concat(merged_data, ignore_index=True)
            
            # 添加元数据
            merged_df = self.add_metadata(merged_df, group_info)
            
            print(f"      ✅ 合并完成: {len(merged_df)} 行 × {len(merged_df.columns)} 列")
            
            return merged_df
            
        except Exception as e:
            print(f"   ❌ 合并CSV文件失败: {str(e)}")
            return None
    
    def safe_read_csv(self, file_path: Path) -> Optional[pd.DataFrame]:
        """
        安全读取CSV文件，处理编码问题
        
        参数:
        file_path: 文件路径
        
        返回:
        df: 数据框或None
        """
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                return df
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"      ⚠️  读取文件失败 (编码 {encoding}): {str(e)}")
                break
        
        return None
    
    def standardize_column_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化列名
        
        参数:
        df: 原始数据框
        
        返回:
        df_standardized: 标准化后的数据框
        """
        try:
            df_standardized = df.copy()
            
            # 生成标准列名
            new_columns = ['count']  # 第一列为count
            
            # 后续20列为传感器列
            for i in range(1, min(21, len(df.columns))):
                new_columns.append(f'sensor_{i:02d}')
            
            # 如果有额外的列，保持原名
            if len(df.columns) > 21:
                new_columns.extend(df.columns[21:])
            
            df_standardized.columns = new_columns[:len(df.columns)]
            
            return df_standardized
            
        except Exception as e:
            print(f"      ⚠️  标准化列名失败: {str(e)}")
            return df
    
    def add_metadata(self, df: pd.DataFrame, group_info: Dict[str, str]) -> pd.DataFrame:
        """
        添加元数据列
        
        参数:
        df: 数据框
        group_info: 组信息
        
        返回:
        df_with_metadata: 添加元数据后的数据框
        """
        try:
            df_with_metadata = df.copy()
            
            # 解析轴重信息
            weight_str = group_info.get('weight', '')
            load_tons = self.extract_weight_value(weight_str)
            
            # 解析轴型信息
            axle_str = group_info.get('axle_type', '')
            axle_type = self.extract_axle_type(axle_str)
            
            # 解析速度信息
            speed_str = group_info.get('speed', '')
            speed_kmh = self.extract_speed_value(speed_str)
            
            # 添加元数据列
            df_with_metadata['speed_kmh'] = speed_kmh
            df_with_metadata['load_tons'] = load_tons
            df_with_metadata['axle_type'] = axle_type
            df_with_metadata['lane_number'] = 1  # 默认车道1
            df_with_metadata['monitoring_point'] = 'LEGACY_DATA'  # 标识为旧格式数据
            
            # 传感器状态元数据（假设所有传感器都有效）
            sensor_columns = [col for col in df.columns if col.startswith('sensor_')]
            df_with_metadata['valid_sensors_count'] = len(sensor_columns)
            df_with_metadata['total_sensors_count'] = 20
            df_with_metadata['valid_sensors_list'] = ','.join(sensor_columns)
            df_with_metadata['sensor_mapping_info'] = f"legacy_{len(sensor_columns)}_sensors"
            
            return df_with_metadata
            
        except Exception as e:
            print(f"      ⚠️  添加元数据失败: {str(e)}")
            return df
    
    def extract_weight_value(self, weight_str: str) -> float:
        """从轴重字符串中提取数值"""
        try:
            # 匹配数字（包括小数）
            match = re.search(r'(\d+\.?\d*)', weight_str)
            if match:
                return float(match.group(1))
            return 0.0
        except:
            return 0.0
    
    def extract_axle_type(self, axle_str: str) -> int:
        """从轴型字符串中提取数值"""
        try:
            # 匹配轴数
            match = re.search(r'(\d+)轴', axle_str)
            if match:
                return int(match.group(1))
            elif '单' in axle_str:
                return 1
            elif '双' in axle_str:
                return 2
            elif '三' in axle_str:
                return 3
            elif '四' in axle_str:
                return 4
            return 2  # 默认双轴
        except:
            return 2
    
    def extract_speed_value(self, speed_str: str) -> int:
        """从速度字符串中提取数值"""
        try:
            # 匹配数字
            match = re.search(r'(\d+)', speed_str)
            if match:
                return int(match.group(1))
            return 60  # 默认60km/h
        except:
            return 60

    def generate_output_filename(self, group_info: Dict[str, str]) -> str:
        """
        生成输出文件名

        参数:
        group_info: 组信息

        返回:
        filename: 输出文件名
        """
        try:
            # 提取信息
            weight_str = group_info.get('weight', 'unknown')
            axle_str = group_info.get('axle_type', 'unknown')
            speed_str = group_info.get('speed', 'unknown')

            # 清理文件名中的特殊字符
            weight_clean = re.sub(r'[^\w\.]', '', weight_str)
            axle_clean = re.sub(r'[^\w]', '', axle_str)
            speed_clean = re.sub(r'[^\w]', '', speed_str)

            # 生成文件名
            filename = f"legacy_{weight_clean}_{axle_clean}_{speed_clean}.csv"

            return filename

        except Exception as e:
            return f"legacy_data_{hash(str(group_info)) % 10000}.csv"

    def process_all_groups(self) -> Dict[str, any]:
        """
        处理所有数据组

        返回:
        processing_summary: 处理摘要
        """
        print(f"\n🔄 开始处理所有数据组...")

        # 扫描目录结构
        structure_info = self.scan_legacy_structure()

        if structure_info['total_groups'] == 0:
            print(f"❌ 未找到有效的数据组")
            return {
                'success': False,
                'message': '未找到有效的数据组',
                'total_groups': 0,
                'processed_groups': 0,
                'failed_groups': 0
            }

        processed_groups = 0
        failed_groups = 0

        # 处理每个数据组
        for weight_name, weight_info in structure_info['weight_dirs'].items():
            for axle_name, axle_info in weight_info['axle_dirs'].items():
                for speed_name, speed_info in axle_info['speed_dirs'].items():

                    group_info = {
                        'weight': weight_name,
                        'axle_type': axle_name,
                        'speed': speed_name
                    }

                    print(f"\n📊 处理数据组: {weight_name}/{axle_name}/{speed_name}")

                    try:
                        # 合并CSV文件
                        merged_df = self.merge_csv_files(speed_info['csv_files'], group_info)

                        if merged_df is not None:
                            # 生成输出路径
                            output_subdir = self.output_dir / weight_name / axle_name / speed_name
                            output_subdir.mkdir(parents=True, exist_ok=True)

                            # 生成输出文件名
                            output_filename = self.generate_output_filename(group_info)
                            output_path = output_subdir / output_filename

                            # 保存合并后的数据
                            merged_df.to_csv(output_path, index=False, encoding='utf-8')

                            # 记录处理结果
                            group_key = f"{weight_name}/{axle_name}/{speed_name}"
                            self.processed_files.append(group_key)
                            self.file_mapping[group_key] = str(output_path.relative_to(self.output_dir))
                            self.merged_files[group_key] = {
                                'source_files': [str(f) for f in speed_info['csv_files']],
                                'output_file': str(output_path),
                                'rows': len(merged_df),
                                'columns': len(merged_df.columns)
                            }

                            processed_groups += 1
                            print(f"   ✅ 处理成功: {output_path}")

                        else:
                            failed_groups += 1
                            group_key = f"{weight_name}/{axle_name}/{speed_name}"
                            self.failed_files.append(group_key)
                            print(f"   ❌ 处理失败: 数据合并失败")

                    except Exception as e:
                        failed_groups += 1
                        group_key = f"{weight_name}/{axle_name}/{speed_name}"
                        self.failed_files.append(group_key)
                        print(f"   ❌ 处理失败: {str(e)}")

        # 生成处理摘要
        processing_summary = {
            'success': True,
            'total_groups': structure_info['total_groups'],
            'processed_groups': processed_groups,
            'failed_groups': failed_groups,
            'success_rate': processed_groups / structure_info['total_groups'] * 100 if structure_info['total_groups'] > 0 else 0,
            'processed_list': self.processed_files,
            'failed_list': self.failed_files,
            'file_mapping': self.file_mapping,
            'merged_files': self.merged_files,
            'output_directory': str(self.output_dir)
        }

        # 保存处理摘要
        self.save_processing_summary(processing_summary)

        # 生成数据信息文件
        self.generate_data_info_file()

        return processing_summary

    def save_processing_summary(self, summary: Dict[str, any]):
        """
        保存处理摘要到文件

        参数:
        summary: 处理摘要
        """
        try:
            import json

            # 保存JSON格式摘要
            summary_file = self.output_dir / 'legacy_preprocessing_summary.json'
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)

            # 保存Markdown格式报告
            report_file = self.output_dir / 'legacy_preprocessing_report.md'
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("# 旧格式数据预处理报告\n\n")
                f.write(f"## 处理概况\n")
                f.write(f"- 总数据组数: {summary['total_groups']}\n")
                f.write(f"- 成功处理: {summary['processed_groups']}\n")
                f.write(f"- 处理失败: {summary['failed_groups']}\n")
                f.write(f"- 成功率: {summary['success_rate']:.1f}%\n\n")

                if summary['processed_list']:
                    f.write(f"## 成功处理的数据组\n")
                    for group_name in summary['processed_list']:
                        output_path = summary['file_mapping'].get(group_name, '未知')
                        f.write(f"- {group_name} -> {output_path}\n")
                    f.write("\n")

                if summary['failed_list']:
                    f.write(f"## 处理失败的数据组\n")
                    for group_name in summary['failed_list']:
                        f.write(f"- {group_name}\n")
                    f.write("\n")

                f.write(f"## 合并文件详情\n")
                for group_name, merge_info in summary['merged_files'].items():
                    f.write(f"### {group_name}\n")
                    f.write(f"- 源文件数: {len(merge_info['source_files'])}\n")
                    f.write(f"- 输出文件: {merge_info['output_file']}\n")
                    f.write(f"- 数据行数: {merge_info['rows']}\n")
                    f.write(f"- 数据列数: {merge_info['columns']}\n\n")

            print(f"   ✅ 处理摘要已保存:")
            print(f"      - {summary_file}")
            print(f"      - {report_file}")

        except Exception as e:
            print(f"   ⚠️  保存处理摘要失败: {str(e)}")

    def generate_data_info_file(self):
        """
        生成数据信息文件，用于现有系统识别
        """
        try:
            info_file = self.output_dir / 'data_info.json'

            # 统计数据信息
            data_info = {
                'data_format': 'preprocessed_legacy_format',
                'total_groups': len(self.processed_files),
                'directory_structure': 'weight/axle_type/speed',
                'file_naming': 'legacy_weight_axletype_speed.csv',
                'column_format': {
                    'count_column': 'count',
                    'sensor_columns': ['sensor_01', 'sensor_02', '...', 'sensor_20'],
                    'metadata_columns': ['speed_kmh', 'load_tons', 'axle_type', 'lane_number', 'monitoring_point',
                                       'valid_sensors_count', 'total_sensors_count', 'valid_sensors_list', 'sensor_mapping_info']
                },
                'compatible_with': 'unified_vibration_analysis.py',
                'preprocessing_date': pd.Timestamp.now().isoformat(),
                'file_mapping': self.file_mapping,
                'merged_files': self.merged_files
            }

            # 保存信息文件
            import json
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(data_info, f, ensure_ascii=False, indent=2)

            print(f"   ✅ 数据信息文件已生成: {info_file}")

        except Exception as e:
            print(f"   ⚠️  生成数据信息文件失败: {str(e)}")
