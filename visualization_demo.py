#!/usr/bin/env python3
"""
可视化功能演示
展示振动信号分析系统的全面可视化功能
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.datasets import make_regression, make_classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.metrics import r2_score, accuracy_score
from advanced_visualization import AdvancedVisualization
from visualization_manager import VisualizationManager
import os

def create_demo_data():
    """创建演示数据"""
    print("📊 创建演示数据...")
    
    # 回归数据（速度预测）
    X_reg, y_reg = make_regression(n_samples=1000, n_features=20, noise=0.1, random_state=42)
    
    # 分类数据（轴型分类）
    X_cls, y_cls = make_classification(n_samples=800, n_features=15, n_classes=3, 
                                      n_informative=10, random_state=42)
    
    datasets = {
        'speed_prediction': {
            'X': X_reg,
            'y': y_reg,
            'task_type': 'regression',
            'feature_names': [f'传感器特征_{i+1}' for i in range(20)]
        },
        'axle_classification': {
            'X': X_cls,
            'y': y_cls,
            'task_type': 'classification',
            'feature_names': [f'振动特征_{i+1}' for i in range(15)]
        }
    }
    
    return datasets

def train_demo_models(datasets):
    """训练演示模型"""
    print("🤖 训练演示模型...")
    
    results = {}
    feature_importances = {}
    
    for dataset_name, dataset in datasets.items():
        X = dataset['X']
        y = dataset['y']
        task_type = dataset['task_type']
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42,
            stratify=y if task_type == 'classification' else None
        )
        
        dataset_results = {}
        
        # 模拟多个模型的结果
        models_config = {
            'Random Forest': {'base_score': 0.85, 'time': 1.2},
            'XGBoost': {'base_score': 0.82, 'time': 2.1},
            'Extra Trees': {'base_score': 0.88, 'time': 0.8},
            'Gradient Boosting': {'base_score': 0.84, 'time': 1.8},
            'SVM': {'base_score': 0.78, 'time': 3.2},
            'BP Neural Network': {'base_score': 0.80, 'time': 15.5},
            'CNN-LSTM': {'base_score': 0.75, 'time': 25.3},
            'TCN': {'base_score': 0.77, 'time': 18.7}
        }
        
        for model_name, config in models_config.items():
            base_score = config['base_score']
            training_time = config['time']
            
            # 添加一些随机变化
            score_variation = np.random.normal(0, 0.02)
            final_score = max(0.5, min(0.95, base_score + score_variation))
            
            if task_type == 'regression':
                dataset_results[model_name] = {
                    'r2_score': final_score,
                    'mae': (1 - final_score) * 2,
                    'rmse': (1 - final_score) * 2.5,
                    'training_time': training_time
                }
            else:
                dataset_results[model_name] = {
                    'accuracy': final_score,
                    'training_time': training_time
                }
            
            # 为树模型添加特征重要性
            if any(tree_model in model_name for tree_model in ['Random Forest', 'XGBoost', 'Extra Trees', 'Gradient Boosting']):
                importance = np.random.exponential(0.1, X.shape[1])
                importance = importance / np.sum(importance)
                feature_importances[model_name] = importance
        
        results[dataset_name] = dataset_results
    
    return results, feature_importances

def create_training_histories():
    """创建训练历史数据"""
    training_histories = {}
    
    # BP神经网络训练历史
    epochs = 50
    bp_history = {
        'loss': [1.0 - 0.8 * (1 - np.exp(-i/10)) + 0.1 * np.random.random() for i in range(epochs)],
        'val_loss': [1.2 - 0.7 * (1 - np.exp(-i/12)) + 0.15 * np.random.random() for i in range(epochs)],
        'mae': [0.8 * (1 - np.exp(-i/8)) + 0.05 * np.random.random() for i in range(epochs)],
        'val_mae': [0.75 * (1 - np.exp(-i/10)) + 0.08 * np.random.random() for i in range(epochs)]
    }
    training_histories['BP Neural Network'] = bp_history
    
    # CNN-LSTM训练历史
    cnn_lstm_history = {
        'loss': [1.5 - 1.0 * (1 - np.exp(-i/15)) + 0.12 * np.random.random() for i in range(epochs)],
        'val_loss': [1.7 - 0.9 * (1 - np.exp(-i/18)) + 0.18 * np.random.random() for i in range(epochs)],
        'mae': [0.9 * (1 - np.exp(-i/12)) + 0.06 * np.random.random() for i in range(epochs)],
        'val_mae': [0.85 * (1 - np.exp(-i/15)) + 0.09 * np.random.random() for i in range(epochs)]
    }
    training_histories['CNN-LSTM'] = cnn_lstm_history
    
    return training_histories

def demo_individual_visualizations():
    """演示单个可视化功能"""
    print("\n🎨 演示单个可视化功能...")
    
    viz = AdvancedVisualization()
    
    # 创建测试数据
    np.random.seed(42)
    n_samples = 200
    
    # 1. 预测散点图演示
    print("  📈 预测散点图...")
    y_true = np.random.randn(n_samples)
    y_pred = y_true + 0.3 * np.random.randn(n_samples)
    
    viz.create_prediction_scatter(
        y_true, y_pred,
        task_name='速度预测演示', model_name='Random Forest',
        save_path='demo_prediction_scatter.png'
    )
    
    # 2. 误差分析演示
    print("  📉 误差分析图...")
    viz.create_error_analysis(
        y_true, y_pred,
        model_name='Random Forest',
        save_path='demo_error_analysis.png'
    )
    
    # 3. 混淆矩阵演示
    print("  🎯 混淆矩阵...")
    y_true_cls = np.random.randint(0, 3, n_samples)
    y_pred_cls = y_true_cls.copy()
    # 添加一些错误
    error_indices = np.random.choice(n_samples, size=30, replace=False)
    y_pred_cls[error_indices] = np.random.randint(0, 3, 30)
    
    viz.create_confusion_matrix(
        y_true_cls, y_pred_cls,
        class_names=['双轴', '三轴', '四轴'],
        model_name='XGBoost',
        save_path='demo_confusion_matrix.png'
    )
    
    # 4. 特征重要性演示
    print("  🎯 特征重要性...")
    importance = np.random.exponential(0.1, 20)
    importance = importance / np.sum(importance)
    feature_names = [f'传感器_{i+1}_RMS' for i in range(10)] + [f'频域特征_{i+1}' for i in range(10)]
    
    viz.create_feature_importance(
        importance, feature_names,
        model_name='Random Forest',
        save_path='demo_feature_importance.png'
    )
    
    # 5. 训练历史演示
    print("  📈 训练历史...")
    history = create_training_histories()['BP Neural Network']
    
    viz.create_training_history(
        history,
        model_name='BP Neural Network',
        save_path='demo_training_history.png'
    )

def demo_comprehensive_visualization():
    """演示综合可视化功能"""
    print("\n🎨 演示综合可视化功能...")
    
    # 创建数据和结果
    datasets = create_demo_data()
    results, feature_importances = train_demo_models(datasets)
    training_histories = create_training_histories()
    
    # 初始化可视化管理器
    viz_manager = VisualizationManager(output_dir='demo_visualizations')
    
    # 生成所有可视化
    viz_manager.generate_all_visualizations(
        datasets=datasets,
        results=results,
        feature_importances=feature_importances,
        training_histories=training_histories
    )

def generate_summary_report():
    """生成可视化功能总结报告"""
    print("\n📋 生成可视化功能总结报告...")
    
    report_content = []
    report_content.append("# 🎨 振动信号分析系统 - 可视化功能演示报告")
    report_content.append("=" * 80)
    report_content.append("")
    
    report_content.append("## 📊 可视化功能概览")
    report_content.append("")
    report_content.append("本系统提供了全面的模型性能可视化功能，包括：")
    report_content.append("")
    
    visualization_features = [
        "📈 **预测效果可视化**: 预测值vs实际值散点图，包含拟合线和性能指标",
        "📊 **模型性能对比**: 柱状图显示所有算法性能，包含目标线",
        "📉 **误差分析图**: 残差图、误差分布、Q-Q图、统计分析",
        "🎯 **分类任务专用图**: 混淆矩阵、ROC曲线、精确率-召回率曲线",
        "🎯 **特征重要性图**: 排序显示最重要特征",
        "📈 **训练历史图**: 深度学习模型的损失和指标曲线",
        "🔥 **性能矩阵热力图**: 所有模型在所有任务上的性能对比",
        "🌊 **特征相关性图**: 特征间相关性热力图",
        "📦 **误差箱线图**: 不同模型误差分布对比",
        "🔧 **超参数优化图**: 优化历史和参数重要性"
    ]
    
    for feature in visualization_features:
        report_content.append(f"- {feature}")
    
    report_content.append("")
    report_content.append("## 🎯 技术特点")
    report_content.append("")
    
    technical_features = [
        "**高质量图表**: 300 DPI分辨率，适合学术论文使用",
        "**专业配色**: 科学可视化标准配色方案",
        "**中英文对照**: 标题、轴标签、图例支持中英文",
        "**自动布局**: 智能调整图表布局和字体大小",
        "**统一命名**: 规范的文件命名和目录结构",
        "**批量生成**: 一键生成所有可视化图表",
        "**模块化设计**: 可独立使用各个可视化功能",
        "**错误处理**: 完善的异常处理和日志记录"
    ]
    
    for feature in technical_features:
        report_content.append(f"- {feature}")
    
    report_content.append("")
    report_content.append("## 📁 生成的图表文件")
    report_content.append("")
    
    # 列出所有生成的图表
    if os.path.exists('demo_visualizations'):
        files = os.listdir('demo_visualizations')
        files.sort()
        
        for file in files:
            if file.endswith('.png'):
                report_content.append(f"- `{file}`")
    
    report_content.append("")
    report_content.append("## 💡 使用建议")
    report_content.append("")
    report_content.append("1. **学术论文**: 所有图表均为300 DPI高分辨率，可直接用于学术论文")
    report_content.append("2. **技术报告**: 图表包含详细的性能指标和统计信息")
    report_content.append("3. **模型选择**: 通过可视化对比选择最佳算法")
    report_content.append("4. **错误诊断**: 通过误差分析图发现模型问题")
    report_content.append("5. **特征工程**: 通过特征重要性和相关性指导特征选择")
    
    # 保存报告
    with open('visualization_demo_report.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_content))
    
    print("✅ 可视化功能报告已保存: visualization_demo_report.md")

def main():
    """主演示函数"""
    print("🚀 振动信号分析系统 - 可视化功能演示")
    print("=" * 60)
    
    # 1. 演示单个可视化功能
    demo_individual_visualizations()
    
    # 2. 演示综合可视化功能
    demo_comprehensive_visualization()
    
    # 3. 生成总结报告
    generate_summary_report()
    
    print("\n🎉 可视化功能演示完成!")
    print("📁 生成的文件:")
    print("   - demo_*.png (单个功能演示图)")
    print("   - demo_visualizations/ (综合演示图表)")
    print("   - visualization_demo_report.md (功能总结报告)")

if __name__ == "__main__":
    main()
