# 传感器数据分析器使用说明

## 📋 **程序概述**

传感器数据分析器（`sensor_data_analyzer.py`）是振动信号分析系统的专用工具，用于从振动信号数据中提取指定传感器的数据，并生成对应的时频域特征可视化图表。

## ✨ **主要功能**

### 🔍 **数据处理功能**
- **多格式支持**: 读取CSV格式的振动信号数据文件
- **传感器选择**: 指定特定的传感器编号（Sensor_01到Sensor_20）
- **数据截取**: 支持指定时间段或数据点范围进行截取
- **智能预处理**: 自动去噪、滤波、异常值检测和处理

### 📊 **分析功能**
- **时域分析**: 时间序列、统计特征、自相关函数
- **频域分析**: 功率谱密度、频谱图、频段能量分布
- **时频域分析**: 短时傅里叶变换(STFT)、连续小波变换(CWT)、瞬时频率

### 🎨 **可视化输出**
- **高质量图表**: 330 DPI分辨率，Times New Roman字体，英文标签
- **统一管理**: 所有图表保存到unified_charts目录
- **规范命名**: 使用"sensor_analysis_"前缀，便于识别和管理

## 🚀 **使用方法**

### **命令行使用**

#### **基本用法**
```bash
# 分析指定传感器的完整数据
python sensor_data_analyzer.py data.csv Sensor_01

# 分析指定传感器的数据段
python sensor_data_analyzer.py data.csv Sensor_01 --start 1000 --end 5000

# 跳过预处理步骤
python sensor_data_analyzer.py data.csv Sensor_01 --no-preprocessing

# 自定义输出目录和文件前缀
python sensor_data_analyzer.py data.csv Sensor_01 --output-dir my_charts --prefix my_sensor_
```

#### **参数说明**
- `file_path`: CSV数据文件路径（必需）
- `sensor_id`: 传感器ID，如Sensor_01（必需）
- `--start`: 数据起始索引（可选）
- `--end`: 数据结束索引（可选）
- `--no-preprocessing`: 跳过数据预处理（可选）
- `--output-dir`: 输出目录（默认：unified_charts）
- `--prefix`: 文件前缀（默认：sensor_analysis_）

### **Python代码使用**

```python
from sensor_data_analyzer import SensorDataAnalyzer

# 创建分析器实例
analyzer = SensorDataAnalyzer(
    output_dir='unified_charts',
    file_prefix='sensor_analysis_'
)

# 执行完整分析
success = analyzer.analyze_sensor(
    file_path='vibration_data.csv',
    sensor_id='Sensor_01',
    start_idx=1000,
    end_idx=5000,
    apply_preprocessing=True
)

if success:
    print("分析完成！")
else:
    print("分析失败！")
```

## 📊 **输出文件说明**

### **生成的图表文件**
每次分析会生成3个高质量图表文件：

1. **时域分析图** (`{prefix}{sensor_id}_time_domain_analysis.png`)
   - 时间序列信号
   - 幅值分布直方图
   - 自相关函数
   - 统计特征柱状图

2. **频域分析图** (`{prefix}{sensor_id}_frequency_domain_analysis.png`)
   - 功率谱密度
   - 频谱图
   - 频段能量分布
   - 相位谱

3. **时频域分析图** (`{prefix}{sensor_id}_time_frequency_analysis.png`)
   - 短时傅里叶变换(STFT)
   - 连续小波变换(CWT)
   - 瞬时频率
   - 时变频段能量

### **文件命名示例**
```
unified_charts/
├── sensor_analysis_Sensor_01_time_domain_analysis.png
├── sensor_analysis_Sensor_01_frequency_domain_analysis.png
├── sensor_analysis_Sensor_01_time_frequency_analysis.png
├── sensor_analysis_Sensor_05_time_domain_analysis.png
├── sensor_analysis_Sensor_05_frequency_domain_analysis.png
└── sensor_analysis_Sensor_05_time_frequency_analysis.png
```

## 🔧 **技术规格**

### **数据要求**
- **文件格式**: CSV格式
- **数据结构**: 包含传感器列（如Sensor_01, Sensor_02等）
- **采样率**: 默认1000 Hz（可在代码中调整）
- **数据类型**: 数值型振动加速度数据

### **处理能力**
- **数据量**: 支持大型数据文件（数万到数十万数据点）
- **传感器数**: 支持1-20个传感器的数据
- **内存优化**: 智能内存管理，适合长时间序列分析

### **输出质量**
- **分辨率**: 330 DPI（学术发表级）
- **字体**: Times New Roman（国际标准）
- **语言**: 英文标签和说明
- **格式**: PNG格式，白色背景

## 📈 **应用场景**

### **科研应用**
- **学术论文**: 生成高质量的传感器分析图表
- **研究报告**: 详细的时频域特征分析
- **数据探索**: 快速了解单个传感器的信号特性

### **工程应用**
- **故障诊断**: 分析特定传感器的异常信号
- **性能监控**: 监测传感器的工作状态
- **质量控制**: 验证传感器数据的有效性

### **教学应用**
- **信号处理教学**: 演示时频域分析方法
- **实验数据分析**: 学生实验数据的可视化分析
- **概念理解**: 直观展示振动信号的特征

## ⚠️ **注意事项**

### **数据格式要求**
- CSV文件必须包含指定的传感器列
- 数据应为数值型，避免缺失值和非数值字符
- 建议数据长度至少1000个点以获得良好的频域分析效果

### **性能考虑**
- 大数据文件（>100万点）可能需要较长处理时间
- 时频域分析（特别是小波变换）计算量较大
- 建议在分析大数据时使用数据截取功能

### **输出管理**
- 重复运行会覆盖同名文件
- 建议使用不同的前缀区分不同的分析任务
- 定期清理输出目录以节省存储空间

## 🔗 **与主程序集成**

传感器数据分析器可以与主程序`unified_vibration_analysis.py`无缝集成：

```python
# 在主程序中调用传感器分析
from sensor_data_analyzer import SensorDataAnalyzer

def analyze_specific_sensor(data_file, sensor_id):
    analyzer = SensorDataAnalyzer()
    return analyzer.analyze_sensor(data_file, sensor_id)
```

## 📞 **技术支持**

### **常见问题**
1. **传感器ID不存在**: 检查CSV文件中的列名是否正确
2. **内存不足**: 使用数据截取功能分段分析
3. **图表质量问题**: 确保安装了正确的字体和图形库

### **错误处理**
程序包含完整的错误处理机制，会提供详细的错误信息和解决建议。

### **性能优化建议**
- 对于大数据文件，建议先进行数据截取
- 可以关闭预处理以加快分析速度
- 使用SSD存储可以提高文件读写速度

---

**版本**: 1.0  
**更新日期**: 2024-12-07  
**兼容性**: Python 3.7+, 与振动信号分析系统完全兼容
