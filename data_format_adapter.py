#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据格式适配器
为主系统提供新格式数据的适配接口

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import os
import json
from pathlib import Path
from typing import Dict, Optional, List
from new_data_preprocessor import NewDataPreprocessor

class DataFormatAdapter:
    """数据格式适配器"""
    
    def __init__(self):
        """初始化适配器"""
        self.preprocessor = None
        self.current_data_format = None
        self.preprocessed_data_dir = None
        
    def detect_data_format(self, data_dir: str) -> str:
        """
        检测数据格式类型
        
        参数:
        data_dir: 数据目录
        
        返回:
        format_type: 数据格式类型
        """
        data_path = Path(data_dir)
        
        if not data_path.exists():
            return "not_found"
        
        # 检查是否有data_info.json文件（预处理过的数据）
        info_file = data_path / 'data_info.json'
        if info_file.exists():
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    info = json.load(f)
                data_format = info.get('data_format')
                if data_format == 'preprocessed_new_format':
                    return "preprocessed_new_format"
                elif data_format == 'preprocessed_legacy_format':
                    return "preprocessed_legacy_format"
            except:
                pass
        
        # 检查CSV文件命名格式
        csv_files = list(data_path.glob("**/*.csv"))
        if not csv_files:
            return "no_csv_files"

        # 检查是否为旧格式目录结构（data文件夹下的三级层次结构）
        if self._is_legacy_directory_structure(data_path):
            return "legacy_format"

        # 检查是否为新格式命名
        new_format_count = 0
        old_format_count = 0

        for csv_file in csv_files[:10]:  # 检查前10个文件
            filename = csv_file.name

            # 新格式：监测点位_监测时间_AcceData_车道_轴型_载重_速度
            if '_AcceData_' in filename and '轴' in filename and 't' in filename and 'km' in filename:
                new_format_count += 1
            # 旧格式：acce_开头或包含sensor
            elif filename.startswith('acce_') or 'sensor' in filename.lower():
                old_format_count += 1

        if new_format_count > old_format_count:
            return "new_format"
        elif old_format_count > 0:
            return "old_format"
        else:
            return "unknown_format"

    def _is_legacy_directory_structure(self, data_path: Path) -> bool:
        """
        检查是否为旧格式的目录结构

        参数:
        data_path: 数据目录路径

        返回:
        is_legacy: 是否为旧格式目录结构
        """
        try:
            # 检查是否有三级目录结构：轴重/轴型/速度
            subdirs = [d for d in data_path.iterdir() if d.is_dir()]

            if not subdirs:
                return False

            # 检查第一级目录（轴重）
            weight_dirs = 0
            for weight_dir in subdirs:
                if '吨' in weight_dir.name:
                    weight_dirs += 1

                    # 检查第二级目录（轴型）
                    axle_dirs = [d for d in weight_dir.iterdir() if d.is_dir()]
                    axle_type_dirs = 0

                    for axle_dir in axle_dirs:
                        if '轴' in axle_dir.name:
                            axle_type_dirs += 1

                            # 检查第三级目录（速度）
                            speed_dirs = [d for d in axle_dir.iterdir() if d.is_dir()]
                            speed_dirs_count = 0

                            for speed_dir in speed_dirs:
                                if 'km' in speed_dir.name and ('h' in speed_dir.name or '_' in speed_dir.name):
                                    speed_dirs_count += 1

                                    # 检查是否包含CSV文件
                                    csv_files = list(speed_dir.glob("*.csv"))
                                    if len(csv_files) >= 1:  # 至少有一个CSV文件
                                        return True

            # 如果找到了符合条件的目录结构，认为是旧格式
            return weight_dirs > 0

        except Exception as e:
            return False
    
    def preprocess_new_format_data(self, input_dir: str, output_dir: str = "preprocessed_data") -> Dict[str, any]:
        """
        预处理新格式数据
        
        参数:
        input_dir: 输入目录
        output_dir: 输出目录
        
        返回:
        result: 预处理结果
        """
        print(f"🔄 开始预处理新格式数据...")
        print(f"   输入目录: {input_dir}")
        print(f"   输出目录: {output_dir}")
        
        try:
            # 初始化预处理器
            self.preprocessor = NewDataPreprocessor(input_dir, output_dir)
            
            # 执行预处理
            summary = self.preprocessor.process_all_files()
            
            if summary['success'] and summary['processed_files'] > 0:
                self.preprocessed_data_dir = output_dir
                self.current_data_format = "preprocessed_new_format"
                
                print(f"✅ 新格式数据预处理完成")
                print(f"   成功处理: {summary['processed_files']}/{summary['total_files']} 个文件")
                print(f"   成功率: {summary['success_rate']:.1f}%")
                
                return {
                    'success': True,
                    'output_dir': output_dir,
                    'summary': summary
                }
            else:
                print(f"❌ 新格式数据预处理失败")
                return {
                    'success': False,
                    'message': '预处理失败或无有效文件'
                }
                
        except Exception as e:
            print(f"❌ 预处理过程出错: {str(e)}")
            return {
                'success': False,
                'message': str(e)
            }

    def preprocess_legacy_format_data(self, input_dir: str, output_dir: str = "legacy_preprocessed_data") -> Dict[str, any]:
        """
        预处理旧格式数据

        参数:
        input_dir: 输入目录
        output_dir: 输出目录

        返回:
        result: 预处理结果
        """
        print(f"🔄 开始预处理旧格式数据...")
        print(f"   输入目录: {input_dir}")
        print(f"   输出目录: {output_dir}")

        try:
            from legacy_data_preprocessor import LegacyDataPreprocessor

            # 初始化预处理器
            preprocessor = LegacyDataPreprocessor(input_dir, output_dir)

            # 执行预处理
            summary = preprocessor.process_all_groups()

            if summary['success'] and summary['processed_groups'] > 0:
                self.preprocessed_data_dir = output_dir
                self.current_data_format = "preprocessed_legacy_format"

                print(f"✅ 旧格式数据预处理完成")
                print(f"   成功处理: {summary['processed_groups']}/{summary['total_groups']} 个数据组")
                print(f"   成功率: {summary['success_rate']:.1f}%")

                return {
                    'success': True,
                    'output_dir': output_dir,
                    'summary': summary
                }
            else:
                print(f"❌ 旧格式数据预处理失败")
                return {
                    'success': False,
                    'message': '预处理失败或无有效数据组'
                }

        except ImportError as e:
            print(f"❌ 旧格式预处理器导入失败: {str(e)}")
            return {
                'success': False,
                'message': f'导入失败: {str(e)}'
            }
        except Exception as e:
            print(f"❌ 预处理过程出错: {str(e)}")
            return {
                'success': False,
                'message': str(e)
            }
    
    def get_compatible_data_dir(self, original_data_dir: str) -> Optional[str]:
        """
        获取与主系统兼容的数据目录
        
        参数:
        original_data_dir: 原始数据目录
        
        返回:
        compatible_dir: 兼容的数据目录路径
        """
        # 检测数据格式
        format_type = self.detect_data_format(original_data_dir)
        
        print(f"🔍 检测到数据格式: {format_type}")
        
        if format_type == "old_format":
            # 已经是旧格式，直接返回
            print(f"   ✅ 数据已为兼容格式")
            return original_data_dir
            
        elif format_type == "preprocessed_new_format":
            # 已经预处理过的新格式
            print(f"   ✅ 数据已预处理为兼容格式")
            return original_data_dir

        elif format_type == "preprocessed_legacy_format":
            # 已经预处理过的旧格式
            print(f"   ✅ 旧格式数据已预处理为兼容格式")
            return original_data_dir

        elif format_type == "new_format":
            # 需要预处理的新格式
            print(f"   🔄 需要预处理新格式数据")

            # 生成输出目录名
            output_dir = f"{original_data_dir}_preprocessed"

            # 执行预处理
            result = self.preprocess_new_format_data(original_data_dir, output_dir)

            if result['success']:
                return output_dir
            else:
                print(f"   ❌ 预处理失败，无法提供兼容数据")
                return None

        elif format_type == "legacy_format":
            # 需要预处理的旧格式
            print(f"   🔄 需要预处理旧格式数据")

            # 生成输出目录名
            output_dir = f"{original_data_dir}_legacy_preprocessed"

            # 执行预处理
            result = self.preprocess_legacy_format_data(original_data_dir, output_dir)

            if result['success']:
                return output_dir
            else:
                print(f"   ❌ 旧格式预处理失败，无法提供兼容数据")
                return None
                
        else:
            print(f"   ❌ 未识别的数据格式或无有效数据")
            return None
    
    def get_preprocessing_info(self) -> Dict[str, any]:
        """
        获取预处理信息
        
        返回:
        info: 预处理信息
        """
        if self.preprocessed_data_dir and os.path.exists(self.preprocessed_data_dir):
            info_file = Path(self.preprocessed_data_dir) / 'data_info.json'
            
            if info_file.exists():
                try:
                    with open(info_file, 'r', encoding='utf-8') as f:
                        return json.load(f)
                except:
                    pass
        
        return {}
    
    def generate_usage_instructions(self, output_file: str = "data_preprocessing_instructions.md"):
        """
        生成使用说明
        
        参数:
        output_file: 输出文件名
        """
        instructions = """# 新格式数据预处理使用说明

## 概述
本模块用于将新命名格式的CSV文件转换为与现有振动信号分析系统兼容的格式。

## 新格式文件命名规则
```
监测点位_监测时间_AcceData_车道_轴型_载重_速度.csv
```

### 示例
```
GW100001_20231101174605_AcceData_车道1_2轴-2.5t-100kmh.csv
```

### 命名组成部分
- **监测点位**: 如GW100001
- **监测时间**: YYYYMMDDHHMMSS格式
- **固定标识**: AcceData
- **车道信息**: 如车道1
- **轴型**: 如2轴、3轴
- **载重**: 如2.5t、25t
- **速度**: 如100kmh、60km/h

## 使用方法

### 1. 自动预处理（推荐）
```python
# 运行主系统时自动检测和预处理
python unified_vibration_analysis.py
```

### 2. 手动预处理
```python
from data_format_adapter import DataFormatAdapter

# 初始化适配器
adapter = DataFormatAdapter()

# 获取兼容的数据目录
compatible_dir = adapter.get_compatible_data_dir("your_new_format_data_dir")

# 使用兼容目录运行分析
if compatible_dir:
    # 设置数据目录并运行分析
    pass
```

### 3. 直接使用预处理器
```python
from new_data_preprocessor import NewDataPreprocessor

# 初始化预处理器
preprocessor = NewDataPreprocessor("input_dir", "output_dir")

# 处理所有文件
summary = preprocessor.process_all_files()
```

## 输出结构
预处理后的数据将按以下结构组织：
```
preprocessed_data/
├── 2.5吨/
│   └── 双轴/
│       └── 100km_h/
│           └── acce_GW100001_20231101174605_lane1.csv
├── 25吨/
│   └── 三轴/
│       └── 60km_h/
│           └── acce_GW100002_20231101180000_lane1.csv
└── data_info.json
```

## 数据格式转换
- **列名标准化**: acc01, acc02, ... → sensor_01, sensor_02, ...
- **元数据添加**: 添加speed_kmh, load_tons, axle_type等列
- **数据清理**: 处理缺失值和异常值
- **格式验证**: 确保与现有系统兼容

## 兼容性验证
系统会自动验证：
- ✅ 列结构正确性
- ✅ 数据类型一致性
- ✅ 元数据完整性
- ✅ 文件格式兼容性

## 注意事项
1. 确保原始CSV文件包含21列数据（1个count列 + 20个传感器列）
2. 文件命名必须严格遵循指定格式
3. 传感器数据应为数值型
4. 预处理过程会自动处理编码问题

## 故障排除
- **文件名解析失败**: 检查文件命名格式是否正确
- **CSV结构验证失败**: 确认文件包含正确的列数和数据类型
- **数据读取失败**: 检查文件编码和格式
- **兼容性验证失败**: 查看详细错误信息进行调试
"""
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(instructions)
            print(f"✅ 使用说明已生成: {output_file}")
        except Exception as e:
            print(f"⚠️  生成使用说明失败: {str(e)}")

# 便捷函数
def auto_preprocess_data(data_dir: str) -> Optional[str]:
    """
    自动预处理数据的便捷函数
    
    参数:
    data_dir: 数据目录
    
    返回:
    compatible_dir: 兼容的数据目录
    """
    adapter = DataFormatAdapter()
    return adapter.get_compatible_data_dir(data_dir)

def check_data_format(data_dir: str) -> str:
    """
    检查数据格式的便捷函数
    
    参数:
    data_dir: 数据目录
    
    返回:
    format_type: 数据格式类型
    """
    adapter = DataFormatAdapter()
    return adapter.detect_data_format(data_dir)
