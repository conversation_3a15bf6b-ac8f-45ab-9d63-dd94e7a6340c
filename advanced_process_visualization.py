#!/usr/bin/env python3
"""
高级数据处理流程可视化模块
包含时频分析、传感器融合、算法对比等专业可视化
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import signal
from scipy.fft import fft, fftfreq
import os
from process_visualization import ProcessVisualization

class AdvancedProcessVisualization(ProcessVisualization):
    """高级数据处理流程可视化器"""
    
    def __init__(self, output_dir='process_visualization'):
        """初始化高级可视化器"""
        super().__init__(output_dir)
    
    def create_time_frequency_analysis(self, signal_data=None, fs=1000):
        """创建时频分析图（小波变换、短时傅里叶变换）"""
        print("📊 生成时频分析图...")
        
        # 生成模拟信号
        if signal_data is None:
            t = np.linspace(0, 2, 2*fs)
            # 创建频率变化的信号
            signal_data = (np.sin(2*np.pi*10*t) * (t < 0.5) + 
                          np.sin(2*np.pi*25*t) * ((t >= 0.5) & (t < 1.0)) + 
                          np.sin(2*np.pi*50*t) * ((t >= 1.0) & (t < 1.5)) + 
                          np.sin(2*np.pi*15*t) * (t >= 1.5) + 
                          0.1 * np.random.randn(len(t)))
        else:
            t = np.linspace(0, len(signal_data)/fs, len(signal_data))
        
        # 生成中英文版本
        for language in ['chinese', 'english']:
            self.set_fonts(language)
            
            fig = plt.figure(figsize=(17.8/2.54, 15/2.54))
            gs = fig.add_gridspec(4, 2, height_ratios=[1, 1.5, 1.5, 1.5])
            
            if language == 'chinese':
                fig.suptitle('时频分析：小波变换与短时傅里叶变换', fontsize=16, fontweight='bold')
            else:
                fig.suptitle('Time-Frequency Analysis: Wavelet Transform and STFT', fontsize=16, fontweight='bold')
            
            # 原始信号
            ax1 = fig.add_subplot(gs[0, :])
            ax1.plot(t, signal_data, color=self.colors['primary'], linewidth=1.0)
            if language == 'chinese':
                ax1.set_title('原始信号', fontsize=14)
                ax1.set_ylabel('幅值 (m/s²)', fontsize=12)
            else:
                ax1.set_title('Original Signal', fontsize=14)
                ax1.set_ylabel('Amplitude (m/s²)', fontsize=12)
            ax1.grid(True, alpha=0.3)
            
            # 短时傅里叶变换
            ax2 = fig.add_subplot(gs[1, 0])
            f, t_stft, Zxx = signal.stft(signal_data, fs, nperseg=256)
            ax2.pcolormesh(t_stft, f, np.abs(Zxx), shading='gouraud', cmap='viridis')
            if language == 'chinese':
                ax2.set_title('短时傅里叶变换 (STFT)', fontsize=14)
                ax2.set_ylabel('频率 (Hz)', fontsize=12)
                ax2.set_xlabel('时间 (s)', fontsize=12)
            else:
                ax2.set_title('Short-Time Fourier Transform (STFT)', fontsize=14)
                ax2.set_ylabel('Frequency (Hz)', fontsize=12)
                ax2.set_xlabel('Time (s)', fontsize=12)
            ax2.set_ylim(0, 100)
            
            # 连续小波变换（模拟）
            ax3 = fig.add_subplot(gs[1, 1])
            # 创建小波变换的模拟结果
            scales = np.arange(1, 128)
            coefficients = np.random.rand(len(scales), len(signal_data)) * np.abs(signal_data)
            im = ax3.imshow(coefficients, aspect='auto', cmap='viridis', 
                           extent=[t[0], t[-1], scales[-1], scales[0]])
            if language == 'chinese':
                ax3.set_title('连续小波变换 (CWT)', fontsize=14)
                ax3.set_ylabel('尺度', fontsize=12)
                ax3.set_xlabel('时间 (s)', fontsize=12)
            else:
                ax3.set_title('Continuous Wavelet Transform (CWT)', fontsize=14)
                ax3.set_ylabel('Scale', fontsize=12)
                ax3.set_xlabel('Time (s)', fontsize=12)
            
            # 频谱图
            ax4 = fig.add_subplot(gs[2, :])
            f, t_spec, Sxx = signal.spectrogram(signal_data, fs, nperseg=256)
            pcm = ax4.pcolormesh(t_spec, f, 10 * np.log10(Sxx), shading='gouraud', cmap='plasma')
            if language == 'chinese':
                ax4.set_title('功率谱密度谱图', fontsize=14)
                ax4.set_ylabel('频率 (Hz)', fontsize=12)
                ax4.set_xlabel('时间 (s)', fontsize=12)
            else:
                ax4.set_title('Power Spectral Density Spectrogram', fontsize=14)
                ax4.set_ylabel('Frequency (Hz)', fontsize=12)
                ax4.set_xlabel('Time (s)', fontsize=12)
            ax4.set_ylim(0, 100)
            plt.colorbar(pcm, ax=ax4, label='功率 (dB)' if language == 'chinese' else 'Power (dB)')
            
            # 瞬时频率
            ax5 = fig.add_subplot(gs[3, :])
            analytic_signal = signal.hilbert(signal_data)
            instantaneous_phase = np.unwrap(np.angle(analytic_signal))
            instantaneous_frequency = (np.diff(instantaneous_phase) / (2.0*np.pi) * fs)
            ax5.plot(t[1:], instantaneous_frequency, color=self.colors['accent'], linewidth=1.5)
            if language == 'chinese':
                ax5.set_title('瞬时频率', fontsize=14)
                ax5.set_ylabel('频率 (Hz)', fontsize=12)
                ax5.set_xlabel('时间 (s)', fontsize=12)
            else:
                ax5.set_title('Instantaneous Frequency', fontsize=14)
                ax5.set_ylabel('Frequency (Hz)', fontsize=12)
                ax5.set_xlabel('Time (s)', fontsize=12)
            ax5.grid(True, alpha=0.3)
            ax5.set_ylim(0, 100)
            
            plt.tight_layout()
            
            # 保存图片
            if language == 'chinese':
                filename = '时频分析_流程图_中文.png'
            else:
                filename = 'time_frequency_analysis_process_diagram_english.png'
            
            save_path = os.path.join(self.output_dir, language, filename)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✅ {language.upper()}版时频分析图已保存: {save_path}")
    
    def create_sensor_fusion_visualization(self, sensor_data=None):
        """创建传感器数据融合过程图"""
        print("📊 生成传感器数据融合图...")
        
        # 生成模拟多传感器数据
        if sensor_data is None:
            t = np.linspace(0, 5, 5000)
            sensor_data = {
                'sensor_1': 0.8 * np.sin(2*np.pi*10*t) + 0.2 * np.random.randn(len(t)),
                'sensor_2': 0.6 * np.sin(2*np.pi*12*t + np.pi/4) + 0.2 * np.random.randn(len(t)),
                'sensor_3': 0.7 * np.sin(2*np.pi*8*t + np.pi/2) + 0.2 * np.random.randn(len(t)),
                'sensor_4': 0.5 * np.sin(2*np.pi*15*t + np.pi/3) + 0.2 * np.random.randn(len(t))
            }
            
            # 添加车辆通过事件
            event_time = 2.5
            event_idx = int(event_time * 1000)
            event_width = 500
            for sensor in sensor_data:
                event_signal = 2.0 * np.exp(-((t - event_time)**2) / 0.1)
                sensor_data[sensor] += event_signal
        
        # 计算融合信号
        signals = np.array(list(sensor_data.values()))
        
        # 不同融合方法
        fusion_methods = {
            'mean': np.mean(signals, axis=0),
            'rms': np.sqrt(np.mean(signals**2, axis=0)),
            'weighted': 0.4*signals[0] + 0.3*signals[1] + 0.2*signals[2] + 0.1*signals[3],
            'pca': signals[0] + signals[1] - signals[2]  # 简化的PCA
        }
        
        # 生成中英文版本
        for language in ['chinese', 'english']:
            self.set_fonts(language)
            
            fig, axes = plt.subplots(3, 2, figsize=(17.8/2.54, 15/2.54))
            
            if language == 'chinese':
                fig.suptitle('多传感器数据融合过程', fontsize=16, fontweight='bold')
                sensor_names = ['传感器1', '传感器2', '传感器3', '传感器4']
                fusion_names = ['均值融合', 'RMS融合', '加权融合', 'PCA融合']
            else:
                fig.suptitle('Multi-Sensor Data Fusion Process', fontsize=16, fontweight='bold')
                sensor_names = ['Sensor 1', 'Sensor 2', 'Sensor 3', 'Sensor 4']
                fusion_names = ['Mean Fusion', 'RMS Fusion', 'Weighted Fusion', 'PCA Fusion']
            
            # 原始传感器数据
            colors = [self.colors['primary'], self.colors['secondary'], 
                     self.colors['accent'], self.colors['success']]
            
            for i, (sensor, data) in enumerate(sensor_data.items()):
                ax = axes[i//2, i%2]
                ax.plot(t, data, color=colors[i], linewidth=1.0)
                ax.set_title(sensor_names[i], fontsize=12)
                ax.set_ylabel('幅值 (m/s²)' if language == 'chinese' else 'Amplitude (m/s²)', fontsize=10)
                if i >= 2:
                    ax.set_xlabel('时间 (s)' if language == 'chinese' else 'Time (s)', fontsize=10)
                ax.grid(True, alpha=0.3)
                ax.set_xlim(0, 5)
            
            # 融合结果对比
            ax_fusion = axes[2, :]
            ax_fusion[0].remove()
            ax_fusion[1].remove()
            ax_fusion_combined = fig.add_subplot(3, 1, 3)
            
            fusion_colors = [self.colors['primary'], self.colors['secondary'], 
                           self.colors['accent'], self.colors['warning']]
            
            for i, (method, fused_signal) in enumerate(fusion_methods.items()):
                ax_fusion_combined.plot(t, fused_signal, color=fusion_colors[i], 
                                      linewidth=1.5, label=fusion_names[i], alpha=0.8)
            
            if language == 'chinese':
                ax_fusion_combined.set_title('融合结果对比', fontsize=14)
                ax_fusion_combined.set_ylabel('融合信号幅值 (m/s²)', fontsize=12)
                ax_fusion_combined.set_xlabel('时间 (s)', fontsize=12)
            else:
                ax_fusion_combined.set_title('Fusion Results Comparison', fontsize=14)
                ax_fusion_combined.set_ylabel('Fused Signal Amplitude (m/s²)', fontsize=12)
                ax_fusion_combined.set_xlabel('Time (s)', fontsize=12)
            
            ax_fusion_combined.legend(loc='upper right')
            ax_fusion_combined.grid(True, alpha=0.3)
            ax_fusion_combined.set_xlim(0, 5)
            
            plt.tight_layout()
            
            # 保存图片
            if language == 'chinese':
                filename = '传感器融合_流程图_中文.png'
            else:
                filename = 'sensor_fusion_process_diagram_english.png'
            
            save_path = os.path.join(self.output_dir, language, filename)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✅ {language.upper()}版传感器融合图已保存: {save_path}")
    
    def create_algorithm_performance_radar(self, performance_data=None):
        """创建算法性能对比雷达图"""
        print("📊 生成算法性能雷达图...")
        
        # 生成模拟性能数据
        if performance_data is None:
            algorithms = ['Random Forest', 'XGBoost', 'SVM', 'Deep Learning', 'Ensemble']
            metrics = ['Accuracy', 'Speed', 'Robustness', 'Interpretability', 'Memory Usage']
            
            # 性能数据 (0-1标准化)
            performance_data = {
                'Random Forest': [0.85, 0.90, 0.80, 0.95, 0.75],
                'XGBoost': [0.88, 0.85, 0.85, 0.70, 0.80],
                'SVM': [0.82, 0.60, 0.90, 0.60, 0.85],
                'Deep Learning': [0.90, 0.40, 0.75, 0.30, 0.50],
                'Ensemble': [0.92, 0.70, 0.88, 0.50, 0.65]
            }
        
        # 生成中英文版本
        for language in ['chinese', 'english']:
            self.set_fonts(language)
            
            fig, ax = plt.subplots(figsize=(12/2.54, 12/2.54), subplot_kw=dict(projection='polar'))
            
            if language == 'chinese':
                fig.suptitle('算法性能对比雷达图', fontsize=16, fontweight='bold', y=0.95)
                metrics_cn = ['准确率', '速度', '鲁棒性', '可解释性', '内存使用']
                algorithms_cn = ['随机森林', 'XGBoost', '支持向量机', '深度学习', '集成模型']
                metrics_labels = metrics_cn
                algorithms_labels = algorithms_cn
            else:
                fig.suptitle('Algorithm Performance Comparison Radar Chart', fontsize=16, fontweight='bold', y=0.95)
                metrics_labels = metrics
                algorithms_labels = algorithms
            
            # 设置角度
            angles = np.linspace(0, 2 * np.pi, len(metrics_labels), endpoint=False).tolist()
            angles += angles[:1]  # 闭合图形

            # 绘制每个算法
            colors = [self.colors['primary'], self.colors['secondary'], self.colors['accent'],
                     self.colors['warning'], self.colors['success']]

            for i, (alg, alg_label) in enumerate(zip(algorithms, algorithms_labels)):
                values = performance_data[alg].copy()  # 创建副本
                values.append(values[0])  # 闭合图形

                ax.plot(angles, values, 'o-', linewidth=2, label=alg_label,
                       color=colors[i], markersize=4)
                ax.fill(angles, values, alpha=0.1, color=colors[i])
            
            # 设置标签
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(metrics_labels, fontsize=11)
            ax.set_ylim(0, 1)
            ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
            ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'], fontsize=10)
            ax.grid(True, alpha=0.3)
            
            # 图例
            ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=10)
            
            plt.tight_layout()
            
            # 保存图片
            if language == 'chinese':
                filename = '算法性能雷达图_流程图_中文.png'
            else:
                filename = 'algorithm_performance_radar_process_diagram_english.png'
            
            save_path = os.path.join(self.output_dir, language, filename)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✅ {language.upper()}版算法性能雷达图已保存: {save_path}")

def main():
    """测试函数"""
    print("🧪 测试高级数据处理流程可视化...")
    
    # 初始化高级可视化器
    viz = AdvancedProcessVisualization()
    
    # 生成时频分析图
    viz.create_time_frequency_analysis()
    
    # 生成传感器融合图
    viz.create_sensor_fusion_visualization()
    
    # 生成算法性能雷达图
    viz.create_algorithm_performance_radar()
    
    print("✅ 测试完成!")

if __name__ == "__main__":
    main()
