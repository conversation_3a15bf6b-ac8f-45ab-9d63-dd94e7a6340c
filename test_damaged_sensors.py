#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
传感器损坏场景测试脚本
测试新格式数据预处理系统处理传感器损坏情况的能力

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import os
import sys
import tempfile
import shutil
import numpy as np
import pandas as pd
from pathlib import Path

def create_damaged_sensor_test_data():
    """创建包含损坏传感器的测试数据"""
    print("🔧 创建包含损坏传感器的测试数据...")
    
    # 创建临时目录
    test_dir = Path("test_damaged_sensors_data")
    test_dir.mkdir(exist_ok=True)
    
    n_samples = 1000
    test_files = []
    
    # 场景1: 大部分传感器正常，少数损坏
    print("   📄 场景1: 15个正常传感器 + 5个损坏传感器")
    data1 = create_sensor_data_scenario1(n_samples)
    file1 = test_dir / "GW100001_20231101174605_AcceData_车道1_2轴-2.5t-100kmh.csv"
    pd.DataFrame(data1).to_csv(file1, index=False)
    test_files.append(file1)
    
    # 场景2: 一半传感器损坏
    print("   📄 场景2: 10个正常传感器 + 10个损坏传感器")
    data2 = create_sensor_data_scenario2(n_samples)
    file2 = test_dir / "GW100002_20231101180000_AcceData_车道1_3轴-25t-60kmh.csv"
    pd.DataFrame(data2).to_csv(file2, index=False)
    test_files.append(file2)
    
    # 场景3: 大部分传感器损坏（边界情况）
    print("   📄 场景3: 5个正常传感器 + 15个损坏传感器")
    data3 = create_sensor_data_scenario3(n_samples)
    file3 = test_dir / "GW100003_20231101182000_AcceData_车道2_2轴-5t-80kmh.csv"
    pd.DataFrame(data3).to_csv(file3, index=False)
    test_files.append(file3)
    
    # 场景4: 传感器数量不足（应该失败）
    print("   📄 场景4: 3个正常传感器 + 17个损坏传感器（应该失败）")
    data4 = create_sensor_data_scenario4(n_samples)
    file4 = test_dir / "GW100004_20231101184500_AcceData_车道1_4轴-40t-50kmh.csv"
    pd.DataFrame(data4).to_csv(file4, index=False)
    test_files.append(file4)
    
    print(f"   ✅ 创建了 {len(test_files)} 个测试文件")
    return str(test_dir), test_files

def create_sensor_data_scenario1(n_samples):
    """场景1: 15个正常传感器 + 5个损坏传感器"""
    data = {}
    
    # count列
    data['0'] = range(n_samples)
    # 无用列
    data['1'] = np.random.randn(n_samples) * 0.1
    
    # 15个正常传感器 (acce01-acce15)
    for i in range(1, 16):
        t = np.arange(n_samples) / 1000
        signal = 2 * np.sin(2 * np.pi * (10 + i) * t) * np.exp(-((t-0.5)**2)/0.3)
        noise = 0.3 * np.random.randn(n_samples)
        data[f'acce{i:02d}'] = signal + noise
    
    # 5个损坏传感器 (acce16-acce20)
    # acce16: 全零
    data['acce16'] = np.zeros(n_samples)
    # acce17: 全NaN
    data['acce17'] = np.full(n_samples, np.nan)
    # acce18: 异常值过多
    data['acce18'] = np.random.randn(n_samples) * 1000
    # acce19: 无变化
    data['acce19'] = np.full(n_samples, 5.0)
    # acce20: 零值过多
    zeros = np.zeros(n_samples)
    zeros[::100] = np.random.randn(n_samples//100)  # 只有1%非零值
    data['acce20'] = zeros
    
    return data

def create_sensor_data_scenario2(n_samples):
    """场景2: 10个正常传感器 + 10个损坏传感器"""
    data = {}
    
    # count列
    data['0'] = range(n_samples)
    # 无用列
    data['1'] = np.random.randn(n_samples) * 0.1
    
    # 10个正常传感器 (acce01-acce10)
    for i in range(1, 11):
        t = np.arange(n_samples) / 1000
        signal = 1.5 * np.sin(2 * np.pi * (8 + i * 2) * t) * np.exp(-((t-0.5)**2)/0.4)
        noise = 0.2 * np.random.randn(n_samples)
        data[f'acce{i:02d}'] = signal + noise
    
    # 10个损坏传感器 (acce11-acce20)
    for i in range(11, 21):
        if i % 4 == 0:
            data[f'acce{i:02d}'] = np.zeros(n_samples)  # 全零
        elif i % 4 == 1:
            data[f'acce{i:02d}'] = np.full(n_samples, np.nan)  # 全NaN
        elif i % 4 == 2:
            data[f'acce{i:02d}'] = np.random.randn(n_samples) * 500  # 异常值
        else:
            data[f'acce{i:02d}'] = np.full(n_samples, 3.0)  # 无变化
    
    return data

def create_sensor_data_scenario3(n_samples):
    """场景3: 5个正常传感器 + 15个损坏传感器"""
    data = {}
    
    # count列
    data['0'] = range(n_samples)
    # 无用列
    data['1'] = np.random.randn(n_samples) * 0.1
    
    # 5个正常传感器 (acce01-acce05)
    for i in range(1, 6):
        t = np.arange(n_samples) / 1000
        signal = 3 * np.sin(2 * np.pi * (5 + i * 3) * t) * np.exp(-((t-0.5)**2)/0.2)
        noise = 0.4 * np.random.randn(n_samples)
        data[f'acce{i:02d}'] = signal + noise
    
    # 15个损坏传感器 (acce06-acce20)
    for i in range(6, 21):
        if i % 3 == 0:
            data[f'acce{i:02d}'] = np.zeros(n_samples)  # 全零
        elif i % 3 == 1:
            data[f'acce{i:02d}'] = np.full(n_samples, np.nan)  # 全NaN
        else:
            data[f'acce{i:02d}'] = np.full(n_samples, 1.0)  # 无变化
    
    return data

def create_sensor_data_scenario4(n_samples):
    """场景4: 3个正常传感器 + 17个损坏传感器（应该失败）"""
    data = {}
    
    # count列
    data['0'] = range(n_samples)
    # 无用列
    data['1'] = np.random.randn(n_samples) * 0.1
    
    # 3个正常传感器 (acce01-acce03)
    for i in range(1, 4):
        t = np.arange(n_samples) / 1000
        signal = 2.5 * np.sin(2 * np.pi * (12 + i * 4) * t) * np.exp(-((t-0.5)**2)/0.25)
        noise = 0.35 * np.random.randn(n_samples)
        data[f'acce{i:02d}'] = signal + noise
    
    # 17个损坏传感器 (acce04-acce20)
    for i in range(4, 21):
        data[f'acce{i:02d}'] = np.zeros(n_samples)  # 全部为零
    
    return data

def test_damaged_sensor_processing():
    """测试损坏传感器处理功能"""
    print("🧪 测试损坏传感器处理功能...")
    
    try:
        from new_data_preprocessor import NewDataPreprocessor
        
        # 创建测试数据
        test_dir, test_files = create_damaged_sensor_test_data()
        output_dir = "test_damaged_output"
        
        # 初始化预处理器
        preprocessor = NewDataPreprocessor(test_dir, output_dir)
        
        # 处理所有测试文件
        summary = preprocessor.process_all_files()
        
        print(f"\n📊 处理结果:")
        print(f"   总文件数: {summary['total_files']}")
        print(f"   成功处理: {summary['processed_files']}")
        print(f"   处理失败: {summary['failed_files']}")
        print(f"   成功率: {summary['success_rate']:.1f}%")
        
        # 分析处理结果
        expected_results = {
            'GW100001_20231101174605_AcceData_车道1_2轴-2.5t-100kmh.csv': True,   # 15个正常传感器
            'GW100002_20231101180000_AcceData_车道1_3轴-25t-60kmh.csv': True,   # 10个正常传感器
            'GW100003_20231101182000_AcceData_车道2_2轴-5t-80kmh.csv': True,   # 5个正常传感器
            'GW100004_20231101184500_AcceData_车道1_4轴-40t-50kmh.csv': False,  # 3个正常传感器（应该失败）
        }
        
        correct_predictions = 0
        for filename, expected in expected_results.items():
            actual = filename in summary['processed_list']
            if actual == expected:
                correct_predictions += 1
                status = "✅ 正确"
            else:
                status = "❌ 错误"
            
            print(f"   {filename}: 期望{'成功' if expected else '失败'}, 实际{'成功' if actual else '失败'} {status}")
        
        accuracy = correct_predictions / len(expected_results)
        print(f"\n📈 预测准确率: {accuracy:.1%}")
        
        # 检查输出文件的传感器数量
        if summary['processed_files'] > 0:
            print(f"\n🔍 检查输出文件传感器数量:")
            output_path = Path(output_dir)
            for csv_file in output_path.glob("**/*.csv"):
                try:
                    df = pd.read_csv(csv_file)
                    sensor_cols = [col for col in df.columns if col.startswith('sensor_')]
                    valid_count = df['valid_sensors_count'].iloc[0] if 'valid_sensors_count' in df.columns else 'N/A'
                    total_count = df['total_sensors_count'].iloc[0] if 'total_sensors_count' in df.columns else 'N/A'
                    
                    print(f"   {csv_file.name}: {len(sensor_cols)}个传感器列 (有效:{valid_count}/总计:{total_count})")
                except Exception as e:
                    print(f"   {csv_file.name}: 读取失败 - {str(e)}")
        
        # 清理测试数据
        try:
            shutil.rmtree(test_dir)
            shutil.rmtree(output_dir)
            print(f"\n🧹 测试数据已清理")
        except:
            pass
        
        return accuracy >= 0.75  # 至少75%的预测准确率
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        return False

def test_sensor_validity_check():
    """测试传感器有效性检查功能"""
    print("🧪 测试传感器有效性检查功能...")
    
    try:
        from new_data_preprocessor import NewDataPreprocessor
        
        preprocessor = NewDataPreprocessor("dummy", "dummy")
        
        # 创建各种类型的传感器数据
        n_samples = 1000
        
        test_cases = [
            ("正常传感器", np.sin(np.linspace(0, 10*np.pi, n_samples)) + 0.1*np.random.randn(n_samples), True),
            ("全零传感器", np.zeros(n_samples), False),
            ("全NaN传感器", np.full(n_samples, np.nan), False),
            ("无变化传感器", np.full(n_samples, 5.0), False),
            ("异常值过多", np.random.randn(n_samples) * 1000, False),
            ("零值过多", np.concatenate([np.zeros(900), np.random.randn(100)]), False),
            ("缺失值过多", np.concatenate([np.full(600, np.nan), np.random.randn(400)]), False),
        ]
        
        correct_predictions = 0
        
        for name, data, expected in test_cases:
            sensor_data = pd.Series(data)
            is_valid, reason = preprocessor._check_sensor_validity(sensor_data, name)
            
            if is_valid == expected:
                correct_predictions += 1
                status = "✅ 正确"
            else:
                status = "❌ 错误"
            
            print(f"   {name}: 期望{'有效' if expected else '无效'}, 实际{'有效' if is_valid else '无效'} ({reason}) {status}")
        
        accuracy = correct_predictions / len(test_cases)
        print(f"\n📈 检查准确率: {accuracy:.1%}")
        
        return accuracy >= 0.85  # 至少85%的检查准确率
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 传感器损坏场景测试")
    print("=" * 60)
    
    test_results = []
    
    # 执行测试
    test_results.append(("传感器有效性检查", test_sensor_validity_check()))
    test_results.append(("损坏传感器处理", test_damaged_sensor_processing()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 测试统计: {passed}/{total} 个测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！传感器损坏处理功能正常。")
        
        print("\n✅ 功能验证:")
        print("   - 传感器有效性识别 ✓")
        print("   - 动态传感器数量处理 ✓")
        print("   - 损坏传感器过滤 ✓")
        print("   - 元数据记录 ✓")
        
        print("\n💡 支持的损坏类型:")
        print("   - 全零值传感器")
        print("   - 全缺失值传感器")
        print("   - 无变化传感器")
        print("   - 异常值过多传感器")
        print("   - 零值比例过高传感器")
        
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
