#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特征提取修复效果
验证系统是否强制重新提取特征而不使用缓存文件

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import chinese_font_config  # 中文字体配置
import os
import sys
import pandas as pd
import numpy as np
import shutil
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def create_test_data():
    """创建测试数据"""
    print("🔧 创建测试数据...")
    
    # 创建测试目录结构
    test_dir = Path("test_feature_extraction_data")
    test_dir.mkdir(exist_ok=True)
    
    # 创建嵌套目录结构
    experiment_dir = test_dir / "2吨" / "双轴" / "60km_h"
    experiment_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成模拟传感器数据
    n_samples = 2000
    fs = 1000
    t = np.arange(n_samples) / fs
    
    # 创建3个CSV文件
    for i in range(1, 4):
        data = {
            'count': range(n_samples)
        }
        
        # 生成20个传感器的数据
        for j in range(1, 21):
            # 模拟振动信号：基础频率 + 噪声
            base_freq = 5 + j * 0.5
            signal = np.sin(2 * np.pi * base_freq * t) * np.exp(-((t-1)**2)/0.5)
            noise = 0.1 * np.random.randn(n_samples)
            data[f'sensor_{j:02d}'] = signal + noise
        
        # 保存CSV文件
        df = pd.DataFrame(data)
        file_path = experiment_dir / f"data_{i:03d}.csv"
        df.to_csv(file_path, index=False)
        
        print(f"   ✅ 创建文件: {file_path}")
    
    print(f"   📁 测试数据目录: {test_dir}")
    return str(test_dir)

def create_old_feature_files():
    """创建旧的特征文件来模拟缓存"""
    print("📄 创建旧的特征文件...")
    
    # 创建模拟的旧特征文件
    old_features = {
        'experiment_id': ['old_exp_1', 'old_exp_2'],
        'sensor_id': ['sensor_01', 'sensor_02'],
        'mean': [1.0, 2.0],
        'std': [0.5, 0.8],
        'rms': [1.2, 2.1],
        'speed_kmh': [60, 60],
        'load_tons': [2.0, 2.0],
        'axle_type': [2, 2]
    }
    
    old_df = pd.DataFrame(old_features)
    
    # 保存到不同位置
    old_files = [
        'combined_features.csv',
        './ml/combined_features_clean.csv'
    ]
    
    for file_path in old_files:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path) if os.path.dirname(file_path) else '.', exist_ok=True)
        old_df.to_csv(file_path, index=False)
        print(f"   ✅ 创建旧特征文件: {file_path}")
    
    return old_files

def test_force_feature_extraction():
    """测试强制特征提取功能"""
    print("🧪 测试强制特征提取功能...")
    
    try:
        # 创建测试数据
        test_data_dir = create_test_data()
        
        # 创建旧特征文件
        old_files = create_old_feature_files()
        
        # 验证旧文件存在
        for file_path in old_files:
            if os.path.exists(file_path):
                old_df = pd.read_csv(file_path)
                print(f"   📄 旧特征文件存在: {file_path} (形状: {old_df.shape})")
            else:
                print(f"   ⚠️  旧特征文件不存在: {file_path}")
        
        # 导入主系统
        from unified_vibration_analysis import UnifiedVibrationAnalysisSystem
        
        # 创建系统实例
        system = UnifiedVibrationAnalysisSystem()
        
        # 设置测试数据目录
        system.data_dir = test_data_dir
        
        # 验证强制特征提取标志
        print(f"   🔧 强制特征提取标志: {system.force_feature_extraction}")
        
        # 测试load_existing_features方法
        print("   🔍 测试load_existing_features方法...")
        existing_features = system.load_existing_features()
        
        if existing_features is None:
            print("   ✅ load_existing_features正确返回None（强制重新提取）")
        else:
            print("   ❌ load_existing_features返回了现有特征（应该返回None）")
            return False
        
        # 测试特征提取
        print("   🔄 测试特征提取...")
        features_df = system.extract_features_from_data()
        
        if features_df is not None and not features_df.empty:
            print(f"   ✅ 特征提取成功")
            print(f"      数据形状: {features_df.shape}")
            print(f"      实验数量: {features_df['experiment_id'].nunique() if 'experiment_id' in features_df.columns else 'N/A'}")
            
            # 检查特征列
            feature_columns = [col for col in features_df.columns 
                             if col not in ['experiment_id', 'sensor_id', 'passage_idx', 'segment_idx', 
                                          'start_time', 'end_time', 'duration', 'valid_sensor_count',
                                          'sensor_group', 'depth_cm', 'lane_type', 'near_joint']]
            
            print(f"      特征列数: {len(feature_columns)}")
            
            # 验证30个核心特征是否存在
            expected_features = [
                # 时域特征 (11个)
                'mean', 'std', 'var', 'rms', 'peak', 'peak_to_peak', 
                'crest_factor', 'skewness', 'kurtosis', 'energy', 'zero_crossing_rate',
                # 频域特征 (10个)
                'dominant_frequency', 'mean_frequency', 'frequency_std',
                'spectral_centroid', 'spectral_rolloff', 'spectral_flux',
                'total_power', 'low_freq_power', 'mid_freq_power', 'high_freq_power',
                # 时频域特征 (9个)
                'spectrogram_mean', 'spectrogram_std', 'spectrogram_max',
                'time_bandwidth_product', 'wavelet_energy_d1', 'wavelet_energy_d2',
                'wavelet_energy_d3', 'wavelet_energy_d4', 'wavelet_energy_a4'
            ]
            
            found_features = [feat for feat in expected_features if feat in features_df.columns]
            print(f"      找到的核心特征: {len(found_features)}/30")
            
            if len(found_features) >= 20:  # 至少找到20个特征
                print("   ✅ 特征提取包含了预期的核心特征")
            else:
                print("   ⚠️  特征提取缺少一些核心特征")
                print(f"      缺少的特征: {set(expected_features) - set(found_features)}")
            
            # 验证新特征文件是否保存
            if os.path.exists('combined_features.csv'):
                new_df = pd.read_csv('combined_features.csv')
                print(f"   ✅ 新特征文件已保存: combined_features.csv (形状: {new_df.shape})")
                
                # 验证是否是新数据（不是旧的缓存）
                if 'old_exp_1' not in new_df.get('experiment_id', []):
                    print("   ✅ 确认使用的是新提取的特征，而非旧缓存")
                else:
                    print("   ❌ 仍在使用旧的缓存特征")
                    return False
            else:
                print("   ❌ 新特征文件未保存")
                return False
            
            return True
        else:
            print("   ❌ 特征提取失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        return False

def test_feature_extraction_completeness():
    """测试特征提取的完整性"""
    print("🧪 测试特征提取完整性...")
    
    try:
        # 检查特征提取器
        sys.path.insert(0, 'core')
        from vibration_signal_analysis_framework import FeatureExtractor
        
        # 创建特征提取器
        extractor = FeatureExtractor(fs=1000)
        
        # 生成测试信号
        t = np.linspace(0, 2, 2000)  # 2秒，1000Hz
        test_signal = np.sin(2 * np.pi * 10 * t) + 0.1 * np.random.randn(2000)
        
        # 提取所有特征
        features = extractor.extract_all_features(test_signal)
        
        if features:
            print(f"   ✅ 特征提取器工作正常")
            print(f"      提取的特征数: {len(features)}")
            
            # 检查30个核心特征
            expected_features = [
                # 时域特征 (11个)
                'mean', 'std', 'var', 'rms', 'peak', 'peak_to_peak', 
                'crest_factor', 'skewness', 'kurtosis', 'energy', 'zero_crossing_rate',
                # 频域特征 (10个)
                'dominant_frequency', 'mean_frequency', 'frequency_std',
                'spectral_centroid', 'spectral_rolloff', 'spectral_flux',
                'total_power', 'low_freq_power', 'mid_freq_power', 'high_freq_power',
                # 时频域特征 (9个)
                'spectrogram_mean', 'spectrogram_std', 'spectrogram_max',
                'time_bandwidth_product', 'wavelet_energy_d1', 'wavelet_energy_d2',
                'wavelet_energy_d3', 'wavelet_energy_d4', 'wavelet_energy_a4'
            ]
            
            found_features = [feat for feat in expected_features if feat in features]
            missing_features = [feat for feat in expected_features if feat not in features]
            
            print(f"      核心特征完整性: {len(found_features)}/30")
            
            if missing_features:
                print(f"      缺少的特征: {missing_features}")
            
            if len(found_features) == 30:
                print("   ✅ 所有30个核心特征都正确提取")
                return True
            elif len(found_features) >= 25:
                print("   ⚠️  大部分核心特征已提取，可能有少量问题")
                return True
            else:
                print("   ❌ 核心特征提取不完整")
                return False
        else:
            print("   ❌ 特征提取器返回空结果")
            return False
            
    except Exception as e:
        print(f"   ❌ 特征提取完整性测试失败: {str(e)}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("🧹 清理测试文件...")
    
    # 删除测试目录
    test_dirs = ["test_feature_extraction_data"]
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            try:
                shutil.rmtree(test_dir)
                print(f"   ✅ 删除测试目录: {test_dir}")
            except Exception as e:
                print(f"   ⚠️  删除目录失败 {test_dir}: {e}")
    
    # 删除测试生成的特征文件
    test_files = ["combined_features.csv"]
    for test_file in test_files:
        if os.path.exists(test_file):
            try:
                os.remove(test_file)
                print(f"   ✅ 删除测试文件: {test_file}")
            except Exception as e:
                print(f"   ⚠️  删除文件失败 {test_file}: {e}")
    
    # 删除ml目录（如果是测试创建的）
    if os.path.exists('./ml/combined_features_clean.csv'):
        try:
            os.remove('./ml/combined_features_clean.csv')
            print(f"   ✅ 删除测试文件: ./ml/combined_features_clean.csv")
            # 如果ml目录为空，删除它
            if os.path.exists('./ml') and not os.listdir('./ml'):
                os.rmdir('./ml')
                print(f"   ✅ 删除空目录: ./ml")
        except Exception as e:
            print(f"   ⚠️  删除ml目录文件失败: {e}")

def main():
    """主测试函数"""
    print("🚀 特征提取修复效果测试")
    print("=" * 60)
    
    test_results = []
    
    # 执行测试
    test_results.append(("强制特征提取功能", test_force_feature_extraction()))
    test_results.append(("特征提取完整性", test_feature_extraction_completeness()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 测试统计: {passed}/{total} 个测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！特征提取修复成功。")
        
        print("\n✅ 修复验证:")
        print("   - 强制重新提取特征 ✓")
        print("   - 不使用缓存文件 ✓")
        print("   - 30个核心特征正确提取 ✓")
        print("   - 新特征文件正确保存 ✓")
        
        print("\n🚀 现在系统将:")
        print("   - 每次运行时重新提取特征")
        print("   - 从原始传感器数据开始处理")
        print("   - 生成最新的combined_features.csv文件")
        print("   - 确保数据集优化使用最新特征")
        
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    # 询问是否清理测试文件
    try:
        choice = input("\n是否清理测试文件？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            cleanup_test_files()
        else:
            print("   保留测试文件用于进一步检查")
    except:
        print("   保留测试文件")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
