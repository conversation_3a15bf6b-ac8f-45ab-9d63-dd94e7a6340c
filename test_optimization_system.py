#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集优化和模型性能提升系统测试脚本
测试完整的优化流程

作者: AI Assistant
版本: 1.0
日期: 2024-12-07
"""

import chinese_font_config  # 中文字体配置
import pandas as pd
import numpy as np
import os
import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def create_test_dataset():
    """创建测试数据集"""
    print("🔧 创建测试数据集...")
    
    np.random.seed(42)
    n_samples = 1000
    
    # 生成特征数据
    features = {}
    
    # 时域特征 (11个)
    time_features = ['mean', 'std', 'var', 'rms', 'peak', 'peak_to_peak', 
                    'crest_factor', 'skewness', 'kurtosis', 'energy', 'zero_crossing_rate']
    
    for i, feature in enumerate(time_features):
        features[f'time_{feature}'] = np.random.normal(i*0.5, 1, n_samples)
    
    # 频域特征 (10个)
    freq_features = ['dominant_frequency', 'mean_frequency', 'frequency_std',
                    'spectral_centroid', 'spectral_rolloff', 'spectral_flux',
                    'total_power', 'low_freq_power', 'mid_freq_power', 'high_freq_power']
    
    for i, feature in enumerate(freq_features):
        features[f'freq_{feature}'] = np.random.exponential(i*0.3 + 0.5, n_samples)
    
    # 时频域特征 (9个)
    timefreq_features = ['spectrogram_mean', 'spectrogram_std', 'spectrogram_max',
                        'time_bandwidth_product', 'wavelet_energy_d1', 'wavelet_energy_d2',
                        'wavelet_energy_d3', 'wavelet_energy_d4', 'wavelet_energy_a4']
    
    for i, feature in enumerate(timefreq_features):
        features[f'timefreq_{feature}'] = np.random.gamma(2, i*0.2 + 0.5, n_samples)
    
    # 创建数据框
    df = pd.DataFrame(features)
    
    # 生成目标变量 (速度预测)
    # 基于特征的线性组合 + 噪声
    speed_base = (
        0.3 * df['time_mean'] + 
        0.2 * df['freq_dominant_frequency'] + 
        0.15 * df['timefreq_spectrogram_mean'] +
        0.1 * df['time_rms'] +
        0.05 * df['freq_total_power']
    )
    
    # 添加非线性关系
    speed_nonlinear = 0.1 * df['time_mean'] * df['freq_dominant_frequency']
    
    # 添加噪声和异常值
    noise = np.random.normal(0, 2, n_samples)
    
    # 人工添加一些异常值
    outlier_indices = np.random.choice(n_samples, size=50, replace=False)
    outlier_noise = np.random.normal(0, 10, 50)
    noise[outlier_indices] += outlier_noise
    
    # 最终速度 (40-100 km/h范围)
    speed = 60 + speed_base + speed_nonlinear + noise
    speed = np.clip(speed, 30, 120)  # 限制在合理范围
    
    df['speed_kmh'] = speed
    
    # 生成轴重目标变量
    weight_base = (
        0.4 * df['time_energy'] + 
        0.3 * df['freq_total_power'] + 
        0.2 * df['timefreq_wavelet_energy_a4'] +
        0.1 * df['time_peak']
    )
    
    weight_noise = np.random.normal(0, 1.5, n_samples)
    weight_outlier_noise = np.random.normal(0, 8, 50)
    weight_noise[outlier_indices] += weight_outlier_noise
    
    # 轴重 (2-60吨范围)
    weight = 15 + weight_base + weight_noise
    weight = np.clip(weight, 1, 80)
    
    df['axle_weight_tons'] = weight
    
    # 添加一些分类特征
    df['axle_type'] = np.random.choice([1, 2, 3, 4], n_samples, p=[0.3, 0.4, 0.2, 0.1])
    df['lane_number'] = np.random.choice([1, 2], n_samples, p=[0.7, 0.3])
    
    print(f"   ✅ 测试数据集创建完成")
    print(f"   样本数: {len(df)}")
    print(f"   特征数: {len(df.columns) - 2}")  # 减去两个目标变量
    print(f"   速度范围: {df['speed_kmh'].min():.1f} - {df['speed_kmh'].max():.1f} km/h")
    print(f"   轴重范围: {df['axle_weight_tons'].min():.1f} - {df['axle_weight_tons'].max():.1f} 吨")
    
    return df

def test_dataset_optimization():
    """测试数据集优化功能"""
    print("🧪 测试数据集优化功能...")
    
    try:
        from dataset_optimization import DatasetOptimizer
        
        # 创建测试数据
        df = create_test_dataset()
        
        # 保存测试数据
        test_data_path = "test_optimization_data.csv"
        df.to_csv(test_data_path, index=False)
        
        # 初始化优化器
        optimizer = DatasetOptimizer("test_dataset_optimization")
        
        # 测试数据分布分析
        print("   🔍 测试数据分布分析...")
        distribution_info = optimizer.analyze_data_distribution(df, 'speed_kmh')
        
        if distribution_info and 'total_samples' in distribution_info:
            print(f"      ✅ 数据分布分析成功")
        else:
            print(f"      ❌ 数据分布分析失败")
            return False
        
        # 测试数据集划分
        print("   🔄 测试数据集划分...")
        datasets = optimizer.stratified_split_dataset(df, 'speed_kmh')
        
        if datasets and len(datasets) == 3:
            print(f"      ✅ 数据集划分成功")
            print(f"         训练集: {len(datasets['train'])} 样本")
            print(f"         验证集: {len(datasets['validation'])} 样本")
            print(f"         测试集: {len(datasets['test'])} 样本")
        else:
            print(f"      ❌ 数据集划分失败")
            return False
        
        # 测试异常值检测
        print("   🔍 测试异常值检测...")
        outlier_info = optimizer.comprehensive_outlier_detection(df, 'speed_kmh')
        
        if outlier_info and 'outlier_count' in outlier_info:
            print(f"      ✅ 异常值检测成功")
            print(f"         检测到异常值: {outlier_info['outlier_count']} 个")
        else:
            print(f"      ❌ 异常值检测失败")
            return False
        
        # 测试数据清理
        print("   🧹 测试数据清理...")
        cleaned_df = optimizer.clean_dataset(df, outlier_info, strategy='remove')
        
        if len(cleaned_df) < len(df):
            print(f"      ✅ 数据清理成功")
            print(f"         清理前: {len(df)} 样本")
            print(f"         清理后: {len(cleaned_df)} 样本")
        else:
            print(f"      ⚠️  数据清理完成，但未删除样本")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 数据集优化测试失败: {str(e)}")
        return False

def test_model_performance_enhancement():
    """测试模型性能提升功能"""
    print("🧪 测试模型性能提升功能...")
    
    try:
        from model_performance_enhancer import ModelPerformanceEnhancer
        
        # 创建测试数据
        df = create_test_dataset()
        
        # 准备数据
        X = df.drop(columns=['speed_kmh', 'axle_weight_tons'])
        y = df['speed_kmh']
        
        # 简单划分数据
        split_idx = int(0.8 * len(df))
        X_train, X_val = X[:split_idx], X[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        # 初始化性能提升器
        enhancer = ModelPerformanceEnhancer("test_performance_enhancement")
        
        # 测试特征工程
        print("   🔧 测试特征工程...")
        X_train_enhanced = enhancer.enhanced_feature_engineering(X_train, y_train)
        
        if len(X_train_enhanced.columns) >= len(X_train.columns):
            print(f"      ✅ 特征工程成功")
            print(f"         原始特征: {len(X_train.columns)}")
            print(f"         增强特征: {len(X_train_enhanced.columns)}")
        else:
            print(f"      ❌ 特征工程失败")
            return False
        
        # 测试单模型优化
        print("   🚀 测试单模型优化...")
        
        # 使用公共特征
        common_features = list(set(X_train_enhanced.columns) & set(X_val.columns))
        if common_features:
            X_train_common = X_train_enhanced[common_features]
            X_val_common = X_val[common_features]
        else:
            X_train_common = X_train
            X_val_common = X_val
        
        model_result = enhancer.optimize_single_model(
            'random_forest', X_train_common, y_train, X_val_common, y_val
        )
        
        if model_result and 'val_metrics' in model_result:
            val_r2 = model_result['val_metrics']['r2']
            print(f"      ✅ 单模型优化成功")
            print(f"         验证R²: {val_r2:.4f}")
        else:
            print(f"      ❌ 单模型优化失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模型性能提升测试失败: {str(e)}")
        return False

def test_complete_optimization_workflow():
    """测试完整优化工作流"""
    print("🧪 测试完整优化工作流...")
    
    try:
        from optimization_manager import OptimizationManager
        
        # 创建测试数据
        df = create_test_dataset()
        
        # 保存测试数据
        test_data_path = "test_complete_optimization_data.csv"
        df.to_csv(test_data_path, index=False)
        
        # 初始化优化管理器
        manager = OptimizationManager("test_complete_optimization")
        
        # 测试速度预测优化
        print("   🎯 测试速度预测优化...")
        speed_results = manager.run_complete_optimization(
            test_data_path, 'speed_kmh', 'speed_prediction'
        )
        
        if speed_results.get('success'):
            print(f"      ✅ 速度预测优化成功")
            
            # 检查性能指标
            final_results = speed_results.get('performance_enhancement', {}).get('final_test_results', {})
            if final_results:
                test_r2 = final_results['test_metrics']['r2']
                test_mae = final_results['test_metrics']['mae']
                print(f"         测试R²: {test_r2:.4f}")
                print(f"         测试MAE: {test_mae:.4f}")
                
                # 检查是否达到目标
                target_achieved = speed_results.get('performance_enhancement', {}).get('improvement_analysis', {}).get('target_achieved', False)
                if target_achieved:
                    print(f"         🎯 性能目标已达成!")
                else:
                    print(f"         ⚠️  性能目标未完全达成")
        else:
            print(f"      ❌ 速度预测优化失败")
            return False
        
        # 测试轴重预测优化
        print("   ⚖️  测试轴重预测优化...")
        weight_results = manager.run_complete_optimization(
            test_data_path, 'axle_weight_tons', 'axle_weight_prediction'
        )
        
        if weight_results.get('success'):
            print(f"      ✅ 轴重预测优化成功")
            
            final_results = weight_results.get('performance_enhancement', {}).get('final_test_results', {})
            if final_results:
                test_r2 = final_results['test_metrics']['r2']
                test_mae = final_results['test_metrics']['mae']
                print(f"         测试R²: {test_r2:.4f}")
                print(f"         测试MAE: {test_mae:.4f}")
        else:
            print(f"      ❌ 轴重预测优化失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 完整优化工作流测试失败: {str(e)}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("🧹 清理测试文件...")
    
    test_files = [
        "test_optimization_data.csv",
        "test_complete_optimization_data.csv"
    ]
    
    test_dirs = [
        "test_dataset_optimization",
        "test_performance_enhancement", 
        "test_complete_optimization"
    ]
    
    # 删除测试文件
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"   ✅ 删除文件: {file_path}")
            except Exception as e:
                print(f"   ⚠️  删除文件失败 {file_path}: {e}")
    
    # 删除测试目录
    import shutil
    for dir_path in test_dirs:
        if os.path.exists(dir_path):
            try:
                shutil.rmtree(dir_path)
                print(f"   ✅ 删除目录: {dir_path}")
            except Exception as e:
                print(f"   ⚠️  删除目录失败 {dir_path}: {e}")

def main():
    """主测试函数"""
    print("🚀 数据集优化和模型性能提升系统测试")
    print("=" * 70)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("数据集优化功能", test_dataset_optimization()))
    test_results.append(("模型性能提升功能", test_model_performance_enhancement()))
    test_results.append(("完整优化工作流", test_complete_optimization_workflow()))
    
    # 显示测试结果
    print("\n" + "=" * 70)
    print("📊 测试结果汇总:")
    print("=" * 70)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 测试统计: {passed}/{total} 个测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！数据集优化和模型性能提升系统正常工作。")
        
        print("\n✅ 功能验证:")
        print("   - 数据集分层划分 ✓")
        print("   - 异常值检测和清理 ✓")
        print("   - 增强特征工程 ✓")
        print("   - 模型优化和集成 ✓")
        print("   - 性能提升评估 ✓")
        print("   - 完整优化工作流 ✓")
        
        print("\n🚀 系统已准备就绪，可以集成到主程序中:")
        print("   python unified_vibration_analysis.py")
        
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    # 询问是否清理测试文件
    try:
        choice = input("\n是否清理测试文件？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            cleanup_test_files()
        else:
            print("   保留测试文件用于进一步检查")
    except:
        print("   保留测试文件")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
