#!/usr/bin/env python3
"""
PyTorch GPU测试脚本
"""

def test_pytorch_gpu():
    """测试PyTorch GPU功能"""
    print("🔍 测试PyTorch GPU功能...")
    
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA可用，版本: {torch.version.cuda}")
            print(f"✅ 检测到 {torch.cuda.device_count()} 个GPU设备:")
            
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
            
            # 测试GPU计算
            try:
                device = torch.device('cuda:0')
                print(f"\n🧪 GPU计算测试:")
                
                # 简单矩阵运算
                x = torch.randn(2, 2).to(device)
                y = torch.randn(2, 2).to(device)
                z = torch.matmul(x, y)
                print(f"✅ 简单矩阵运算成功")
                print(f"   设备: {z.device}")
                
                # 大矩阵运算
                a = torch.randn(1000, 1000).to(device)
                b = torch.randn(1000, 1000).to(device)
                c = torch.matmul(a, b)
                print(f"✅ 大矩阵运算成功 (1000x1000)")
                
                # 神经网络层测试
                import torch.nn as nn
                linear = nn.Linear(100, 50).to(device)
                input_tensor = torch.randn(32, 100).to(device)
                output = linear(input_tensor)
                print(f"✅ 神经网络层测试成功")
                print(f"   输入形状: {input_tensor.shape}")
                print(f"   输出形状: {output.shape}")
                
                return True
                
            except Exception as e:
                print(f"❌ PyTorch GPU计算测试失败: {str(e)}")
                return False
        else:
            print("❌ PyTorch CUDA不可用")
            return False
            
    except ImportError:
        print("❌ PyTorch未安装")
        return False
    except Exception as e:
        print(f"❌ PyTorch GPU测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 PyTorch GPU测试")
    print("=" * 40)
    
    success = test_pytorch_gpu()
    
    if success:
        print("\n🎉 PyTorch GPU配置成功！")
    else:
        print("\n⚠️  PyTorch GPU配置需要调整")
    
    return success

if __name__ == "__main__":
    main()
