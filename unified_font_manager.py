#!/usr/bin/env python3
"""
统一中文字体管理器
解决振动信号分析系统中所有可视化模块的中文字体显示问题
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
import platform
import os
import warnings
from pathlib import Path

class UnifiedFontManager:
    """统一字体管理器"""
    
    def __init__(self):
        """初始化字体管理器"""
        self.system = platform.system()
        self.available_fonts = []
        self.selected_font = None
        self.font_config_applied = False
        
        # 按系统和优先级排序的字体列表
        self.font_priorities = {
            'Windows': [
                'Microsoft YaHei',    # 微软雅黑 - 最佳选择
                'SimHei',            # 黑体 - 经典选择
                'Microsoft YaHei UI', # 微软雅黑UI
                'SimSun',            # 宋体
                'KaiTi',             # 楷体
                'FangSong'           # 仿宋
            ],
            'Darwin': [  # macOS
                'PingFang SC',       # 苹方 - 最佳选择
                'Hiragino Sans GB',  # 冬青黑体
                'Heiti SC',          # 黑体-简
                'STHeiti',           # 华文黑体
                'STSong',            # 华文宋体
                'STKaiti'            # 华文楷体
            ],
            'Linux': [
                'Noto Sans CJK SC',      # 思源黑体 - 最佳选择
                'WenQuanYi Micro Hei',   # 文泉驿微米黑
                'WenQuanYi Zen Hei',     # 文泉驿正黑
                'Source Han Sans SC',     # 思源黑体
                'Droid Sans Fallback',   # Android字体
                'AR PL UMing CN'         # 文鼎字体
            ]
        }
        
        # 通用备用字体
        self.fallback_fonts = [
            'DejaVu Sans',
            'Arial Unicode MS',
            'Lucida Grande',
            'Arial',
            'sans-serif'
        ]
        
        print(f"🔧 初始化统一字体管理器 (系统: {self.system})")
        
    def detect_available_fonts(self):
        """检测系统中可用的中文字体"""
        print("🔍 检测系统中可用的中文字体...")
        
        # 获取系统字体优先级列表
        priority_fonts = self.font_priorities.get(self.system, [])
        
        available_fonts = []
        
        # 检测优先级字体
        for font_name in priority_fonts:
            if self._is_font_available(font_name):
                available_fonts.append(font_name)
                print(f"   ✅ 发现字体: {font_name}")
        
        # 如果没有找到优先级字体，搜索所有系统字体
        if not available_fonts:
            print("   🔍 在所有系统字体中搜索中文字体...")
            all_fonts = [f.name for f in fm.fontManager.ttflist]
            
            # 搜索包含中文关键词的字体
            chinese_keywords = ['Chinese', 'CJK', 'Han', 'Hei', 'Song', 'Kai', 'Ming', 'Gothic']
            
            for font_name in all_fonts:
                if any(keyword in font_name for keyword in chinese_keywords):
                    if self._test_chinese_rendering(font_name):
                        available_fonts.append(font_name)
                        print(f"   ✅ 发现中文字体: {font_name}")
        
        self.available_fonts = available_fonts
        
        if available_fonts:
            print(f"   📊 共发现 {len(available_fonts)} 个可用中文字体")
        else:
            print("   ⚠️  未发现可用的中文字体")
            
        return available_fonts
    
    def _is_font_available(self, font_name):
        """检查字体是否可用"""
        try:
            font_path = fm.findfont(fm.FontProperties(family=font_name))
            return os.path.exists(font_path) and self._test_chinese_rendering(font_name)
        except:
            return False
    
    def _test_chinese_rendering(self, font_name):
        """测试字体是否能正确渲染中文"""
        try:
            # 创建临时图形测试中文渲染
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                fig, ax = plt.subplots(figsize=(1, 1))
                ax.text(0.5, 0.5, '测试中文字体显示', fontfamily=font_name, fontsize=10)
                plt.close(fig)
            return True
        except:
            return False
    
    def select_best_font(self):
        """选择最佳中文字体"""
        print("🎯 选择最佳中文字体...")
        
        if not self.available_fonts:
            self.detect_available_fonts()
        
        if self.available_fonts:
            # 选择第一个可用字体（优先级最高）
            self.selected_font = self.available_fonts[0]
            print(f"   ✅ 选择字体: {self.selected_font}")
        else:
            # 使用备用字体
            self.selected_font = self.fallback_fonts[0]
            print(f"   ⚠️  使用备用字体: {self.selected_font}")
        
        return self.selected_font
    
    def apply_font_config(self, force_update=False):
        """应用字体配置到matplotlib"""
        if self.font_config_applied and not force_update:
            return
        
        print("🎨 应用统一字体配置...")
        
        if not self.selected_font:
            self.select_best_font()
        
        # 构建字体列表
        font_list = [self.selected_font] + self.available_fonts + self.fallback_fonts
        # 去重并保持顺序
        font_list = list(dict.fromkeys(font_list))
        
        # 配置matplotlib全局参数
        plt.rcParams.update({
            # 字体设置
            'font.sans-serif': font_list,
            'font.family': 'sans-serif',
            'axes.unicode_minus': False,  # 解决负号显示问题
            
            # 图形质量设置
            'figure.dpi': 330,
            'savefig.dpi': 330,
            'savefig.bbox': 'tight',
            'savefig.facecolor': 'white',
            'savefig.edgecolor': 'none',
            'savefig.transparent': False,
            
            # 字体大小设置
            'font.size': 12,
            'axes.titlesize': 16,
            'axes.labelsize': 14,
            'xtick.labelsize': 12,
            'ytick.labelsize': 12,
            'legend.fontsize': 12,
            
            # 线条和网格设置
            'lines.linewidth': 2,
            'axes.linewidth': 1,
            'grid.alpha': 0.3,
            'grid.linewidth': 0.5,
            'axes.grid': True,
            'axes.axisbelow': True,
            
            # 图例设置
            'legend.frameon': True,
            'legend.fancybox': True,
            'legend.shadow': False,
            'legend.framealpha': 0.9,
            'legend.edgecolor': 'gray',
            
            # 其他设置
            'figure.autolayout': False,
            'figure.constrained_layout.use': True  # 启用约束布局
        })
        
        # 配置seaborn
        try:
            sns.set_style("whitegrid", {
                'axes.grid': True,
                'grid.alpha': 0.3,
                'grid.linewidth': 0.5
            })
        except:
            pass
        
        self.font_config_applied = True
        
        print(f"   ✅ 字体配置完成: {self.selected_font}")
        print(f"   📝 字体列表: {font_list[:3]}...")
        print(f"   🎨 图表DPI: 330")
        
    def create_font_test_chart(self, output_path='font_test_unified.png'):
        """创建字体测试图表"""
        print("🧪 创建字体测试图表...")
        
        # 确保字体配置已应用
        self.apply_font_config()
        
        # 创建测试图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 测试数据
        import numpy as np
        x = np.linspace(0, 10, 100)
        y1 = np.sin(x)
        y2 = np.cos(x)
        y3 = np.sin(x) * np.exp(-x/10)
        y4 = np.random.normal(0, 0.1, 100).cumsum()
        
        # 子图1: 基本线图
        ax1.plot(x, y1, label='正弦波', linewidth=2)
        ax1.plot(x, y2, label='余弦波', linewidth=2)
        ax1.set_title('基本线图测试 - 中文字体显示', fontweight='bold')
        ax1.set_xlabel('时间 (秒)', fontweight='bold')
        ax1.set_ylabel('幅值', fontweight='bold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 子图2: 散点图
        ax2.scatter(x[::5], y1[::5], label='正弦采样点', alpha=0.7, s=50)
        ax2.scatter(x[::5], y2[::5], label='余弦采样点', alpha=0.7, s=50)
        ax2.set_title('散点图测试 - 中文标签显示', fontweight='bold')
        ax2.set_xlabel('时间轴 (秒)', fontweight='bold')
        ax2.set_ylabel('数值轴', fontweight='bold')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 子图3: 柱状图
        categories = ['类别一', '类别二', '类别三', '类别四', '类别五']
        values = [23, 45, 56, 78, 32]
        ax3.bar(categories, values, alpha=0.8)
        ax3.set_title('柱状图测试 - 中文分类标签', fontweight='bold')
        ax3.set_xlabel('分类标签', fontweight='bold')
        ax3.set_ylabel('数值大小', fontweight='bold')
        ax3.grid(True, alpha=0.3, axis='y')
        
        # 旋转x轴标签以避免重叠
        plt.setp(ax3.get_xticklabels(), rotation=45, ha='right')
        
        # 子图4: 复杂图表
        ax4.plot(x, y3, label='衰减正弦波', linewidth=2)
        ax4.plot(x, y4, label='随机游走', linewidth=2)
        ax4.fill_between(x, y3, alpha=0.3, label='填充区域')
        ax4.set_title('复杂图表测试 - 多种中文元素', fontweight='bold')
        ax4.set_xlabel('时间序列 (秒)', fontweight='bold')
        ax4.set_ylabel('信号强度', fontweight='bold')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # 添加总标题
        fig.suptitle(f'统一字体管理器测试 - 当前字体: {self.selected_font}', 
                    fontsize=18, fontweight='bold', y=0.98)
        
        # 调整布局
        plt.tight_layout()
        plt.subplots_adjust(top=0.93)
        
        # 保存图表
        plt.savefig(output_path, dpi=330, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close(fig)
        
        print(f"   ✅ 字体测试图表已保存: {output_path}")
        return output_path
    
    def get_font_info(self):
        """获取字体信息"""
        return {
            'system': self.system,
            'selected_font': self.selected_font,
            'available_fonts': self.available_fonts,
            'config_applied': self.font_config_applied
        }
    
    def print_font_status(self):
        """打印字体状态信息"""
        print("\n" + "="*60)
        print("🔤 统一字体管理器状态")
        print("="*60)
        print(f"操作系统: {self.system}")
        print(f"选择字体: {self.selected_font}")
        print(f"配置状态: {'✅ 已应用' if self.font_config_applied else '❌ 未应用'}")
        print(f"可用字体数量: {len(self.available_fonts)}")
        
        if self.available_fonts:
            print("可用字体列表:")
            for i, font in enumerate(self.available_fonts[:5], 1):
                print(f"  {i}. {font}")
            if len(self.available_fonts) > 5:
                print(f"  ... 还有 {len(self.available_fonts) - 5} 个字体")
        
        print("="*60)

# 创建全局字体管理器实例
_global_font_manager = None

def get_font_manager():
    """获取全局字体管理器实例"""
    global _global_font_manager
    if _global_font_manager is None:
        _global_font_manager = UnifiedFontManager()
        _global_font_manager.apply_font_config()
    return _global_font_manager

def apply_unified_font_config():
    """应用统一字体配置（便捷函数）"""
    font_manager = get_font_manager()
    font_manager.apply_font_config(force_update=True)
    return font_manager.selected_font

def create_font_test():
    """创建字体测试（便捷函数）"""
    font_manager = get_font_manager()
    return font_manager.create_font_test_chart()

if __name__ == "__main__":
    print("🚀 统一字体管理器测试")
    
    # 创建字体管理器
    font_manager = UnifiedFontManager()
    
    # 检测字体
    font_manager.detect_available_fonts()
    
    # 选择最佳字体
    font_manager.select_best_font()
    
    # 应用配置
    font_manager.apply_font_config()
    
    # 打印状态
    font_manager.print_font_status()
    
    # 创建测试图表
    font_manager.create_font_test_chart()
    
    print("\n✅ 字体管理器测试完成!")
