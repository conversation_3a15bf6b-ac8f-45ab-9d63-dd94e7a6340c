{"optimization_settings": {"n_trials": 5, "random_state": 42}, "best_parameters": {"BP Neural Network": {"hidden_layers": 2, "neurons_layer1": 100, "neurons_layer2": 75, "neurons_layer3": 32, "dropout_rate": 0.21649165607921678, "learning_rate": 0.0016738085788752138, "batch_size": 128, "activation": "relu", "batch_norm": true}, "CNN-LSTM": {"timesteps": 44, "cnn_filters1": 52, "cnn_filters2": 24, "cnn_kernel_size": 3, "lstm_units1": 61, "lstm_units2": 41, "dense_units": 37, "dropout_rate": 0.21649165607921678, "learning_rate": 0.0016738085788752138, "batch_size": 64}, "TCN": {"timesteps": 36, "nb_filters": 52, "kernel_size": 3, "nb_stacks": 1, "dilations": [1, 2, 4, 8], "dropout_rate": 0.1873687420594126, "learning_rate": 0.0016738085788752138, "batch_size": 64}}, "optimization_results": {"BP Neural Network": {"best_score": 0.914810264840283, "best_params": {"hidden_layers": 2, "neurons_layer1": 100, "neurons_layer2": 75, "neurons_layer3": 32, "dropout_rate": 0.21649165607921678, "learning_rate": 0.0016738085788752138, "batch_size": 128, "activation": "relu", "batch_norm": true}, "n_trials": 5}, "CNN-LSTM": {"best_score": -0.022903088210127986, "best_params": {"timesteps": 44, "cnn_filters1": 52, "cnn_filters2": 24, "cnn_kernel_size": 3, "lstm_units1": 61, "lstm_units2": 41, "dense_units": 37, "dropout_rate": 0.21649165607921678, "learning_rate": 0.0016738085788752138, "batch_size": 64}, "n_trials": 5}, "TCN": {"best_score": 0.8974298462700145, "best_params": {"timesteps": 36, "nb_filters": 52, "kernel_size": 3, "nb_stacks": 1, "dilations": [1, 2, 4, 8], "dropout_rate": 0.1873687420594126, "learning_rate": 0.0016738085788752138, "batch_size": 64}, "n_trials": 5}}}