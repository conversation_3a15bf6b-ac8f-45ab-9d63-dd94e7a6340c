# 振动信号降噪方法对比分析系统

## 📋 概述

本系统为振动信号分析系统新增了全面的降噪方法对比分析功能，专门针对路面嵌入式加速度传感器数据进行优化。系统实现了多种降噪算法的自动对比、评估和推荐，帮助用户找到最适合特定应用场景的降噪策略。

## 🔧 功能特性

### 降噪方法
- **小波降噪**: 支持多种小波基函数（Daubechies、Biorthogonal、Coiflets、Haar）
- **低通滤波器**: Butterworth、Chebyshev、椭圆滤波器
- **中值滤波器**: 去除脉冲噪声和异常值
- **移动平均滤波**: 简单、加权、指数移动平均
- **SVMD降噪**: 逐次变分模态分解（简化实现）
- **自适应滤波**: 基于维纳滤波原理

### 评估指标
- **信噪比（SNR）改善程度**: 量化降噪效果
- **均方根误差（RMSE）**: 评估信号失真
- **信号保真度**: 相关系数分析
- **频域特征保持度**: 功率谱密度对比
- **峰值保持度**: 车辆通过事件特征保持
- **计算效率**: 处理时间和吞吐量

### 可视化功能
- **时域对比图**: 降噪前后波形对比
- **频域分析图**: 功率谱密度变化
- **性能评估图**: 综合指标热力图
- **SNR改善对比**: 各方法效果排名
- **处理效率对比**: 计算性能分析
- **学术质量图表**: 330 DPI，中英文双语

## 📁 文件结构

```
振动信号分析系统/
├── denoising_methods.py              # 降噪方法实现
├── denoising_evaluator.py            # 降噪效果评估
├── denoising_visualizer.py           # 降噪可视化
├── denoising_comparison_system.py    # 降噪对比分析主系统
├── test_denoising_system.py          # 系统测试脚本
├── demo_denoising_analysis.py        # 演示脚本
├── unified_vibration_analysis.py     # 主程序（已集成降噪功能）
└── DENOISING_SYSTEM_README.md        # 本文档
```

## 🚀 使用方法

### 1. 集成到主系统（推荐）

```bash
# 运行完整的振动信号分析系统
python unified_vibration_analysis.py
```

系统将自动执行以下流程：
1. 数据加载和特征提取
2. **降噪方法对比分析** ← 新增功能
3. 高级特征工程
4. 超参数优化
5. 模型训练和评估
6. 结果可视化和报告生成

### 2. 独立运行降噪分析

```bash
# 运行降噪演示
python demo_denoising_analysis.py

# 运行系统测试
python test_denoising_system.py
```

### 3. 编程接口使用

```python
from denoising_comparison_system import DenoisingComparisonSystem

# 初始化系统
system = DenoisingComparisonSystem(fs=1000, output_dir='results')

# 分析单个信号
result = system.analyze_single_signal(signal_data, 'signal_name')

# 批量分析多个信号
signals = {'signal1': data1, 'signal2': data2}
batch_results = system.analyze_multiple_signals(signals)

# 获取自动推荐
recommendation = system.get_automatic_recommendation(signal_data)
```

## 📊 输出结果

### 文件输出
```
denoising_analysis_results/
├── visualizations/                    # 可视化图表
│   ├── chinese/                      # 中文版图表
│   │   ├── *_denoising_comparison.png
│   │   ├── *_frequency_analysis.png
│   │   ├── denoising_evaluation_results.png
│   │   └── snr_improvement_comparison.png
│   └── english/                      # 英文版图表
│       └── (同上)
├── *_denoising_report.md             # 详细分析报告
├── *_evaluation_results.csv          # 评估结果数据
├── *_best_method.json               # 最佳方法信息
└── batch_analysis_summary.md        # 批量分析摘要
```

### 控制台输出示例
```
🔊 开始降噪方法对比分析...
📊 对 3 个信号样本进行降噪分析...
🏆 降噪方法排名 (按综合评分):
  1. wavelet_db4: 综合评分=0.856, SNR改善=4.2dB, 保真度=0.923
  2. butterworth_50hz: 综合评分=0.834, SNR改善=3.8dB, 保真度=0.901
  3. median_5: 综合评分=0.798, SNR改善=2.9dB, 保真度=0.887

✅ 降噪方法对比分析完成!
   推荐方法: wavelet_db4
   推荐理由: 在3个信号中获得2票，平均评分0.856
```

## 🎯 应用场景

### 1. 车辆通过检测
- **推荐方法**: 小波降噪（db4）
- **理由**: 保持峰值特征，适合非平稳信号

### 2. 高频振动分析
- **推荐方法**: Butterworth低通滤波
- **理由**: 有效去除高频噪声，保持主要频率成分

### 3. 脉冲噪声处理
- **推荐方法**: 中值滤波
- **理由**: 专门针对脉冲和异常值

### 4. 实时处理
- **推荐方法**: 移动平均滤波
- **理由**: 计算简单，处理速度快

## ⚙️ 参数配置

### 降噪方法参数
```python
# 小波降噪
wavelet_params = {
    'wavelet': 'db4',      # 小波基函数
    'mode': 'soft',        # 阈值模式
    'levels': 4            # 分解层数
}

# Butterworth滤波
butter_params = {
    'cutoff_freq': 50,     # 截止频率 (Hz)
    'order': 4             # 滤波器阶数
}

# 中值滤波
median_params = {
    'kernel_size': 5       # 滤波器核大小
}
```

### 评估权重
```python
evaluation_weights = {
    'snr_improvement': 0.25,      # SNR改善权重
    'signal_fidelity': 0.25,      # 信号保真度权重
    'frequency_preservation': 0.25, # 频域保持度权重
    'peak_preservation': 0.25      # 峰值保持度权重
}
```

## 🔬 技术细节

### 信号特征分析
- **噪声水平估计**: 基于高频成分分析
- **峰值检测**: 车辆通过事件识别
- **频率内容分析**: 低频/高频/宽带信号分类
- **平稳性检测**: 信号统计特性分析

### 自动推荐算法
```python
if signal_features['noise_level'] > 0.5:
    # 高噪声 → 强降噪方法
    recommended = ['wavelet_db4', 'butterworth_30hz']
elif signal_features['has_strong_peaks']:
    # 强峰值 → 保持峰值特征的方法
    recommended = ['wavelet_db4', 'svmd']
else:
    # 一般信号 → 平衡性能方法
    recommended = ['butterworth_50hz', 'ma_weighted_10']
```

## 📈 性能目标

- **速度预测**: R² > 0.90
- **轴重预测**: R² > 0.85
- **轴型分类**: 准确率 > 0.90
- **SNR改善**: > 3 dB
- **信号保真度**: > 0.85
- **处理效率**: > 1000 样本/秒

## 🛠️ 依赖要求

```bash
pip install numpy pandas scipy matplotlib seaborn
pip install pywt scikit-learn  # 小波变换和机器学习
```

## 📞 技术支持

如有问题或建议，请参考：
1. 运行测试脚本进行故障诊断
2. 查看生成的分析报告
3. 检查可视化图表了解详细结果

## 🔄 更新日志

### v1.0 (2024-12-07)
- ✅ 实现6种主要降噪方法
- ✅ 完整的评估指标体系
- ✅ 学术质量可视化
- ✅ 自动方法推荐
- ✅ 集成到主分析系统
- ✅ 中英文双语支持
