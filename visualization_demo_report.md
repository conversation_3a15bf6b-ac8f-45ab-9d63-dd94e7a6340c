# 🎨 振动信号分析系统 - 可视化功能演示报告
================================================================================

## 📊 可视化功能概览

本系统提供了全面的模型性能可视化功能，包括：

- 📈 **预测效果可视化**: 预测值vs实际值散点图，包含拟合线和性能指标
- 📊 **模型性能对比**: 柱状图显示所有算法性能，包含目标线
- 📉 **误差分析图**: 残差图、误差分布、Q-Q图、统计分析
- 🎯 **分类任务专用图**: 混淆矩阵、ROC曲线、精确率-召回率曲线
- 🎯 **特征重要性图**: 排序显示最重要特征
- 📈 **训练历史图**: 深度学习模型的损失和指标曲线
- 🔥 **性能矩阵热力图**: 所有模型在所有任务上的性能对比
- 🌊 **特征相关性图**: 特征间相关性热力图
- 📦 **误差箱线图**: 不同模型误差分布对比
- 🔧 **超参数优化图**: 优化历史和参数重要性

## 🎯 技术特点

- **高质量图表**: 300 DPI分辨率，适合学术论文使用
- **专业配色**: 科学可视化标准配色方案
- **中英文对照**: 标题、轴标签、图例支持中英文
- **自动布局**: 智能调整图表布局和字体大小
- **统一命名**: 规范的文件命名和目录结构
- **批量生成**: 一键生成所有可视化图表
- **模块化设计**: 可独立使用各个可视化功能
- **错误处理**: 完善的异常处理和日志记录

## 📁 生成的图表文件

- `BP Neural Network_training_history.png`
- `CNN-LSTM_training_history.png`
- `Extra Trees_feature_importance.png`
- `Gradient Boosting_feature_importance.png`
- `Random Forest_feature_importance.png`
- `XGBoost_feature_importance.png`
- `axle_classification_BP Neural Network_confusion_matrix.png`
- `axle_classification_BP Neural Network_precision_recall.png`
- `axle_classification_BP Neural Network_roc_curves.png`
- `axle_classification_CNN-LSTM_confusion_matrix.png`
- `axle_classification_CNN-LSTM_precision_recall.png`
- `axle_classification_CNN-LSTM_roc_curves.png`
- `axle_classification_Extra Trees_confusion_matrix.png`
- `axle_classification_Extra Trees_precision_recall.png`
- `axle_classification_Extra Trees_roc_curves.png`
- `axle_classification_Gradient Boosting_confusion_matrix.png`
- `axle_classification_Gradient Boosting_precision_recall.png`
- `axle_classification_Gradient Boosting_roc_curves.png`
- `axle_classification_Random Forest_confusion_matrix.png`
- `axle_classification_Random Forest_precision_recall.png`
- `axle_classification_Random Forest_roc_curves.png`
- `axle_classification_SVM_confusion_matrix.png`
- `axle_classification_SVM_precision_recall.png`
- `axle_classification_SVM_roc_curves.png`
- `axle_classification_TCN_confusion_matrix.png`
- `axle_classification_TCN_precision_recall.png`
- `axle_classification_TCN_roc_curves.png`
- `axle_classification_XGBoost_confusion_matrix.png`
- `axle_classification_XGBoost_precision_recall.png`
- `axle_classification_XGBoost_roc_curves.png`
- `axle_classification_performance_comparison.png`
- `performance_matrix_heatmap.png`
- `speed_prediction_BP Neural Network_error_analysis.png`
- `speed_prediction_BP Neural Network_prediction_scatter.png`
- `speed_prediction_CNN-LSTM_error_analysis.png`
- `speed_prediction_CNN-LSTM_prediction_scatter.png`
- `speed_prediction_Extra Trees_error_analysis.png`
- `speed_prediction_Extra Trees_prediction_scatter.png`
- `speed_prediction_Gradient Boosting_error_analysis.png`
- `speed_prediction_Gradient Boosting_prediction_scatter.png`
- `speed_prediction_Random Forest_error_analysis.png`
- `speed_prediction_Random Forest_prediction_scatter.png`
- `speed_prediction_SVM_error_analysis.png`
- `speed_prediction_SVM_prediction_scatter.png`
- `speed_prediction_TCN_error_analysis.png`
- `speed_prediction_TCN_prediction_scatter.png`
- `speed_prediction_XGBoost_error_analysis.png`
- `speed_prediction_XGBoost_prediction_scatter.png`
- `speed_prediction_error_boxplot.png`
- `speed_prediction_performance_comparison.png`

## 💡 使用建议

1. **学术论文**: 所有图表均为300 DPI高分辨率，可直接用于学术论文
2. **技术报告**: 图表包含详细的性能指标和统计信息
3. **模型选择**: 通过可视化对比选择最佳算法
4. **错误诊断**: 通过误差分析图发现模型问题
5. **特征工程**: 通过特征重要性和相关性指导特征选择