#!/usr/bin/env python3
"""
传感器对比分析和可视化模块
生成传感器性能对比和模型优化效果的可视化图表
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Rectangle
import os
import warnings
warnings.filterwarnings('ignore')

class SensorComparisonVisualizer:
    """传感器对比分析可视化器"""
    
    def __init__(self, output_dir='sensor_analysis_results'):
        """初始化可视化器"""
        self.output_dir = output_dir
        self.setup_style()
        self.ensure_output_dir()
        
        # 传感器配置
        self.sensor_config = {
            'main_lane_5cm': ['sensor_01', 'sensor_02', 'sensor_03', 'sensor_04', 'sensor_05',
                             'sensor_07', 'sensor_08', 'sensor_09', 'sensor_10'],
            'main_lane_3_5cm': ['sensor_11', 'sensor_12', 'sensor_13', 'sensor_14', 'sensor_15',
                               'sensor_17', 'sensor_18', 'sensor_19', 'sensor_20'],
            'overtaking_lane': ['sensor_06', 'sensor_16']
        }
        
        # 颜色配置
        self.colors = {
            'main_lane_5cm': '#2E86AB',
            'main_lane_3_5cm': '#A23B72',
            'overtaking_lane': '#F18F01',
            'improvement': '#2CA02C',
            'degradation': '#D62728'
        }
    
    def setup_style(self):
        """设置图表样式"""
        plt.rcParams.update({
            'figure.figsize': (12, 8),
            'figure.dpi': 300,
            'savefig.dpi': 300,
            'savefig.bbox': 'tight',
            'font.size': 12,
            'axes.titlesize': 16,
            'axes.labelsize': 14,
            'xtick.labelsize': 12,
            'ytick.labelsize': 12,
            'legend.fontsize': 12,
            'lines.linewidth': 2,
            'grid.alpha': 0.3
        })
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def ensure_output_dir(self):
        """确保输出目录存在"""
        os.makedirs(self.output_dir, exist_ok=True)
    
    def visualize_sensor_layout(self):
        """可视化传感器布局"""
        print("📊 生成传感器布局图...")
        
        fig, ax = plt.subplots(figsize=(14, 8))
        
        # 绘制道路
        road_width = 10
        road_length = 20
        
        # 主车道
        main_lane = Rectangle((0, 2), road_length, 3, 
                            facecolor='lightgray', edgecolor='black', linewidth=2)
        ax.add_patch(main_lane)
        
        # 超车道
        overtaking_lane = Rectangle((0, 5.3), road_length, 3, 
                                  facecolor='lightblue', edgecolor='black', linewidth=2)
        ax.add_patch(overtaking_lane)
        
        # 切缝
        ax.plot([0, road_length], [5.15, 5.15], 'k-', linewidth=3, label='切缝 (3mm宽, 10cm深)')
        
        # 传感器位置
        sensor_positions = {
            # 第1组 (5cm深度, 主车道)
            'sensor_01': (2, 3.5, '5cm'), 'sensor_02': (4, 3.5, '5cm'), 'sensor_03': (6, 3.5, '5cm'),
            'sensor_04': (8, 3.5, '5cm'), 'sensor_05': (10, 3.5, '5cm'),
            
            # 第2组 (5cm深度, sensor_06在超车道)
            'sensor_06': (12, 6.8, '5cm'), 'sensor_07': (12, 3.5, '5cm'), 'sensor_08': (14, 3.5, '5cm'),
            'sensor_09': (16, 3.5, '5cm'), 'sensor_10': (18, 3.5, '5cm'),
            
            # 第3组 (3.5cm深度, 主车道)
            'sensor_11': (2, 2.5, '3.5cm'), 'sensor_12': (4, 2.5, '3.5cm'), 'sensor_13': (6, 2.5, '3.5cm'),
            'sensor_14': (8, 2.5, '3.5cm'), 'sensor_15': (10, 2.5, '3.5cm'),
            
            # 第4组 (3.5cm深度, sensor_16在超车道)
            'sensor_16': (12, 7.5, '3.5cm'), 'sensor_17': (12, 2.5, '3.5cm'), 'sensor_18': (14, 2.5, '3.5cm'),
            'sensor_19': (16, 2.5, '3.5cm'), 'sensor_20': (18, 2.5, '3.5cm')
        }
        
        # 绘制传感器
        for sensor, (x, y, depth) in sensor_positions.items():
            if sensor in ['sensor_06', 'sensor_16']:
                color = self.colors['overtaking_lane']
                marker = 's'  # 方形
                size = 100
            elif depth == '5cm':
                color = self.colors['main_lane_5cm']
                marker = 'o'  # 圆形
                size = 80
            else:
                color = self.colors['main_lane_3_5cm']
                marker = '^'  # 三角形
                size = 80
            
            ax.scatter(x, y, c=color, marker=marker, s=size, edgecolors='black', linewidth=1)
            ax.text(x, y-0.3, sensor.replace('sensor_', ''), ha='center', va='top', fontsize=8)
        
        # 添加图例
        legend_elements = [
            plt.scatter([], [], c=self.colors['main_lane_5cm'], marker='o', s=80, 
                       label='主车道传感器 (5cm深度)', edgecolors='black'),
            plt.scatter([], [], c=self.colors['main_lane_3_5cm'], marker='^', s=80, 
                       label='主车道传感器 (3.5cm深度)', edgecolors='black'),
            plt.scatter([], [], c=self.colors['overtaking_lane'], marker='s', s=100, 
                       label='超车道传感器', edgecolors='black'),
            plt.Line2D([0], [0], color='black', linewidth=3, label='切缝')
        ]
        ax.legend(handles=legend_elements, loc='upper right')
        
        # 设置坐标轴
        ax.set_xlim(-1, 21)
        ax.set_ylim(1, 9)
        ax.set_xlabel('道路纵向位置 (m)', fontweight='bold')
        ax.set_ylabel('道路横向位置 (m)', fontweight='bold')
        ax.set_title('20个加速度传感器布设方案\n水泥混凝土路面埋设配置', fontweight='bold', pad=20)
        
        # 添加道路标注
        ax.text(10, 3.5, '主车道', ha='center', va='center', fontsize=14, fontweight='bold')
        ax.text(10, 6.8, '超车道', ha='center', va='center', fontsize=14, fontweight='bold')
        
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # 保存图片
        save_path = os.path.join(self.output_dir, 'sensor_layout_diagram.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 传感器布局图已保存: {save_path}")
    
    def visualize_data_quality_comparison(self, quality_results):
        """可视化数据质量对比"""
        print("📊 生成数据质量对比图...")
        
        if not quality_results:
            print("⚠️  没有数据质量结果")
            return
        
        # 准备数据
        sensors = list(quality_results.keys())
        metrics = ['signal_to_noise_ratio', 'outlier_ratio', 'std', 'missing_ratio']
        metric_labels = ['信噪比', '异常值比例', '标准差', '缺失值比例']
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()
        
        for i, (metric, label) in enumerate(zip(metrics, metric_labels)):
            ax = axes[i]
            
            # 提取数据
            main_5cm_values = []
            main_3_5cm_values = []
            overtaking_values = []
            
            for sensor in sensors:
                value = quality_results[sensor].get(metric, 0)
                
                if sensor in self.sensor_config['main_lane_5cm']:
                    main_5cm_values.append(value)
                elif sensor in self.sensor_config['main_lane_3_5cm']:
                    main_3_5cm_values.append(value)
                elif sensor in self.sensor_config['overtaking_lane']:
                    overtaking_values.append(value)
            
            # 绘制箱线图
            data_to_plot = []
            labels = []
            colors = []
            
            if main_5cm_values:
                data_to_plot.append(main_5cm_values)
                labels.append('主车道\n(5cm深度)')
                colors.append(self.colors['main_lane_5cm'])
            
            if main_3_5cm_values:
                data_to_plot.append(main_3_5cm_values)
                labels.append('主车道\n(3.5cm深度)')
                colors.append(self.colors['main_lane_3_5cm'])
            
            if overtaking_values:
                data_to_plot.append(overtaking_values)
                labels.append('超车道')
                colors.append(self.colors['overtaking_lane'])
            
            if data_to_plot:
                bp = ax.boxplot(data_to_plot, labels=labels, patch_artist=True)
                
                for patch, color in zip(bp['boxes'], colors):
                    patch.set_facecolor(color)
                    patch.set_alpha(0.7)
            
            ax.set_title(f'{label}对比', fontweight='bold')
            ax.grid(True, alpha=0.3)
            
            # 特殊处理异常值比例和缺失值比例（使用百分比）
            if 'ratio' in metric:
                ax.set_ylabel('比例 (%)')
                # 转换为百分比显示
                current_ticks = ax.get_yticks()
                ax.set_yticklabels([f'{tick*100:.1f}' for tick in current_ticks])
            else:
                ax.set_ylabel(label)
        
        plt.suptitle('传感器数据质量对比分析', fontsize=18, fontweight='bold')
        plt.tight_layout()
        
        # 保存图片
        save_path = os.path.join(self.output_dir, 'data_quality_comparison.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 数据质量对比图已保存: {save_path}")
    
    def visualize_correlation_analysis(self, correlation_matrix):
        """可视化相关性分析"""
        print("📊 生成相关性分析图...")
        
        if correlation_matrix is None or correlation_matrix.empty:
            print("⚠️  没有相关性数据")
            return
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
        
        # 完整相关性矩阵
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        sns.heatmap(correlation_matrix, mask=mask, annot=False, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8}, ax=ax1)
        ax1.set_title('传感器间相关性矩阵', fontweight='bold')
        
        # 特殊传感器相关性分析
        special_sensors = ['sensor_06', 'sensor_16']
        if all(sensor in correlation_matrix.columns for sensor in special_sensors):
            special_corr_data = []
            labels = []
            
            for special_sensor in special_sensors:
                correlations = correlation_matrix[special_sensor].drop(special_sensor)
                special_corr_data.append(correlations.values)
                labels.append(special_sensor)
            
            # 添加主车道传感器的平均相关性作为对比
            main_sensors = [col for col in correlation_matrix.columns 
                          if col not in special_sensors]
            
            if main_sensors:
                main_corr_values = []
                for main_sensor in main_sensors:
                    other_main = [s for s in main_sensors if s != main_sensor]
                    if other_main:
                        avg_corr = correlation_matrix.loc[main_sensor, other_main].mean()
                        main_corr_values.append(avg_corr)
                
                if main_corr_values:
                    special_corr_data.append(main_corr_values)
                    labels.append('主车道传感器\n(平均)')
            
            # 绘制箱线图
            bp = ax2.boxplot(special_corr_data, labels=labels, patch_artist=True)
            
            colors = [self.colors['overtaking_lane'], self.colors['overtaking_lane'], 
                     self.colors['main_lane_5cm']]
            
            for patch, color in zip(bp['boxes'], colors[:len(bp['boxes'])]):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            
            ax2.set_title('特殊传感器相关性对比', fontweight='bold')
            ax2.set_ylabel('相关系数')
            ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        save_path = os.path.join(self.output_dir, 'correlation_analysis.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 相关性分析图已保存: {save_path}")
    
    def visualize_model_performance_comparison(self, baseline_results, optimized_results, 
                                            with_special_sensors, without_special_sensors):
        """可视化模型性能对比"""
        print("📊 生成模型性能对比图...")
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 优化前后对比
        models = list(baseline_results.keys())
        baseline_scores = [baseline_results[model].get('r2_score', 0) for model in models]
        optimized_scores = [optimized_results.get(model, {}).get('best_score', baseline_results[model].get('r2_score', 0)) for model in models]
        
        x = np.arange(len(models))
        width = 0.35
        
        bars1 = ax1.bar(x - width/2, baseline_scores, width, label='优化前', 
                       color=self.colors['main_lane_5cm'], alpha=0.7)
        bars2 = ax1.bar(x + width/2, optimized_scores, width, label='优化后', 
                       color=self.colors['improvement'], alpha=0.7)
        
        # 添加目标线
        ax1.axhline(y=0.9, color=self.colors['degradation'], linestyle='--', 
                   linewidth=2, label='目标 R² = 0.9')
        
        ax1.set_xlabel('模型')
        ax1.set_ylabel('R² 分数')
        ax1.set_title('模型优化前后性能对比', fontweight='bold')
        ax1.set_xticks(x)
        ax1.set_xticklabels(models, rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)
        
        for bar in bars2:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)
        
        # 2. 包含/排除特殊传感器对比
        if with_special_sensors and without_special_sensors:
            models_sensor = list(with_special_sensors.keys())
            with_scores = [with_special_sensors[model].get('r2_score', 0) for model in models_sensor]
            without_scores = [without_special_sensors.get(model, {}).get('r2_score', 0) for model in models_sensor]
            
            x2 = np.arange(len(models_sensor))
            
            bars3 = ax2.bar(x2 - width/2, with_scores, width, label='包含特殊传感器', 
                           color=self.colors['overtaking_lane'], alpha=0.7)
            bars4 = ax2.bar(x2 + width/2, without_scores, width, label='排除特殊传感器', 
                           color=self.colors['main_lane_5cm'], alpha=0.7)
            
            ax2.set_xlabel('模型')
            ax2.set_ylabel('R² 分数')
            ax2.set_title('特殊传感器影响分析', fontweight='bold')
            ax2.set_xticks(x2)
            ax2.set_xticklabels(models_sensor, rotation=45, ha='right')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
        
        # 3. 性能改进幅度
        improvements = [(opt - base) / base * 100 if base > 0 else 0 
                       for base, opt in zip(baseline_scores, optimized_scores)]
        
        colors_imp = [self.colors['improvement'] if imp > 0 else self.colors['degradation'] 
                     for imp in improvements]
        
        bars5 = ax3.bar(models, improvements, color=colors_imp, alpha=0.7)
        ax3.set_xlabel('模型')
        ax3.set_ylabel('性能改进 (%)')
        ax3.set_title('模型性能改进幅度', fontweight='bold')
        ax3.set_xticklabels(models, rotation=45, ha='right')
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # 添加数值标签
        for bar, imp in zip(bars5, improvements):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + (1 if height > 0 else -1),
                    f'{imp:.1f}%', ha='center', va='bottom' if height > 0 else 'top', fontsize=9)
        
        # 4. 达到目标情况统计
        target_achievement = {
            '优化前达标': sum(1 for score in baseline_scores if score >= 0.9),
            '优化后达标': sum(1 for score in optimized_scores if score >= 0.9),
            '总模型数': len(models)
        }
        
        categories = ['优化前', '优化后']
        achieved = [target_achievement['优化前达标'], target_achievement['优化后达标']]
        total = target_achievement['总模型数']
        
        bars6 = ax4.bar(categories, achieved, color=[self.colors['main_lane_5cm'], self.colors['improvement']], alpha=0.7)
        ax4.set_ylabel('达标模型数量')
        ax4.set_title(f'R² ≥ 0.9 目标达成情况\n(总共{total}个模型)', fontweight='bold')
        ax4.grid(True, alpha=0.3)
        
        # 添加数值标签和百分比
        for bar, count in zip(bars6, achieved):
            height = bar.get_height()
            percentage = (count / total) * 100
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{count}\n({percentage:.1f}%)', ha='center', va='bottom', fontsize=10, fontweight='bold')
        
        plt.suptitle('模型性能优化全面对比分析', fontsize=18, fontweight='bold')
        plt.tight_layout()
        
        # 保存图片
        save_path = os.path.join(self.output_dir, 'model_performance_comparison.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 模型性能对比图已保存: {save_path}")

def main():
    """测试函数"""
    print("🧪 测试传感器对比分析可视化...")
    
    # 初始化可视化器
    visualizer = SensorComparisonVisualizer()
    
    # 生成传感器布局图
    visualizer.visualize_sensor_layout()
    
    # 模拟数据质量结果
    quality_results = {}
    for i in range(1, 21):
        sensor_name = f'sensor_{i:02d}'
        if sensor_name in ['sensor_06', 'sensor_16']:
            # 特殊传感器数据质量较差
            quality_results[sensor_name] = {
                'signal_to_noise_ratio': np.random.uniform(8, 12),
                'outlier_ratio': np.random.uniform(0.08, 0.15),
                'std': np.random.uniform(1.2, 1.8),
                'missing_ratio': np.random.uniform(0.02, 0.08)
            }
        else:
            # 正常传感器
            quality_results[sensor_name] = {
                'signal_to_noise_ratio': np.random.uniform(15, 25),
                'outlier_ratio': np.random.uniform(0.02, 0.06),
                'std': np.random.uniform(0.8, 1.2),
                'missing_ratio': np.random.uniform(0.001, 0.02)
            }
    
    # 生成数据质量对比图
    visualizer.visualize_data_quality_comparison(quality_results)
    
    # 模拟相关性矩阵
    sensor_names = [f'sensor_{i:02d}' for i in range(1, 21)]
    correlation_matrix = pd.DataFrame(
        np.random.rand(20, 20) * 0.8 + 0.1,
        index=sensor_names,
        columns=sensor_names
    )
    np.fill_diagonal(correlation_matrix.values, 1.0)
    
    # 生成相关性分析图
    visualizer.visualize_correlation_analysis(correlation_matrix)
    
    # 模拟模型性能结果
    baseline_results = {
        'Random Forest': {'r2_score': 0.8573},
        'XGBoost': {'r2_score': 0.8460},
        'SVM': {'r2_score': 0.7500},
        'AdaBoost': {'r2_score': 0.6500}
    }
    
    optimized_results = {
        'Random Forest': {'best_score': 0.9100},
        'XGBoost': {'best_score': 0.9050},
        'SVM': {'best_score': 0.8800},
        'AdaBoost': {'best_score': 0.8200}
    }
    
    # 生成模型性能对比图
    visualizer.visualize_model_performance_comparison(
        baseline_results, optimized_results, 
        baseline_results, optimized_results
    )
    
    print("✅ 测试完成!")

if __name__ == "__main__":
    main()
