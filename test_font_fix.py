#!/usr/bin/env python3
"""
测试中文字体修复效果
生成修复前后的对比图表，验证字体显示问题是否解决
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    
    # 时间序列数据
    time = np.linspace(0, 5, 1000)
    sensor_1 = np.sin(2 * np.pi * time) + 0.1 * np.random.normal(0, 1, 1000)
    sensor_2 = np.cos(2 * np.pi * time) + 0.1 * np.random.normal(0, 1, 1000)
    sensor_3 = 0.5 * np.sin(4 * np.pi * time) + 0.1 * np.random.normal(0, 1, 1000)
    sensor_4 = 0.3 * np.cos(6 * np.pi * time) + 0.1 * np.random.normal(0, 1, 1000)
    
    # 统计数据
    categories = ['类别一', '类别二', '类别三', '类别四', '类别五']
    values = [23, 45, 56, 78, 32]
    
    # 模型性能数据
    models = ['随机森林', 'XGBoost', '支持向量机', '神经网络', '梯度提升']
    accuracy = [0.85, 0.88, 0.82, 0.90, 0.87]
    training_time = [1.2, 2.1, 0.8, 5.3, 1.8]
    
    return {
        'time_series': {
            'time': time,
            'sensor_1': sensor_1,
            'sensor_2': sensor_2,
            'sensor_3': sensor_3,
            'sensor_4': sensor_4
        },
        'categories': {
            'names': categories,
            'values': values
        },
        'models': {
            'names': models,
            'accuracy': accuracy,
            'training_time': training_time
        }
    }

def test_font_before_fix():
    """测试修复前的字体显示（使用默认配置）"""
    print("🔍 测试修复前的字体显示...")
    
    # 重置matplotlib配置
    plt.rcdefaults()
    
    # 创建测试数据
    data = create_test_data()
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 子图1: 时间序列图
    ax1.plot(data['time_series']['time'], data['time_series']['sensor_1'], 
             label='传感器1', linewidth=2)
    ax1.plot(data['time_series']['time'], data['time_series']['sensor_2'], 
             label='传感器2', linewidth=2)
    ax1.set_title('振动信号时域分析', fontsize=16, fontweight='bold')
    ax1.set_xlabel('时间 (秒)', fontsize=14)
    ax1.set_ylabel('振幅 (m/s²)', fontsize=14)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 子图2: 柱状图
    ax2.bar(data['categories']['names'], data['categories']['values'], 
            alpha=0.8, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'])
    ax2.set_title('传感器数据分类统计', fontsize=16, fontweight='bold')
    ax2.set_xlabel('数据类别', fontsize=14)
    ax2.set_ylabel('数量', fontsize=14)
    ax2.grid(True, alpha=0.3, axis='y')
    plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')
    
    # 子图3: 散点图
    ax3.scatter(data['models']['accuracy'], data['models']['training_time'], 
                s=100, alpha=0.7, c=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'])
    for i, model in enumerate(data['models']['names']):
        ax3.annotate(model, 
                    (data['models']['accuracy'][i], data['models']['training_time'][i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=10)
    ax3.set_title('模型性能对比分析', fontsize=16, fontweight='bold')
    ax3.set_xlabel('准确率', fontsize=14)
    ax3.set_ylabel('训练时间 (秒)', fontsize=14)
    ax3.grid(True, alpha=0.3)
    
    # 子图4: 多传感器对比
    ax4.plot(data['time_series']['time'], data['time_series']['sensor_3'], 
             label='传感器3', linewidth=2)
    ax4.plot(data['time_series']['time'], data['time_series']['sensor_4'], 
             label='传感器4', linewidth=2)
    ax4.fill_between(data['time_series']['time'], data['time_series']['sensor_3'], 
                     alpha=0.3, label='填充区域')
    ax4.set_title('多传感器信号对比', fontsize=16, fontweight='bold')
    ax4.set_xlabel('时间序列 (秒)', fontsize=14)
    ax4.set_ylabel('信号强度', fontsize=14)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 添加总标题
    fig.suptitle('修复前字体显示测试 - 可能出现中文显示问题', 
                fontsize=18, fontweight='bold', y=0.98)
    
    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    
    # 保存图表
    output_path = 'font_test_before_fix.png'
    plt.savefig(output_path, dpi=330, bbox_inches='tight', 
               facecolor='white', edgecolor='none')
    plt.close(fig)
    
    print(f"   ✅ 修复前测试图表已保存: {output_path}")
    return output_path

def test_font_after_fix():
    """测试修复后的字体显示（使用统一字体管理器）"""
    print("🔧 测试修复后的字体显示...")
    
    # 导入并应用统一字体管理器
    try:
        from unified_font_manager import apply_unified_font_config, get_font_manager
        font_name = apply_unified_font_config()
        print(f"   ✅ 统一字体管理器已应用: {font_name}")
    except ImportError:
        print("   ⚠️  统一字体管理器不可用，使用备用配置")
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        font_name = 'Microsoft YaHei'
    
    # 创建测试数据
    data = create_test_data()
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 子图1: 时间序列图
    ax1.plot(data['time_series']['time'], data['time_series']['sensor_1'], 
             label='传感器1', linewidth=2)
    ax1.plot(data['time_series']['time'], data['time_series']['sensor_2'], 
             label='传感器2', linewidth=2)
    ax1.set_title('振动信号时域分析', fontsize=16, fontweight='bold')
    ax1.set_xlabel('时间 (秒)', fontsize=14)
    ax1.set_ylabel('振幅 (m/s²)', fontsize=14)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 子图2: 柱状图
    ax2.bar(data['categories']['names'], data['categories']['values'], 
            alpha=0.8, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'])
    ax2.set_title('传感器数据分类统计', fontsize=16, fontweight='bold')
    ax2.set_xlabel('数据类别', fontsize=14)
    ax2.set_ylabel('数量', fontsize=14)
    ax2.grid(True, alpha=0.3, axis='y')
    plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')
    
    # 子图3: 散点图
    ax3.scatter(data['models']['accuracy'], data['models']['training_time'], 
                s=100, alpha=0.7, c=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'])
    for i, model in enumerate(data['models']['names']):
        ax3.annotate(model, 
                    (data['models']['accuracy'][i], data['models']['training_time'][i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=10)
    ax3.set_title('模型性能对比分析', fontsize=16, fontweight='bold')
    ax3.set_xlabel('准确率', fontsize=14)
    ax3.set_ylabel('训练时间 (秒)', fontsize=14)
    ax3.grid(True, alpha=0.3)
    
    # 子图4: 多传感器对比
    ax4.plot(data['time_series']['time'], data['time_series']['sensor_3'], 
             label='传感器3', linewidth=2)
    ax4.plot(data['time_series']['time'], data['time_series']['sensor_4'], 
             label='传感器4', linewidth=2)
    ax4.fill_between(data['time_series']['time'], data['time_series']['sensor_3'], 
                     alpha=0.3, label='填充区域')
    ax4.set_title('多传感器信号对比', fontsize=16, fontweight='bold')
    ax4.set_xlabel('时间序列 (秒)', fontsize=14)
    ax4.set_ylabel('信号强度', fontsize=14)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 添加总标题
    fig.suptitle(f'修复后字体显示测试 - 使用字体: {font_name}', 
                fontsize=18, fontweight='bold', y=0.98)
    
    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    
    # 保存图表
    output_path = 'font_test_after_fix.png'
    plt.savefig(output_path, dpi=330, bbox_inches='tight', 
               facecolor='white', edgecolor='none')
    plt.close(fig)
    
    print(f"   ✅ 修复后测试图表已保存: {output_path}")
    return output_path

def test_improved_visualization_base():
    """测试改进的可视化基础类"""
    print("🎨 测试改进的可视化基础类...")
    
    try:
        from improved_visualization_base import ImprovedVisualizationBase
        
        # 初始化可视化基础类
        viz_base = ImprovedVisualizationBase(output_dir='font_test_output')
        
        # 创建测试数据
        data = create_test_data()
        
        # 创建图表
        fig, ax = plt.subplots(figsize=viz_base.chart_config['figsize_single'])
        
        # 绘制测试图表
        ax.plot(data['time_series']['time'], data['time_series']['sensor_1'], 
                label='传感器1', linewidth=2)
        ax.plot(data['time_series']['time'], data['time_series']['sensor_2'], 
                label='传感器2', linewidth=2)
        
        ax.set_xlabel('时间 (秒)', fontweight='bold')
        ax.set_ylabel('振幅 (m/s²)', fontweight='bold')
        ax.set_title('改进可视化基础类测试', fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=viz_base.chart_config['grid_alpha'])
        
        # 保存图表
        filepath = viz_base.save_chart(fig, 'improved_base_test.png', 'chinese')
        plt.close(fig)
        
        print(f"   ✅ 改进基础类测试图表已保存: {filepath}")
        print(f"   🔤 使用字体: {viz_base.chinese_font}")
        
        return filepath
        
    except ImportError as e:
        print(f"   ❌ 改进可视化基础类导入失败: {e}")
        return None

def main():
    """主测试函数"""
    print("🔧 中文字体修复效果测试")
    print("=" * 80)
    print("🎯 目标: 验证振动信号分析系统中文字体显示问题修复效果")
    print("📊 测试内容: 修复前后对比、统一字体管理器、改进可视化基础类")
    print("=" * 80)
    
    test_results = []
    
    # 1. 测试修复前的字体显示
    try:
        before_path = test_font_before_fix()
        test_results.append(('修复前测试', True, before_path))
    except Exception as e:
        print(f"   ❌ 修复前测试失败: {e}")
        test_results.append(('修复前测试', False, str(e)))
    
    # 2. 测试修复后的字体显示
    try:
        after_path = test_font_after_fix()
        test_results.append(('修复后测试', True, after_path))
    except Exception as e:
        print(f"   ❌ 修复后测试失败: {e}")
        test_results.append(('修复后测试', False, str(e)))
    
    # 3. 测试改进的可视化基础类
    try:
        base_path = test_improved_visualization_base()
        test_results.append(('改进基础类测试', base_path is not None, base_path))
    except Exception as e:
        print(f"   ❌ 改进基础类测试失败: {e}")
        test_results.append(('改进基础类测试', False, str(e)))
    
    # 4. 测试统一字体管理器
    try:
        from unified_font_manager import create_font_test
        font_test_path = create_font_test()
        test_results.append(('统一字体管理器测试', True, font_test_path))
    except Exception as e:
        print(f"   ❌ 统一字体管理器测试失败: {e}")
        test_results.append(('统一字体管理器测试', False, str(e)))
    
    # 总结测试结果
    print("\n" + "=" * 80)
    print("🎉 中文字体修复测试结果总结")
    print("=" * 80)
    
    successful_tests = sum(1 for _, success, _ in test_results if success)
    total_tests = len(test_results)
    
    print(f"📊 测试通过率: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
    
    for test_name, success, result in test_results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success and result and result.endswith('.png'):
            print(f"      📁 输出文件: {result}")
        elif not success:
            print(f"      ❌ 错误信息: {result}")
    
    if successful_tests == total_tests:
        print("\n🎉 所有测试通过!")
        print("✅ 中文字体显示问题修复成功!")
        print("🔤 统一字体管理器工作正常")
        print("🎨 改进可视化基础类功能完善")
        print("\n📋 生成的测试文件:")
        for test_name, success, result in test_results:
            if success and result and result.endswith('.png'):
                print(f"   - {result}")
        print("\n💡 建议:")
        print("   1. 查看生成的图表文件，对比修复前后效果")
        print("   2. 运行主程序 unified_vibration_analysis.py 验证完整系统")
        return True
    else:
        print("\n❌ 部分测试失败")
        print("🔧 需要进一步检查和修复")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
