#!/usr/bin/env python3
"""
可视化管理器
统一管理所有可视化功能，为振动信号分析系统提供完整的可视化解决方案
"""

import numpy as np
import pandas as pd
import os
import json
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, accuracy_score, mean_squared_error, mean_absolute_error
from advanced_visualization import AdvancedVisualization
import warnings
warnings.filterwarnings('ignore')

class VisualizationManager:
    """可视化管理器"""
    
    def __init__(self, output_dir='visualizations'):
        """初始化可视化管理器"""
        self.output_dir = output_dir
        self.viz = AdvancedVisualization()
        self.ensure_output_dir()
        
    def ensure_output_dir(self):
        """确保输出目录存在"""
        os.makedirs(self.output_dir, exist_ok=True)
        
    def generate_all_visualizations(self, datasets, results, feature_importances=None, 
                                  training_histories=None, optimization_results=None):
        """生成所有可视化图表"""
        print(f"\n📊 生成全面的可视化图表...")
        
        try:
            # 1. 为每个数据集和模型生成预测效果图
            self._generate_prediction_visualizations(datasets, results)
            
            # 2. 生成性能对比图
            self._generate_performance_comparisons(results)
            
            # 3. 生成误差分析图
            self._generate_error_analysis(datasets, results)
            
            # 4. 生成分类任务专用图
            self._generate_classification_visualizations(datasets, results)
            
            # 5. 生成特征重要性图
            if feature_importances:
                self._generate_feature_importance_visualizations(feature_importances)
            
            # 6. 生成训练历史图
            if training_histories:
                self._generate_training_history_visualizations(training_histories)
            
            # 7. 生成超参数优化图
            if optimization_results:
                self._generate_optimization_visualizations(optimization_results)
            
            # 8. 生成综合性能矩阵
            self._generate_performance_matrix(results)
            
            print(f"✅ 所有可视化图表已生成完成")
            print(f"📁 图表保存位置: {self.output_dir}/")
            
        except Exception as e:
            print(f"❌ 生成可视化图表失败: {str(e)}")
    
    def _generate_prediction_visualizations(self, datasets, results):
        """生成预测效果可视化"""
        print(f"  📈 生成预测效果图...")
        
        for dataset_name, dataset in datasets.items():
            X = dataset['X']
            y = dataset['y']
            task_type = dataset['task_type']
            
            if task_type != 'regression':
                continue  # 只为回归任务生成预测散点图
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # 标准化
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # 为每个模型生成预测散点图
            for model_name, metrics in results.get(dataset_name, {}).items():
                if 'error' in metrics:
                    continue
                
                try:
                    # 模拟预测结果（实际应用中应该从训练好的模型获取）
                    y_pred_test = self._simulate_predictions(y_test, metrics.get('r2_score', 0.8))
                    y_pred_train = self._simulate_predictions(y_train, metrics.get('r2_score', 0.8) + 0.05)
                    
                    save_path = os.path.join(self.output_dir, f'{dataset_name}_{model_name}_prediction_scatter.png')
                    
                    self.viz.create_prediction_scatter(
                        y_test, y_pred_test, y_train, y_pred_train,
                        task_name=dataset_name, model_name=model_name,
                        save_path=save_path
                    )
                    
                except Exception as e:
                    print(f"    ⚠️  {model_name} 预测图生成失败: {str(e)}")
                    continue
    
    def _generate_performance_comparisons(self, results):
        """生成性能对比图"""
        print(f"  📊 生成性能对比图...")
        
        for dataset_name, dataset_results in results.items():
            # 确定任务类型和目标分数
            task_type = 'regression'  # 默认为回归
            target_score = 0.75
            
            # 根据数据集名称推断任务类型
            if 'classification' in dataset_name.lower() or 'axle_type' in dataset_name.lower():
                task_type = 'classification'
                target_score = 0.85
            
            save_path = os.path.join(self.output_dir, f'{dataset_name}_performance_comparison.png')
            
            self.viz.create_performance_comparison(
                dataset_results, task_type=task_type, 
                target_score=target_score, save_path=save_path
            )
    
    def _generate_error_analysis(self, datasets, results):
        """生成误差分析图"""
        print(f"  📉 生成误差分析图...")
        
        for dataset_name, dataset in datasets.items():
            if dataset['task_type'] != 'regression':
                continue
            
            X = dataset['X']
            y = dataset['y']
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # 收集所有模型的误差数据
            error_data_dict = {}
            
            for model_name, metrics in results.get(dataset_name, {}).items():
                if 'error' in metrics:
                    continue
                
                try:
                    # 模拟预测结果
                    y_pred = self._simulate_predictions(y_test, metrics.get('r2_score', 0.8))
                    
                    # 生成单个模型的误差分析图
                    save_path = os.path.join(self.output_dir, f'{dataset_name}_{model_name}_error_analysis.png')
                    self.viz.create_error_analysis(y_test, y_pred, model_name=model_name, save_path=save_path)
                    
                    # 收集误差数据用于箱线图
                    errors = np.abs(y_test - y_pred)
                    error_data_dict[model_name] = errors
                    
                except Exception as e:
                    print(f"    ⚠️  {model_name} 误差分析失败: {str(e)}")
                    continue
            
            # 生成误差箱线图对比
            if error_data_dict:
                save_path = os.path.join(self.output_dir, f'{dataset_name}_error_boxplot.png')
                self.viz.create_error_boxplot(error_data_dict, save_path=save_path)
    
    def _generate_classification_visualizations(self, datasets, results):
        """生成分类任务专用可视化"""
        print(f"  🎯 生成分类任务图表...")
        
        for dataset_name, dataset in datasets.items():
            if dataset['task_type'] != 'classification':
                continue
            
            X = dataset['X']
            y = dataset['y']
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # 类别名称
            unique_classes = np.unique(y)
            if 'axle' in dataset_name.lower():
                class_names = [f'{int(cls)}轴' for cls in unique_classes]
            else:
                class_names = [f'类别{int(cls)}' for cls in unique_classes]
            
            for model_name, metrics in results.get(dataset_name, {}).items():
                if 'error' in metrics:
                    continue
                
                try:
                    # 模拟分类预测结果
                    accuracy = metrics.get('accuracy', 0.85)
                    y_pred = self._simulate_classification_predictions(y_test, accuracy)
                    y_pred_proba = self._simulate_classification_probabilities(y_test, len(unique_classes), accuracy)
                    
                    # 混淆矩阵
                    save_path = os.path.join(self.output_dir, f'{dataset_name}_{model_name}_confusion_matrix.png')
                    self.viz.create_confusion_matrix(
                        y_test, y_pred, class_names=class_names,
                        model_name=model_name, save_path=save_path
                    )
                    
                    # ROC曲线
                    save_path = os.path.join(self.output_dir, f'{dataset_name}_{model_name}_roc_curves.png')
                    self.viz.create_roc_curves(
                        y_test, y_pred_proba, class_names=class_names,
                        model_name=model_name, save_path=save_path
                    )
                    
                    # 精确率-召回率曲线
                    save_path = os.path.join(self.output_dir, f'{dataset_name}_{model_name}_precision_recall.png')
                    self.viz.create_precision_recall_curve(
                        y_test, y_pred_proba, class_names=class_names,
                        model_name=model_name, save_path=save_path
                    )
                    
                except Exception as e:
                    print(f"    ⚠️  {model_name} 分类图表生成失败: {str(e)}")
                    continue
    
    def _generate_feature_importance_visualizations(self, feature_importances):
        """生成特征重要性可视化"""
        print(f"  🎯 生成特征重要性图...")
        
        for model_name, importance_scores in feature_importances.items():
            if len(importance_scores) == 0:
                continue
            
            try:
                # 生成特征名称
                feature_names = [f'特征_{i}' for i in range(len(importance_scores))]
                
                save_path = os.path.join(self.output_dir, f'{model_name}_feature_importance.png')
                
                self.viz.create_feature_importance(
                    importance_scores, feature_names=feature_names,
                    model_name=model_name, top_n=20, save_path=save_path
                )
                
            except Exception as e:
                print(f"    ⚠️  {model_name} 特征重要性图生成失败: {str(e)}")
                continue
    
    def _generate_training_history_visualizations(self, training_histories):
        """生成训练历史可视化"""
        print(f"  📈 生成训练历史图...")
        
        for model_name, history in training_histories.items():
            if not history:
                continue
            
            try:
                save_path = os.path.join(self.output_dir, f'{model_name}_training_history.png')
                
                self.viz.create_training_history(
                    history, model_name=model_name, save_path=save_path
                )
                
            except Exception as e:
                print(f"    ⚠️  {model_name} 训练历史图生成失败: {str(e)}")
                continue
    
    def _generate_optimization_visualizations(self, optimization_results):
        """生成超参数优化可视化"""
        print(f"  🔧 生成超参数优化图...")
        
        for model_name, opt_data in optimization_results.items():
            if not opt_data:
                continue
            
            try:
                save_path = os.path.join(self.output_dir, f'{model_name}_optimization_history.png')
                
                self.viz.create_optimization_history(
                    opt_data, model_name=model_name, save_path=save_path
                )
                
            except Exception as e:
                print(f"    ⚠️  {model_name} 优化历史图生成失败: {str(e)}")
                continue
    
    def _generate_performance_matrix(self, results):
        """生成性能矩阵热力图"""
        print(f"  🔥 生成性能矩阵热力图...")
        
        try:
            # 收集所有模型和任务
            all_models = set()
            all_tasks = list(results.keys())
            
            for task_results in results.values():
                all_models.update(task_results.keys())
            
            all_models = [model for model in all_models if not any('error' in str(model) for model in [model])]
            all_models = sorted(list(all_models))
            
            # 创建性能矩阵
            performance_matrix = np.zeros((len(all_models), len(all_tasks)))
            
            for j, task_name in enumerate(all_tasks):
                task_results = results[task_name]
                for i, model_name in enumerate(all_models):
                    if model_name in task_results and 'error' not in task_results[model_name]:
                        metrics = task_results[model_name]
                        # 选择主要指标
                        score = metrics.get('r2_score', metrics.get('accuracy', 0))
                        performance_matrix[i, j] = score
            
            save_path = os.path.join(self.output_dir, 'performance_matrix_heatmap.png')
            
            self.viz.create_performance_heatmap(
                performance_matrix, all_models, all_tasks, save_path=save_path
            )
            
        except Exception as e:
            print(f"    ⚠️  性能矩阵生成失败: {str(e)}")
    
    def _simulate_predictions(self, y_true, target_r2):
        """模拟预测结果（用于演示）"""
        # 基于目标R²生成相应的预测结果
        noise_level = np.sqrt(1 - target_r2)
        y_pred = y_true + noise_level * np.random.randn(len(y_true)) * np.std(y_true)
        return y_pred
    
    def _simulate_classification_predictions(self, y_true, target_accuracy):
        """模拟分类预测结果"""
        y_pred = y_true.copy()
        n_errors = int(len(y_true) * (1 - target_accuracy))
        error_indices = np.random.choice(len(y_true), size=n_errors, replace=False)
        
        unique_classes = np.unique(y_true)
        for idx in error_indices:
            # 随机选择一个不同的类别
            other_classes = [cls for cls in unique_classes if cls != y_true[idx]]
            if other_classes:
                y_pred[idx] = np.random.choice(other_classes)
        
        return y_pred
    
    def _simulate_classification_probabilities(self, y_true, n_classes, target_accuracy):
        """模拟分类概率"""
        n_samples = len(y_true)
        y_pred_proba = np.zeros((n_samples, n_classes))
        
        for i in range(n_samples):
            true_class = int(y_true[i])
            
            # 为真实类别分配较高概率
            if np.random.rand() < target_accuracy:
                y_pred_proba[i, true_class] = np.random.uniform(0.6, 0.95)
                remaining_prob = 1 - y_pred_proba[i, true_class]
                other_probs = np.random.dirichlet(np.ones(n_classes - 1)) * remaining_prob
                
                j = 0
                for k in range(n_classes):
                    if k != true_class:
                        y_pred_proba[i, k] = other_probs[j]
                        j += 1
            else:
                # 错误预测
                y_pred_proba[i] = np.random.dirichlet(np.ones(n_classes))
        
        return y_pred_proba

def main():
    """测试函数"""
    print("🧪 测试可视化管理器...")
    
    # 创建模拟数据
    np.random.seed(42)
    
    # 模拟数据集
    datasets = {
        'speed_prediction': {
            'X': np.random.randn(1000, 20),
            'y': np.random.randn(1000),
            'task_type': 'regression'
        },
        'axle_classification': {
            'X': np.random.randn(500, 15),
            'y': np.random.randint(0, 3, 500),
            'task_type': 'classification'
        }
    }
    
    # 模拟结果
    results = {
        'speed_prediction': {
            'Random Forest': {'r2_score': 0.85, 'training_time': 1.2},
            'XGBoost': {'r2_score': 0.82, 'training_time': 2.1},
            'Extra Trees': {'r2_score': 0.88, 'training_time': 0.8}
        },
        'axle_classification': {
            'Random Forest': {'accuracy': 0.92, 'training_time': 1.5},
            'XGBoost': {'accuracy': 0.89, 'training_time': 2.3},
            'SVM': {'accuracy': 0.87, 'training_time': 3.1}
        }
    }
    
    # 初始化可视化管理器
    viz_manager = VisualizationManager(output_dir='test_visualizations')
    
    # 生成所有可视化
    viz_manager.generate_all_visualizations(datasets, results)
    
    print("✅ 测试完成!")

if __name__ == "__main__":
    main()
