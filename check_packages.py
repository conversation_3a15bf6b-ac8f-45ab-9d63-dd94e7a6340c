#!/usr/bin/env python3
"""
包检测和诊断脚本
专门用于诊断深度学习增强振动信号分析系统的依赖包问题
"""

import sys
import importlib
import subprocess
from typing import Dict, List, Tuple
import os

def get_package_mapping() -> Dict[str, str]:
    """获取包名到导入名的映射"""
    return {
        # 核心数据处理
        'numpy': 'numpy',
        'pandas': 'pandas',
        'scipy': 'scipy',
        
        # 机器学习 (注意包名和导入名的差异)
        'scikit-learn': 'sklearn',  # 重要：包名是scikit-learn，导入名是sklearn
        'joblib': 'joblib',
        
        # 深度学习
        'torch': 'torch',
        'torchvision': 'torchvision', 
        'torchaudio': 'torchaudio',
        
        # 梯度提升
        'xgboost': 'xgboost',
        'lightgbm': 'lightgbm',
        'optuna': 'optuna',
        
        # 可视化
        'matplotlib': 'matplotlib',
        'seaborn': 'seaborn',
        'plotly': 'plotly',
        
        # 工具包
        'chardet': 'chardet',
        'tqdm': 'tqdm',
        'psutil': 'psutil',
        'openpyxl': 'openpyxl',
        
        # 信号处理
        'PyWavelets': 'pywt',  # 注意：包名是PyWavelets，导入名是pywt
        'statsmodels': 'statsmodels'
    }

def check_python_environment():
    """检查Python环境信息"""
    print("🔍 Python环境信息:")
    print(f"   Python版本: {sys.version}")
    print(f"   Python路径: {sys.executable}")
    print(f"   虚拟环境: {'是' if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix) else '否'}")
    print(f"   工作目录: {os.getcwd()}")
    print()

def check_package_installation() -> Dict[str, Tuple[bool, str, str]]:
    """检查包安装情况"""
    print("📦 检查包安装情况:")
    print("-" * 60)
    
    package_mapping = get_package_mapping()
    results = {}
    
    for package_name, import_name in package_mapping.items():
        try:
            # 尝试导入包
            module = importlib.import_module(import_name)
            
            # 获取版本信息
            version = getattr(module, '__version__', 'unknown')
            
            print(f"✅ {package_name:15} -> {import_name:15} (v{version})")
            results[package_name] = (True, version, import_name)
            
        except ImportError as e:
            print(f"❌ {package_name:15} -> {import_name:15} (未安装: {str(e)})")
            results[package_name] = (False, str(e), import_name)
            
        except Exception as e:
            print(f"⚠️  {package_name:15} -> {import_name:15} (错误: {str(e)})")
            results[package_name] = (False, str(e), import_name)
    
    return results

def check_pip_list():
    """检查pip list中的包"""
    print("\n📋 检查pip安装的包:")
    print("-" * 60)
    
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'list'], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            
            # 查找关键包
            key_packages = ['numpy', 'pandas', 'scikit-learn', 'torch', 'xgboost', 'optuna']
            found_packages = {}
            
            for line in lines[2:]:  # 跳过标题行
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 2:
                        pkg_name = parts[0].lower()
                        pkg_version = parts[1]
                        
                        for key_pkg in key_packages:
                            if key_pkg.lower() in pkg_name or pkg_name in key_pkg.lower():
                                found_packages[pkg_name] = pkg_version
            
            print("关键包在pip中的安装情况:")
            for pkg, version in found_packages.items():
                print(f"  ✅ {pkg}: {version}")
            
            # 检查是否有缺失的关键包
            missing_in_pip = []
            for key_pkg in key_packages:
                found = False
                for installed_pkg in found_packages.keys():
                    if key_pkg.lower() in installed_pkg or installed_pkg in key_pkg.lower():
                        found = True
                        break
                if not found:
                    missing_in_pip.append(key_pkg)
            
            if missing_in_pip:
                print(f"\n⚠️  pip中未找到的关键包: {missing_in_pip}")
            
        else:
            print(f"❌ 无法获取pip列表: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 检查pip列表失败: {str(e)}")

def diagnose_specific_package(package_name: str, import_name: str):
    """诊断特定包的问题"""
    print(f"\n🔬 诊断包: {package_name} (导入名: {import_name})")
    print("-" * 40)
    
    # 1. 检查是否在pip中安装
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'show', package_name], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ pip show {package_name}:")
            for line in result.stdout.strip().split('\n'):
                if line.startswith(('Name:', 'Version:', 'Location:')):
                    print(f"   {line}")
        else:
            print(f"❌ pip show {package_name} 失败")
            
    except Exception as e:
        print(f"❌ pip show 检查失败: {str(e)}")
    
    # 2. 检查导入路径
    try:
        module = importlib.import_module(import_name)
        module_file = getattr(module, '__file__', 'unknown')
        print(f"✅ 导入成功，模块路径: {module_file}")
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        
        # 3. 检查可能的替代导入名
        alternative_names = [
            package_name.lower(),
            package_name.replace('-', '_'),
            package_name.replace('_', '-'),
            import_name.lower()
        ]
        
        print("尝试替代导入名:")
        for alt_name in set(alternative_names):
            try:
                importlib.import_module(alt_name)
                print(f"   ✅ {alt_name} 可以导入")
            except ImportError:
                print(f"   ❌ {alt_name} 无法导入")

def provide_fix_suggestions(results: Dict[str, Tuple[bool, str, str]]):
    """提供修复建议"""
    print("\n💡 修复建议:")
    print("-" * 60)
    
    failed_packages = [pkg for pkg, (success, _, _) in results.items() if not success]
    
    if not failed_packages:
        print("🎉 所有包都已正确安装!")
        return
    
    print("以下包需要安装或修复:")
    
    # 按包名分组提供安装命令
    install_commands = []
    
    for pkg in failed_packages:
        if pkg == 'scikit-learn':
            install_commands.append('scikit-learn')
        elif pkg == 'PyWavelets':
            install_commands.append('PyWavelets')
        else:
            install_commands.append(pkg)
    
    if install_commands:
        print(f"\n📦 安装命令:")
        print(f"python -m pip install {' '.join(install_commands)}")
        
        print(f"\n🔄 或者分别安装:")
        for cmd in install_commands:
            print(f"python -m pip install {cmd}")
    
    # 特殊情况的建议
    if 'torch' in failed_packages:
        print(f"\n🚀 PyTorch安装建议:")
        print(f"# CPU版本:")
        print(f"python -m pip install torch torchvision torchaudio")
        print(f"# GPU版本 (CUDA 12.1):")
        print(f"python -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121")

def run_comprehensive_check():
    """运行全面检查"""
    print("🔍 深度学习增强振动信号分析系统 - 包检测诊断")
    print("=" * 70)
    
    # 1. 检查Python环境
    check_python_environment()
    
    # 2. 检查包安装情况
    results = check_package_installation()
    
    # 3. 检查pip列表
    check_pip_list()
    
    # 4. 特别诊断scikit-learn
    diagnose_specific_package('scikit-learn', 'sklearn')
    
    # 5. 提供修复建议
    provide_fix_suggestions(results)
    
    # 6. 生成总结
    print(f"\n📊 检查总结:")
    print("-" * 30)
    
    total_packages = len(results)
    successful_packages = sum(1 for success, _, _ in results.values() if success)
    
    print(f"总包数: {total_packages}")
    print(f"成功安装: {successful_packages}")
    print(f"成功率: {successful_packages/total_packages*100:.1f}%")
    
    if successful_packages == total_packages:
        print("🎉 所有包检测通过!")
        print("✅ 可以运行: python setup_real_data_training.py")
    elif successful_packages >= total_packages * 0.8:
        print("⚠️  大部分包已安装，系统基本可用")
    else:
        print("❌ 需要安装更多包才能正常使用")
    
    return results

def main():
    """主函数"""
    try:
        results = run_comprehensive_check()
        
        # 保存诊断报告
        with open('package_diagnosis_report.txt', 'w', encoding='utf-8') as f:
            f.write("深度学习增强振动信号分析系统 - 包诊断报告\n")
            f.write("=" * 60 + "\n\n")
            
            f.write("包检测结果:\n")
            for pkg, (success, info, import_name) in results.items():
                status = "✅ 成功" if success else "❌ 失败"
                f.write(f"{pkg} -> {import_name}: {status}\n")
                if not success:
                    f.write(f"   错误信息: {info}\n")
            
            f.write(f"\n总体成功率: {sum(1 for s, _, _ in results.values() if s)}/{len(results)}\n")
        
        print(f"\n📁 诊断报告已保存: package_diagnosis_report.txt")
        
    except Exception as e:
        print(f"❌ 诊断过程出错: {str(e)}")

if __name__ == "__main__":
    main()
